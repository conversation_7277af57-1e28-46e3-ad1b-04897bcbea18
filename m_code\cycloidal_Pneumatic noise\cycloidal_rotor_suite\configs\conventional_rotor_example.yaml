B: 2
L_span: 1.74
N_points: 25
N_wake_max: 3000
R_rotor: 2.0
T_acoustic_record: 1.5
T_buildup: 0.75
V_inf_x: 34.6
V_inf_y: 0.0
_auto_fix_applied: '2025-08-01 15:04:45'
_auto_fix_version: v3.0
auto_performance_summary: true
c: 0.121
c0: 343.0
conventional_rotor_params:
  A2s: 0.0
  A3s: 0.0
  B2s: 0.0
  B3s: 0.0
  chord_distribution_type: linear
  collective_pitch: 6.8
  cyclic_pitch_lat: -1.5
  cyclic_pitch_lon: -2.0
  enable_coning_angle: false
  enable_higher_harmonics: false
  hub_radius: 0.15
  root_chord: 0.15
  root_twist: 12.0
  solidity: 0.08
  thrust_coefficient: 0.008
  tip_chord: 0.08
  tip_twist: -8.0
  twist_deg: -8.0
  twist_distribution_type: linear
create_animations: true
cycloidal_rotor_params:
  enable_asymmetric_pitch: true
  enable_nonlinear_pitch: false
  pitch_amplitude_bottom: 15.0
  pitch_amplitude_top: 15.0
  pitch_bias_angle: 0.0
  pitch_nonlinearity_factor: 1.0
  pitch_phase_offset: 0.0
dt_sim: 0.0001
enable_3d_uvlm: true
enable_broadband_noise: true
enable_compressibility: false
enable_ground_effect: false
enable_lb_model: true
enable_turbulence_model: true
enable_vatistas_core: true
enable_viscous_effects: true
enable_wake_pruning: false
max_iterations: 150
n_rpm: 1040.0
observer_pos:
- 3.45
- -1.33
- -1.18
output_level: comprehensive
physics:
  aerodynamics:
    uvlm_settings:
      batch_processing: true
      condition_number_threshold: 1000000000000.0
      cuda_optimization_level: O2
      enable_mixed_precision: true
      enable_preconditioning: true
      gpu_batch_size: 1000
      gpu_device_id: 0
      gpu_memory_fraction: 0.8
      numerical_damping: 1.0e-06
      preconditioner_type: diagonal
      use_gpu: true
      vortex_core_radius: 0.02
rho: 1.225
rotor_type: conventional
save_checkpoints: true
solver_options:
  uvlm:
    batch_processing: true
    condition_number_threshold: 1000000000000.0
    cuda_optimization_level: O2
    enable_mixed_precision: true
    enable_preconditioning: true
    gpu_batch_size: 1000
    gpu_device_id: 0
    gpu_memory_fraction: 0.8
    numerical_damping: 1.0e-06
    preconditioner_type: diagonal
    use_gpu: true
    vortex_core_radius: 0.02
solvers:
  uvlm:
    condition_number_threshold: 1000000000000.0
    enable_preconditioning: true
    numerical_damping: 1.0e-06
    preconditioner_type: diagonal
    vortex_core_radius: 0.02
tolerance: 1.0e-07
use_advanced_lb_features: true
uvlm_settings:
  batch_processing: true
  condition_number_threshold: 1000000000000.0
  cuda_optimization_level: O2
  enable_mixed_precision: true
  enable_preconditioning: true
  gpu_batch_size: 1000
  gpu_device_id: 0
  gpu_memory_fraction: 0.8
  numerical_damping: 1.0e-06
  preconditioner_type: diagonal
  use_gpu: true
  vortex_core_radius: 0.02
