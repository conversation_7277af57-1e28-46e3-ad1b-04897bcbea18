#!/usr/bin/env python3
"""
系统全面诊断脚本
===============

对整个cycloidal_rotor_suite项目进行全面的系统诊断，
识别所有模块中的潜在问题和错误。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import traceback
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class SystemDiagnostics:
    """系统诊断器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.successes = []
        self.module_status = {}
        
    def log_issue(self, category: str, module: str, issue: str, severity: str = "ERROR"):
        """记录问题"""
        self.issues.append({
            'category': category,
            'module': module,
            'issue': issue,
            'severity': severity
        })
        
    def log_warning(self, category: str, module: str, warning: str):
        """记录警告"""
        self.warnings.append({
            'category': category,
            'module': module,
            'warning': warning
        })
        
    def log_success(self, category: str, module: str, message: str):
        """记录成功"""
        self.successes.append({
            'category': category,
            'module': module,
            'message': message
        })

    def test_import_modules(self):
        """测试模块导入"""
        print("=" * 60)
        print("1. 模块导入测试")
        print("=" * 60)
        
        # 核心模块列表
        core_modules = [
            'cyclone_sim',
            'cyclone_sim.simulation',
            'cyclone_sim.config_loader',
            'cyclone_sim.core.aerodynamics.uvlm_solver',
            'cyclone_sim.core.aerodynamics.bemt_solver',
            'cyclone_sim.core.aerodynamics.lifting_line_solver',
            'cyclone_sim.core.aerodynamics.solver_factory',
            'cyclone_sim.core.acoustics.fwh_solver',
            'cyclone_sim.core.acoustics.bpm_noise',
            'cyclone_sim.core.solvers.factory',
            'cyclone_sim.postpro.acoustic_analyzer',
            'cyclone_sim.utils.error_handling',
            'academic_validation_pro',
        ]
        
        for module_name in core_modules:
            try:
                module = importlib.import_module(module_name)
                self.log_success("IMPORT", module_name, "导入成功")
                print(f"✅ {module_name}")
                
                # 检查模块属性
                if hasattr(module, '__version__'):
                    print(f"   版本: {module.__version__}")
                    
            except ImportError as e:
                self.log_issue("IMPORT", module_name, f"导入失败: {e}")
                print(f"❌ {module_name}: {e}")
            except Exception as e:
                self.log_issue("IMPORT", module_name, f"未知错误: {e}")
                print(f"⚠️  {module_name}: {e}")

    def test_config_loading(self):
        """测试配置加载"""
        print("\n" + "=" * 60)
        print("2. 配置文件测试")
        print("=" * 60)
        
        config_files = [
            'configs/basic_simulation.yaml',
            'configs/advanced_simulation.yaml',
            'configs/caradonna_tung_validation.yaml',
            'configs/conventional_rotor_example.yaml',
        ]
        
        for config_file in config_files:
            config_path = project_root / config_file
            
            if not config_path.exists():
                self.log_issue("CONFIG", config_file, "配置文件不存在")
                print(f"❌ {config_file}: 文件不存在")
                continue
                
            try:
                # 尝试加载配置
                from cyclone_sim.config_loader import ConfigLoader
                
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    config = ConfigLoader.from_yaml(str(config_path))
                elif config_file.endswith('.json'):
                    config = ConfigLoader.from_json(str(config_path))
                else:
                    raise ValueError("不支持的配置文件格式")
                
                self.log_success("CONFIG", config_file, "配置加载成功")
                print(f"✅ {config_file}")
                
                # 检查关键参数
                required_params = ['n_rpm', 'B', 'c', 'R_rotor']
                missing_params = []
                
                for param in required_params:
                    if not hasattr(config, param):
                        missing_params.append(param)
                
                if missing_params:
                    self.log_warning("CONFIG", config_file, f"缺少参数: {missing_params}")
                    print(f"   ⚠️  缺少参数: {missing_params}")
                else:
                    print(f"   参数完整: RPM={config.n_rpm}, B={config.B}")
                    
            except Exception as e:
                self.log_issue("CONFIG", config_file, f"配置加载失败: {e}")
                print(f"❌ {config_file}: {e}")

    def test_solver_creation(self):
        """测试求解器创建"""
        print("\n" + "=" * 60)
        print("3. 求解器创建测试")
        print("=" * 60)
        
        try:
            # 创建基本配置
            from cyclone_sim.config_loader import ConfigLoader
            
            # 使用内置的基本配置
            basic_config_dict = {
                'n_rpm': 600.0,
                'B': 4,
                'c': 0.08,
                'R_rotor': 0.4,
                'rho': 1.225,
                'enable_3d_uvlm': False,
                'enable_lb_model': False,
                'solver_fidelity': 'medium',
                'dt_sim': 0.001,
                'T_buildup': 0.1,
                'T_acoustic_record': 0.1,
            }
            
            config = ConfigLoader.from_dict(basic_config_dict)
            print("✅ 基本配置创建成功")
            
            # 测试不同求解器
            solver_types = ['BEMT', 'UVLM', 'LiftingLine']
            
            for solver_type in solver_types:
                try:
                    from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
                    
                    # 创建求解器
                    solver = SolverFactory.create_solver(
                        solver_type, 
                        config,
                        wake_system=None,
                        airfoil_database=None
                    )
                    
                    self.log_success("SOLVER", solver_type, "求解器创建成功")
                    print(f"✅ {solver_type} 求解器")
                    
                    # 测试基本方法
                    if hasattr(solver, 'get_solver_info'):
                        info = solver.get_solver_info()
                        print(f"   类型: {info.get('solver_type', 'unknown')}")
                        print(f"   保真度: {info.get('fidelity_level', 'unknown')}")
                    
                except Exception as e:
                    self.log_issue("SOLVER", solver_type, f"求解器创建失败: {e}")
                    print(f"❌ {solver_type}: {e}")
                    
        except Exception as e:
            self.log_issue("SOLVER", "GENERAL", f"求解器测试失败: {e}")
            print(f"❌ 求解器测试失败: {e}")

    def test_simulation_workflow(self):
        """测试仿真工作流程"""
        print("\n" + "=" * 60)
        print("4. 仿真工作流程测试")
        print("=" * 60)
        
        try:
            from cyclone_sim.simulation import CycloneSimulation
            from cyclone_sim.config_loader import ConfigLoader
            
            # 创建最小配置
            minimal_config = {
                'n_rpm': 300.0,
                'B': 3,
                'c': 0.06,
                'R_rotor': 0.3,
                'rho': 1.225,
                'enable_3d_uvlm': False,
                'enable_lb_model': False,
                'solver_fidelity': 'low',
                'dt_sim': 0.01,
                'T_buildup': 0.05,
                'T_acoustic_record': 0.05,
                'run_acoustics': False,  # 禁用声学计算以简化测试
            }
            
            config = ConfigLoader.from_dict(minimal_config)
            print("✅ 最小配置创建成功")
            
            # 创建仿真实例
            simulation = CycloneSimulation(config, output_dir="test_output")
            print("✅ 仿真实例创建成功")
            
            # 检查初始化状态
            if hasattr(simulation, 'is_initialized') and simulation.is_initialized:
                self.log_success("WORKFLOW", "SIMULATION", "仿真初始化成功")
                print("✅ 仿真初始化完成")
            else:
                self.log_warning("WORKFLOW", "SIMULATION", "仿真初始化状态未知")
                print("⚠️  仿真初始化状态未知")
            
            # 检查关键组件
            components = ['aero_solver', 'wake_system', 'data_manager']
            for component in components:
                if hasattr(simulation, component):
                    print(f"✅ {component} 组件存在")
                else:
                    self.log_warning("WORKFLOW", "SIMULATION", f"缺少{component}组件")
                    print(f"⚠️  缺少 {component} 组件")
            
            # 测试获取仿真信息
            if hasattr(simulation, 'get_simulation_info'):
                info = simulation.get_simulation_info()
                print(f"✅ 仿真信息获取成功")
                print(f"   输出目录: {info.get('output_dir', 'unknown')}")
            
        except Exception as e:
            self.log_issue("WORKFLOW", "SIMULATION", f"仿真工作流程测试失败: {e}")
            print(f"❌ 仿真工作流程测试失败: {e}")
            traceback.print_exc()

    def test_academic_validation(self):
        """测试学术验证模块"""
        print("\n" + "=" * 60)
        print("5. 学术验证模块测试")
        print("=" * 60)
        
        try:
            # 测试学术验证模块导入
            import academic_validation_pro
            print("✅ academic_validation_pro 模块导入成功")
            
            # 检查验证案例
            validation_modules = [
                'academic_validation_pro.cases.caradonna_tung',
                'academic_validation_pro.cases.hart_ii',
                'academic_validation_pro.analysis.error_metrics',
            ]
            
            for module_name in validation_modules:
                try:
                    module = importlib.import_module(module_name)
                    self.log_success("VALIDATION", module_name, "验证模块导入成功")
                    print(f"✅ {module_name}")
                except ImportError as e:
                    self.log_issue("VALIDATION", module_name, f"验证模块导入失败: {e}")
                    print(f"❌ {module_name}: {e}")
            
        except Exception as e:
            self.log_issue("VALIDATION", "GENERAL", f"学术验证测试失败: {e}")
            print(f"❌ 学术验证测试失败: {e}")

    def test_file_structure(self):
        """测试文件结构完整性"""
        print("\n" + "=" * 60)
        print("6. 文件结构完整性测试")
        print("=" * 60)
        
        # 关键目录和文件
        critical_paths = [
            'cyclone_sim/',
            'cyclone_sim/core/',
            'cyclone_sim/core/aerodynamics/',
            'cyclone_sim/core/acoustics/',
            'cyclone_sim/core/solvers/',
            'cyclone_sim/postpro/',
            'cyclone_sim/utils/',
            'academic_validation_pro/',
            'configs/',
            'scripts/',
            'tests/',
            'main.py',
            'requirements.txt',
        ]
        
        for path_str in critical_paths:
            path = project_root / path_str
            
            if path.exists():
                self.log_success("STRUCTURE", path_str, "路径存在")
                print(f"✅ {path_str}")
                
                # 对于目录，检查是否有__init__.py
                if path.is_dir() and path_str.endswith('/'):
                    init_file = path / '__init__.py'
                    if init_file.exists():
                        print(f"   📦 包结构完整")
                    else:
                        self.log_warning("STRUCTURE", path_str, "缺少__init__.py")
                        print(f"   ⚠️  缺少 __init__.py")
                        
            else:
                self.log_issue("STRUCTURE", path_str, "路径不存在")
                print(f"❌ {path_str}: 不存在")

    def test_dependencies(self):
        """测试依赖项"""
        print("\n" + "=" * 60)
        print("7. 依赖项测试")
        print("=" * 60)
        
        # 核心依赖
        core_dependencies = [
            'numpy',
            'scipy',
            'matplotlib',
            'yaml',
            'torch',
        ]
        
        # 可选依赖
        optional_dependencies = [
            'pyvista',
            'mayavi',
            'dash',
            'gputil',
            'psutil',
        ]
        
        print("核心依赖:")
        for dep in core_dependencies:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                self.log_success("DEPENDENCY", dep, f"版本 {version}")
                print(f"✅ {dep} ({version})")
            except ImportError:
                self.log_issue("DEPENDENCY", dep, "核心依赖缺失")
                print(f"❌ {dep}: 缺失")
        
        print("\n可选依赖:")
        for dep in optional_dependencies:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                self.log_success("DEPENDENCY", dep, f"版本 {version}")
                print(f"✅ {dep} ({version})")
            except ImportError:
                self.log_warning("DEPENDENCY", dep, "可选依赖缺失")
                print(f"⚠️  {dep}: 缺失 (可选)")

    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始系统全面诊断...")
        print("项目路径:", project_root)
        print()
        
        # 运行所有测试
        self.test_dependencies()
        self.test_file_structure()
        self.test_import_modules()
        self.test_config_loading()
        self.test_solver_creation()
        self.test_simulation_workflow()
        self.test_academic_validation()
        
        # 生成诊断报告
        self.generate_report()

    def generate_report(self):
        """生成诊断报告"""
        print("\n" + "=" * 80)
        print("📋 系统诊断报告")
        print("=" * 80)
        
        # 统计信息
        total_issues = len(self.issues)
        total_warnings = len(self.warnings)
        total_successes = len(self.successes)
        
        print(f"✅ 成功项目: {total_successes}")
        print(f"⚠️  警告项目: {total_warnings}")
        print(f"❌ 错误项目: {total_issues}")
        
        # 按类别分组
        categories = {}
        
        for item in self.issues + self.warnings + self.successes:
            category = item.get('category', 'UNKNOWN')
            if category not in categories:
                categories[category] = {'issues': 0, 'warnings': 0, 'successes': 0}
        
        for issue in self.issues:
            categories[issue['category']]['issues'] += 1
            
        for warning in self.warnings:
            categories[warning['category']]['warnings'] += 1
            
        for success in self.successes:
            categories[success['category']]['successes'] += 1
        
        print(f"\n按类别统计:")
        for category, stats in categories.items():
            print(f"  {category}: ✅{stats['successes']} ⚠️{stats['warnings']} ❌{stats['issues']}")
        
        # 详细错误列表
        if self.issues:
            print(f"\n❌ 详细错误列表:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. [{issue['category']}] {issue['module']}: {issue['issue']}")
        
        # 详细警告列表
        if self.warnings:
            print(f"\n⚠️  详细警告列表:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. [{warning['category']}] {warning['module']}: {warning['warning']}")
        
        # 总体评估
        print(f"\n🎯 总体评估:")
        if total_issues == 0:
            if total_warnings == 0:
                print("🎉 系统状态优秀！所有组件正常工作。")
            else:
                print("✅ 系统状态良好，有一些可选功能缺失。")
        elif total_issues < 5:
            print("⚠️  系统状态一般，存在一些需要修复的问题。")
        else:
            print("❌ 系统状态较差，存在多个严重问题需要修复。")
        
        # 保存报告
        self.save_report()

    def save_report(self):
        """保存诊断报告"""
        report_file = project_root / "system_diagnosis_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 系统诊断报告\n\n")
            f.write(f"生成时间: {os.popen('date').read().strip()}\n")
            f.write(f"项目路径: {project_root}\n\n")
            
            # 统计信息
            f.write("## 统计信息\n\n")
            f.write(f"- ✅ 成功项目: {len(self.successes)}\n")
            f.write(f"- ⚠️  警告项目: {len(self.warnings)}\n")
            f.write(f"- ❌ 错误项目: {len(self.issues)}\n\n")
            
            # 错误详情
            if self.issues:
                f.write("## 错误详情\n\n")
                for issue in self.issues:
                    f.write(f"### [{issue['category']}] {issue['module']}\n")
                    f.write(f"**错误**: {issue['issue']}\n")
                    f.write(f"**严重程度**: {issue['severity']}\n\n")
            
            # 警告详情
            if self.warnings:
                f.write("## 警告详情\n\n")
                for warning in self.warnings:
                    f.write(f"### [{warning['category']}] {warning['module']}\n")
                    f.write(f"**警告**: {warning['warning']}\n\n")
        
        print(f"\n📄 诊断报告已保存: {report_file}")


def main():
    """主函数"""
    diagnostics = SystemDiagnostics()
    diagnostics.run_full_diagnosis()


if __name__ == "__main__":
    main()