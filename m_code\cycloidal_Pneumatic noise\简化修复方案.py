#!/usr/bin/env python3
"""
BEMT求解器简化修复方案
====================

基于问题分析，提供一个简洁、可运行的BEMT求解器实现。
去除过度复杂的架构，专注于核心功能。

修复的主要问题：
1. 循环导入 -> 使用简单的类结构
2. 未完成的方法 -> 提供完整实现
3. 过度复杂的架构 -> 简化为单一类
4. 缺失的依赖 -> 内置基本实现

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, Tuple
import time


class SimpleBEMTSolver:
    """
    简化的BEMT求解器
    
    专注于核心功能，去除不必要的复杂性。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化求解器
        
        Args:
            config: 配置参数字典
        """
        # 核心参数（必需）
        self.R_rotor = config.get('R_rotor', 0.5)  # 转子半径 [m]
        self.B = config.get('B', 4)  # 桨叶数
        self.c = config.get('c', 0.08)  # 弦长 [m]
        self.omega = config.get('omega_rotor', 150.0)  # 角速度 [rad/s]
        self.rho = config.get('rho', 1.225)  # 空气密度 [kg/m³]
        
        # 求解参数
        self.n_elements = config.get('n_elements', 20)
        self.max_iter = config.get('max_iterations', 50)
        self.tolerance = config.get('tolerance', 1e-4)
        self.relaxation = config.get('relaxation_factor', 0.5)
        
        # 转子类型和控制
        self.rotor_type = config.get('rotor_type', 'conventional')
        self.collective = np.radians(config.get('collective_pitch', 8.0))
        self.twist = np.radians(config.get('twist_deg', -8.0))
        
        # 循环翼参数
        self.pitch_amplitude = np.radians(config.get('pitch_amplitude', 15.0))
        self.pitch_phase = np.radians(config.get('pitch_phase_offset', 0.0))
        
        # 物理模型开关
        self.enable_tip_loss = config.get('enable_tip_loss', True)
        self.enable_hub_loss = config.get('enable_hub_loss', True)
        
        # 计算派生参数
        self.disk_area = np.pi * self.R_rotor**2
        self.tip_speed = self.omega * self.R_rotor
        self.solidity = self.B * self.c / (np.pi * self.R_rotor)
        
        # 径向位置分布
        self.r_positions = self._generate_radial_positions()
        
        print(f"BEMT求解器初始化完成:")
        print(f"  R={self.R_rotor}m, B={self.B}, Ω={self.omega:.1f}rad/s")
        print(f"  叶素数: {self.n_elements}, 实度: {self.solidity:.3f}")
    
    def _generate_radial_positions(self) -> np.ndarray:
        """生成径向位置分布（余弦分布）"""
        hub_ratio = 0.1  # 桨毂比
        positions = []
        for i in range(self.n_elements):
            eta = np.cos(np.pi * (i + 0.5) / self.n_elements)
            r = hub_ratio * self.R_rotor + (1 - hub_ratio) * self.R_rotor * (eta + 1) / 2
            positions.append(r)
        return np.array(positions)
    
    def solve(self, azimuth: float = 0.0) -> Dict[str, Any]:
        """
        执行BEMT求解
        
        Args:
            azimuth: 方位角 [rad]
            
        Returns:
            求解结果字典
        """
        start_time = time.time()
        
        try:
            # 初始化诱导因子
            a = np.zeros(self.n_elements)      # 轴向诱导因子
            ap = np.zeros(self.n_elements)     # 切向诱导因子
            
            # BEMT迭代求解
            converged = False
            iteration = 0
            
            for iteration in range(self.max_iter):
                a_old = a.copy()
                ap_old = ap.copy()
                
                # 叶素计算
                for i in range(self.n_elements):
                    r = self.r_positions[i]
                    r_R = r / self.R_rotor
                    
                    # 局部速度
                    V_axial = a[i] * self.omega * self.R_rotor
                    V_tangential = self.omega * r * (1 + ap[i])
                    V_rel = np.sqrt(V_axial**2 + V_tangential**2)
                    
                    # 入流角
                    phi = np.arctan2(V_axial, V_tangential)
                    
                    # 攻角
                    pitch_angle = self._calculate_pitch_angle(azimuth, r_R)
                    alpha = pitch_angle - phi
                    
                    # 翼型特性
                    Cl, Cd = self._get_airfoil_coefficients(alpha, r_R)
                    
                    # 应用物理修正
                    if self.enable_tip_loss:
                        F_tip = self._tip_loss_correction(r_R, phi)
                        Cl *= F_tip
                        Cd *= F_tip
                    
                    if self.enable_hub_loss:
                        F_hub = self._hub_loss_correction(r_R)
                        Cl *= F_hub
                        Cd *= F_hub
                    
                    # 局部载荷系数
                    sigma = self.B * self.c / (2 * np.pi * r)  # 局部实度
                    
                    # BEM方程
                    CT_local = sigma * (Cl * np.cos(phi) + Cd * np.sin(phi))
                    CQ_local = sigma * (Cl * np.sin(phi) - Cd * np.cos(phi))
                    
                    # 更新诱导因子（添加安全检查）
                    try:
                        if CT_local > 0 and (1 + a[i]) > 1e-6:
                            a_new = CT_local / (4 * (1 + a[i]))
                            # Glauert修正
                            if a_new > 0.4:
                                a_new = 0.25 * (2 + np.sqrt(max(4 + 8 * CT_local, 0)))
                        else:
                            a_new = 0.0
                        
                        if abs(CQ_local) > 1e-10 and (1 - ap[i]) > 1e-6:
                            ap_new = CQ_local / (4 * r_R * (1 - ap[i]))
                        else:
                            ap_new = 0.0
                    except (ValueError, ZeroDivisionError):
                        a_new = 0.0
                        ap_new = 0.0
                    
                    # 应用松弛
                    a[i] = (1 - self.relaxation) * a[i] + self.relaxation * a_new
                    ap[i] = (1 - self.relaxation) * ap[i] + self.relaxation * ap_new
                    
                    # 限制范围
                    a[i] = np.clip(a[i], -0.5, 0.8)
                    ap[i] = np.clip(ap[i], -0.2, 0.2)
                
                # 检查收敛
                residual_a = np.max(np.abs(a - a_old))
                residual_ap = np.max(np.abs(ap - ap_old))
                residual = max(residual_a, residual_ap)
                
                if residual < self.tolerance:
                    converged = True
                    break
            
            # 计算最终结果
            thrust, power, torque = self._calculate_performance(a, ap)
            
            # 计算系数
            CT = thrust / (self.rho * self.disk_area * self.tip_speed**2)
            CP = power / (self.rho * self.disk_area * self.tip_speed**3)
            
            # 品质因数
            if power > 1e-6:
                ideal_power = thrust**1.5 / np.sqrt(2 * self.rho * self.disk_area)
                figure_of_merit = ideal_power / power
            else:
                figure_of_merit = 0.0
            
            solve_time = time.time() - start_time
            
            return {
                'thrust': thrust,
                'power': power,
                'torque': torque,
                'CT': CT,
                'CP': CP,
                'figure_of_merit': figure_of_merit,
                'converged': converged,
                'iterations': iteration + 1,
                'residual': residual,
                'solve_time': solve_time,
                'induction_factors': {
                    'axial': a,
                    'tangential': ap
                }
            }
            
        except Exception as e:
            print(f"BEMT求解失败: {e}")
            return {
                'thrust': 0.0,
                'power': 0.0,
                'torque': 0.0,
                'CT': 0.0,
                'CP': 0.0,
                'figure_of_merit': 0.0,
                'converged': False,
                'error': str(e)
            }
    
    def _calculate_pitch_angle(self, azimuth: float, r_R: float) -> float:
        """计算桨叶俯仰角"""
        if self.rotor_type == 'cycloidal':
            # 循环翼转子
            return (self.pitch_amplitude * np.sin(azimuth + self.pitch_phase) + 
                   self.collective)
        else:
            # 传统旋翼
            return self.collective + self.twist * r_R
    
    def _get_airfoil_coefficients(self, alpha: float, r_R: float) -> Tuple[float, float]:
        """获取翼型气动系数（简化模型）"""
        alpha_deg = np.degrees(alpha)
        
        # 基于薄翼理论的升力系数
        if abs(alpha_deg) < 15:
            Cl = 2 * np.pi * alpha  # 线性范围
        else:
            # 失速后修正
            Cl_max = 1.4
            alpha_stall = np.radians(15)
            if alpha > alpha_stall:
                Cl = Cl_max * np.cos(alpha - alpha_stall)**2
            else:
                Cl = -Cl_max * np.cos(alpha + alpha_stall)**2
        
        # 阻力系数
        Cd0 = 0.01  # 零升阻力
        Cd_induced = Cl**2 / (np.pi * 6.0)  # 诱导阻力
        Cd = Cd0 + Cd_induced
        
        return Cl, Cd
    
    def _tip_loss_correction(self, r_R: float, phi: float) -> float:
        """叶尖损失修正（Prandtl）"""
        if r_R > 0.99:
            return 0.1
        
        f = (self.B / 2) * (1 - r_R) / r_R
        F = (2 / np.pi) * np.arccos(np.exp(-np.clip(f, 0, 10)))
        return np.clip(F, 0.1, 1.0)
    
    def _hub_loss_correction(self, r_R: float) -> float:
        """叶根损失修正"""
        hub_ratio = 0.1
        if r_R < hub_ratio * 2:
            return r_R / (hub_ratio * 2)
        return 1.0
    
    def _calculate_performance(self, a: np.ndarray, ap: np.ndarray) -> Tuple[float, float, float]:
        """计算性能参数"""
        thrust = 0.0
        power = 0.0
        torque = 0.0
        
        dr = self.R_rotor / self.n_elements
        
        for i in range(self.n_elements):
            r = self.r_positions[i]
            
            # 局部速度
            V_axial = a[i] * self.omega * self.R_rotor
            V_tangential = self.omega * r * (1 + ap[i])
            V_rel = np.sqrt(V_axial**2 + V_tangential**2)
            
            # 入流角
            phi = np.arctan2(V_axial, V_tangential)
            
            # 攻角和系数
            pitch_angle = self._calculate_pitch_angle(0.0, r / self.R_rotor)
            alpha = pitch_angle - phi
            Cl, Cd = self._get_airfoil_coefficients(alpha, r / self.R_rotor)
            
            # 物理修正
            if self.enable_tip_loss:
                F_tip = self._tip_loss_correction(r / self.R_rotor, phi)
                Cl *= F_tip
                Cd *= F_tip
            
            # 局部载荷
            dA = self.c * dr
            q = 0.5 * self.rho * V_rel**2
            
            dL = Cl * q * dA
            dD = Cd * q * dA
            
            # 推力和转矩分量
            dT = dL * np.cos(phi) - dD * np.sin(phi)
            dQ = (dL * np.sin(phi) + dD * np.cos(phi)) * r
            
            thrust += self.B * dT
            torque += self.B * dQ
        
        power = torque * self.omega
        
        return thrust, power, torque


def test_simple_bemt():
    """测试简化的BEMT求解器"""
    print("🧪 测试简化BEMT求解器")
    print("=" * 50)
    
    # 测试配置
    configs = [
        {
            'name': '传统旋翼',
            'config': {
                'R_rotor': 0.5,
                'B': 4,
                'c': 0.08,
                'omega_rotor': 150.0,
                'rotor_type': 'conventional',
                'collective_pitch': 8.0,
                'twist_deg': -8.0,
                'n_elements': 15
            }
        },
        {
            'name': '循环翼转子',
            'config': {
                'R_rotor': 0.3,
                'B': 3,
                'c': 0.06,
                'omega_rotor': 200.0,
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 15.0,
                'pitch_phase_offset': 0.0,
                'n_elements': 12
            }
        }
    ]
    
    for test_case in configs:
        print(f"\n📋 测试案例: {test_case['name']}")
        print("-" * 30)
        
        try:
            # 创建求解器
            solver = SimpleBEMTSolver(test_case['config'])
            
            # 执行求解
            result = solver.solve()
            
            # 显示结果
            if result['converged']:
                print(f"✅ 求解成功:")
                print(f"   推力: {result['thrust']:.2f} N")
                print(f"   功率: {result['power']:.2f} W")
                print(f"   转矩: {result['torque']:.4f} N·m")
                print(f"   CT: {result['CT']:.5f}")
                print(f"   CP: {result['CP']:.5f}")
                print(f"   品质因数: {result['figure_of_merit']:.3f}")
                print(f"   迭代次数: {result['iterations']}")
                print(f"   求解时间: {result['solve_time']:.4f} s")
            else:
                print(f"❌ 求解失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    test_simple_bemt()