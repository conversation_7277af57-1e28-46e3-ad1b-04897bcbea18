#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Profiler - Medium Fidelity BEMT
性能分析器 - 中保真度BEMT

This module provides performance profiling capabilities for BEMT analysis:
- Execution time measurement
- Memory usage tracking
- Function call profiling
- Performance bottleneck identification

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import time
import functools
from typing import Dict, List, Optional, Any, Callable
import warnings

class PerformanceProfiler:
    """
    性能分析器
    
    提供BEMT分析的性能监控和分析功能：
    - 执行时间测量
    - 函数调用统计
    - 性能瓶颈识别
    - 内存使用监控
    """
    
    def __init__(self, enable_profiling: bool = True):
        """
        初始化性能分析器
        
        Parameters:
        -----------
        enable_profiling : bool
            是否启用性能分析
        """
        
        self.enable_profiling = enable_profiling
        
        # 性能统计
        self.timing_data = {}
        self.call_counts = {}
        self.memory_usage = {}
        
        # 当前计时器
        self.active_timers = {}
        
        print(f"性能分析器初始化完成 ({'启用' if enable_profiling else '禁用'})")
    
    def start_timer(self, name: str):
        """开始计时"""
        if not self.enable_profiling:
            return
        
        self.active_timers[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if not self.enable_profiling:
            return 0.0
        
        if name not in self.active_timers:
            warnings.warn(f"计时器 {name} 未启动")
            return 0.0
        
        elapsed_time = time.time() - self.active_timers[name]
        del self.active_timers[name]
        
        # 记录统计
        if name not in self.timing_data:
            self.timing_data[name] = []
        self.timing_data[name].append(elapsed_time)
        
        return elapsed_time
    
    def profile_function(self, func: Callable) -> Callable:
        """函数性能分析装饰器"""
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not self.enable_profiling:
                return func(*args, **kwargs)
            
            func_name = f"{func.__module__}.{func.__name__}"
            
            # 记录调用次数
            self.call_counts[func_name] = self.call_counts.get(func_name, 0) + 1
            
            # 计时执行
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                elapsed_time = time.time() - start_time
                
                # 记录执行时间
                if func_name not in self.timing_data:
                    self.timing_data[func_name] = []
                self.timing_data[func_name].append(elapsed_time)
        
        return wrapper
    
    def get_timing_summary(self) -> Dict[str, Dict[str, float]]:
        """获取计时摘要"""
        
        summary = {}
        
        for name, times in self.timing_data.items():
            if times:
                summary[name] = {
                    'total_time': sum(times),
                    'average_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'call_count': len(times)
                }
        
        return summary
    
    def get_call_summary(self) -> Dict[str, int]:
        """获取调用次数摘要"""
        return self.call_counts.copy()
    
    def print_performance_report(self):
        """打印性能报告"""
        
        if not self.enable_profiling:
            print("性能分析已禁用")
            return
        
        print("\n🔍 BEMT性能分析报告")
        print("=" * 50)
        
        # 计时摘要
        timing_summary = self.get_timing_summary()
        if timing_summary:
            print("\n⏱️  执行时间统计:")
            print(f"{'函数名':<30} {'总时间(s)':<12} {'平均时间(ms)':<15} {'调用次数':<10}")
            print("-" * 70)
            
            # 按总时间排序
            sorted_items = sorted(timing_summary.items(), 
                                key=lambda x: x[1]['total_time'], reverse=True)
            
            for name, stats in sorted_items:
                print(f"{name:<30} {stats['total_time']:<12.4f} "
                      f"{stats['average_time']*1000:<15.2f} {stats['call_count']:<10}")
        
        # 调用次数摘要
        call_summary = self.get_call_summary()
        if call_summary:
            print("\n📞 函数调用统计:")
            sorted_calls = sorted(call_summary.items(), key=lambda x: x[1], reverse=True)
            
            for name, count in sorted_calls[:10]:  # 显示前10个
                print(f"  {name}: {count} 次")
        
        # 性能建议
        self._print_performance_suggestions(timing_summary)
    
    def _print_performance_suggestions(self, timing_summary: Dict[str, Dict[str, float]]):
        """打印性能建议"""
        
        print("\n💡 性能优化建议:")
        
        # 找出最耗时的函数
        if timing_summary:
            max_time_func = max(timing_summary.items(), key=lambda x: x[1]['total_time'])
            if max_time_func[1]['total_time'] > 0.1:
                print(f"  • 最耗时函数: {max_time_func[0]} ({max_time_func[1]['total_time']:.3f}s)")
                print(f"    建议优化此函数以提高整体性能")
        
        # 检查频繁调用的函数
        frequent_calls = [(name, stats) for name, stats in timing_summary.items() 
                         if stats['call_count'] > 100]
        
        if frequent_calls:
            print(f"  • 发现 {len(frequent_calls)} 个高频调用函数")
            print(f"    考虑缓存结果或优化算法")
        
        # 检查执行时间变化大的函数
        variable_funcs = [(name, stats) for name, stats in timing_summary.items()
                         if stats['max_time'] > stats['min_time'] * 5]
        
        if variable_funcs:
            print(f"  • 发现 {len(variable_funcs)} 个执行时间不稳定的函数")
            print(f"    可能存在性能瓶颈或输入相关的复杂度")
    
    def reset_statistics(self):
        """重置统计数据"""
        
        self.timing_data.clear()
        self.call_counts.clear()
        self.memory_usage.clear()
        self.active_timers.clear()
        
        print("性能统计数据已重置")
    
    def enable(self):
        """启用性能分析"""
        self.enable_profiling = True
        print("性能分析已启用")
    
    def disable(self):
        """禁用性能分析"""
        self.enable_profiling = False
        print("性能分析已禁用")

# 全局性能分析器实例
_global_profiler = PerformanceProfiler()

def get_profiler() -> PerformanceProfiler:
    """获取全局性能分析器"""
    return _global_profiler

def set_profiler(profiler: PerformanceProfiler):
    """设置全局性能分析器"""
    global _global_profiler
    _global_profiler = profiler

# 便捷装饰器
def profile(func: Callable) -> Callable:
    """性能分析装饰器"""
    return _global_profiler.profile_function(func)

# 上下文管理器
class Timer:
    """计时上下文管理器"""
    
    def __init__(self, name: str, profiler: Optional[PerformanceProfiler] = None):
        self.name = name
        self.profiler = profiler or _global_profiler
        self.start_time = None
    
    def __enter__(self):
        self.profiler.start_timer(self.name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        elapsed = self.profiler.end_timer(self.name)
        return False

# 便捷函数
def start_timer(name: str):
    """开始计时"""
    _global_profiler.start_timer(name)

def end_timer(name: str) -> float:
    """结束计时"""
    return _global_profiler.end_timer(name)

def print_performance_report():
    """打印性能报告"""
    _global_profiler.print_performance_report()

def reset_statistics():
    """重置统计数据"""
    _global_profiler.reset_statistics()

# 导出所有主要类和函数
__all__ = [
    'PerformanceProfiler',
    'Timer',
    'profile',
    'start_timer',
    'end_timer',
    'print_performance_report',
    'reset_statistics',
    'get_profiler',
    'set_profiler'
]
