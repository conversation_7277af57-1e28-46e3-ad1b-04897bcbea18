"""
叶尖损失修正
===========

实现各种叶尖损失修正模型。

核心功能：
- Prandtl叶尖损失修正
- Goldstein叶尖损失修正
- 修正因子计算

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any
from .corrections import PhysicalCorrectionBase


class TipLossCorrection(PhysicalCorrectionBase):
    """叶尖损失修正基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.correction_type = 'tip_loss'
    
    def apply(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """应用叶尖损失修正"""
        r = input_data['r']
        R = input_data['R']
        B = input_data['B']
        phi = input_data.get('phi', 0.1)
        
        # 计算叶尖损失因子
        tip_loss_factor = self._calculate_tip_loss_factor(r, R, B, phi)
        
        # 应用修正
        result = input_data.copy()
        result['Cl'] = input_data['Cl'] * tip_loss_factor
        result['tip_loss_factor'] = tip_loss_factor
        
        return result
    
    def _calculate_tip_loss_factor(self, r: float, R: float, B: int, phi: float) -> float:
        """计算叶尖损失因子"""
        # Prandtl叶尖损失修正
        r_R = r / R
        
        if r_R >= 0.99:
            return 0.0
        
        f_arg = B * (1 - r_R) / (2 * r_R * abs(np.sin(phi)))
        f_arg = max(f_arg, 0.01)  # 避免数值问题
        
        F = (2 / np.pi) * np.arccos(np.exp(-f_arg))
        
        return max(F, 0.1)  # 设置最小值
    
    def get_correction_info(self) -> Dict[str, Any]:
        """获取修正信息"""
        return {
            'type': self.correction_type,
            'description': 'Prandtl叶尖损失修正',
            'parameters': self.config
        }


class PrandtlTipLoss(TipLossCorrection):
    """Prandtl叶尖损失修正"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.correction_type = 'prandtl_tip_loss'


class GoldsteinTipLoss(TipLossCorrection):
    """Goldstein叶尖损失修正"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.correction_type = 'goldstein_tip_loss'
    
    def _calculate_tip_loss_factor(self, r: float, R: float, B: int, phi: float) -> float:
        """Goldstein叶尖损失因子"""
        r_R = r / R
        
        if r_R >= 0.99:
            return 0.0
        
        # Goldstein修正（简化实现）
        lambda_tip = B * (1 - r_R) / (2 * r_R)
        
        if lambda_tip < 0.1:
            F = 1.0 - 0.5 * lambda_tip
        else:
            F = (2 / np.pi) * np.arccos(np.exp(-lambda_tip))
        
        return max(F, 0.1)