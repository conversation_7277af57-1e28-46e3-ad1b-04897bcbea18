"""
自适应网格细化
=============

提供自适应网格细化功能。

核心功能：
- 网格细化算法
- 误差估计
- 网格优化

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List


class AdaptiveMeshRefinement:
    """自适应网格细化器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.refinement_threshold = config.get('refinement_threshold', 0.1)
        self.max_refinement_levels = config.get('max_refinement_levels', 3)
        self.min_element_size = config.get('min_element_size', 0.01)
    
    def refine_mesh(self, n_elements: int, gradients: np.ndarray) -> int:
        """
        根据梯度信息细化网格
        
        Args:
            n_elements: 当前叶素数量
            gradients: 梯度信息
            
        Returns:
            细化后的叶素数量
        """
        # 简化的网格细化算法
        max_gradient = np.max(np.abs(gradients))
        
        if max_gradient > self.refinement_threshold:
            # 需要细化
            refinement_factor = min(2.0, max_gradient / self.refinement_threshold)
            new_elements = int(n_elements * refinement_factor)
            
            # 限制最大叶素数量
            max_elements = n_elements * (2 ** self.max_refinement_levels)
            new_elements = min(new_elements, max_elements)
            
            return new_elements
        else:
            return n_elements
    
    def estimate_error(self, solution: np.ndarray) -> np.ndarray:
        """估计解的误差"""
        # 简化的误差估计
        if len(solution) < 3:
            return np.zeros_like(solution)
        
        # 使用二阶差分估计误差
        error = np.zeros_like(solution)
        
        for i in range(1, len(solution) - 1):
            error[i] = abs(solution[i+1] - 2*solution[i] + solution[i-1])
        
        # 边界处理
        error[0] = error[1]
        error[-1] = error[-2]
        
        return error
    
    def get_refinement_info(self) -> Dict[str, Any]:
        """获取细化信息"""
        return {
            'refinement_threshold': self.refinement_threshold,
            'max_refinement_levels': self.max_refinement_levels,
            'min_element_size': self.min_element_size
        }   
 
    def create_initial_mesh(self, r_start, r_end):
        """创建初始网格"""
        n_stations = self.config.get('initial_stations', 10)
        return np.linspace(r_start, r_end, n_stations)
    
    def refine_mesh(self, mesh, errors):
        """细化网格"""
        refined_mesh = []
        threshold = self.config.get('refinement_threshold', 0.1)
        
        for i in range(len(mesh) - 1):
            refined_mesh.append(mesh[i])
            
            # 如果误差大于阈值，在中点插入新点
            if i < len(errors) and errors[i] > threshold:
                mid_point = (mesh[i] + mesh[i + 1]) / 2
                refined_mesh.append(mid_point)
        
        refined_mesh.append(mesh[-1])  # 添加最后一个点
        return np.array(refined_mesh)
    
    def coarsen_mesh(self, mesh, errors):
        """粗化网格"""
        coarsened_mesh = []
        threshold = self.config.get('coarsening_threshold', 0.01)
        
        i = 0
        while i < len(mesh):
            coarsened_mesh.append(mesh[i])
            
            # 如果误差小于阈值，跳过下一个点
            if i < len(errors) - 1 and errors[i] < threshold and i + 2 < len(mesh):
                i += 2  # 跳过一个点
            else:
                i += 1
        
        return np.array(coarsened_mesh)