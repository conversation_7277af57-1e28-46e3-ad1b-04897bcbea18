#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数值方法模块
===========

提供高阶数值积分方法和自适应算法。
基于原始cycloidal_rotor_suite的数值方法实现。

作者: Augment Agent
日期: 2025-07-24
"""

import numpy as np
from typing import Callable, Tuple, Optional, Dict, Any
from .math_utils import safe_sqrt


class TimeIntegrationMethods:
    """
    时间积分方法类
    
    提供多种高阶时间积分算法，包括RK4、Adams-Bashforth等
    """
    
    def __init__(self):
        """初始化时间积分方法"""
        self.method_registry = {
            'euler': self.euler_step,
            'rk4': self.rk4_step,
            'adams_bashforth': self.adams_bashforth_step,
            'adaptive_rk45': self.adaptive_rk45_step
        }
        
        # Adams-Bashforth历史存储
        self.ab_history = []
        self.max_ab_history = 4
    
    def euler_step(self, y: np.ndarray, t: float, dt: float, 
                   dydt_func: Callable) -> np.ndarray:
        """
        一阶欧拉方法
        
        参数:
        ----
        y : np.ndarray
            当前状态向量
        t : float
            当前时间
        dt : float
            时间步长
        dydt_func : Callable
            导数函数 dy/dt = f(t, y)
            
        返回:
        ----
        y_new : np.ndarray
            下一时刻的状态向量
        """
        dydt = dydt_func(t, y)
        return y + dt * dydt
    
    def rk4_step(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """
        四阶Runge-Kutta方法
        
        参数:
        ----
        y : np.ndarray
            当前状态向量
        t : float
            当前时间
        dt : float
            时间步长
        dydt_func : Callable
            导数函数 dy/dt = f(t, y)
            
        返回:
        ----
        y_new : np.ndarray
            下一时刻的状态向量
        """
        # RK4四个阶段
        k1 = dt * dydt_func(t, y)
        k2 = dt * dydt_func(t + dt/2, y + k1/2)
        k3 = dt * dydt_func(t + dt/2, y + k2/2)
        k4 = dt * dydt_func(t + dt, y + k3)
        
        # 加权平均
        return y + (k1 + 2*k2 + 2*k3 + k4) / 6
    
    def adams_bashforth_step(self, y: np.ndarray, t: float, dt: float, 
                           dydt_func: Callable, order: int = 4) -> np.ndarray:
        """
        Adams-Bashforth多步法
        
        参数:
        ----
        y : np.ndarray
            当前状态向量
        t : float
            当前时间
        dt : float
            时间步长
        dydt_func : Callable
            导数函数 dy/dt = f(t, y)
        order : int
            方法阶数 (1-4)
            
        返回:
        ----
        y_new : np.ndarray
            下一时刻的状态向量
        """
        # 计算当前导数
        current_dydt = dydt_func(t, y)
        
        # 存储历史导数
        self.ab_history.append(current_dydt.copy())
        if len(self.ab_history) > self.max_ab_history:
            self.ab_history.pop(0)
        
        # 根据可用历史确定实际阶数
        actual_order = min(order, len(self.ab_history))
        
        if actual_order == 1:
            # 一阶Adams-Bashforth (等价于欧拉法)
            return y + dt * current_dydt
        elif actual_order == 2:
            # 二阶Adams-Bashforth
            return y + dt * (3*self.ab_history[-1] - self.ab_history[-2]) / 2
        elif actual_order == 3:
            # 三阶Adams-Bashforth
            return y + dt * (23*self.ab_history[-1] - 16*self.ab_history[-2] + 5*self.ab_history[-3]) / 12
        else:
            # 四阶Adams-Bashforth
            return y + dt * (55*self.ab_history[-1] - 59*self.ab_history[-2] + 
                           37*self.ab_history[-3] - 9*self.ab_history[-4]) / 24
    
    def adaptive_rk45_step(self, y: np.ndarray, t: float, dt: float, 
                          dydt_func: Callable, tol: float = 1e-6) -> Tuple[np.ndarray, float]:
        """
        自适应Runge-Kutta-Fehlberg方法 (RK45)
        
        参数:
        ----
        y : np.ndarray
            当前状态向量
        t : float
            当前时间
        dt : float
            初始时间步长
        dydt_func : Callable
            导数函数 dy/dt = f(t, y)
        tol : float
            误差容差
            
        返回:
        ----
        y_new : np.ndarray
            下一时刻的状态向量
        dt_new : float
            建议的下一步时间步长
        """
        # RK45系数
        a = np.array([0, 1/4, 3/8, 12/13, 1, 1/2])
        b = np.array([
            [0, 0, 0, 0, 0, 0],
            [1/4, 0, 0, 0, 0, 0],
            [3/32, 9/32, 0, 0, 0, 0],
            [1932/2197, -7200/2197, 7296/2197, 0, 0, 0],
            [439/216, -8, 3680/513, -845/4104, 0, 0],
            [-8/27, 2, -3544/2565, 1859/4104, -11/40, 0]
        ])
        
        # 4阶和5阶方法的权重
        c4 = np.array([25/216, 0, 1408/2565, 2197/4104, -1/5, 0])
        c5 = np.array([16/135, 0, 6656/12825, 28561/56430, -9/50, 2/55])
        
        # 计算k值
        k = np.zeros((6, len(y)))
        k[0] = dt * dydt_func(t, y)
        
        for i in range(1, 6):
            y_temp = y + np.sum(b[i, :i, np.newaxis] * k[:i], axis=0)
            k[i] = dt * dydt_func(t + a[i] * dt, y_temp)
        
        # 4阶和5阶解
        y4 = y + np.sum(c4[:, np.newaxis] * k, axis=0)
        y5 = y + np.sum(c5[:, np.newaxis] * k, axis=0)
        
        # 误差估计
        error = np.max(np.abs(y5 - y4))
        
        # 自适应步长控制
        if error < tol:
            # 接受步长，计算下一步建议步长
            dt_new = dt * min(2.0, 0.9 * (tol / max(error, 1e-12))**0.2)
            return y5, dt_new
        else:
            # 拒绝步长，减小步长重新计算
            dt_new = dt * max(0.1, 0.9 * (tol / error)**0.25)
            return self.adaptive_rk45_step(y, t, dt_new, dydt_func, tol)
    
    def integrate(self, y0: np.ndarray, t_span: Tuple[float, float], 
                  dydt_func: Callable, method: str = 'rk4', 
                  dt: float = 0.01, **kwargs) -> Dict[str, Any]:
        """
        通用时间积分接口
        
        参数:
        ----
        y0 : np.ndarray
            初始状态向量
        t_span : Tuple[float, float]
            时间范围 (t_start, t_end)
        dydt_func : Callable
            导数函数 dy/dt = f(t, y)
        method : str
            积分方法名称
        dt : float
            时间步长
            
        返回:
        ----
        result : Dict[str, Any]
            积分结果字典
        """
        if method not in self.method_registry:
            raise ValueError(f"未知的积分方法: {method}")
        
        t_start, t_end = t_span
        integrator = self.method_registry[method]
        
        # 初始化
        t_current = t_start
        y_current = y0.copy()
        
        # 存储结果
        t_history = [t_current]
        y_history = [y_current.copy()]
        
        # 时间积分循环
        while t_current < t_end:
            # 调整最后一步的步长
            if t_current + dt > t_end:
                dt = t_end - t_current
            
            # 执行一步积分
            if method == 'adaptive_rk45':
                y_new, dt_new = integrator(y_current, t_current, dt, dydt_func, **kwargs)
                dt = min(dt_new, t_end - t_current - dt)
            else:
                y_new = integrator(y_current, t_current, dt, dydt_func)
            
            # 更新状态
            t_current += dt
            y_current = y_new
            
            # 存储结果
            t_history.append(t_current)
            y_history.append(y_current.copy())
        
        return {
            't': np.array(t_history),
            'y': np.array(y_history),
            'method': method,
            'success': True
        }
    
    def reset_history(self):
        """重置Adams-Bashforth历史"""
        self.ab_history.clear()


class AdaptiveMeshRefinement:
    """
    自适应网格细化类
    
    基于误差估计和梯度检测进行网格自适应
    """
    
    def __init__(self, min_spacing: float = 0.01, max_spacing: float = 0.1,
                 refinement_threshold: float = 1e-3):
        """
        初始化自适应网格
        
        参数:
        ----
        min_spacing : float
            最小网格间距
        max_spacing : float
            最大网格间距
        refinement_threshold : float
            细化阈值
        """
        self.min_spacing = min_spacing
        self.max_spacing = max_spacing
        self.refinement_threshold = refinement_threshold
    
    def refine_mesh(self, x: np.ndarray, y: np.ndarray, 
                    error_estimate: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于误差估计细化网格
        
        参数:
        ----
        x : np.ndarray
            当前网格点
        y : np.ndarray
            对应的函数值
        error_estimate : np.ndarray
            误差估计
            
        返回:
        ----
        x_new : np.ndarray
            细化后的网格点
        y_new : np.ndarray
            插值后的函数值
        """
        x_new = [x[0]]
        y_new = [y[0]]
        
        for i in range(len(x) - 1):
            x_new.append(x[i+1])
            y_new.append(y[i+1])
            
            # 检查是否需要细化
            if error_estimate[i] > self.refinement_threshold:
                spacing = x[i+1] - x[i]
                if spacing > self.min_spacing:
                    # 在中点插入新点
                    x_mid = (x[i] + x[i+1]) / 2
                    y_mid = (y[i] + y[i+1]) / 2  # 简单线性插值
                    
                    x_new.insert(-1, x_mid)
                    y_new.insert(-1, y_mid)
        
        return np.array(x_new), np.array(y_new)
    
    def coarsen_mesh(self, x: np.ndarray, y: np.ndarray, 
                     error_estimate: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于误差估计粗化网格
        
        参数:
        ----
        x : np.ndarray
            当前网格点
        y : np.ndarray
            对应的函数值
        error_estimate : np.ndarray
            误差估计
            
        返回:
        ----
        x_new : np.ndarray
            粗化后的网格点
        y_new : np.ndarray
            对应的函数值
        """
        keep_indices = [0]  # 总是保留第一个点
        
        for i in range(1, len(x) - 1):
            # 检查是否可以移除此点
            spacing_left = x[i] - x[i-1]
            spacing_right = x[i+1] - x[i]
            total_spacing = spacing_left + spacing_right
            
            # 如果误差很小且间距不会太大，则可以移除
            if (error_estimate[i] < self.refinement_threshold / 10 and 
                total_spacing < self.max_spacing):
                continue  # 跳过此点
            else:
                keep_indices.append(i)
        
        keep_indices.append(len(x) - 1)  # 总是保留最后一个点
        
        return x[keep_indices], y[keep_indices]
