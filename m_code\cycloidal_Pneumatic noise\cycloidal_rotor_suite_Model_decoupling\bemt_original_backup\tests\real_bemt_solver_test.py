#!/usr/bin/env python3
"""
真实BEMT求解器功能验证测试
========================

调用真实的BEMT求解器，验证是否完整实现了cycloidal_rotor_suite下的
bemt中保真度模型的所有功能。

测试内容：
1. 核心BEMT求解器功能
2. 物理修正模型
3. 动态失速模型
4. GPU加速功能
5. 自适应网格细化
6. 高级收敛策略

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import numpy as np
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 添加项目根目录
project_root = os.path.dirname(parent_dir)
sys.path.insert(0, project_root)

def test_core_bemt_solver():
    """测试核心BEMT求解器"""
    print("🔧 测试核心BEMT求解器...")
    
    try:
        from core.bemt_solver import BEMTSolver
        from utils.config import ConfigManager
        from geometry.rotor import RotorGeometry
        
        # 创建基本配置
        config = ConfigManager({
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 150.0,
            'rho': 1.225,
            'collective_pitch': 8.0,
            'n_radial_stations': 10,
            'convergence_tolerance': 1e-6,
            'max_iterations': 50
        })
        
        # 创建转子几何
        geometry = RotorGeometry(config.to_dict())
        
        # 创建求解器
        solver = BEMTSolver(config, geometry)
        
        # 执行求解
        print("   执行BEMT求解...")
        results = solver.solve()
        
        # 验证结果
        if results and 'thrust' in results and 'power' in results:
            thrust = results['thrust']
            power = results['power']
            print(f"   ✅ 求解成功: 推力={thrust:.2f}N, 功率={power:.2f}W")
            
            # 验证结果合理性
            if thrust > 0 and power > 0:
                figure_of_merit = results.get('figure_of_merit', 0)
                print(f"   ✅ 结果合理: 品质因数={figure_of_merit:.3f}")
                return True
            else:
                print(f"   ❌ 结果异常: 推力或功率为负值")
                return False
        else:
            print(f"   ❌ 求解失败: 结果格式错误")
            return False
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_physical_corrections():
    """测试物理修正模型"""
    print("🔬 测试物理修正模型...")
    
    try:
        from physics.corrections import UnifiedPhysicalCorrections
        from physics.tip_loss import TipLossCorrection
        from physics.hub_loss import HubLossCorrection
        
        # 测试统一物理修正系统
        config = {
            'B': 4,
            'R_rotor': 0.5,
            'hub_radius_ratio': 0.1,
            'enable_tip_loss': True,
            'enable_hub_loss': True
        }
        
        corrections = UnifiedPhysicalCorrections(config)
        
        # 测试数据
        test_data = {
            'Cl': 1.0,
            'Cd': 0.01,
            'alpha': 0.1,
            'r_R': 0.8,
            'Re': 1e6,
            'M': 0.1
        }
        
        # 应用修正
        corrected_data = corrections.apply_all_corrections(test_data)
        
        if corrected_data and 'Cl' in corrected_data:
            print(f"   ✅ 物理修正成功: Cl={corrected_data['Cl']:.3f}")
            
            # 验证修正因子
            if 'tip_loss_factor' in corrected_data:
                print(f"   ✅ 叶尖损失修正: F_tip={corrected_data['tip_loss_factor']:.3f}")
            
            if 'hub_loss_factor' in corrected_data:
                print(f"   ✅ 叶根损失修正: F_hub={corrected_data['hub_loss_factor']:.3f}")
            
            return True
        else:
            print(f"   ❌ 物理修正失败")
            return False
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_dynamic_stall_model():
    """测试动态失速模型"""
    print("🌪️ 测试动态失速模型...")
    
    try:
        from aerodynamics.dynamic_stall import LeishmanBeddoesModel
        
        # 创建动态失速模型
        config = {
            'airfoil_type': 'NACA0012',
            'alpha_stall': 12.0,
            'Cl_max': 1.2,
            'enable_dynamic_stall': True
        }
        
        ds_model = LeishmanBeddoesModel(config)
        
        # 测试动态失速计算
        alpha_history = np.linspace(0, 20, 100)  # 攻角历史
        dt = 0.01
        
        results = []
        for i, alpha in enumerate(alpha_history):
            # 模拟动态失速过程
            state = {
                'alpha': np.radians(alpha),
                'alpha_dot': np.radians(1.0) if i > 0 else 0,
                'dt': dt,
                'V_rel': 50.0,
                'c': 0.08
            }
            
            result = ds_model.calculate_dynamic_stall(state)
            results.append(result)
        
        if results and len(results) > 0:
            final_result = results[-1]
            if 'Cl_dynamic' in final_result:
                print(f"   ✅ 动态失速计算成功: Cl_dynamic={final_result['Cl_dynamic']:.3f}")
                return True
            else:
                print(f"   ❌ 动态失速结果格式错误")
                return False
        else:
            print(f"   ❌ 动态失速计算失败")
            return False
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_gpu_acceleration():
    """测试GPU加速功能"""
    print("🚀 测试GPU加速功能...")
    
    try:
        from utils.gpu_acceleration import GPUAccelerator
        
        # 创建GPU加速器
        gpu_accel = GPUAccelerator()
        
        # 检查GPU可用性
        if gpu_accel.is_gpu_available():
            print(f"   ✅ GPU可用: {gpu_accel.get_device_info()}")
            
            # 测试GPU计算
            test_array = np.random.rand(1000, 1000)
            
            # CPU计算
            start_time = time.time()
            cpu_result = np.dot(test_array, test_array.T)
            cpu_time = time.time() - start_time
            
            # GPU计算
            start_time = time.time()
            gpu_result = gpu_accel.matrix_multiply(test_array, test_array.T)
            gpu_time = time.time() - start_time
            
            # 验证结果一致性
            if np.allclose(cpu_result, gpu_result, rtol=1e-5):
                speedup = cpu_time / gpu_time if gpu_time > 0 else 1
                print(f"   ✅ GPU加速成功: 加速比={speedup:.2f}x")
                return True
            else:
                print(f"   ❌ GPU计算结果不一致")
                return False
        else:
            print(f"   ⚠️  GPU不可用，跳过GPU测试")
            return True  # GPU不可用不算失败
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_adaptive_mesh_refinement():
    """测试自适应网格细化"""
    print("🕸️ 测试自适应网格细化...")
    
    try:
        from utils.adaptive_mesh import AdaptiveMeshRefinement
        
        # 创建自适应网格
        config = {
            'initial_stations': 10,
            'max_stations': 50,
            'refinement_threshold': 0.1,
            'coarsening_threshold': 0.01
        }
        
        mesh = AdaptiveMeshRefinement(config)
        
        # 初始网格
        initial_mesh = mesh.create_initial_mesh(0.1, 1.0)
        print(f"   初始网格点数: {len(initial_mesh)}")
        
        # 模拟误差分布（在某些区域误差较大）
        errors = np.zeros(len(initial_mesh))
        errors[3:7] = 0.15  # 中间区域误差较大
        
        # 执行网格细化
        refined_mesh = mesh.refine_mesh(initial_mesh, errors)
        
        if len(refined_mesh) > len(initial_mesh):
            print(f"   ✅ 网格细化成功: {len(initial_mesh)} -> {len(refined_mesh)} 点")
            
            # 测试网格粗化
            small_errors = np.full(len(refined_mesh), 0.005)
            coarsened_mesh = mesh.coarsen_mesh(refined_mesh, small_errors)
            
            print(f"   ✅ 网格粗化成功: {len(refined_mesh)} -> {len(coarsened_mesh)} 点")
            return True
        else:
            print(f"   ❌ 网格细化失败")
            return False
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_advanced_convergence():
    """测试高级收敛策略"""
    print("🎯 测试高级收敛策略...")
    
    try:
        from utils.advanced_convergence import AdvancedConvergenceController
        
        # 创建高级收敛控制器
        config = {
            'method': 'aitken',
            'tolerance': 1e-6,
            'max_iterations': 100,
            'relaxation_factor': 0.5
        }
        
        controller = AdvancedConvergenceController(config)
        
        # 模拟迭代过程
        residuals = []
        x_old = np.array([1.0, 2.0, 3.0])
        
        for i in range(20):
            # 模拟求解过程
            x_new = x_old * 0.9 + np.array([0.1, 0.2, 0.3])
            
            # 计算残差
            residual = np.linalg.norm(x_new - x_old)
            residuals.append(residual)
            
            # 应用收敛加速
            x_accelerated = controller.accelerate_convergence(x_old, x_new, i)
            
            # 检查收敛
            if controller.check_convergence(residuals):
                print(f"   ✅ 收敛成功: 第{i+1}次迭代，残差={residual:.2e}")
                return True
            
            x_old = x_accelerated
        
        print(f"   ❌ 未在最大迭代次数内收敛")
        return False
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        return False

def test_complete_integration():
    """测试完整集成功能"""
    print("🔗 测试完整集成功能...")
    
    try:
        # 导入所有必要模块
        from core.bemt_solver import BEMTSolver
        from core.solver_factory import SolverFactory
        from utils.config import ConfigManager
        from geometry.rotor import RotorGeometry
        
        # 创建完整配置
        config_dict = {
            # 基本参数
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 150.0,
            'rho': 1.225,
            'collective_pitch': 8.0,
            
            # 数值参数
            'n_radial_stations': 15,
            'n_azimuth_stations': 36,
            'convergence_tolerance': 1e-6,
            'max_iterations': 50,
            
            # 物理模型开关
            'enable_tip_loss': True,
            'enable_hub_loss': True,
            'enable_dynamic_stall': True,
            'enable_compressibility': False,
            'enable_viscous_effects': True,
            
            # 高级功能
            'enable_gpu_acceleration': False,
            'enable_adaptive_mesh': True,
            'enable_advanced_convergence': True,
            
            # 求解器类型
            'solver_type': 'medium_fidelity'
        }
        
        config = ConfigManager(config_dict)
        
        # 使用工厂模式创建求解器
        solver = SolverFactory.create_solver(config)
        
        if solver:
            print(f"   ✅ 求解器创建成功: {type(solver).__name__}")
            
            # 执行完整求解
            print("   执行完整BEMT求解...")
            start_time = time.time()
            
            results = solver.solve()
            
            solve_time = time.time() - start_time
            
            if results and isinstance(results, dict):
                print(f"   ✅ 完整求解成功 (耗时: {solve_time:.3f}s)")
                
                # 显示关键结果
                if 'thrust' in results:
                    print(f"     推力: {results['thrust']:.2f} N")
                if 'power' in results:
                    print(f"     功率: {results['power']:.2f} W")
                if 'figure_of_merit' in results:
                    print(f"     品质因数: {results['figure_of_merit']:.3f}")
                if 'iterations' in results:
                    print(f"     收敛迭代数: {results['iterations']}")
                
                return True
            else:
                print(f"   ❌ 完整求解失败")
                return False
        else:
            print(f"   ❌ 求解器创建失败")
            return False
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_validation():
    """运行综合验证测试"""
    
    print("=" * 80)
    print("🧪 BEMT中保真度模块 - 真实求解器功能验证")
    print("=" * 80)
    
    test_functions = [
        ("核心BEMT求解器", test_core_bemt_solver),
        ("物理修正模型", test_physical_corrections),
        ("动态失速模型", test_dynamic_stall_model),
        ("GPU加速功能", test_gpu_acceleration),
        ("自适应网格细化", test_adaptive_mesh_refinement),
        ("高级收敛策略", test_advanced_convergence),
        ("完整集成功能", test_complete_integration)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in test_functions:
        print(f"\\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\\n" + "=" * 80)
    print("📋 功能验证测试报告")
    print("=" * 80)
    
    print("\\n测试项目                    结果")
    print("-" * 50)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25s} {status}")
    
    print("-" * 50)
    success_rate = passed_tests / len(test_functions) * 100
    print(f"总体结果: {passed_tests}/{len(test_functions)} 通过 ({success_rate:.1f}%)")
    
    # 功能完整性评估
    print("\\n🎯 功能完整性评估:")
    
    if success_rate >= 90:
        print("✅ 优秀 - BEMT中保真度模型功能基本完整")
        print("   所有核心功能正常工作，与原始版本功能对等")
    elif success_rate >= 70:
        print("✅ 良好 - 大部分功能正常，部分功能需要完善")
        print("   核心BEMT算法工作正常，高级功能可能需要调优")
    elif success_rate >= 50:
        print("⚠️  一般 - 基本功能可用，多个功能需要修复")
        print("   建议优先修复失败的核心功能")
    else:
        print("❌ 不足 - 多个核心功能存在问题")
        print("   需要系统性检查和修复代码实现")
    
    print("\\n📝 改进建议:")
    
    failed_tests = [name for name, result in results.items() if not result]
    if failed_tests:
        print("需要修复的功能:")
        for test_name in failed_tests:
            print(f"  - {test_name}")
    
    if success_rate < 100:
        print("\\n🔧 后续工作:")
        print("  1. 修复失败的测试项目")
        print("  2. 完善错误处理机制")
        print("  3. 优化算法精度和稳定性")
        print("  4. 增加更多验证测试用例")
    
    return success_rate >= 70

def main():
    """主函数"""
    try:
        success = run_comprehensive_validation()
        return 0 if success else 1
    except Exception as e:
        print(f"\\n❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

def run_comprehensive_flow_test():
    """运行完整调用流程测试"""
    print("🔧 开始完整调用流程测试...")
    
    # 测试1: 配置管理器
    print("\n1. 测试配置管理器...")
    try:
        from utils.config import ConfigManager, test_config_manager
        config_result = test_config_manager()
        print(f"   配置管理器测试: {'✅ 通过' if config_result else '❌ 失败'}")
    except Exception as e:
        print(f"   配置管理器测试: ❌ 异常 - {e}")
        config_result = False
    
    # 测试2: 几何模块
    print("\n2. 测试几何模块...")
    try:
        from geometry.rotor import test_rotor_geometry
        geometry_result = test_rotor_geometry()
        print(f"   几何模块测试: {'✅ 通过' if geometry_result else '❌ 失败'}")
    except Exception as e:
        print(f"   几何模块测试: ❌ 异常 - {e}")
        geometry_result = False
    
    # 测试3: 物理修正
    print("\n3. 测试物理修正...")
    try:
        from physics.corrections import test_physical_corrections
        corrections_result = test_physical_corrections()
        print(f"   物理修正测试: {'✅ 通过' if corrections_result else '❌ 失败'}")
    except Exception as e:
        print(f"   物理修正测试: ❌ 异常 - {e}")
        corrections_result = False
    
    # 测试4: 求解器工厂
    print("\n4. 测试求解器工厂...")
    try:
        from core.solver_factory import SolverFactory
        from utils.config import ConfigManager
        
        config = ConfigManager({
            'R_rotor': 0.4,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 120.0,
            'rho': 1.225
        })
        
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        if solver:
            print("   ✅ 求解器工厂创建成功")
            factory_result = True
        else:
            print("   ❌ 求解器工厂创建失败")
            factory_result = False
            
    except Exception as e:
        print(f"   求解器工厂测试: ❌ 异常 - {e}")
        factory_result = False
    
    # 测试5: 完整BEMT求解
    print("\n5. 测试完整BEMT求解...")
    try:
        if factory_result:
            # 执行求解
            result = solver.solve()
            
            if result and 'thrust' in result:
                thrust = result['thrust']
                power = result['power']
                converged = result.get('converged', False)
                
                print(f"   ✅ BEMT求解成功:")
                print(f"     推力: {thrust:.2f} N")
                print(f"     功率: {power:.2f} W")
                print(f"     收敛: {'是' if converged else '否'}")
                
                bemt_result = True
            else:
                print("   ❌ BEMT求解失败 - 结果格式错误")
                bemt_result = False
        else:
            print("   ⚠️  跳过BEMT求解测试（工厂创建失败）")
            bemt_result = False
            
    except Exception as e:
        print(f"   BEMT求解测试: ❌ 异常 - {e}")
        import traceback
        traceback.print_exc()
        bemt_result = False
    
    # 测试6: 时域仿真
    print("\n6. 测试时域仿真...")
    try:
        if factory_result:
            # 简单时域仿真
            t_end = 0.1
            dt = 0.01
            t = 0.0
            step_count = 0
            
            while t < t_end and step_count < 5:  # 限制步数
                step_result = solver.solve_step(t, dt)
                
                if step_result and 'performance' in step_result:
                    step_count += 1
                    t += dt
                else:
                    break
            
            if step_count > 0:
                print(f"   ✅ 时域仿真成功: {step_count} 步")
                time_result = True
            else:
                print("   ❌ 时域仿真失败")
                time_result = False
        else:
            print("   ⚠️  跳过时域仿真测试（求解器创建失败）")
            time_result = False
            
    except Exception as e:
        print(f"   时域仿真测试: ❌ 异常 - {e}")
        time_result = False
    
    # 汇总结果
    test_results = {
        '配置管理器': config_result,
        '几何模块': geometry_result,
        '物理修正': corrections_result,
        '求解器工厂': factory_result,
        'BEMT求解': bemt_result,
        '时域仿真': time_result
    }
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n{'='*60}")
    print("🧪 完整调用流程测试结果")
    print(f"{'='*60}")
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12s} {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！调用流程正常工作。")
    elif passed_tests >= total_tests * 0.7:
        print("\n✅ 大部分测试通过，系统基本可用。")
    else:
        print("\n⚠️  多个测试失败，需要修复关键问题。")
    
    return passed_tests >= total_tests * 0.7

if __name__ == "__main__":
    # 运行完整流程测试
    success = run_comprehensive_flow_test()
    
    # 如果流程测试通过，再运行详细验证
    if success:
        print("\n" + "="*60)
        print("继续运行详细功能验证...")
        print("="*60)
        detailed_success = run_comprehensive_validation()
        sys.exit(0 if detailed_success else 1)
    else:
        print("\n基础流程测试失败，请先修复基础问题。")
        sys.exit(1)