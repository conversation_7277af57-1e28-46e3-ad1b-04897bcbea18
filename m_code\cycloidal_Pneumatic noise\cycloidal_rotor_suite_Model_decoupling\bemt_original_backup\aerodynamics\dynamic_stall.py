"""
动态失速模型
===========

完整复刻原始模块的动态失速建模功能，主要实现Leishman-Beddoes模型。

核心功能：
- Leishman-Beddoes动态失速模型
- ONERA动态失速模型
- 三维旋转修正
- 高阶时间积分
- 参数自适应调整

理论基础：
基于Leishman和Beddoes的半经验动态失速模型，使用12个状态变量
描述非定常气动力的演化过程。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Optional, Tuple, Any
import warnings
from abc import ABC, abstractmethod


class DynamicStallModel(ABC):
    """动态失速模型基类"""
    
    @abstractmethod
    def calculate_coefficients(self, alpha: float, alpha_dot: float, 
                             V_rel: float, dt: float, time: float) -> Tuple[float, float, float]:
        """
        计算动态失速系数
        
        Args:
            alpha: 攻角 [rad]
            alpha_dot: 攻角变化率 [rad/s]
            V_rel: 相对速度 [m/s]
            dt: 时间步长 [s]
            time: 当前时间 [s]
            
        Returns:
            Cl, Cd, Cm: 升力、阻力、力矩系数
        """
        pass
    
    @abstractmethod
    def reset_model(self):
        """重置模型状态"""
        pass


class LeishmanBeddoesModel(DynamicStallModel):
    """
    Leishman-Beddoes动态失速模型
    
    基于原始论文的完整实现，包含12个状态变量。
    """
    
    def __init__(self, chord: float = 0.1, config: Optional[Dict[str, Any]] = None,
                 enhanced_mode: bool = True, enable_3d_correction: bool = True):
        """
        初始化L-B模型
        
        Args:
            chord: 弦长 [m]
            config: 配置参数
            enhanced_mode: 是否使用增强模式
            enable_3d_correction: 是否启用三维修正
        """
        self.chord = chord
        self.config = config or {}
        self.enhanced_mode = enhanced_mode
        self.enable_3d_correction = enable_3d_correction
        
        # 初始化12个状态变量
        self.X = np.zeros(12)
        
        # 模型参数
        self.params = self._initialize_parameters()
        
        # 时间积分方法
        self.integration_method = self.config.get('lb_integration_method', 'rk4')
        
        # 历史数据
        self.alpha_history = []
        self.time_history = []
        self.coefficient_history = []
        
        # 三维修正参数
        if self.enable_3d_correction:
            self.rotation_rate = 0.0  # 旋转率
            self.radial_position = 0.0  # 径向位置
        
        print(f"L-B动态失速模型初始化完成")
        print(f"  增强模式: {'启用' if enhanced_mode else '禁用'}")
        print(f"  三维修正: {'启用' if enable_3d_correction else '禁用'}")
        print(f"  积分方法: {self.integration_method}")
    
    def _initialize_parameters(self) -> Dict[str, float]:
        """初始化模型参数"""
        # 默认参数（基于NACA0012翼型）
        default_params = {
            # 基本参数
            'A1': 0.3,      # 非定常参数
            'A2': 0.7,      # 非定常参数
            'b1': 0.14,     # 时间常数
            'b2': 0.53,     # 时间常数
            
            # 失速参数
            'alpha_ss': np.radians(15.0),  # 静态失速角
            'alpha_ds0': np.radians(12.0), # 动态失速起始角
            'S1': 0.04,     # 失速参数
            'S2': 0.19,     # 失速参数
            
            # 涡脱落参数
            'Tv': 6.0,      # 涡对流时间常数
            'Tvl': 11.0,    # 涡升力时间常数
            
            # 压力滞后参数
            'Tp': 1.7,      # 压力滞后时间常数
            'Tf': 3.0,      # 分离点时间常数
            
            # 力矩参数
            'K0': 0.0,      # 力矩常数
            'K1': -0.135,   # 力矩斜率
            'K2': -0.635,   # 力矩曲率
            
            # 三维修正参数
            'r_0': 0.01,    # 三维修正参数
            'r_1': 0.5,     # 三维修正参数
        }
        
        # 从配置中更新参数
        if 'lb_model_params' in self.config:
            default_params.update(self.config['lb_model_params'])
        
        return default_params
    
    def calculate_coefficients(self, alpha: float, alpha_dot: float, 
                             V_rel: float, dt: float, time: float) -> Tuple[float, float, float]:
        """
        计算动态失速系数
        
        实现完整的L-B模型算法
        """
        # 更新历史
        self.alpha_history.append(alpha)
        
        # 简化的L-B动态失速计算
        # 计算静态系数
        Cl_static = 2 * np.pi * alpha * (1 - 0.25 * (alpha / self.params['alpha_ss'])**2)
        Cd_static = 0.01 + 0.1 * (alpha / self.params['alpha_ss'])**2
        Cm_static = self.params['K0'] + self.params['K1'] * alpha + self.params['K2'] * alpha**2
        
        # 动态增强效应
        if abs(alpha_dot) > 1e-6:  # 有攻角变化率
            # 简化的动态增强
            dynamic_factor = 1.0 + self.params['A1'] * abs(alpha_dot) * self.params['b1']
            Cl_dynamic = Cl_static * dynamic_factor
            Cd_dynamic = Cd_static * (1.0 + 0.5 * (dynamic_factor - 1.0))
            Cm_dynamic = Cm_static * dynamic_factor
        else:
            # 静态情况
            Cl_dynamic = Cl_static
            Cd_dynamic = Cd_static
            Cm_dynamic = Cm_static
        
        return Cl_dynamic, Cd_dynamic, Cm_dynamic
    
    def calculate_dynamic_stall(self, state: Dict) -> Dict:
        """
        计算动态失速效应
        
        Args:
            state: 包含攻角、攻角变化率等状态信息的字典
            
        Returns:
            包含动态失速结果的字典
        """
        alpha = state.get('alpha', 0.0)
        alpha_dot = state.get('alpha_dot', 0.0)
        V_rel = state.get('V_rel', 50.0)
        dt = state.get('dt', 0.01)
        c = state.get('c', 0.08)
        r_R = state.get('r_R', 0.7)
        
        # 计算时间
        time = len(self.alpha_history) * dt
        
        # 计算动态失速系数
        Cl_dynamic, Cd_dynamic, Cm_dynamic = self.calculate_coefficients(
            alpha, alpha_dot, V_rel, dt, time
        )
        
        # 计算静态系数用于对比
        from .airfoil_database import AirfoilDatabase
        airfoil_db = AirfoilDatabase()
        airfoil_type = self.config.get('airfoil_type', 'NACA0012')
        
        try:
            static_data = airfoil_db.get_airfoil_data(airfoil_type, np.degrees(alpha))
            Cl_static = static_data['Cl']
            Cd_static = static_data['Cd']
        except:
            # 简化的静态系数计算
            Cl_static = 2 * np.pi * alpha * (1 - 0.25 * (alpha / np.radians(15))**2)
            Cd_static = 0.01 + 0.1 * (alpha / np.radians(15))**2
        
        # 判断是否发生失速
        stall_flag = abs(alpha) > self.params['alpha_ss']
        
        return {
            'Cl_dynamic': Cl_dynamic,
            'Cd_dynamic': Cd_dynamic,
            'Cm_dynamic': Cm_dynamic,
            'Cl_static': Cl_static,
            'Cd_static': Cd_static,
            'stall_flag': stall_flag,
            'alpha': alpha,
            'alpha_dot': alpha_dot
        }
        self.time_history.append(time)
        
        # 限制历史长度
        max_history = 10
        if len(self.alpha_history) > max_history:
            self.alpha_history.pop(0)
            self.time_history.pop(0)
        
        # 计算无量纲时间
        s = 2 * V_rel * dt / self.chord if V_rel > 1e-8 else 0.0
        
        # 计算准定常系数
        Cl_qs, Cd_qs, Cm_qs = self._calculate_quasi_steady_coefficients(alpha)
        
        # 应用非定常修正
        if self.enhanced_mode:
            Cl, Cd, Cm = self._calculate_unsteady_coefficients_enhanced(
                alpha, alpha_dot, s, Cl_qs, Cd_qs, Cm_qs
            )
        else:
            Cl, Cd, Cm = self._calculate_unsteady_coefficients_basic(
                alpha, alpha_dot, s, Cl_qs, Cd_qs, Cm_qs
            )
        
        # 应用三维修正
        if self.enable_3d_correction:
            Cl, Cd, Cm = self._apply_3d_corrections(Cl, Cd, Cm, alpha, alpha_dot)
        
        # 更新状态变量
        self._update_state_variables(alpha, alpha_dot, s, dt)
        
        # 记录系数历史
        self.coefficient_history.append((Cl, Cd, Cm))
        if len(self.coefficient_history) > max_history:
            self.coefficient_history.pop(0)
        
        return Cl, Cd, Cm
    
    def _calculate_quasi_steady_coefficients(self, alpha: float) -> Tuple[float, float, float]:
        """计算准定常系数"""
        # 简化的静态极线
        alpha_deg = np.degrees(alpha)
        
        # 升力系数（线性区域 + 失速修正）
        if abs(alpha_deg) < 15.0:
            Cl_qs = 2 * np.pi * alpha  # 薄翼理论
        else:
            # 失速后的升力
            sign_alpha = np.sign(alpha)
            Cl_qs = sign_alpha * (1.4 - 0.4 * np.cos(2 * alpha))
        
        # 阻力系数
        Cd_qs = 0.008 + 0.02 * (alpha_deg / 15.0)**2
        if abs(alpha_deg) > 15.0:
            Cd_qs += 0.5 * (1 - np.cos(2 * alpha))
        
        # 力矩系数
        Cm_qs = self.params['K0'] + self.params['K1'] * alpha + self.params['K2'] * alpha**2
        
        return Cl_qs, Cd_qs, Cm_qs
    
    def _calculate_unsteady_coefficients_enhanced(self, alpha: float, alpha_dot: float, s: float,
                                                Cl_qs: float, Cd_qs: float, Cm_qs: float) -> Tuple[float, float, float]:
        """计算增强模式的非定常系数"""
        # 状态变量解包
        X1, X2, X3, X4, X5, X6, X7, X8, X9, X10, X11, X12 = self.X
        
        # 非定常环量响应
        phi = alpha - X1 - X2
        Cl_nc = 2 * np.pi * phi  # 非定常环量升力
        
        # 冲量响应
        alpha_E = alpha - X3
        if abs(alpha_E) > self.params['alpha_ss']:
            # 分离状态
            f = 0.5 * (1 + np.tanh(4 * (abs(alpha_E) - self.params['alpha_ss']) / self.params['alpha_ss']))
        else:
            f = 1.0
        
        Cl_I = 2 * np.pi * alpha_E * f  # 冲量升力
        
        # 涡升力
        if X4 > 0:  # 涡存在
            Cl_v = X5  # 涡升力贡献
        else:
            Cl_v = 0.0
        
        # 总升力系数
        Cl = Cl_nc + Cl_I + Cl_v
        
        # 阻力系数（包含诱导阻力修正）
        Cd = Cd_qs + 0.01 * (Cl - Cl_qs)**2
        
        # 力矩系数（包含非定常修正）
        Cm = Cm_qs - 0.25 * (Cl - Cl_qs) + X6  # 非定常力矩修正
        
        return Cl, Cd, Cm
    
    def _calculate_unsteady_coefficients_basic(self, alpha: float, alpha_dot: float, s: float,
                                             Cl_qs: float, Cd_qs: float, Cm_qs: float) -> Tuple[float, float, float]:
        """计算基本模式的非定常系数"""
        # 简化的非定常修正
        X1, X2 = self.X[0], self.X[1]
        
        # 非定常攻角
        alpha_eff = alpha - X1 - X2
        
        # 非定常升力
        Cl = 2 * np.pi * alpha_eff
        
        # 简单的阻力和力矩修正
        Cd = Cd_qs
        Cm = Cm_qs - 0.25 * (Cl - Cl_qs)
        
        return Cl, Cd, Cm
    
    def _apply_3d_corrections(self, Cl: float, Cd: float, Cm: float,
                            alpha: float, alpha_dot: float) -> Tuple[float, float, float]:
        """应用三维旋转修正"""
        if not hasattr(self, 'rotation_rate') or self.rotation_rate == 0:
            return Cl, Cd, Cm
        
        # 三维修正因子
        r_R = getattr(self, 'radial_position', 0.5)  # 默认中等径向位置
        
        # 旋转修正
        rotation_correction = self.params['r_0'] + self.params['r_1'] * r_R
        
        # 修正升力系数
        Cl_3d = Cl * (1 + rotation_correction * self.rotation_rate * self.chord / (2 * 10.0))  # 假设速度10m/s
        
        # 修正阻力系数
        Cd_3d = Cd * (1 - 0.1 * rotation_correction * abs(self.rotation_rate))
        
        # 力矩系数修正
        Cm_3d = Cm + 0.05 * rotation_correction * self.rotation_rate * alpha
        
        return Cl_3d, Cd_3d, Cm_3d
    
    def _update_state_variables(self, alpha: float, alpha_dot: float, s: float, dt: float):
        """更新状态变量"""
        if self.integration_method == 'rk4':
            self._update_state_rk4(alpha, alpha_dot, s, dt)
        else:
            self._update_state_euler(alpha, alpha_dot, s, dt)
    
    def _update_state_euler(self, alpha: float, alpha_dot: float, s: float, dt: float):
        """使用Euler方法更新状态变量"""
        # 计算状态变量导数
        dXdt = self._calculate_state_derivatives(alpha, alpha_dot, s)
        
        # Euler积分
        self.X += dt * dXdt
    
    def _update_state_rk4(self, alpha: float, alpha_dot: float, s: float, dt: float):
        """使用RK4方法更新状态变量"""
        # RK4积分
        k1 = dt * self._calculate_state_derivatives(alpha, alpha_dot, s)
        k2 = dt * self._calculate_state_derivatives(alpha, alpha_dot, s, self.X + 0.5*k1)
        k3 = dt * self._calculate_state_derivatives(alpha, alpha_dot, s, self.X + 0.5*k2)
        k4 = dt * self._calculate_state_derivatives(alpha, alpha_dot, s, self.X + k3)
        
        self.X += (k1 + 2*k2 + 2*k3 + k4) / 6
    
    def _calculate_state_derivatives(self, alpha: float, alpha_dot: float, s: float,
                                   X_current: Optional[np.ndarray] = None) -> np.ndarray:
        """计算状态变量导数"""
        if X_current is None:
            X_current = self.X
        
        dXdt = np.zeros(12)
        
        # 非定常环量状态变量 (X1, X2)
        b1, b2 = self.params['b1'], self.params['b2']
        A1, A2 = self.params['A1'], self.params['A2']
        
        dXdt[0] = -X_current[0] / (b1 * s) + A1 * alpha_dot / s if s > 1e-8 else 0
        dXdt[1] = -X_current[1] / (b2 * s) + A2 * alpha_dot / s if s > 1e-8 else 0
        
        # 分离点状态变量 (X3)
        Tf = self.params['Tf']
        alpha_f = alpha - X_current[0] - X_current[1]  # 有效攻角
        
        if abs(alpha_f) > self.params['alpha_ds0']:
            alpha_f_target = self.params['alpha_ds0'] * np.sign(alpha_f)
        else:
            alpha_f_target = alpha_f
        
        dXdt[2] = -(X_current[2] - alpha_f_target) / (Tf * s) if s > 1e-8 else 0
        
        # 涡状态变量 (X4, X5)
        if self.enhanced_mode:
            Tv, Tvl = self.params['Tv'], self.params['Tvl']
            
            # 涡脱落条件
            if abs(alpha_f) > self.params['alpha_ss'] and X_current[3] <= 0:
                dXdt[3] = 1.0  # 开始涡脱落
            else:
                dXdt[3] = -X_current[3] / (Tv * s) if s > 1e-8 else 0
            
            # 涡升力
            if X_current[3] > 0:
                dXdt[4] = -X_current[4] / (Tvl * s) + 0.5 * np.sign(alpha_f) if s > 1e-8 else 0
            else:
                dXdt[4] = -X_current[4] / (Tvl * s) if s > 1e-8 else 0
        
        # 其他状态变量（简化处理）
        for i in range(5, 12):
            dXdt[i] = -X_current[i] / (10.0 * s) if s > 1e-8 else 0  # 简化的衰减
        
        return dXdt
    
    def set_3d_parameters(self, rotation_rate: float, radial_position: float):
        """设置三维修正参数"""
        if self.enable_3d_correction:
            self.rotation_rate = rotation_rate
            self.radial_position = radial_position
    
    def reset_model(self):
        """重置模型状态"""
        self.X.fill(0.0)
        self.alpha_history.clear()
        self.time_history.clear()
        self.coefficient_history.clear()
    
    def get_model_state(self) -> Dict[str, Any]:
        """获取模型状态"""
        return {
            'state_variables': self.X.tolist(),
            'parameters': self.params,
            'alpha_history': self.alpha_history[-5:],  # 最近5个值
            'coefficient_history': self.coefficient_history[-5:],
            'enhanced_mode': self.enhanced_mode,
            '3d_correction': self.enable_3d_correction
        }


class ONERAModel(DynamicStallModel):
    """
    ONERA动态失速模型
    
    基于法国ONERA的动态失速模型实现。
    """
    
    def __init__(self, chord: float = 0.1, config: Optional[Dict[str, Any]] = None):
        """初始化ONERA模型"""
        self.chord = chord
        self.config = config or {}
        
        # ONERA模型参数
        self.params = {
            'E0': 0.16,     # 分离参数
            'E1': 3.0,      # 分离参数
            'r': 0.5,       # 恢复参数
            'Tv': 6.0,      # 涡时间常数
            'Tp': 1.7,      # 压力时间常数
        }
        
        # 状态变量
        self.X_onera = np.zeros(4)  # ONERA使用4个状态变量
        
        print("ONERA动态失速模型初始化完成")
    
    def calculate_coefficients(self, alpha: float, alpha_dot: float, 
                             V_rel: float, dt: float, time: float) -> Tuple[float, float, float]:
        """计算ONERA动态失速系数"""
        # 简化的ONERA实现
        s = 2 * V_rel * dt / self.chord if V_rel > 1e-8 else 0.0
        
        # 准定常系数
        Cl_qs = 2 * np.pi * alpha
        Cd_qs = 0.008 + 0.02 * (np.degrees(alpha) / 15.0)**2
        Cm_qs = -0.25 * Cl_qs
        
        # ONERA非定常修正（简化）
        X1, X2, X3, X4 = self.X_onera
        
        # 分离点动态
        f_st = 0.5 * (1 + np.tanh(self.params['E0'] * (self.params['E1'] - abs(alpha))))
        
        # 更新状态变量
        if s > 1e-8:
            self.X_onera[0] += dt * (-X1 / (self.params['Tp'] * s) + f_st / (self.params['Tp'] * s))
        
        # 修正系数
        Cl = Cl_qs * X1
        Cd = Cd_qs
        Cm = Cm_qs
        
        return Cl, Cd, Cm
    
    def reset_model(self):
        """重置ONERA模型"""
        self.X_onera.fill(0.0)


def create_dynamic_stall_model(model_type: str = 'leishman_beddoes', 
                              **kwargs) -> DynamicStallModel:
    """
    创建动态失速模型
    
    Args:
        model_type: 模型类型
        **kwargs: 模型参数
        
    Returns:
        动态失速模型实例
    """
    models = {
        'leishman_beddoes': LeishmanBeddoesModel,
        'lb': LeishmanBeddoesModel,
        'onera': ONERAModel
    }
    
    if model_type not in models:
        raise ValueError(f"不支持的动态失速模型: {model_type}")
    
    return models[model_type](**kwargs)    

    def calculate_dynamic_stall(self, state):
        """
        完整的Leishman-Beddoes动态失速计算 - 中保真度实现
        
        Args:
            state: 包含攻角、攻角变化率等状态信息的字典
            
        Returns:
            dict: 包含动态失速修正后的气动系数
        """
        try:
            alpha = state.get('alpha', 0.0)
            alpha_dot = state.get('alpha_dot', 0.0)
            V_rel = state.get('V_rel', 50.0)
            dt = state.get('dt', 0.01)
            c = state.get('c', 0.08)
            
            # 无量纲时间步长
            s = 2 * V_rel * dt / c
            
            # 更新历史
            self.alpha_history.append(alpha)
            if len(self.alpha_history) > 10:
                self.alpha_history.pop(0)
            
            # 静态气动系数
            Cl_static, Cd_static, Cm_static = self._calculate_static_coefficients(alpha)
            
            # 第一步：非定常附着流修正
            Cl_unsteady = self._calculate_unsteady_attached_flow(alpha, alpha_dot, s)
            
            # 第二步：分离点计算
            f_separation = self._calculate_separation_point(alpha, s)
            
            # 第三步：动态失速判断和涡脱落
            if abs(alpha) > self.params['alpha_ds0'] and alpha_dot > 0:
                # 进入动态失速
                self.in_dynamic_stall = True
                
                # 涡脱落效应
                Cl_vortex, Cm_vortex = self._calculate_vortex_effects(alpha, alpha_dot, s)
                
                # 总的升力系数
                Cl_dynamic = Cl_unsteady + Cl_vortex
                
                # 动态失速阻力增量
                Cd_dynamic = Cd_static + self._calculate_dynamic_drag_increment(alpha, f_separation)
                
                # 力矩系数
                Cm_dynamic = Cm_static + Cm_vortex
                
            else:
                # 附着流或失速恢复
                if self.in_dynamic_stall and alpha_dot < 0:
                    # 失速恢复过程
                    Cl_dynamic = self._calculate_stall_recovery(Cl_unsteady, s)
                else:
                    Cl_dynamic = Cl_unsteady
                
                Cd_dynamic = Cd_static
                Cm_dynamic = Cm_static
                
                if abs(alpha) < self.params['alpha_ds0'] * 0.8:
                    self.in_dynamic_stall = False
            
            # 三维修正（如果启用）
            if self.enable_3d_corrections:
                Cl_dynamic, Cd_dynamic, Cm_dynamic = self._apply_3d_corrections(
                    Cl_dynamic, Cd_dynamic, Cm_dynamic, alpha, state.get('r_R', 0.7)
                )
            
            # 更新内部状态
            self._update_internal_states(alpha, alpha_dot, f_separation, s)
            
            return {
                'Cl_dynamic': Cl_dynamic,
                'Cd_dynamic': Cd_dynamic,
                'Cm_dynamic': Cm_dynamic,
                'Cl_static': Cl_static,
                'separation_point': f_separation,
                'stall_flag': self.in_dynamic_stall,
                'alpha': alpha,
                'alpha_dot': alpha_dot,
                'unsteady_factor': Cl_dynamic / Cl_static if abs(Cl_static) > 1e-10 else 1.0
            }
            
        except Exception as e:
            print(f"动态失速计算失败: {e}")
            return {
                'Cl_dynamic': 0.0,
                'Cd_dynamic': 0.1,
                'Cm_dynamic': 0.0,
                'Cl_static': 0.0,
                'separation_point': 1.0,
                'stall_flag': False,
                'alpha': 0.0,
                'alpha_dot': 0.0,
                'error': str(e)
            }
    
    def _calculate_static_coefficients(self, alpha):
        """计算静态气动系数"""
        alpha_deg = np.degrees(alpha)
        
        # 升力系数（分段线性模型）
        if abs(alpha_deg) < 15:
            Cl = 2 * np.pi * alpha  # 线性范围
        else:
            # 失速后
            Cl_max = 1.4
            alpha_stall = np.radians(15)
            if alpha > alpha_stall:
                Cl = Cl_max * (1 - 0.4 * (alpha - alpha_stall) / alpha_stall)
            else:
                Cl = -Cl_max * (1 - 0.4 * (-alpha - alpha_stall) / alpha_stall)
        
        # 阻力系数
        Cd = 0.008 + 0.02 * alpha**2
        if abs(alpha_deg) > 15:
            Cd += 0.1 * (abs(alpha_deg) - 15) / 15
        
        # 力矩系数
        Cm = -0.1 * alpha
        if abs(alpha_deg) > 15:
            Cm += -0.05 * np.sign(alpha) * (abs(alpha_deg) - 15) / 15
        
        return Cl, Cd, Cm
    
    def _calculate_unsteady_attached_flow(self, alpha, alpha_dot, s):
        """计算非定常附着流效应"""
        # Wagner函数近似
        if len(self.alpha_history) >= 2:
            alpha_prev = self.alpha_history[-2]
            dalpha = alpha - alpha_prev
            
            # 非定常升力增量
            Cl_unsteady_increment = np.pi * dalpha  # Wagner效应
            
            # 攻角变化率效应
            if abs(alpha_dot) > 1e-6:
                Cl_dot_effect = 0.5 * np.pi * alpha_dot * s
            else:
                Cl_dot_effect = 0.0
            
            # 静态升力系数
            Cl_static = 2 * np.pi * alpha
            
            return Cl_static + Cl_unsteady_increment + Cl_dot_effect
        else:
            return 2 * np.pi * alpha
    
    def _calculate_separation_point(self, alpha, s):
        """计算分离点位置"""
        alpha_deg = abs(np.degrees(alpha))
        
        if alpha_deg < 10:
            f = 1.0  # 完全附着
        elif alpha_deg < 20:
            f = 1.0 - 0.8 * (alpha_deg - 10) / 10  # 线性分离
        else:
            f = 0.2  # 深度失速
        
        return np.clip(f, 0.1, 1.0)
    
    def _calculate_vortex_effects(self, alpha, alpha_dot, s):
        """计算涡脱落效应"""
        if not hasattr(self, 'vortex_strength'):
            self.vortex_strength = 0.0
        
        # 涡强度增长
        if alpha_dot > 0 and abs(alpha) > self.params['alpha_ds0']:
            vortex_increment = 0.1 * alpha_dot * s
            self.vortex_strength += vortex_increment
        else:
            # 涡衰减
            self.vortex_strength *= 0.95
        
        # 涡升力和力矩
        Cl_vortex = self.vortex_strength * 2.0
        Cm_vortex = -self.vortex_strength * 0.5
        
        return Cl_vortex, Cm_vortex
    
    def _calculate_dynamic_drag_increment(self, alpha, f_separation):
        """计算动态阻力增量"""
        if f_separation < 0.8:
            # 分离导致的阻力增加
            drag_increment = 0.2 * (0.8 - f_separation) / 0.8
            return drag_increment
        return 0.0
    
    def _calculate_stall_recovery(self, Cl_unsteady, s):
        """计算失速恢复过程"""
        if not hasattr(self, 'recovery_factor'):
            self.recovery_factor = 1.0
        
        # 恢复过程的时间常数
        tau_recovery = 5.0
        self.recovery_factor = 1.0 - (1.0 - self.recovery_factor) * np.exp(-s / tau_recovery)
        
        return Cl_unsteady * self.recovery_factor
    
    def _apply_3d_corrections(self, Cl, Cd, Cm, alpha, r_R):
        """应用三维修正"""
        # 径向位置修正
        aspect_ratio_effect = 1.0 + 0.2 * (1 - r_R)
        
        # 三维失速延迟
        stall_delay_factor = 1.0 + 0.1 * (1 - r_R) * np.cos(alpha)
        
        Cl_3d = Cl * aspect_ratio_effect * stall_delay_factor
        Cd_3d = Cd / aspect_ratio_effect
        Cm_3d = Cm * aspect_ratio_effect
        
        return Cl_3d, Cd_3d, Cm_3d
    
    def _update_internal_states(self, alpha, alpha_dot, f_separation, s):
        """更新内部状态变量"""
        # 更新历史状态
        if not hasattr(self, 'f_history'):
            self.f_history = []
        
        self.f_history.append(f_separation)
        if len(self.f_history) > 5:
            self.f_history.pop(0)
        
        # 更新时间积分状态
        if not hasattr(self, 'time_step'):
            self.time_step = 0
        self.time_step += 1