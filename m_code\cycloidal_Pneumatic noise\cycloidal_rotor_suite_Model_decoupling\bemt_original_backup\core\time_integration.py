"""
时间积分器
=========

实现BEMT求解器的时间积分方法，支持多种数值积分算法。

核心功能：
- 多种时间积分方法（Euler、RK4、Adams-Bashforth等）
- 自适应时间步长控制
- 稳定性监控
- 误差估计和控制

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, Callable, Optional, Tuple
import warnings
from abc import ABC, abstractmethod


class TimeIntegrationMethod(ABC):
    """时间积分方法基类"""
    
    @abstractmethod
    def integrate(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """
        执行时间积分
        
        Args:
            y: 当前状态向量
            t: 当前时间
            dt: 时间步长
            dydt_func: 导数函数
            
        Returns:
            下一时刻的状态向量
        """
        pass
    
    @property
    @abstractmethod
    def order(self) -> int:
        """积分方法的阶数"""
        pass


class EulerMethod(TimeIntegrationMethod):
    """显式Euler方法"""
    
    def integrate(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """显式Euler积分"""
        dydt = dydt_func(y, t)
        return y + dt * dydt
    
    @property
    def order(self) -> int:
        return 1


class RungeKutta4Method(TimeIntegrationMethod):
    """四阶Runge-Kutta方法"""
    
    def integrate(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """RK4积分"""
        k1 = dt * dydt_func(y, t)
        k2 = dt * dydt_func(y + 0.5 * k1, t + 0.5 * dt)
        k3 = dt * dydt_func(y + 0.5 * k2, t + 0.5 * dt)
        k4 = dt * dydt_func(y + k3, t + dt)
        
        return y + (k1 + 2*k2 + 2*k3 + k4) / 6
    
    @property
    def order(self) -> int:
        return 4


class AdamsBashforth2Method(TimeIntegrationMethod):
    """二阶Adams-Bashforth方法"""
    
    def __init__(self):
        self.previous_derivative = None
    
    def integrate(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """Adams-Bashforth积分"""
        current_derivative = dydt_func(y, t)
        
        if self.previous_derivative is None:
            # 第一步使用Euler方法
            result = y + dt * current_derivative
        else:
            # Adams-Bashforth公式
            result = y + dt * (1.5 * current_derivative - 0.5 * self.previous_derivative)
        
        self.previous_derivative = current_derivative.copy()
        return result
    
    @property
    def order(self) -> int:
        return 2


class AdaptiveRungeKuttaMethod(TimeIntegrationMethod):
    """自适应Runge-Kutta方法（RK45）"""
    
    def __init__(self, rtol: float = 1e-6, atol: float = 1e-9):
        self.rtol = rtol  # 相对误差容差
        self.atol = atol  # 绝对误差容差
        self.safety_factor = 0.9
        self.min_factor = 0.2
        self.max_factor = 5.0
    
    def integrate(self, y: np.ndarray, t: float, dt: float, 
                 dydt_func: Callable) -> np.ndarray:
        """自适应RK45积分"""
        # Dormand-Prince系数
        a = np.array([
            [0, 0, 0, 0, 0, 0],
            [1/5, 0, 0, 0, 0, 0],
            [3/40, 9/40, 0, 0, 0, 0],
            [44/45, -56/15, 32/9, 0, 0, 0],
            [19372/6561, -25360/2187, 64448/6561, -212/729, 0, 0],
            [9017/3168, -355/33, 46732/5247, 49/176, -5103/18656, 0]
        ])
        
        b4 = np.array([35/384, 0, 500/1113, 125/192, -2187/6784, 11/84])
        b5 = np.array([5179/57600, 0, 7571/16695, 393/640, -92097/339200, 187/2100, 1/40])
        
        # 计算k值
        k = np.zeros((7, len(y)))
        k[0] = dt * dydt_func(y, t)
        
        for i in range(1, 6):
            y_temp = y + np.sum(a[i, :i] * k[:i].T, axis=1)
            k[i] = dt * dydt_func(y_temp, t + np.sum(a[i, :i]) * dt)
        
        # 4阶和5阶解
        y4 = y + np.sum(b4[:6] * k[:6].T, axis=1)
        y5 = y + np.sum(b5 * k.T, axis=1)
        
        # 误差估计
        error = np.abs(y5 - y4)
        tolerance = self.atol + self.rtol * np.maximum(np.abs(y), np.abs(y5))
        
        # 检查是否接受当前步长
        error_ratio = np.max(error / tolerance)
        
        if error_ratio <= 1.0:
            # 接受步长
            return y5
        else:
            # 拒绝步长，需要重新计算（这里简化返回4阶解）
            warnings.warn(f"自适应步长控制：误差比 {error_ratio:.2e}")
            return y4
    
    @property
    def order(self) -> int:
        return 5


class TimeIntegrator:
    """
    时间积分器主类
    
    管理不同的时间积分方法，提供统一的接口。
    """
    
    def __init__(self, method: str = 'rk4', config: Optional[Dict[str, Any]] = None):
        """
        初始化时间积分器
        
        Args:
            method: 积分方法名称
            config: 配置参数
        """
        self.config = config or {}
        self.method_name = method
        
        # 创建积分方法
        self.method = self._create_method(method)
        
        # 自适应时间步长参数
        self.adaptive_timestep = self.config.get('adaptive_timestep', False)
        self.min_dt = self.config.get('min_timestep', 1e-8)
        self.max_dt = self.config.get('max_timestep', 0.01)
        self.dt_safety_factor = self.config.get('dt_safety_factor', 0.9)
        
        # 稳定性监控
        self.stability_threshold = self.config.get('stability_threshold', 1e6)
        self.max_growth_rate = self.config.get('max_growth_rate', 10.0)
        
        # 历史数据
        self.solution_history = []
        self.timestep_history = []
        self.error_history = []
        
        print(f"时间积分器初始化完成")
        print(f"  方法: {method} (阶数: {self.method.order})")
        print(f"  自适应步长: {'启用' if self.adaptive_timestep else '禁用'}")
    
    def _create_method(self, method: str) -> TimeIntegrationMethod:
        """创建积分方法实例"""
        methods = {
            'euler': EulerMethod,
            'rk4': RungeKutta4Method,
            'ab2': AdamsBashforth2Method,
            'adaptive_rk': AdaptiveRungeKuttaMethod
        }
        
        if method not in methods:
            raise ValueError(f"不支持的积分方法: {method}")
        
        if method == 'adaptive_rk':
            return methods[method](
                rtol=self.config.get('rtol', 1e-6),
                atol=self.config.get('atol', 1e-9)
            )
        else:
            return methods[method]()
    
    def integrate_step(self, y: np.ndarray, t: float, dt: float,
                      dydt_func: Callable) -> Tuple[np.ndarray, float, Dict[str, Any]]:
        """
        执行单步时间积分
        
        Args:
            y: 当前状态向量
            t: 当前时间
            dt: 时间步长
            dydt_func: 导数函数
            
        Returns:
            新状态向量、实际使用的时间步长、积分信息
        """
        # 自适应时间步长控制
        if self.adaptive_timestep:
            dt_actual, integration_info = self._adaptive_timestep_control(
                y, t, dt, dydt_func
            )
        else:
            dt_actual = dt
            integration_info = {'adaptive': False}
        
        # 执行积分
        try:
            y_new = self.method.integrate(y, t, dt_actual, dydt_func)
        except Exception as e:
            warnings.warn(f"时间积分失败: {e}")
            # 回退到Euler方法
            euler_method = EulerMethod()
            y_new = euler_method.integrate(y, t, dt_actual, dydt_func)
            integration_info['fallback_to_euler'] = True
        
        # 稳定性检查
        stability_info = self._check_stability(y, y_new, dt_actual)
        integration_info.update(stability_info)
        
        # 更新历史
        self.solution_history.append(y_new.copy())
        self.timestep_history.append(dt_actual)
        
        # 限制历史长度
        max_history = self.config.get('max_history_length', 1000)
        if len(self.solution_history) > max_history:
            self.solution_history.pop(0)
            self.timestep_history.pop(0)
        
        return y_new, dt_actual, integration_info
    
    def _adaptive_timestep_control(self, y: np.ndarray, t: float, dt: float,
                                 dydt_func: Callable) -> Tuple[float, Dict[str, Any]]:
        """
        自适应时间步长控制
        
        Args:
            y: 当前状态
            t: 当前时间
            dt: 建议时间步长
            dydt_func: 导数函数
            
        Returns:
            调整后的时间步长和信息
        """
        # 计算当前导数
        dydt = dydt_func(y, t)
        
        # 基于导数大小调整时间步长
        max_derivative = np.max(np.abs(dydt))
        if max_derivative > 1e-15:
            # CFL条件
            dt_cfl = self.dt_safety_factor / max_derivative
            dt_suggested = min(dt, dt_cfl)
        else:
            dt_suggested = dt
        
        # 应用时间步长限制
        dt_actual = np.clip(dt_suggested, self.min_dt, self.max_dt)
        
        info = {
            'adaptive': True,
            'dt_suggested': dt_suggested,
            'dt_actual': dt_actual,
            'max_derivative': max_derivative,
            'cfl_limited': dt_actual == dt_cfl
        }
        
        return dt_actual, info
    
    def _check_stability(self, y_old: np.ndarray, y_new: np.ndarray, 
                        dt: float) -> Dict[str, Any]:
        """
        检查数值稳定性
        
        Args:
            y_old: 旧状态
            y_new: 新状态
            dt: 时间步长
            
        Returns:
            稳定性信息
        """
        # 计算增长率
        if np.allclose(y_old, 0):
            growth_rate = np.linalg.norm(y_new)
        else:
            growth_rate = np.linalg.norm(y_new - y_old) / (np.linalg.norm(y_old) * dt)
        
        # 检查是否超过阈值
        is_stable = (np.all(np.isfinite(y_new)) and 
                    np.max(np.abs(y_new)) < self.stability_threshold and
                    growth_rate < self.max_growth_rate)
        
        stability_info = {
            'stable': is_stable,
            'growth_rate': growth_rate,
            'max_value': np.max(np.abs(y_new)),
            'has_nan': np.any(np.isnan(y_new)),
            'has_inf': np.any(np.isinf(y_new))
        }
        
        if not is_stable:
            warnings.warn(f"数值不稳定检测: 增长率={growth_rate:.2e}")
        
        return stability_info
    
    def estimate_optimal_timestep(self, y: np.ndarray, t: float, 
                                dydt_func: Callable) -> float:
        """
        估计最优时间步长
        
        Args:
            y: 当前状态
            t: 当前时间
            dydt_func: 导数函数
            
        Returns:
            建议的时间步长
        """
        # 计算导数
        dydt = dydt_func(y, t)
        
        # 基于导数的时间尺度估计
        if np.any(np.abs(dydt) > 1e-15):
            time_scale = np.min(np.abs(y[np.abs(dydt) > 1e-15] / dydt[np.abs(dydt) > 1e-15]))
        else:
            time_scale = self.max_dt
        
        # 基于积分方法阶数的安全因子
        safety_factors = {1: 0.5, 2: 0.7, 4: 0.9, 5: 0.95}
        safety_factor = safety_factors.get(self.method.order, 0.5)
        
        optimal_dt = safety_factor * time_scale
        
        return np.clip(optimal_dt, self.min_dt, self.max_dt)
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """获取积分统计信息"""
        if not self.timestep_history:
            return {}
        
        stats = {
            'method': self.method_name,
            'order': self.method.order,
            'total_steps': len(self.timestep_history),
            'average_timestep': np.mean(self.timestep_history),
            'min_timestep': np.min(self.timestep_history),
            'max_timestep': np.max(self.timestep_history),
            'timestep_std': np.std(self.timestep_history)
        }
        
        # 计算解的变化统计
        if len(self.solution_history) > 1:
            changes = [np.linalg.norm(self.solution_history[i] - self.solution_history[i-1])
                      for i in range(1, len(self.solution_history))]
            stats.update({
                'average_solution_change': np.mean(changes),
                'max_solution_change': np.max(changes),
                'solution_change_std': np.std(changes)
            })
        
        return stats
    
    def reset(self):
        """重置积分器状态"""
        self.solution_history.clear()
        self.timestep_history.clear()
        self.error_history.clear()
        
        # 重置方法特定的状态
        if hasattr(self.method, 'previous_derivative'):
            self.method.previous_derivative = None
    
    def suggest_method_upgrade(self) -> Optional[str]:
        """建议方法升级"""
        if not self.timestep_history:
            return None
        
        # 如果时间步长变化很大，建议使用自适应方法
        if len(self.timestep_history) > 10:
            timestep_variation = np.std(self.timestep_history) / np.mean(self.timestep_history)
            if timestep_variation > 0.5 and self.method_name != 'adaptive_rk':
                return 'adaptive_rk'
        
        # 如果当前方法阶数较低且求解稳定，建议升级
        if self.method.order < 4 and len(self.solution_history) > 50:
            # 检查解的平滑性
            if len(self.solution_history) >= 3:
                smoothness = self._calculate_solution_smoothness()
                if smoothness < 0.1:  # 解比较平滑
                    return 'rk4'
        
        return None
    
    def _calculate_solution_smoothness(self) -> float:
        """计算解的平滑性指标"""
        if len(self.solution_history) < 3:
            return float('inf')
        
        # 计算二阶差分
        second_diffs = []
        for i in range(2, len(self.solution_history)):
            diff2 = (self.solution_history[i] - 2*self.solution_history[i-1] + 
                    self.solution_history[i-2])
            second_diffs.append(np.linalg.norm(diff2))
        
        return np.mean(second_diffs) if second_diffs else float('inf')