#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT中保真度分析接口
===================

提供统一的中保真度分析接口，整合原始bemt_medium_module.py的功能。

作者: Augment Agent
日期: 2025-07-24
"""

import time
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings
import json
import os

# 导入工具模块
try:
    from ..utils.error_handling import (
        BEMTError, BEMTInputError, 
        validate_positive, validate_array
    )
    from ..utils.config import get_global_config, ConfigManager
except ImportError:
    # 简化的错误处理
    class BEMTError(Exception): pass
    class BEMTInputError(BEMTError): pass
    def validate_positive(x, name): 
        if x <= 0: raise BEMTInputError(f"{name}必须为正数")
    def validate_array(x, name): 
        if not hasattr(x, '__len__'): raise BEMTInputError(f"{name}必须为数组")

# 导入核心组件
try:
    from .enhanced_bemt_solver import EnhancedBEMTSolver
    from .cycloidal_enhanced_solver import CycloidalEnhancedSolver
    _ENHANCED_SOLVERS_AVAILABLE = True
except ImportError:
    _ENHANCED_SOLVERS_AVAILABLE = False

try:
    from ..geometry.rotor_geometry import RotorGeometry3D
    _GEOMETRY_AVAILABLE = True
except ImportError:
    _GEOMETRY_AVAILABLE = False

try:
    from ..simple_bemt import SimpleBEMT
    _SIMPLE_SOLVER_AVAILABLE = True
except ImportError:
    _SIMPLE_SOLVER_AVAILABLE = False


class FlightCondition:
    """飞行条件类"""
    
    def __init__(self, 
                 rpm: float,
                 forward_speed: float = 0.0,
                 density: float = 1.225,
                 temperature: float = 288.15,
                 pressure: float = 101325.0,
                 **kwargs):
        """
        初始化飞行条件
        
        参数:
        ----
        rpm : float
            转速 [RPM]
        forward_speed : float
            前飞速度 [m/s]
        density : float
            空气密度 [kg/m³]
        temperature : float
            温度 [K]
        pressure : float
            压力 [Pa]
        """
        validate_positive(rpm, "转速")
        validate_positive(density, "空气密度")
        validate_positive(temperature, "温度")
        validate_positive(pressure, "压力")
        
        self.rpm = rpm
        self.forward_speed = forward_speed
        self.density = density
        self.temperature = temperature
        self.pressure = pressure
        
        # 计算派生量
        self.omega = rpm * 2 * 3.14159 / 60  # [rad/s]
        self.sound_speed = (1.4 * 287 * temperature) ** 0.5  # [m/s]
        
        # 存储额外参数
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'rpm': self.rpm,
            'forward_speed': self.forward_speed,
            'density': self.density,
            'temperature': self.temperature,
            'pressure': self.pressure,
            'omega': self.omega,
            'sound_speed': self.sound_speed
        }


def create_enhanced_solver(rotor_type: str = "conventional",
                          radius: float = 1.0,
                          num_blades: int = 4,
                          hub_radius: Optional[float] = None,
                          solver_settings: Optional[Dict] = None,
                          geometry_config: Optional[Dict] = None,
                          **kwargs) -> Union[object, None]:
    """
    创建增强BEMT求解器
    
    参数:
    ----
    rotor_type : str
        旋翼类型 ('conventional', 'cycloidal', 'simple')
    radius : float
        旋翼半径 [m]
    num_blades : int
        桨叶数量
    hub_radius : float, optional
        桂毂半径 [m]
    solver_settings : Dict, optional
        求解器设置
    geometry_config : Dict, optional
        几何配置
        
    返回:
    ----
    solver : 求解器实例
    """
    # 输入验证
    validate_positive(radius, "旋翼半径")
    if num_blades < 2:
        raise BEMTInputError("桨叶数量必须至少为2")
    
    # 默认参数
    if hub_radius is None:
        hub_radius = radius * 0.1
    
    if solver_settings is None:
        solver_settings = {}
    
    # 根据旋翼类型创建求解器
    if rotor_type.lower() == "conventional":
        if _ENHANCED_SOLVERS_AVAILABLE:
            return EnhancedBEMTSolver(
                radius=radius,
                num_blades=num_blades,
                hub_radius=hub_radius,
                **solver_settings,
                **kwargs
            )
        else:
            warnings.warn("增强求解器不可用，使用简化求解器")
            return _create_simple_solver(radius, num_blades, hub_radius, **kwargs)
    
    elif rotor_type.lower() == "cycloidal":
        if _ENHANCED_SOLVERS_AVAILABLE:
            return CycloidalEnhancedSolver(
                radius=radius,
                num_blades=num_blades,
                hub_radius=hub_radius,
                **solver_settings,
                **kwargs
            )
        else:
            warnings.warn("循环翼求解器不可用，使用简化求解器")
            return _create_simple_solver(radius, num_blades, hub_radius, **kwargs)
    
    elif rotor_type.lower() == "simple":
        return _create_simple_solver(radius, num_blades, hub_radius, **kwargs)
    
    else:
        raise BEMTInputError(f"未知的旋翼类型: {rotor_type}")


def _create_simple_solver(radius: float, num_blades: int, hub_radius: float, **kwargs):
    """创建简化求解器"""
    if _SIMPLE_SOLVER_AVAILABLE:
        return SimpleBEMT(
            radius=radius,
            num_blades=num_blades,
            hub_radius=hub_radius,
            **kwargs
        )
    else:
        raise BEMTError("无可用的求解器")


def create_flight_condition(rpm: float,
                           forward_speed: float = 0.0,
                           altitude: float = 0.0,
                           temperature_offset: float = 0.0,
                           **kwargs) -> FlightCondition:
    """
    创建飞行条件对象
    
    参数:
    ----
    rpm : float
        转速 [RPM]
    forward_speed : float
        前飞速度 [m/s]
    altitude : float
        高度 [m]
    temperature_offset : float
        温度偏差 [K]
        
    返回:
    ----
    condition : FlightCondition
        飞行条件对象
    """
    # 标准大气模型
    if altitude <= 11000:  # 对流层
        temperature = 288.15 - 0.0065 * altitude + temperature_offset
        pressure = 101325 * (temperature / 288.15) ** 5.256
    else:  # 简化平流层
        temperature = 216.65 + temperature_offset
        pressure = 22632 * np.exp(-0.0001577 * (altitude - 11000))
    
    density = pressure / (287 * temperature)
    
    return FlightCondition(
        rpm=rpm,
        forward_speed=forward_speed,
        density=density,
        temperature=temperature,
        pressure=pressure,
        altitude=altitude,
        **kwargs
    )


def run_medium_analysis(solver: object,
                       flight_condition: FlightCondition,
                       analysis_type: str = "performance",
                       output_config: Optional[Dict] = None) -> Dict[str, Any]:
    """
    运行中保真度分析
    
    参数:
    ----
    solver : 求解器实例
    flight_condition : FlightCondition
        飞行条件
    analysis_type : str
        分析类型 ('performance', 'loads', 'stability')
    output_config : Dict, optional
        输出配置
        
    返回:
    ----
    results : Dict[str, Any]
        分析结果
    """
    if output_config is None:
        output_config = {'detailed': False}
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行求解
    try:
        if hasattr(solver, 'solve'):
            result = solver.solve(
                rpm=flight_condition.rpm,
                forward_speed=flight_condition.forward_speed,
                density=flight_condition.density,
                verbose=output_config.get('verbose', False)
            )
        else:
            raise BEMTError("求解器缺少solve方法")
        
        # 计算执行时间
        execution_time = time.time() - start_time
        
        # 整理结果
        analysis_result = {
            'performance': result,
            'flight_condition': flight_condition.to_dict(),
            'analysis_type': analysis_type,
            'execution_time': execution_time,
            'solver_info': {
                'type': solver.__class__.__name__,
                'converged': result.get('converged', False),
                'iterations': result.get('iterations', 0)
            }
        }
        
        # 添加详细信息（如果需要）
        if output_config.get('detailed', False):
            analysis_result['detailed_data'] = {
                'lambda_i': result.get('lambda_i', []),
                'alpha': result.get('alpha', []),
                'cl': result.get('cl', []),
                'cd': result.get('cd', []),
                'dT': result.get('dT', []),
                'dQ': result.get('dQ', [])
            }
        
        return analysis_result
        
    except Exception as e:
        return {
            'error': str(e),
            'flight_condition': flight_condition.to_dict(),
            'analysis_type': analysis_type,
            'execution_time': time.time() - start_time,
            'success': False
        }


def load_config(config_file: str) -> Dict[str, Any]:
    """
    从文件加载配置
    
    参数:
    ----
    config_file : str
        配置文件路径
        
    返回:
    ----
    config : Dict[str, Any]
        配置字典
    """
    if not os.path.exists(config_file):
        raise BEMTInputError(f"配置文件不存在: {config_file}")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.endswith('.json'):
                config = json.load(f)
            else:
                # 简化的配置文件解析
                config = {}
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
        
        return config
        
    except Exception as e:
        raise BEMTError(f"配置文件加载失败: {e}")


# 导出接口
__all__ = [
    'FlightCondition',
    'create_enhanced_solver',
    'create_flight_condition',
    'run_medium_analysis',
    'load_config'
]
