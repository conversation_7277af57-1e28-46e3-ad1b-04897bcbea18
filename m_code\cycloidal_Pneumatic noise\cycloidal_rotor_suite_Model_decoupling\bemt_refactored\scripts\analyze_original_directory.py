#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始目录分析脚本
===============

分析原始BEMT目录中的所有文件，确定处理策略。

作者: Augment Agent
日期: 2025-07-28
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
import hashlib


class OriginalDirectoryAnalyzer:
    """原始目录分析器"""
    
    def __init__(self, original_path: str, refactored_path: str):
        """初始化分析器"""
        self.original_path = Path(original_path)
        self.refactored_path = Path(refactored_path)
        
        # 文件分类
        self.file_categories = {
            'core_code': [],           # 核心代码文件
            'test_files': [],          # 测试文件
            'example_files': [],       # 示例文件
            'config_files': [],        # 配置文件
            'documentation': [],       # 文档文件
            'data_files': [],          # 数据文件
            'temporary_files': [],     # 临时文件
            'backup_files': [],        # 备份文件
            'cache_files': [],         # 缓存文件
            'duplicate_files': [],     # 重复文件
            'unknown_files': []        # 未知文件
        }
        
        # 处理策略
        self.processing_strategies = {
            'migrate': [],             # 迁移到新结构
            'merge': [],               # 合并到现有文件
            'delete': [],              # 删除
            'preserve': [],            # 保留但不迁移
            'review': []               # 需要人工审查
        }
    
    def analyze_directory(self) -> Dict[str, Any]:
        """分析目录结构"""
        
        print("🔍 开始分析原始目录结构")
        print("=" * 60)
        
        if not self.original_path.exists():
            print(f"❌ 原始目录不存在: {self.original_path}")
            return {}
        
        # 遍历所有文件
        all_files = list(self.original_path.rglob("*"))
        file_count = len([f for f in all_files if f.is_file()])
        dir_count = len([f for f in all_files if f.is_dir()])
        
        print(f"📊 目录统计:")
        print(f"   总文件数: {file_count}")
        print(f"   总目录数: {dir_count}")
        print()
        
        # 分析每个文件
        for file_path in all_files:
            if file_path.is_file():
                self._analyze_file(file_path)
        
        # 生成分析报告
        return self._generate_analysis_report()
    
    def _analyze_file(self, file_path: Path):
        """分析单个文件"""
        
        relative_path = file_path.relative_to(self.original_path)
        file_info = {
            'path': file_path,
            'relative_path': relative_path,
            'size': file_path.stat().st_size,
            'extension': file_path.suffix.lower(),
            'name': file_path.name
        }
        
        # 文件分类
        category = self._categorize_file(file_info)
        self.file_categories[category].append(file_info)
        
        # 处理策略
        strategy = self._determine_strategy(file_info, category)
        self.processing_strategies[strategy].append(file_info)
    
    def _categorize_file(self, file_info: Dict) -> str:
        """文件分类"""
        
        path_str = str(file_info['relative_path']).lower()
        name = file_info['name'].lower()
        ext = file_info['extension']
        
        # 缓存文件
        if '__pycache__' in path_str or ext == '.pyc':
            return 'cache_files'
        
        # 备份文件
        if 'backup' in name or name.endswith('_backup.py') or name.endswith('.bak'):
            return 'backup_files'
        
        # 临时文件
        if name.startswith('temp_') or name.startswith('tmp_') or ext in ['.tmp', '.temp']:
            return 'temporary_files'
        
        # 测试文件
        if 'test' in path_str or name.startswith('test_') or 'validation' in path_str:
            return 'test_files'
        
        # 示例文件
        if 'example' in path_str or 'demo' in name or name.startswith('quick_'):
            return 'example_files'
        
        # 文档文件
        if ext in ['.md', '.txt', '.rst', '.doc'] or 'doc' in path_str or 'report' in name:
            return 'documentation'
        
        # 配置文件
        if name in ['__init__.py', 'config.py', 'setup.py'] or ext in ['.json', '.yaml', '.yml', '.ini']:
            return 'config_files'
        
        # 数据文件
        if 'data' in path_str or ext in ['.dat', '.csv', '.json', '.npy']:
            return 'data_files'
        
        # 核心代码文件
        if ext == '.py' and not any(x in name for x in ['test_', 'demo_', 'example_']):
            return 'core_code'
        
        # 其他文件
        return 'unknown_files'
    
    def _determine_strategy(self, file_info: Dict, category: str) -> str:
        """确定处理策略"""
        
        path_str = str(file_info['relative_path'])
        name = file_info['name']
        
        # 删除策略
        if category in ['cache_files', 'temporary_files']:
            return 'delete'
        
        # 备份文件通常删除
        if category == 'backup_files':
            return 'delete'
        
        # 检查是否已在重构版本中存在
        if self._exists_in_refactored(file_info):
            if category == 'test_files' and 'final_integration_test' in name:
                return 'review'  # 重要测试文件需要审查
            elif category in ['core_code', 'test_files']:
                return 'merge'   # 可能需要合并
            else:
                return 'delete'  # 其他重复文件删除
        
        # 迁移策略
        if category in ['core_code', 'test_files', 'data_files']:
            return 'migrate'
        
        # 保留策略
        if category == 'documentation':
            return 'preserve'
        
        # 其他情况需要审查
        return 'review'
    
    def _exists_in_refactored(self, file_info: Dict) -> bool:
        """检查文件是否在重构版本中存在"""
        
        # 简化的检查逻辑
        name = file_info['name']
        
        # 检查重构目录中是否有同名文件
        refactored_files = list(self.refactored_path.rglob(name))
        
        return len(refactored_files) > 0
    
    def _generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        
        report = {
            'summary': {
                'total_files': sum(len(files) for files in self.file_categories.values()),
                'categories': {cat: len(files) for cat, files in self.file_categories.items()},
                'strategies': {strategy: len(files) for strategy, files in self.processing_strategies.items()}
            },
            'file_categories': self.file_categories,
            'processing_strategies': self.processing_strategies
        }
        
        return report
    
    def print_analysis_report(self, report: Dict[str, Any]):
        """打印分析报告"""
        
        print("📋 文件分类统计:")
        for category, count in report['summary']['categories'].items():
            if count > 0:
                print(f"   {category:15s}: {count:3d} 个文件")
        
        print(f"\n📋 处理策略统计:")
        for strategy, count in report['summary']['strategies'].items():
            if count > 0:
                print(f"   {strategy:15s}: {count:3d} 个文件")
        
        print(f"\n📋 详细文件清单:")
        
        # 按策略显示文件
        for strategy, files in self.processing_strategies.items():
            if files:
                print(f"\n🔸 {strategy.upper()} ({len(files)} 个文件):")
                for file_info in files[:10]:  # 只显示前10个
                    print(f"     {file_info['relative_path']}")
                if len(files) > 10:
                    print(f"     ... 还有 {len(files) - 10} 个文件")


def main():
    """主函数"""
    
    # 路径设置
    base_path = Path(__file__).parent.parent.parent
    original_path = base_path / "bemt_medium_fidelity_validation"
    refactored_path = base_path / "bemt_refactored"
    
    print("🚀 原始目录分析工具")
    print("=" * 60)
    print(f"原始目录: {original_path}")
    print(f"重构目录: {refactored_path}")
    print()
    
    # 创建分析器
    analyzer = OriginalDirectoryAnalyzer(str(original_path), str(refactored_path))
    
    # 执行分析
    report = analyzer.analyze_directory()
    
    # 显示报告
    analyzer.print_analysis_report(report)
    
    return report


if __name__ == "__main__":
    report = main()
