"""
叶素和桨叶建模
=============

完整复刻原始模块的叶素建模功能，包含几何定义、运动学计算和气动力计算。

核心功能：
- 叶素几何参数化
- 运动学状态更新
- 有效攻角计算
- 气动力系数计算
- 动态失速模型集成
- 粘性修正

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import warnings

from aerodynamics.dynamic_stall import LeishmanBeddoesModel
from aerodynamics.airfoil_database import AirfoilDatabase
from utils.error_handling import validate_input, safe_divide, safe_sqrt


class BladeElement:
    """
    单个叶素类
    
    表示桨叶上的一个离散单元，包含几何、运动学和气动力学属性。
    """
    
    def __init__(self, element_id: int, radius: float, chord: float, 
                 twist: float = 0.0, config: Optional[Dict[str, Any]] = None):
        """
        初始化叶素
        
        Args:
            element_id: 叶素ID
            radius: 径向位置 [m]
            chord: 弦长 [m]
            twist: 扭转角 [rad]
            config: 配置参数
        """
        # 基本属性
        self.element_id = element_id
        self.radius = radius
        self.chord = chord
        self.twist = twist
        self.config = config or {}
        
        # 几何属性
        self.span_width = 0.0  # 展向宽度，由上级设置
        self.area = 0.0  # 叶素面积
        self.airfoil_name = self.config.get('airfoil_name', 'NACA0012')
        
        # 运动学状态
        self.position = np.zeros(3)  # 位置 [x, y, z]
        self.velocity = np.zeros(3)  # 速度 [vx, vy, vz]
        self.angular_velocity = 0.0  # 角速度 [rad/s]
        self.rotation_matrix = np.eye(3)  # 姿态矩阵
        
        # 气动状态
        self.alpha_eff = 0.0  # 有效攻角 [rad]
        self.V_rel = np.zeros(3)  # 相对速度 [m/s]
        self.circulation = 0.0  # 环量 [m²/s]
        
        # 气动力
        self.force = np.zeros(3)  # 力 [N]
        self.moment = np.zeros(3)  # 力矩 [N·m]
        self.lift_force = 0.0  # 升力 [N]
        self.drag_force = 0.0  # 阻力 [N]
        
        # 气动系数
        self.Cl = 0.0  # 升力系数
        self.Cd = 0.0  # 阻力系数
        self.Cm = 0.0  # 力矩系数
        
        # 动态失速模型
        self.lb_model = None
        self.previous_alpha = 0.0
        self.previous_time = 0.0
        self.alpha_history = []
        self.time_history = []
        
        # 初始化动态失速模型
        if self.config.get('enable_dynamic_stall', False):
            self._initialize_dynamic_stall_model()
        
        # 翼型数据库
        self.airfoil_database = None
        if self.config.get('enable_airfoil_database', True):
            try:
                self.airfoil_database = AirfoilDatabase(
                    data_dir=self.config.get('airfoil_data_dir')
                )
            except Exception as e:
                warnings.warn(f"翼型数据库初始化失败: {e}")
        
        print(f"叶素 {element_id} 初始化完成 (r={radius:.3f}m, c={chord:.3f}m)")
    
    def _initialize_dynamic_stall_model(self):
        """初始化动态失速模型"""
        try:
            self.lb_model = LeishmanBeddoesModel(
                airfoil_name=self.airfoil_name,
                config=self.config
            )
        except Exception as e:
            warnings.warn(f"动态失速模型初始化失败: {e}")
            self.lb_model = None
    
    def update_kinematics(self, t: float, blade_angle: float, omega_rotor: float,
                         precone_angle: float = 0.0, pitch_bias_angle: float = 0.0):
        """
        更新运动学状态
        
        Args:
            t: 当前时间 [s]
            blade_angle: 桨叶角度 [rad]
            omega_rotor: 转子角速度 [rad/s]
            precone_angle: 锥角 [rad]
            pitch_bias_angle: 桨距偏置角 [rad]
        """
        # 当前方位角
        theta = omega_rotor * t + blade_angle
        
        # 基础径向位置
        r_hub = np.array([self.radius, 0.0, 0.0])
        
        # 应用锥角变换
        if abs(precone_angle) > 1e-6:
            R_precone = self._rotation_matrix_y(precone_angle)
            r_precone = R_precone @ r_hub
        else:
            r_precone = r_hub.copy()
        
        # 应用方位角旋转
        R_azimuth = self._rotation_matrix_z(theta)
        r_azimuth = R_azimuth @ r_precone
        
        # 更新位置
        self.position = r_azimuth
        
        # 计算速度 (ω × r)
        omega_vec = np.array([0.0, 0.0, omega_rotor])
        self.velocity = np.cross(omega_vec, self.position)
        
        # 更新姿态矩阵
        R_pitch = self._rotation_matrix_x(self.twist + pitch_bias_angle)
        self.rotation_matrix = R_azimuth @ R_precone @ R_pitch
        
        self.angular_velocity = omega_rotor
    
    def _rotation_matrix_x(self, angle: float) -> np.ndarray:
        """绕x轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[1, 0, 0], [0, c, -s], [0, s, c]])
    
    def _rotation_matrix_y(self, angle: float) -> np.ndarray:
        """绕y轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, 0, s], [0, 1, 0], [-s, 0, c]])
    
    def _rotation_matrix_z(self, angle: float) -> np.ndarray:
        """绕z轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]])
    
    def calculate_relative_velocity(self, V_inf: np.ndarray) -> np.ndarray:
        """
        计算相对速度
        
        Args:
            V_inf: 来流速度 [vx, vy, vz]
            
        Returns:
            相对速度向量
        """
        self.V_rel = V_inf - self.velocity
        return self.V_rel
    
    def calculate_effective_angle_of_attack(self, blade_pitch_angle: Optional[float] = None) -> float:
        """
        计算有效攻角
        
        Args:
            blade_pitch_angle: 桨叶俯仰角 [rad]
            
        Returns:
            有效攻角 [rad]
        """
        # 计算相对速度分量
        V_tangential = np.sqrt(self.V_rel[0]**2 + self.V_rel[1]**2)
        V_axial = abs(self.V_rel[2])
        
        # 几何攻角
        if V_tangential > 1e-8:
            geometric_alpha = np.arctan2(V_axial, V_tangential)
        else:
            geometric_alpha = 0.0
        
        # 桨叶俯仰角
        if blade_pitch_angle is not None:
            pitch = blade_pitch_angle
        else:
            pitch = 0.0
        
        # 有效攻角 = 几何攻角 + 桨叶俯仰角 + 扭转角
        self.alpha_eff = geometric_alpha + pitch + self.twist
        
        return self.alpha_eff


class Blade:
    """
    桨叶类
    
    包含多个叶素的桨叶对象。
    """
    
    def __init__(self, blade_id: int, config: Dict[str, Any]):
        """
        初始化桨叶
        
        Args:
            blade_id: 桨叶ID
            config: 配置参数
        """
        self.blade_id = blade_id
        self.config = config
        self.elements = []
        
        # 创建叶素
        n_elements = config.get('bemt_n_elements', 20)
        R_rotor = config.get('R_rotor', 0.3)
        c = config.get('c', 0.1)
        
        # 径向位置分布
        for i in range(n_elements):
            eta = np.cos(np.pi * (i + 0.5) / n_elements)
            r = 0.2 * R_rotor + 0.8 * R_rotor * (eta + 1) / 2
            
            element = BladeElement(
                element_id=i,
                radius=r,
                chord=c,
                twist=0.0,
                config=config
            )
            element.span_width = R_rotor / n_elements  # 设置展向宽度
            self.elements.append(element)
        
        print(f"桨叶 {blade_id} 初始化完成，包含 {len(self.elements)} 个叶素")
    
    def _initialize_dynamic_stall_model(self):
        """初始化动态失速模型"""
        try:
            model_type = self.config.get('dynamic_stall_model', 'leishman_beddoes')
            
            if model_type == 'leishman_beddoes':
                self.lb_model = LeishmanBeddoesModel(
                    chord=self.chord,
                    config=self.config,
                    enhanced_mode=self.config.get('lb_enhanced_mode', True),
                    enable_3d_correction=self.config.get('lb_3d_correction', True)
                )
                print(f"叶素 {self.element_id}: 启用L-B动态失速模型")
            
        except Exception as e:
            warnings.warn(f"动态失速模型初始化失败: {e}")
            self.lb_model = None
    
    def update_kinematics(self, t: float, blade_angle: float, omega_rotor: float,
                         precone_angle: float = 0.0, pitch_bias_angle: float = 0.0):
        """
        更新运动学状态
        
        Args:
            t: 当前时间 [s]
            blade_angle: 桨叶角度 [rad]
            omega_rotor: 转子角速度 [rad/s]
            precone_angle: 锥角 [rad]
            pitch_bias_angle: 桨距偏置角 [rad]
        """
        # 当前方位角
        theta = omega_rotor * t + blade_angle
        
        # 基础径向位置
        r_hub = np.array([self.radius, 0.0, 0.0])
        
        # 应用锥角变换
        if abs(precone_angle) > 1e-6:
            R_precone = self._rotation_matrix_y(precone_angle)
            r_precone = R_precone @ r_hub
        else:
            r_precone = r_hub.copy()
        
        # 应用方位角旋转
        R_azimuth = self._rotation_matrix_z(theta)
        r_azimuth = R_azimuth @ r_precone
        
        # 更新位置
        self.position = r_azimuth
        
        # 计算速度 (ω × r)
        omega_vec = np.array([0.0, 0.0, omega_rotor])
        self.velocity = np.cross(omega_vec, self.position)
        
        # 更新姿态矩阵
        R_pitch = self._rotation_matrix_x(self.twist + pitch_bias_angle)
        self.rotation_matrix = R_azimuth @ R_precone @ R_pitch
        
        self.angular_velocity = omega_rotor
    
    def _rotation_matrix_x(self, angle: float) -> np.ndarray:
        """绕x轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[1, 0, 0], [0, c, -s], [0, s, c]])
    
    def _rotation_matrix_y(self, angle: float) -> np.ndarray:
        """绕y轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, 0, s], [0, 1, 0], [-s, 0, c]])
    
    def _rotation_matrix_z(self, angle: float) -> np.ndarray:
        """绕z轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]])
    
    def calculate_relative_velocity(self, V_inf: np.ndarray) -> np.ndarray:
        """
        计算相对速度
        
        Args:
            V_inf: 来流速度 [vx, vy, vz]
            
        Returns:
            相对速度向量
        """
        self.V_rel = V_inf - self.velocity
        return self.V_rel
    
    def calculate_effective_angle_of_attack(self, blade_pitch_angle: Optional[float] = None) -> float:
        """
        计算有效攻角
        
        Args:
            blade_pitch_angle: 桨叶俯仰角 [rad]
            
        Returns:
            有效攻角 [rad]
        """
        # 计算相对速度分量
        V_tangential = np.sqrt(self.V_rel[0]**2 + self.V_rel[1]**2)
        V_axial = abs(self.V_rel[2])
        
        # 几何攻角
        if V_tangential > 1e-8:
            geometric_alpha = np.arctan2(V_axial, V_tangential)
        else:
            geometric_alpha = 0.0
            warnings.warn(f"叶素 {self.element_id}: 切向速度接近零")
        
        # 应用桨叶角度修正
        if blade_pitch_angle is not None:
            self.alpha_eff = geometric_alpha - blade_pitch_angle + self.twist
        else:
            self.alpha_eff = geometric_alpha + self.twist
        
        # 物理合理性检查
        max_alpha = np.radians(45.0)
        if abs(self.alpha_eff) > max_alpha:
            warnings.warn(f"叶素 {self.element_id}: 攻角过大 ({np.degrees(self.alpha_eff):.1f}°)")
        
        return self.alpha_eff
    
    def calculate_aerodynamic_coefficients(self, dt: float, t: float) -> Tuple[float, float, float]:
        """
        计算气动力系数
        
        Args:
            dt: 时间步长 [s]
            t: 当前时间 [s]
            
        Returns:
            升力系数、阻力系数、力矩系数
        """
        if self.lb_model is not None:
            # 使用动态失速模型
            self.Cl, self.Cd, self.Cm = self._calculate_unsteady_coefficients(dt, t)
        else:
            # 使用静态查表
            self.Cl, self.Cd, self.Cm = self._calculate_static_coefficients()
        
        return self.Cl, self.Cd, self.Cm
    
    def _calculate_unsteady_coefficients(self, dt: float, t: float) -> Tuple[float, float, float]:
        """使用动态失速模型计算非定常系数"""
        try:
            # 计算攻角变化率
            alpha_dot = self._calculate_alpha_dot(t)
            
            # 计算局部速度
            V_local = np.linalg.norm(self.V_rel)
            
            # 调用L-B模型
            Cl, Cd, Cm = self.lb_model.calculate_coefficients(
                alpha=self.alpha_eff,
                alpha_dot=alpha_dot,
                V_rel=V_local,
                dt=dt,
                time=t
            )
            
            return Cl, Cd, Cm
            
        except Exception as e:
            warnings.warn(f"动态失速计算失败，回退到静态方法: {e}")
            return self._calculate_static_coefficients()
    
    def _calculate_alpha_dot(self, t: float) -> float:
        """计算攻角变化率"""
        # 更新历史
        self.alpha_history.append(self.alpha_eff)
        self.time_history.append(t)
        
        # 保持历史长度
        max_history = 3
        if len(self.alpha_history) > max_history:
            self.alpha_history.pop(0)
            self.time_history.pop(0)
        
        # 计算变化率
        if len(self.alpha_history) >= 2:
            dt = self.time_history[-1] - self.time_history[-2]
            if dt > 1e-10:
                alpha_dot = (self.alpha_history[-1] - self.alpha_history[-2]) / dt
            else:
                alpha_dot = 0.0
        else:
            alpha_dot = 0.0
        
        return alpha_dot
    
    def _calculate_static_coefficients(self) -> Tuple[float, float, float]:
        """计算静态气动力系数"""
        if self.airfoil_database is not None:
            try:
                # 从翼型数据库查表
                alpha_deg = np.degrees(self.alpha_eff)
                Re = self._calculate_reynolds_number()
                
                Cl, Cd, Cm = self.airfoil_database.get_coefficients(
                    airfoil_name=self.airfoil_name,
                    alpha_deg=alpha_deg,
                    reynolds_number=Re
                )
                
                return Cl, Cd, Cm
                
            except Exception as e:
                warnings.warn(f"翼型数据库查询失败: {e}")
        
        # 使用理论模型
        return self._calculate_theoretical_coefficients()
    
    def _calculate_theoretical_coefficients(self) -> Tuple[float, float, float]:
        """使用理论模型计算系数"""
        # 薄翼理论
        Cl = 2 * np.pi * self.alpha_eff
        
        # 简化阻力模型
        Cd_0 = 0.008  # 零升阻力
        k = 0.05  # 诱导阻力因子
        Cd = Cd_0 + k * self.alpha_eff**2
        
        # 简化力矩系数
        Cm = -0.25 * Cl
        
        return Cl, Cd, Cm
    
    def _calculate_reynolds_number(self) -> float:
        """计算雷诺数"""
        V_local = np.linalg.norm(self.V_rel)
        rho = self.config.get('rho', 1.225)
        mu = 1.81e-5  # 动力粘度
        
        Re = rho * V_local * self.chord / mu
        return Re
    
    def calculate_forces(self, rho: float, dt: float, t: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算气动力和力矩
        
        Args:
            rho: 空气密度 [kg/m³]
            dt: 时间步长 [s]
            t: 当前时间 [s]
            
        Returns:
            力向量、力矩向量
        """
        # 计算气动系数
        self.calculate_aerodynamic_coefficients(dt, t)
        
        # 动压
        V_rel_mag = np.linalg.norm(self.V_rel)
        q_dyn = 0.5 * rho * V_rel_mag**2
        
        # 叶素面积
        dA = self.chord * self.span_width
        
        # 升力和阻力
        self.lift_force = self.Cl * q_dyn * dA
        self.drag_force = self.Cd * q_dyn * dA
        
        # 力的方向转换（简化实现）
        if V_rel_mag > 1e-8:
            # 升力方向（垂直于相对速度）
            V_rel_unit = self.V_rel / V_rel_mag
            lift_direction = np.array([0, 0, 1])  # 简化为z方向
            
            # 阻力方向（与相对速度相反）
            drag_direction = -V_rel_unit
            
            # 合成力
            self.force = (self.lift_force * lift_direction + 
                         self.drag_force * drag_direction)
        else:
            self.force = np.zeros(3)
        
        # 力矩
        moment_magnitude = self.Cm * q_dyn * dA * self.chord
        self.moment = np.array([0.0, moment_magnitude, 0.0])
        
        # 计算环量（Kutta-Joukowski定理）
        if V_rel_mag > 1e-8 and self.span_width > 1e-8:
            self.circulation = self.lift_force / (rho * V_rel_mag * self.span_width)
        else:
            self.circulation = 0.0
        
        return self.force, self.moment
    
    def get_element_info(self) -> Dict[str, Any]:
        """获取叶素信息"""
        return {
            'element_id': self.element_id,
            'radius': self.radius,
            'chord': self.chord,
            'twist': np.degrees(self.twist),
            'alpha_eff': np.degrees(self.alpha_eff),
            'V_rel_magnitude': np.linalg.norm(self.V_rel),
            'Cl': self.Cl,
            'Cd': self.Cd,
            'Cm': self.Cm,
            'lift_force': self.lift_force,
            'drag_force': self.drag_force,
            'circulation': self.circulation,
            'reynolds_number': self._calculate_reynolds_number()
        }


class Blade:
    """
    完整桨叶模型
    
    包含多个叶素的桨叶，管理叶素的创建、更新和载荷计算。
    """
    
    def __init__(self, blade_id: int, config: Dict[str, Any]):
        """
        初始化桨叶
        
        Args:
            blade_id: 桨叶ID
            config: 配置参数
        """
        self.blade_id = blade_id
        self.config = config
        
        # 几何参数
        self.R_rotor = config.get('R_rotor', 0.3)
        self.c_root = config.get('c', 0.1)
        self.N_elements = config.get('bemt_n_elements', 20)
        self.taper_ratio = config.get('blade_taper', 1.0)
        self.twist_total = np.radians(config.get('blade_twist_deg', 0.0))
        
        # 创建叶素
        self.elements = self._create_blade_elements()
        
        # 总载荷
        self.total_force = np.zeros(3)
        self.total_moment = np.zeros(3)
        self.total_circulation = 0.0
        
        print(f"桨叶 {blade_id} 初始化完成: {self.N_elements} 个叶素")
    
    def _create_blade_elements(self) -> List[BladeElement]:
        """创建叶素"""
        elements = []
        
        for i in range(self.N_elements):
            # 径向位置（余弦分布）
            eta = np.cos(np.pi * (i + 0.5) / self.N_elements)
            r = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta + 1) / 2
            
            # 局部弦长（锥度变化）
            r_norm = (r - 0.2 * self.R_rotor) / (0.8 * self.R_rotor)
            chord = self.c_root * (1.0 - r_norm * (1.0 - self.taper_ratio))
            
            # 局部扭转角
            twist = self.twist_total * r_norm
            
            # 创建叶素
            element = BladeElement(i, r, chord, twist, self.config)
            
            # 设置展向宽度
            if i == 0:
                span_width = r - 0.2 * self.R_rotor
            elif i == self.N_elements - 1:
                span_width = self.R_rotor - r
            else:
                # 相邻叶素中点间距
                eta_prev = np.cos(np.pi * i / self.N_elements)
                eta_next = np.cos(np.pi * (i + 1) / self.N_elements)
                r_prev = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta_prev + 1) / 2
                r_next = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta_next + 1) / 2
                span_width = abs(r_next - r_prev) / 2
            
            element.span_width = span_width
            element.area = chord * span_width
            
            elements.append(element)
        
        return elements
    
    def update_kinematics(self, t: float, blade_angle: float, omega_rotor: float,
                         precone_angle: float = 0.0, pitch_bias_angle: float = 0.0):
        """更新所有叶素的运动学状态"""
        for element in self.elements:
            element.update_kinematics(t, blade_angle, omega_rotor, 
                                    precone_angle, pitch_bias_angle)
    
    def calculate_pitch_angle(self, azimuth: float, r_R: float) -> float:
        """
        计算桨叶俯仰角
        
        Args:
            azimuth: 方位角 [rad]
            r_R: 无量纲径向位置
            
        Returns:
            俯仰角 [rad]
        """
        rotor_type = self.config.get('rotor_type', 'cycloidal')
        
        if rotor_type == 'cycloidal':
            # 循环翼转子俯仰控制
            pitch_amplitude = self.config.get('pitch_amplitude', 15.0)
            pitch_phase = self.config.get('pitch_phase_offset', 0.0)
            pitch_bias = self.config.get('pitch_bias_angle', 0.0)
            
            pitch = (np.radians(pitch_amplitude) * np.sin(azimuth + np.radians(pitch_phase)) + 
                    np.radians(pitch_bias))
            
        elif rotor_type == 'conventional':
            # 传统旋翼俯仰控制
            collective = self.config.get('collective_pitch', 8.0)
            cyclic_lat = self.config.get('cyclic_pitch_lat', 0.0)
            cyclic_lon = self.config.get('cyclic_pitch_lon', 0.0)
            twist = self.config.get('twist_deg', -8.0)
            
            pitch = (np.radians(collective) + 
                    np.radians(cyclic_lat) * np.cos(azimuth) +
                    np.radians(cyclic_lon) * np.sin(azimuth) +
                    np.radians(twist) * r_R)
        else:
            pitch = 0.0
        
        return pitch
    
    def calculate_blade_loads(self, rho: float, dt: float, t: float, 
                            V_inf: np.ndarray) -> Tuple[np.ndarray, np.ndarray, float]:
        """
        计算桨叶总载荷
        
        Args:
            rho: 空气密度
            dt: 时间步长
            t: 当前时间
            V_inf: 来流速度
            
        Returns:
            总力、总力矩、总环量
        """
        self.total_force.fill(0.0)
        self.total_moment.fill(0.0)
        self.total_circulation = 0.0
        
        # 计算方位角
        omega_rotor = self.config.get('omega_rotor', 100.0)
        blade_phase = self.blade_id * 2 * np.pi / self.config.get('B', 4)
        azimuth = omega_rotor * t + blade_phase
        
        for element in self.elements:
            # 计算相对速度
            element.calculate_relative_velocity(V_inf)
            
            # 计算有效攻角
            r_R = element.radius / self.R_rotor
            pitch_angle = self.calculate_pitch_angle(azimuth, r_R)
            element.calculate_effective_angle_of_attack(pitch_angle)
            
            # 计算叶素载荷
            element_force, element_moment = element.calculate_forces(rho, dt, t)
            
            # 累加到桨叶载荷
            self.total_force += element_force
            self.total_moment += element_moment
            self.total_circulation += element.circulation
        
        return self.total_force, self.total_moment, self.total_circulation
    
    def get_blade_info(self) -> Dict[str, Any]:
        """获取桨叶信息"""
        element_info = [element.get_element_info() for element in self.elements]
        
        return {
            'blade_id': self.blade_id,
            'n_elements': self.N_elements,
            'total_force': self.total_force.tolist(),
            'total_moment': self.total_moment.tolist(),
            'total_circulation': self.total_circulation,
            'elements': element_info
        }
    
    def get_radial_distribution(self) -> Dict[str, np.ndarray]:
        """获取径向分布数据"""
        n_elem = len(self.elements)
        
        radii = np.zeros(n_elem)
        chords = np.zeros(n_elem)
        twists = np.zeros(n_elem)
        alphas = np.zeros(n_elem)
        Cls = np.zeros(n_elem)
        Cds = np.zeros(n_elem)
        circulations = np.zeros(n_elem)
        
        for i, element in enumerate(self.elements):
            radii[i] = element.radius
            chords[i] = element.chord
            twists[i] = element.twist
            alphas[i] = element.alpha_eff
            Cls[i] = element.Cl
            Cds[i] = element.Cd
            circulations[i] = element.circulation
        
        return {
            'radius': radii,
            'chord': chords,
            'twist': twists,
            'alpha_eff': alphas,
            'Cl': Cls,
            'Cd': Cds,
            'circulation': circulations
        }