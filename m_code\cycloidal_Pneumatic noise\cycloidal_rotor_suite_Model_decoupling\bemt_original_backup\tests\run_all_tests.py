#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT深度优化功能测试运行器
========================

运行所有深度优化功能的测试，生成完整的测试报告。

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import time
import traceback
from typing import Dict, List, Any

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入所有测试模块
try:
    from test_gpu_acceleration import run_gpu_acceleration_tests
    from test_adaptive_mesh_refinement import run_adaptive_mesh_tests
    from test_advanced_convergence import run_advanced_convergence_tests
    from test_complete_dynamic_stall import run_complete_dynamic_stall_tests
except ImportError as e:
    print(f"❌ 导入测试模块失败: {e}")
    sys.exit(1)


class BEMTTestRunner:
    """BEMT测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.test_modules = [
            {
                'name': 'GPU加速功能',
                'function': run_gpu_acceleration_tests,
                'description': '验证CUDA/OpenCL GPU加速的正确性和性能提升',
                'priority': 'high'
            },
            {
                'name': '自适应网格细化',
                'function': run_adaptive_mesh_tests,
                'description': '验证基于梯度的自适应网格细化数值精度提升',
                'priority': 'high'
            },
            {
                'name': '高级收敛策略',
                'function': run_advanced_convergence_tests,
                'description': '验证Aitken加速和振荡检测的收敛改进',
                'priority': 'high'
            },
            {
                'name': '完整动态失速',
                'function': run_complete_dynamic_stall_tests,
                'description': '验证12状态Leishman-Beddoes动态失速模型',
                'priority': 'high'
            }
        ]
        
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self, verbose: bool = True) -> Dict[str, Any]:
        """运行所有测试"""
        
        if verbose:
            print("🚀 BEMT深度优化功能测试套件")
            print("=" * 80)
            print(f"   测试模块数: {len(self.test_modules)}")
            print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
        
        self.start_time = time.time()
        
        # 运行每个测试模块
        for i, test_module in enumerate(self.test_modules, 1):
            if verbose:
                print(f"📋 [{i}/{len(self.test_modules)}] {test_module['name']}")
                print(f"    {test_module['description']}")
                print("-" * 60)
            
            try:
                # 运行测试
                start_time = time.time()
                result = test_module['function']()
                execution_time = time.time() - start_time
                
                # 存储结果
                self.results[test_module['name']] = {
                    'success': result['success'],
                    'total': result['total'],
                    'passed': result['passed'],
                    'failed': result['failed'],
                    'execution_time': execution_time,
                    'priority': test_module['priority'],
                    'details': result.get('details', {})
                }
                
                if verbose:
                    status = "✅ 通过" if result['success'] else "❌ 失败"
                    print(f"    结果: {status} ({result['passed']}/{result['total']}) "
                          f"- {execution_time:.2f}s")
                    print()
                
            except Exception as e:
                # 处理测试异常
                execution_time = time.time() - start_time if 'start_time' in locals() else 0
                
                self.results[test_module['name']] = {
                    'success': False,
                    'total': 0,
                    'passed': 0,
                    'failed': 1,
                    'execution_time': execution_time,
                    'priority': test_module['priority'],
                    'error': str(e),
                    'traceback': traceback.format_exc()
                }
                
                if verbose:
                    print(f"    结果: ❌ 异常 - {e}")
                    print()
        
        self.end_time = time.time()
        
        # 生成总结报告
        if verbose:
            self._print_summary_report()
        
        return self._generate_test_report()
    
    def _print_summary_report(self):
        """打印总结报告"""
        
        print("📊 测试总结报告")
        print("=" * 80)
        
        # 统计总体结果
        total_modules = len(self.results)
        passed_modules = sum(1 for r in self.results.values() if r['success'])
        failed_modules = total_modules - passed_modules
        
        total_tests = sum(r['total'] for r in self.results.values())
        passed_tests = sum(r['passed'] for r in self.results.values())
        failed_tests = sum(r['failed'] for r in self.results.values())
        
        total_time = self.end_time - self.start_time
        
        print(f"🎯 总体结果:")
        print(f"   测试模块: {passed_modules}/{total_modules} 通过 ({passed_modules/total_modules*100:.1f}%)")
        print(f"   测试用例: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
        print(f"   执行时间: {total_time:.2f}s")
        print()
        
        # 详细模块结果
        print("📋 模块详细结果:")
        for module_name, result in self.results.items():
            status = "✅" if result['success'] else "❌"
            priority = result['priority'].upper()
            
            print(f"   {status} {module_name} [{priority}]")
            print(f"      测试: {result['passed']}/{result['total']} 通过")
            print(f"      时间: {result['execution_time']:.2f}s")
            
            if not result['success']:
                if 'error' in result:
                    print(f"      错误: {result['error']}")
                elif result['failed'] > 0:
                    print(f"      失败: {result['failed']} 个测试用例")
            print()
        
        # 性能统计
        print("⚡ 性能统计:")
        execution_times = [r['execution_time'] for r in self.results.values()]
        print(f"   最快模块: {min(execution_times):.2f}s")
        print(f"   最慢模块: {max(execution_times):.2f}s")
        print(f"   平均时间: {sum(execution_times)/len(execution_times):.2f}s")
        print()
        
        # 最终判断
        overall_success = passed_modules == total_modules
        
        if overall_success:
            print("🎉 所有深度优化功能测试通过！")
            print("   BEMT模块已达到与原始cycloidal_rotor_suite完全相同的功能水平")
        else:
            print("⚠️  部分测试失败，需要进一步优化")
            print(f"   失败模块: {failed_modules}/{total_modules}")
            
            # 列出失败的高优先级模块
            failed_high_priority = [
                name for name, result in self.results.items()
                if not result['success'] and result['priority'] == 'high'
            ]
            
            if failed_high_priority:
                print(f"   高优先级失败: {', '.join(failed_high_priority)}")
        
        print()
        print("=" * 80)
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        
        # 统计总体结果
        total_modules = len(self.results)
        passed_modules = sum(1 for r in self.results.values() if r['success'])
        
        total_tests = sum(r['total'] for r in self.results.values())
        passed_tests = sum(r['passed'] for r in self.results.values())
        
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # 功能完整性评估
        high_priority_modules = [r for r in self.results.values() if r['priority'] == 'high']
        high_priority_passed = sum(1 for r in high_priority_modules if r['success'])
        
        functionality_completeness = (high_priority_passed / len(high_priority_modules) * 100) if high_priority_modules else 0
        
        # 生成报告
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'execution_time': total_time,
            'overall_success': passed_modules == total_modules,
            'summary': {
                'total_modules': total_modules,
                'passed_modules': passed_modules,
                'failed_modules': total_modules - passed_modules,
                'module_success_rate': passed_modules / total_modules * 100,
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'test_success_rate': passed_tests / total_tests * 100 if total_tests > 0 else 0,
                'functionality_completeness': functionality_completeness
            },
            'module_results': self.results,
            'performance_stats': {
                'fastest_module': min(r['execution_time'] for r in self.results.values()),
                'slowest_module': max(r['execution_time'] for r in self.results.values()),
                'average_time': sum(r['execution_time'] for r in self.results.values()) / len(self.results)
            },
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        
        recommendations = []
        
        # 检查失败的模块
        failed_modules = [name for name, result in self.results.items() if not result['success']]
        
        if not failed_modules:
            recommendations.append("✅ 所有功能测试通过，BEMT模块功能完整性达到100%")
            recommendations.append("🚀 建议进行生产环境部署前的最终验证")
        else:
            recommendations.append(f"⚠️  需要修复 {len(failed_modules)} 个失败模块:")
            for module in failed_modules:
                recommendations.append(f"   - {module}")
        
        # 性能建议
        slow_modules = [
            name for name, result in self.results.items()
            if result['execution_time'] > 30.0  # 超过30秒的模块
        ]
        
        if slow_modules:
            recommendations.append("⚡ 性能优化建议:")
            for module in slow_modules:
                recommendations.append(f"   - 优化 {module} 的执行效率")
        
        return recommendations
    
    def save_report(self, filename: str = None):
        """保存测试报告到文件"""
        
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"bemt_test_report_{timestamp}.txt"
        
        report = self._generate_test_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("BEMT深度优化功能测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"测试时间: {report['timestamp']}\n")
            f.write(f"执行时间: {report['execution_time']:.2f}s\n")
            f.write(f"总体结果: {'通过' if report['overall_success'] else '失败'}\n\n")
            
            # 总结统计
            summary = report['summary']
            f.write("总结统计:\n")
            f.write(f"  模块通过率: {summary['module_success_rate']:.1f}% ({summary['passed_modules']}/{summary['total_modules']})\n")
            f.write(f"  测试通过率: {summary['test_success_rate']:.1f}% ({summary['passed_tests']}/{summary['total_tests']})\n")
            f.write(f"  功能完整性: {summary['functionality_completeness']:.1f}%\n\n")
            
            # 模块详情
            f.write("模块详细结果:\n")
            for module_name, result in report['module_results'].items():
                status = "通过" if result['success'] else "失败"
                f.write(f"  {module_name}: {status} ({result['passed']}/{result['total']}) - {result['execution_time']:.2f}s\n")
            
            f.write("\n")
            
            # 建议
            f.write("改进建议:\n")
            for rec in report['recommendations']:
                f.write(f"  {rec}\n")
        
        print(f"📄 测试报告已保存到: {filename}")


def main():
    """主函数"""
    
    # 创建测试运行器
    runner = BEMTTestRunner()
    
    # 运行所有测试
    report = runner.run_all_tests(verbose=True)
    
    # 保存报告
    runner.save_report()
    
    # 返回退出码
    if report['overall_success']:
        print("\n🎯 所有测试通过，退出码: 0")
        sys.exit(0)
    else:
        print(f"\n⚠️  {report['summary']['failed_modules']} 个模块失败，退出码: 1")
        sys.exit(1)


if __name__ == "__main__":
    main()
