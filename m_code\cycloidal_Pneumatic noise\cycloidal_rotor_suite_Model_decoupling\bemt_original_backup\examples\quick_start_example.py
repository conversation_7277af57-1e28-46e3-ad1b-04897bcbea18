#!/usr/bin/env python3
"""
BEMT中保真度模型 - 快速开始示例
==============================

这个示例展示了如何使用修复后的BEMT中保真度求解器进行基本的转子性能计算。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import numpy as np

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def main():
    """快速开始示例"""
    print("=" * 60)
    print("🚁 BEMT中保真度模型 - 快速开始示例")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.solver_factory import SolverFactory
        from utils.config import ConfigManager
        from geometry.rotor import RotorGeometry
        
        print("✅ 模块导入成功")
        
        # 配置转子参数
        config = {
            # 基本几何参数
            'R_rotor': 0.5,          # 转子半径 (m)
            'B': 4,                  # 桨叶数
            'c': 0.08,               # 弦长 (m)
            'omega_rotor': 150.0,    # 角速度 (rad/s)
            'collective_pitch': 8.0,  # 总距角 (度)
            
            # 环境参数
            'rho': 1.225,            # 空气密度 (kg/m³)
            'V_climb': 0.0,          # 爬升速度 (m/s)
            
            # 数值参数
            'n_radial_stations': 20,  # 径向离散点数
            'convergence_tolerance': 1e-6,  # 收敛容差
            'max_iterations': 100,    # 最大迭代次数
            
            # 物理模型开关
            'enable_tip_loss': True,      # 叶尖损失
            'enable_hub_loss': True,      # 叶根损失
            'enable_dynamic_stall': True, # 动态失速
            'enable_viscous_effects': True, # 粘性效应
        }
        
        print(f"📋 转子配置:")
        print(f"   半径: {config['R_rotor']} m")
        print(f"   桨叶数: {config['B']}")
        print(f"   转速: {config['omega_rotor']} rad/s")
        print(f"   总距: {config['collective_pitch']}°")
        
        # 创建配置管理器
        config_mgr = ConfigManager(config)
        print("✅ 配置管理器创建成功")
        
        # 创建转子几何（简化输出）
        import io
        import contextlib
        
        # 临时重定向输出以减少冗余信息
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            geometry = RotorGeometry(config)
        print("✅ 转子几何创建成功")
        
        # 使用工厂创建求解器
        solver = SolverFactory.create_solver(config_mgr)
        print("✅ BEMT求解器创建成功")
        
        # 执行求解
        print("\n🔄 开始BEMT求解...")
        results = solver.solve()
        
        if results.get('converged', False):
            print("✅ 求解收敛成功!")
            
            # 显示结果
            print(f"\n📊 计算结果:")
            print(f"   推力: {results['thrust']:.2f} N")
            print(f"   功率: {results['power']:.2f} W")
            print(f"   扭矩: {results['torque']:.2f} N·m")
            print(f"   推力系数 CT: {results['CT']:.6f}")
            print(f"   功率系数 CP: {results['CP']:.6f}")
            print(f"   品质因数: {results['figure_of_merit']:.3f}")
            print(f"   迭代次数: {results['iterations']}")
            print(f"   残差: {results['residual']:.2e}")
            
            # 计算一些派生参数
            tip_speed = config['omega_rotor'] * config['R_rotor']
            disk_loading = abs(results['thrust']) / (np.pi * config['R_rotor']**2)
            power_loading = abs(results['thrust']) / (abs(results['power']) / 1000) if results['power'] != 0 else 0
            
            print(f"\n📈 派生参数:")
            print(f"   叶尖速度: {tip_speed:.1f} m/s")
            print(f"   桨盘载荷: {disk_loading:.1f} N/m²")
            print(f"   功率载荷: {power_loading:.3f} N/W" if power_loading > 0 else "   功率载荷: N/A")
            
            # 性能评估
            print(f"\n🎯 性能评估:")
            if abs(results['figure_of_merit']) > 0.6:
                print("   ✅ 品质因数良好 (>0.6)")
            elif abs(results['figure_of_merit']) > 0.4:
                print("   ⚠️  品质因数一般 (0.4-0.6)")
            else:
                print("   ❌ 品质因数较低 (<0.4)")
            
            if results['iterations'] < 50:
                print("   ✅ 收敛速度快")
            else:
                print("   ⚠️  收敛速度较慢")
                
        else:
            print("❌ 求解未收敛")
            if 'error' in results:
                print(f"   错误信息: {results['error']}")
        
        print(f"\n🎉 示例运行完成!")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())