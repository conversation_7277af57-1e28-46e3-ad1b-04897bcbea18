"""
气动力学模块
===========

包含BEMT求解器的气动力学相关组件。

模块内容：
- blade_element.py: 叶素和桨叶建模
- dynamic_stall.py: 动态失速模型（Leishman-Beddoes）
- airfoil_database.py: 翼型数据库管理
- inflow_models.py: 入流模型
- wake_models.py: 尾迹模型

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from .blade_element import BladeElement, Blade
from .dynamic_stall import LeishmanBeddoesModel, ONERAModel
from .airfoil_database import AirfoilDatabase
from .inflow_models import InflowModel, UniformInflow, NonUniformInflow
from .wake_models import WakeModel, PrescribedWake, FreeWake

__all__ = [
    'BladeElement',
    'Blade',
    'LeishmanBeddoesModel',
    'ONERAModel',
    'AirfoilDatabase',
    'InflowModel',
    'UniformInflow',
    'NonUniformInflow',
    'WakeModel',
    'PrescribedWake',
    'FreeWake'
]