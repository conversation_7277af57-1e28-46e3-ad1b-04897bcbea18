#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D Blade Geometry - Medium Fidelity
3D桨叶几何建模 - 中保真度

This module implements 3D blade geometry modeling for medium-fidelity BEMT analysis:
- 3D blade shape definition
- Twist and chord distributions
- Airfoil section management
- Geometric parameter interpolation
- Blade deformation modeling

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
import warnings

class BladeGeometry3D:
    """
    3D桨叶几何类
    
    提供完整的3D桨叶几何建模功能：
    - 径向分布定义（弦长、扭转、厚度）
    - 翼型截面管理
    - 几何参数插值
    - 桨叶变形建模
    """
    
    def __init__(self, 
                 radius: float,
                 hub_radius: float = None,
                 n_stations: int = 20,
                 geometry_data: Optional[Dict] = None,
                 **kwargs):
        """
        初始化3D桨叶几何
        
        Parameters:
        -----------
        radius : float
            桨叶半径 [m]
        hub_radius : float, optional
            桂毂半径 [m]
        n_stations : int
            径向站位数量
        geometry_data : dict, optional
            几何数据字典
        **kwargs : dict
            额外参数
        """
        
        self.radius = radius
        self.hub_radius = hub_radius or 0.1 * radius
        self.n_stations = n_stations
        
        # 径向站位
        self.r_stations = np.linspace(self.hub_radius, self.radius, n_stations)
        self.r_R = self.r_stations / self.radius  # 无量纲径向位置
        self.dr_stations = np.diff(np.concatenate([[self.hub_radius], self.r_stations]))
        
        # 初始化几何分布
        self._initialize_geometry_distributions(geometry_data, **kwargs)
        
        # 翼型管理
        self._initialize_airfoil_sections(**kwargs)
        
        # 几何更新标志
        self._geometry_updated = True
        
        print(f"3D桨叶几何初始化完成")
        print(f"  半径: {self.radius:.3f} m")
        print(f"  桂毂半径: {self.hub_radius:.3f} m")
        print(f"  径向站位: {self.n_stations}")
    
    def _initialize_geometry_distributions(self, geometry_data: Optional[Dict], **kwargs):
        """初始化几何分布"""
        
        if geometry_data:
            # 从数据字典加载
            self.chord_stations = self._load_distribution(
                geometry_data.get('chord', None), 'chord', **kwargs
            )
            self.twist_stations = self._load_distribution(
                geometry_data.get('twist', None), 'twist', **kwargs
            )
            self.thickness_stations = self._load_distribution(
                geometry_data.get('thickness', None), 'thickness', **kwargs
            )
        else:
            # 使用默认分布
            self.chord_stations = self._create_default_chord_distribution(**kwargs)
            self.twist_stations = self._create_default_twist_distribution(**kwargs)
            self.thickness_stations = self._create_default_thickness_distribution(**kwargs)
        
        # 计算几何特性
        self._compute_geometric_properties()
    
    def _load_distribution(self, data: Optional[Any], param_name: str, **kwargs) -> np.ndarray:
        """加载参数分布"""
        
        if data is None:
            # 使用默认分布
            if param_name == 'chord':
                return self._create_default_chord_distribution(**kwargs)
            elif param_name == 'twist':
                return self._create_default_twist_distribution(**kwargs)
            elif param_name == 'thickness':
                return self._create_default_thickness_distribution(**kwargs)
        
        elif isinstance(data, (list, np.ndarray)):
            # 数组数据，需要插值到当前站位
            if len(data) == self.n_stations:
                return np.array(data)
            else:
                # 插值到当前站位
                r_data = np.linspace(self.hub_radius, self.radius, len(data))
                return np.interp(self.r_stations, r_data, data)
        
        elif callable(data):
            # 函数形式
            return data(self.r_R)
        
        elif isinstance(data, dict):
            # 参数化分布
            return self._create_parametric_distribution(data, param_name)
        
        else:
            # 常数值
            return np.full(self.n_stations, float(data))
    
    def _create_default_chord_distribution(self, **kwargs) -> np.ndarray:
        """创建默认弦长分布"""
        
        chord_root = kwargs.get('chord_root', 0.15)
        chord_tip = kwargs.get('chord_tip', 0.05)
        taper_ratio = chord_tip / chord_root
        
        # 线性锥度分布
        chord_stations = chord_root * (1 - (1 - taper_ratio) * self.r_R)
        
        return chord_stations
    
    def _create_default_twist_distribution(self, **kwargs) -> np.ndarray:
        """创建默认扭转分布"""
        
        twist_root = kwargs.get('twist_root', 15.0)  # [deg]
        twist_tip = kwargs.get('twist_tip', -5.0)    # [deg]
        twist_type = kwargs.get('twist_type', 'linear')
        
        if twist_type == 'linear':
            # 线性扭转分布
            twist_stations = twist_root + (twist_tip - twist_root) * self.r_R
        elif twist_type == 'optimal':
            # 优化扭转分布（简化）
            twist_stations = twist_root * (1 - 0.8 * self.r_R**1.5)
        else:
            # 常数扭转
            twist_stations = np.full(self.n_stations, twist_root)
        
        return twist_stations
    
    def _create_default_thickness_distribution(self, **kwargs) -> np.ndarray:
        """创建默认厚度分布"""
        
        thickness_root = kwargs.get('thickness_root', 0.15)  # 厚度比
        thickness_tip = kwargs.get('thickness_tip', 0.08)
        
        # 线性厚度分布
        thickness_stations = thickness_root + (thickness_tip - thickness_root) * self.r_R
        
        return thickness_stations
    
    def _create_parametric_distribution(self, params: Dict, param_name: str) -> np.ndarray:
        """创建参数化分布"""
        
        if param_name == 'chord':
            return self._parametric_chord_distribution(params)
        elif param_name == 'twist':
            return self._parametric_twist_distribution(params)
        elif param_name == 'thickness':
            return self._parametric_thickness_distribution(params)
        else:
            return np.ones(self.n_stations)
    
    def _parametric_chord_distribution(self, params: Dict) -> np.ndarray:
        """参数化弦长分布"""
        
        distribution_type = params.get('type', 'linear')
        
        if distribution_type == 'linear':
            c_root = params.get('root', 0.15)
            c_tip = params.get('tip', 0.05)
            return c_root + (c_tip - c_root) * self.r_R
        
        elif distribution_type == 'elliptical':
            c_max = params.get('max', 0.12)
            return c_max * np.sqrt(1 - self.r_R**2)
        
        elif distribution_type == 'polynomial':
            coeffs = params.get('coefficients', [0.15, -0.1, 0.0])
            chord = np.zeros_like(self.r_R)
            for i, coeff in enumerate(coeffs):
                chord += coeff * self.r_R**i
            return np.maximum(chord, 0.01)  # 确保正值
        
        else:
            return np.full(self.n_stations, 0.1)
    
    def _parametric_twist_distribution(self, params: Dict) -> np.ndarray:
        """参数化扭转分布"""
        
        distribution_type = params.get('type', 'linear')
        
        if distribution_type == 'linear':
            t_root = params.get('root', 15.0)
            t_tip = params.get('tip', -5.0)
            return t_root + (t_tip - t_root) * self.r_R
        
        elif distribution_type == 'optimal':
            # 基于最优理论的扭转分布
            t_root = params.get('root', 15.0)
            k = params.get('optimization_factor', 0.8)
            return t_root * (1 - k * self.r_R**1.5)
        
        elif distribution_type == 'polynomial':
            coeffs = params.get('coefficients', [15.0, -20.0, 0.0])
            twist = np.zeros_like(self.r_R)
            for i, coeff in enumerate(coeffs):
                twist += coeff * self.r_R**i
            return twist
        
        else:
            return np.full(self.n_stations, 0.0)
    
    def _parametric_thickness_distribution(self, params: Dict) -> np.ndarray:
        """参数化厚度分布"""
        
        distribution_type = params.get('type', 'linear')
        
        if distribution_type == 'linear':
            t_root = params.get('root', 0.15)
            t_tip = params.get('tip', 0.08)
            return t_root + (t_tip - t_root) * self.r_R
        
        else:
            return np.full(self.n_stations, 0.12)
    
    def _initialize_airfoil_sections(self, **kwargs):
        """初始化翼型截面"""
        
        # 翼型分布
        airfoil_root = kwargs.get('airfoil_root', 'naca0015')
        airfoil_tip = kwargs.get('airfoil_tip', 'naca0012')
        
        # 简化：线性过渡
        self.airfoil_names = []
        for i, r in enumerate(self.r_R):
            if r < 0.3:
                self.airfoil_names.append(airfoil_root)
            elif r > 0.8:
                self.airfoil_names.append(airfoil_tip)
            else:
                # 中间区域使用插值翼型（简化为根部翼型）
                self.airfoil_names.append(airfoil_root)
        
        # 翼型参数
        self.airfoil_params = []
        for name in self.airfoil_names:
            self.airfoil_params.append(self._get_airfoil_parameters(name))
    
    def _get_airfoil_parameters(self, airfoil_name: str) -> Dict[str, float]:
        """获取翼型参数"""
        
        # 简化的翼型参数数据库
        airfoil_db = {
            'naca0012': {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64},
            'naca0015': {'A1': 0.175, 'A2': 0.135, 'b1': 0.75, 'b2': 0.6},
            'naca23012': {'A1': 0.155, 'A2': 0.115, 'b1': 0.85, 'b2': 0.68},
            'default': {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64}
        }
        
        return airfoil_db.get(airfoil_name.lower(), airfoil_db['default'])
    
    def _compute_geometric_properties(self):
        """计算几何特性"""
        
        # 桨叶面积
        self.blade_area = np.trapz(self.chord_stations, self.r_stations)
        
        # 平均弦长
        self.mean_chord = self.blade_area / (self.radius - self.hub_radius)
        
        # 展弦比
        self.aspect_ratio = (self.radius - self.hub_radius) / self.mean_chord
        
        # 锥度比
        self.taper_ratio = self.chord_stations[-1] / self.chord_stations[0]
        
        # 扭转角度范围
        self.twist_range = np.max(self.twist_stations) - np.min(self.twist_stations)
        
        print(f"几何特性计算完成:")
        print(f"  桨叶面积: {self.blade_area:.4f} m²")
        print(f"  平均弦长: {self.mean_chord:.4f} m")
        print(f"  展弦比: {self.aspect_ratio:.2f}")
        print(f"  锥度比: {self.taper_ratio:.3f}")
    
    def get_station_properties(self, station_index: int) -> Dict[str, Any]:
        """获取指定站位的属性"""
        
        if not (0 <= station_index < self.n_stations):
            raise IndexError(f"站位索引超出范围: {station_index}")
        
        return {
            'r': self.r_stations[station_index],
            'r_R': self.r_R[station_index],
            'chord': self.chord_stations[station_index],
            'twist': self.twist_stations[station_index],
            'thickness': self.thickness_stations[station_index],
            'dr': self.dr_stations[station_index],
            'airfoil_name': self.airfoil_names[station_index],
            'airfoil_params': self.airfoil_params[station_index]
        }
    
    def interpolate_property(self, r_target: float, property_name: str) -> float:
        """插值获取指定径向位置的属性"""
        
        if property_name == 'chord':
            return np.interp(r_target, self.r_stations, self.chord_stations)
        elif property_name == 'twist':
            return np.interp(r_target, self.r_stations, self.twist_stations)
        elif property_name == 'thickness':
            return np.interp(r_target, self.r_stations, self.thickness_stations)
        else:
            raise ValueError(f"未知属性: {property_name}")
    
    def update_geometry(self, **kwargs):
        """更新几何参数"""
        
        updated = False
        
        if 'chord_stations' in kwargs:
            self.chord_stations = np.array(kwargs['chord_stations'])
            updated = True
        
        if 'twist_stations' in kwargs:
            self.twist_stations = np.array(kwargs['twist_stations'])
            updated = True
        
        if 'thickness_stations' in kwargs:
            self.thickness_stations = np.array(kwargs['thickness_stations'])
            updated = True
        
        if updated:
            self._compute_geometric_properties()
            self._geometry_updated = True
            print("几何参数已更新")
    
    def get_geometry_summary(self) -> Dict[str, Any]:
        """获取几何摘要"""
        
        return {
            'radius': self.radius,
            'hub_radius': self.hub_radius,
            'n_stations': self.n_stations,
            'blade_area': self.blade_area,
            'mean_chord': self.mean_chord,
            'aspect_ratio': self.aspect_ratio,
            'taper_ratio': self.taper_ratio,
            'twist_range': self.twist_range,
            'chord_range': [np.min(self.chord_stations), np.max(self.chord_stations)],
            'twist_range_values': [np.min(self.twist_stations), np.max(self.twist_stations)],
            'thickness_range': [np.min(self.thickness_stations), np.max(self.thickness_stations)]
        }

# 工厂函数
def create_blade_geometry_3d(radius: float,
                           hub_radius: float = None,
                           n_stations: int = 20,
                           geometry_data: Optional[Dict] = None,
                           **kwargs) -> BladeGeometry3D:
    """创建3D桨叶几何的工厂函数"""
    return BladeGeometry3D(radius, hub_radius, n_stations, geometry_data, **kwargs)
