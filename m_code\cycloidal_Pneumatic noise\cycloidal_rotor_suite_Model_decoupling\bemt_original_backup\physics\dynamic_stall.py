#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified <PERSON><PERSON>man-Beddoes Dynamic Stall Model - Medium Fidelity
简化Leishman-Beddoes动态失速模型 - 中保真度

This module implements a simplified version of the Leishman-Beddoes dynamic stall model
optimized for medium-fidelity BEMT calculations. It captures the essential physics
while maintaining computational efficiency.

Key Features:
- Simplified 4-state variable model (vs. 12-state full model)
- Essential dynamic stall physics (circulation lag, separation point dynamics)
- Optimized for BEMT integration
- Configurable parameters for different airfoils

References:
[1] <PERSON><PERSON><PERSON>, J. G., and Beddoes, T. S. "A Semi-Empirical Model for Dynamic Stall."
    Journal of the American Helicopter Society, Vol. 34, No. 3, 1989, pp. 3-17.
[2] <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. "Principles of Helicopter Aerodynamics." Cambridge University Press, 2006.

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, Optional, Tuple, Any
import warnings

class SimplifiedLeishmanBeddoes:
    """
    简化Leishman-Beddoes动态失速模型
    
    使用4个状态变量来捕获主要的动态失速效应：
    - X1: 环量力滞后状态
    - X2: 冲量力滞后状态  
    - X3: 分离点动态状态
    - X4: 前缘涡状态
    """
    
    def __init__(self, 
                 airfoil_params: Optional[Dict] = None,
                 chord: float = 0.1,
                 **kwargs):
        """
        初始化简化L-B模型
        
        Parameters:
        -----------
        airfoil_params : dict, optional
            翼型参数字典
        chord : float
            弦长 [m]
        **kwargs : dict
            额外参数
        """
        
        self.chord = chord
        
        # 设置模型参数
        self.params = self._get_default_params()
        if airfoil_params:
            self.params.update(airfoil_params)
        
        # 初始化4个状态变量
        self.X = np.zeros(4)  # [X1, X2, X3, X4]
        
        # 历史变量
        self.alpha_prev = 0.0
        self.q_prev = 0.0
        self.separation_point_prev = 1.0
        self.Cn_v_prev = 0.0
        
        # 时间变量
        self.current_time = 0.0
        self.dt_prev = 0.01
        
        print(f"简化L-B动态失速模型初始化完成")
        print(f"翼型参数: A1={self.params['A1']:.3f}, b1={self.params['b1']:.3f}")
    
    def _get_default_params(self) -> Dict[str, float]:
        """获取默认参数（基于NACA0012）"""
        return {
            # 环量力参数（简化）
            'A1': 0.165,      # 环量力时间常数参数
            'b1': 0.8,        # 环量力时间常数参数
            
            # 冲量力参数（简化）
            'A2': 0.125,      # 冲量力时间常数参数
            'b2': 0.64,       # 冲量力时间常数参数
            
            # 分离点参数
            'A3': 0.04,       # 分离点时间常数参数
            'b3': 0.1,        # 分离点时间常数参数
            
            # 前缘涡参数
            'T_vl': 6.0,      # 前缘涡对流时间常数（简化）
            'C_v': 1.2,       # 前缘涡强度系数（简化）
            
            # 翼型特性参数
            'alpha_0': 0.0,           # 零升攻角 [rad]
            'Cn_alpha': 2 * np.pi,    # 升力线斜率 [1/rad]
            'alpha_ss': np.radians(12.0),  # 静态失速攻角 [rad]（简化）
            'Cn_1': 1.2,             # 失速时的法向力系数（简化）
            
            # 简化参数
            'eta': 0.95,      # 效率因子
            'kappa': 0.75,    # 分离点修正因子
        }
    
    def compute_dynamic_coefficients(self, 
                                   alpha: float,
                                   Cl_static: float,
                                   Cd_static: float,
                                   x1: float,
                                   x2: float,
                                   dt: float,
                                   airfoil_params: Dict) -> Tuple[float, float, float, float]:
        """
        计算动态气动系数
        
        Parameters:
        -----------
        alpha : float
            攻角 [rad]
        Cl_static : float
            静态升力系数
        Cd_static : float
            静态阻力系数
        x1, x2 : float
            状态变量（兼容接口）
        dt : float
            时间步长 [s]
        airfoil_params : dict
            翼型参数
            
        Returns:
        --------
        Cl_dynamic : float
            动态升力系数
        Cd_dynamic : float
            动态阻力系数
        x1_new : float
            更新的状态变量1
        x2_new : float
            更新的状态变量2
        """
        
        # 更新时间
        self.current_time += dt
        self.dt_prev = dt
        
        # 计算攻角变化率
        alpha_dot = (alpha - self.alpha_prev) / dt if dt > 0 else 0.0
        
        # 计算无量纲时间步长
        s_step = 2 * 50.0 * dt / self.chord  # 假设相对速度50 m/s
        
        # 更新状态变量
        self._update_state_variables(alpha, alpha_dot, s_step)
        
        # 计算各分量
        Cn_C = self._compute_circulatory_component(alpha)
        Cn_I = self._compute_impulsive_component(alpha, alpha_dot)
        Cn_f = self._compute_separated_flow_component(Cn_C, alpha)
        Cn_v = self._compute_vortex_component(alpha, s_step)
        
        # 合成总法向力系数
        Cn_total = Cn_f + Cn_I + Cn_v
        
        # 转换为升力和阻力系数
        Cl_dynamic = Cn_total * np.cos(alpha) + Cd_static * np.sin(alpha)
        Cd_dynamic = Cn_total * np.sin(alpha) + Cd_static * np.cos(alpha)
        
        # 应用动态修正
        dynamic_factor = self._compute_dynamic_factor(alpha, alpha_dot)
        Cl_dynamic *= dynamic_factor
        Cd_dynamic *= (1.0 + 0.1 * abs(dynamic_factor - 1.0))
        
        # 更新历史变量
        self.alpha_prev = alpha
        
        return Cl_dynamic, Cd_dynamic, self.X[0], self.X[1]
    
    def _update_state_variables(self, alpha: float, alpha_dot: float, s_step: float):
        """更新4个状态变量"""
        
        # X1: 环量力滞后状态
        tau1 = self.params['A1'] * self.params['b1'] / s_step
        self.X[0] = self.X[0] * np.exp(-s_step/tau1) + alpha * (1 - np.exp(-s_step/tau1))
        
        # X2: 冲量力滞后状态
        tau2 = self.params['A2'] * self.params['b2'] / s_step
        self.X[1] = self.X[1] * np.exp(-s_step/tau2) + alpha_dot * (1 - np.exp(-s_step/tau2))
        
        # X3: 分离点动态状态
        tau3 = self.params['A3'] * self.params['b3'] / s_step
        alpha_eff = alpha - self.params['alpha_0']
        separation_target = 1.0 - 0.3 * abs(alpha_eff) / self.params['alpha_ss']
        separation_target = max(0.0, min(1.0, separation_target))
        self.X[2] = self.X[2] * np.exp(-s_step/tau3) + separation_target * (1 - np.exp(-s_step/tau3))
        
        # X4: 前缘涡状态（简化）
        if abs(alpha) > self.params['alpha_ss']:
            vortex_strength = self.params['C_v'] * (abs(alpha) - self.params['alpha_ss'])
            tau_v = self.params['T_vl'] / s_step
            self.X[3] = self.X[3] * np.exp(-s_step/tau_v) + vortex_strength * (1 - np.exp(-s_step/tau_v))
        else:
            self.X[3] *= 0.95  # 衰减
    
    def _compute_circulatory_component(self, alpha: float) -> float:
        """计算环量力分量"""
        alpha_eff = alpha - self.params['alpha_0']
        Cn_alpha = self.params['Cn_alpha']
        
        # 使用状态变量X1的滞后效应
        alpha_lag = self.X[0]
        Cn_C = Cn_alpha * alpha_lag
        
        return Cn_C
    
    def _compute_impulsive_component(self, alpha: float, alpha_dot: float) -> float:
        """计算冲量力分量"""
        # 简化的冲量力计算
        Cn_I = 0.25 * self.params['Cn_alpha'] * self.X[1] * self.chord / 50.0  # 假设速度50 m/s
        
        return Cn_I
    
    def _compute_separated_flow_component(self, Cn_C: float, alpha: float) -> float:
        """计算分离流分量"""
        # 使用分离点状态变量X3
        separation_point = max(0.0, min(1.0, self.X[2]))
        
        # Kirchhoff修正
        if separation_point < 1.0:
            K_factor = (1 + separation_point) / 2.0
            Cn_f = Cn_C * K_factor
        else:
            Cn_f = Cn_C
        
        return Cn_f
    
    def _compute_vortex_component(self, alpha: float, s_step: float) -> float:
        """计算前缘涡分量"""
        # 使用前缘涡状态变量X4
        if abs(alpha) > self.params['alpha_ss']:
            Cn_v = self.X[3] * np.sign(alpha)
        else:
            Cn_v = 0.0
        
        return Cn_v
    
    def _compute_dynamic_factor(self, alpha: float, alpha_dot: float) -> float:
        """计算动态修正因子"""
        # 基于攻角变化率的动态修正
        reduced_frequency = abs(alpha_dot) * self.chord / (2 * 50.0)  # 假设速度50 m/s
        
        if reduced_frequency > 0.01:
            dynamic_factor = 1.0 + 0.2 * reduced_frequency * np.sin(2 * alpha)
        else:
            dynamic_factor = 1.0
        
        # 限制修正范围
        dynamic_factor = max(0.5, min(1.5, dynamic_factor))
        
        return dynamic_factor
    
    def reset_states(self):
        """重置状态变量"""
        self.X = np.zeros(4)
        self.alpha_prev = 0.0
        self.q_prev = 0.0
        self.separation_point_prev = 1.0
        self.Cn_v_prev = 0.0
        self.current_time = 0.0
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            'state_variables': self.X.copy(),
            'separation_point': self.X[2],
            'vortex_strength': self.X[3],
            'current_time': self.current_time,
            'alpha_prev': self.alpha_prev
        }
    
    def update_parameters(self, new_params: Dict[str, float]):
        """更新模型参数"""
        self.params.update(new_params)
        print(f"L-B模型参数已更新: {list(new_params.keys())}")

# 兼容性函数
def create_simplified_dynamic_stall_model(airfoil_params: Optional[Dict] = None,
                                        chord: float = 0.1,
                                        **kwargs) -> SimplifiedLeishmanBeddoes:
    """创建简化动态失速模型的工厂函数"""
    return SimplifiedLeishmanBeddoes(airfoil_params, chord, **kwargs)
