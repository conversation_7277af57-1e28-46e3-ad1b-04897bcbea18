"""
求解器工厂
=========

创建和管理不同类型的BEMT求解器实例。

核心功能：
- 求解器类型管理
- 配置验证和优化
- 求解器性能监控
- 自动求解器选择

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from typing import Dict, Any, Optional, Type, List
import warnings
from abc import ABC, abstractmethod

from .bemt_solver import BEMTSolver
from utils.config import ConfigManager
from utils.error_handling import ValidationError


class SolverInterface(ABC):
    """求解器接口基类"""
    
    @abstractmethod
    def solve_step(self, t: float, dt: float) -> Dict[str, Any]:
        """执行单步求解"""
        pass
    
    @abstractmethod
    def reset_solver(self):
        """重置求解器"""
        pass
    
    @abstractmethod
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        pass


class BEMTSolverWrapper(SolverInterface):
    """BEMT求解器包装器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.solver = BEMTSolver(config)
        self.solver_type = "BEMT_Medium_Fidelity"
    
    def solve_step(self, t: float, dt: float) -> Dict[str, Any]:
        return self.solver.solve_step(t, dt)
    
    def reset_solver(self):
        self.solver.reset_solver()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        return self.solver.get_performance_stats()


class SolverFactory:
    """
    求解器工厂类
    
    负责创建、配置和管理不同类型的BEMT求解器。
    """
    
    # 注册的求解器类型
    _solver_registry: Dict[str, Type[SolverInterface]] = {
        'bemt_medium': BEMTSolverWrapper,
        'bemt': BEMTSolverWrapper,  # 别名
        'default': BEMTSolverWrapper
    }
    
    # 求解器性能特征
    _solver_characteristics = {
        'bemt_medium': {
            'fidelity': 'medium',
            'computational_cost': 'medium',
            'accuracy': 'medium',
            'stability': 'high',
            'recommended_for': ['parameter_studies', 'design_optimization', 'validation']
        }
    }
    
    def __init__(self):
        """初始化求解器工厂"""
        self.created_solvers = []
        self.performance_database = {}
        
        print("求解器工厂初始化完成")
        print(f"  可用求解器: {list(self._solver_registry.keys())}")
    
    @classmethod
    def register_solver(cls, name: str, solver_class: Type[SolverInterface],
                       characteristics: Optional[Dict[str, Any]] = None):
        """
        注册新的求解器类型
        
        Args:
            name: 求解器名称
            solver_class: 求解器类
            characteristics: 求解器特征描述
        """
        cls._solver_registry[name] = solver_class
        if characteristics:
            cls._solver_characteristics[name] = characteristics
        
        print(f"注册求解器: {name}")
    
    def create_solver(self, solver_type: str, config: Dict[str, Any]) -> SolverInterface:
        """
        创建求解器实例
        
        Args:
            solver_type: 求解器类型
            config: 配置参数
            
        Returns:
            求解器实例
        """
        if solver_type not in self._solver_registry:
            available_types = list(self._solver_registry.keys())
            raise ValueError(f"不支持的求解器类型: {solver_type}. "
                           f"可用类型: {available_types}")
        
        # 验证和优化配置
        validated_config = self._validate_and_optimize_config(solver_type, config)
        
        # 创建求解器
        solver_class = self._solver_registry[solver_type]
        solver = solver_class(validated_config)
        
        # 记录创建的求解器
        self.created_solvers.append({
            'solver': solver,
            'type': solver_type,
            'config': validated_config,
            'created_at': self._get_current_time()
        })
        
        print(f"创建求解器: {solver_type}")
        return solver
    
    def _validate_and_optimize_config(self, solver_type: str, 
                                    config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和优化配置参数
        
        Args:
            solver_type: 求解器类型
            config: 原始配置
            
        Returns:
            验证和优化后的配置
        """
        config_manager = ConfigManager(config)
        
        # 基本参数验证
        self._validate_basic_parameters(config_manager)
        
        # 求解器特定的配置优化
        if solver_type in ['bemt_medium', 'bemt', 'default']:
            config_manager = self._optimize_bemt_config(config_manager)
        
        return config_manager.to_dict()
    
    def _validate_basic_parameters(self, config: ConfigManager):
        """验证基本参数"""
        required_params = {
            'R_rotor': (float, lambda x: x > 0, "转子半径必须大于0"),
            'B': (int, lambda x: x >= 2, "桨叶数必须至少为2"),
            'c': (float, lambda x: x > 0, "弦长必须大于0"),
            'omega_rotor': (float, lambda x: x > 0, "角速度必须大于0"),
            'rho': (float, lambda x: x > 0, "空气密度必须大于0")
        }
        
        for param, (param_type, validator, message) in required_params.items():
            if not config.has(param):
                raise ValidationError(f"缺少必要参数: {param}")
            
            value = config.get(param)
            if not isinstance(value, param_type):
                raise ValidationError(f"参数 {param} 类型错误，期望 {param_type.__name__}")
            
            if not validator(value):
                raise ValidationError(f"参数 {param} 验证失败: {message}")
    
    def _optimize_bemt_config(self, config: ConfigManager) -> ConfigManager:
        """优化BEMT配置"""
        # 自动设置叶素数量
        if not config.has('bemt_n_elements'):
            R_rotor = config.get('R_rotor')
            # 基于转子半径自动设置叶素数
            if R_rotor < 0.5:
                n_elements = 15
            elif R_rotor < 1.0:
                n_elements = 20
            else:
                n_elements = 25
            
            config.set('bemt_n_elements', n_elements)
            print(f"自动设置叶素数量: {n_elements}")
        
        # 自动设置收敛参数
        if not config.has('bemt_tolerance'):
            # 基于应用场景设置容差
            application = config.get('application_type', 'general')
            if application == 'research':
                tolerance = 1e-5
            elif application == 'design':
                tolerance = 1e-4
            else:
                tolerance = 1e-3
            
            config.set('bemt_tolerance', tolerance)
            print(f"自动设置收敛容差: {tolerance:.2e}")
        
        # 自动设置物理模型
        if not config.has('enable_tip_loss'):
            config.set('enable_tip_loss', True)
        
        if not config.has('enable_hub_loss'):
            config.set('enable_hub_loss', True)
        
        # 基于转子类型优化参数
        rotor_type = config.get('rotor_type', 'cycloidal')
        if rotor_type == 'cycloidal':
            # 循环翼转子特定优化
            if not config.has('enable_dynamic_stall'):
                config.set('enable_dynamic_stall', True)
                print("循环翼转子：启用动态失速模型")
        
        return config
    
    def get_solver_recommendations(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        根据需求推荐求解器
        
        Args:
            requirements: 需求描述
            
        Returns:
            推荐的求解器列表
        """
        recommendations = []
        
        # 分析需求
        fidelity_requirement = requirements.get('fidelity', 'medium')
        computational_budget = requirements.get('computational_budget', 'medium')
        accuracy_requirement = requirements.get('accuracy', 'medium')
        application_type = requirements.get('application_type', 'general')
        
        # 评估每个求解器
        for solver_name, characteristics in self._solver_characteristics.items():
            score = self._calculate_solver_score(
                characteristics, fidelity_requirement, 
                computational_budget, accuracy_requirement, application_type
            )
            
            recommendations.append({
                'solver_type': solver_name,
                'score': score,
                'characteristics': characteristics,
                'reasons': self._generate_recommendation_reasons(
                    characteristics, requirements
                )
            })
        
        # 按分数排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return recommendations
    
    def _calculate_solver_score(self, characteristics: Dict[str, Any],
                              fidelity_req: str, budget_req: str,
                              accuracy_req: str, app_type: str) -> float:
        """计算求解器匹配分数"""
        score = 0.0
        
        # 保真度匹配
        fidelity_map = {'low': 1, 'medium': 2, 'high': 3}
        char_fidelity = fidelity_map.get(characteristics.get('fidelity', 'medium'), 2)
        req_fidelity = fidelity_map.get(fidelity_req, 2)
        
        if char_fidelity == req_fidelity:
            score += 30
        else:
            score -= abs(char_fidelity - req_fidelity) * 10
        
        # 计算成本匹配
        cost_map = {'low': 1, 'medium': 2, 'high': 3}
        char_cost = cost_map.get(characteristics.get('computational_cost', 'medium'), 2)
        req_cost = cost_map.get(budget_req, 2)
        
        if char_cost <= req_cost:
            score += 20
        else:
            score -= (char_cost - req_cost) * 15
        
        # 精度匹配
        accuracy_map = {'low': 1, 'medium': 2, 'high': 3}
        char_accuracy = accuracy_map.get(characteristics.get('accuracy', 'medium'), 2)
        req_accuracy = accuracy_map.get(accuracy_req, 2)
        
        if char_accuracy >= req_accuracy:
            score += 25
        else:
            score -= (req_accuracy - char_accuracy) * 10
        
        # 应用类型匹配
        recommended_for = characteristics.get('recommended_for', [])
        if app_type in recommended_for:
            score += 15
        
        return max(0, score)
    
    def _generate_recommendation_reasons(self, characteristics: Dict[str, Any],
                                       requirements: Dict[str, Any]) -> List[str]:
        """生成推荐理由"""
        reasons = []
        
        fidelity = characteristics.get('fidelity', 'medium')
        cost = characteristics.get('computational_cost', 'medium')
        accuracy = characteristics.get('accuracy', 'medium')
        
        reasons.append(f"保真度: {fidelity}")
        reasons.append(f"计算成本: {cost}")
        reasons.append(f"精度: {accuracy}")
        
        recommended_for = characteristics.get('recommended_for', [])
        if recommended_for:
            reasons.append(f"适用于: {', '.join(recommended_for)}")
        
        return reasons
    
    def get_solver_comparison(self) -> Dict[str, Any]:
        """获取求解器对比信息"""
        comparison = {}
        
        for solver_name, characteristics in self._solver_characteristics.items():
            comparison[solver_name] = {
                'characteristics': characteristics,
                'performance_data': self.performance_database.get(solver_name, {})
            }
        
        return comparison
    
    def benchmark_solver(self, solver_type: str, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        对求解器进行基准测试
        
        Args:
            solver_type: 求解器类型
            test_cases: 测试用例列表
            
        Returns:
            基准测试结果
        """
        if solver_type not in self._solver_registry:
            raise ValueError(f"未知求解器类型: {solver_type}")
        
        results = {
            'solver_type': solver_type,
            'test_results': [],
            'summary': {}
        }
        
        total_time = 0.0
        total_accuracy = 0.0
        success_count = 0
        
        for i, test_case in enumerate(test_cases):
            print(f"运行测试用例 {i+1}/{len(test_cases)}")
            
            try:
                # 创建求解器
                solver = self.create_solver(solver_type, test_case['config'])
                
                # 运行测试
                start_time = self._get_current_time()
                test_result = self._run_test_case(solver, test_case)
                end_time = self._get_current_time()
                
                # 记录结果
                test_result.update({
                    'execution_time': end_time - start_time,
                    'success': True
                })
                
                total_time += test_result['execution_time']
                if 'accuracy' in test_result:
                    total_accuracy += test_result['accuracy']
                success_count += 1
                
            except Exception as e:
                test_result = {
                    'success': False,
                    'error': str(e),
                    'execution_time': 0.0
                }
                warnings.warn(f"测试用例 {i+1} 失败: {e}")
            
            results['test_results'].append(test_result)
        
        # 计算汇总统计
        if success_count > 0:
            results['summary'] = {
                'success_rate': success_count / len(test_cases),
                'average_time': total_time / success_count,
                'average_accuracy': total_accuracy / success_count if total_accuracy > 0 else 0.0,
                'total_tests': len(test_cases),
                'successful_tests': success_count
            }
        
        # 更新性能数据库
        self.performance_database[solver_type] = results['summary']
        
        return results
    
    def _run_test_case(self, solver: SolverInterface, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试用例"""
        # 简化的测试实现
        t_end = test_case.get('t_end', 1.0)
        dt = test_case.get('dt', 0.01)
        
        t = 0.0
        results = []
        
        while t < t_end:
            result = solver.solve_step(t, dt)
            results.append(result)
            t += dt
        
        # 计算测试指标
        final_result = results[-1] if results else {}
        
        return {
            'final_thrust': final_result.get('performance', {}).get('thrust', 0.0),
            'final_power': final_result.get('performance', {}).get('power', 0.0),
            'convergence_rate': final_result.get('convergence_info', {}).get('residual', 1.0),
            'steps_completed': len(results)
        }
    
    def _get_current_time(self) -> float:
        """获取当前时间（简化实现）"""
        import time
        return time.time()
    
    def get_factory_statistics(self) -> Dict[str, Any]:
        """获取工厂统计信息"""
        stats = {
            'registered_solvers': len(self._solver_registry),
            'created_solvers': len(self.created_solvers),
            'solver_types': list(self._solver_registry.keys()),
            'performance_data_available': len(self.performance_database)
        }
        
        # 统计创建的求解器类型
        type_counts = {}
        for solver_info in self.created_solvers:
            solver_type = solver_info['type']
            type_counts[solver_type] = type_counts.get(solver_type, 0) + 1
        
        stats['solver_type_usage'] = type_counts
        
        return stats
    
    def cleanup_solvers(self):
        """清理求解器资源"""
        for solver_info in self.created_solvers:
            solver = solver_info['solver']
            if hasattr(solver, 'cleanup'):
                solver.cleanup()
        
        self.created_solvers.clear()
        print("求解器资源清理完成")

class SolverFactory:
    """求解器工厂类"""
    
    _solver_registry = {
        'medium_fidelity': BEMTSolver,
        'bemt': BEMTSolver,
        'default': BEMTSolver
    }
    
    @classmethod
    def create_solver(cls, solver_type, config):
        """
        创建求解器实例
        
        Args:
            solver_type: 求解器类型
            config: 配置对象或字典
            
        Returns:
            求解器实例
        """
        if isinstance(config, ConfigManager):
            config_dict = config.to_dict()
        else:
            config_dict = config
        
        if solver_type not in cls._solver_registry:
            solver_type = 'default'
        
        solver_class = cls._solver_registry[solver_type]
        return solver_class(config_dict)
    
    @classmethod
    def register_solver(cls, solver_type, solver_class):
        """注册新的求解器类型"""
        cls._solver_registry[solver_type] = solver_class
    
    @classmethod
    def get_available_solvers(cls):
        """获取可用的求解器类型"""
        return list(cls._solver_registry.keys())