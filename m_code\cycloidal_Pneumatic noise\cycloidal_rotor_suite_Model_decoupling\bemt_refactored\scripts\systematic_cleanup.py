#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性清理脚本
=============

基于分析结果对原始目录进行系统性清理和整理。

作者: Augment Agent
日期: 2025-07-28
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json
from datetime import datetime


class SystematicCleaner:
    """系统性清理器"""
    
    def __init__(self, original_path: str, refactored_path: str):
        """初始化清理器"""
        self.original_path = Path(original_path)
        self.refactored_path = Path(refactored_path)
        self.backup_path = self.original_path.parent / "bemt_original_backup"
        
        # 清理统计
        self.cleanup_stats = {
            'files_deleted': 0,
            'files_migrated': 0,
            'files_merged': 0,
            'files_preserved': 0,
            'directories_removed': 0,
            'errors': []
        }
        
        # 重要文件映射
        self.important_files = {
            'tests/final_integration_test.py': 'tests/validation/test_final_integration.py',
            'validation/complete_feature_parity_test.py': 'tests/validation/test_feature_parity.py',
            'simple_bemt.py': 'bemt/solver.py',  # 已存在，需要合并检查
        }
    
    def execute_cleanup(self, dry_run: bool = True) -> Dict[str, Any]:
        """执行清理操作"""
        
        print("🧹 开始系统性清理")
        print("=" * 60)
        print(f"模式: {'🔍 预览模式 (不实际执行)' if dry_run else '⚡ 执行模式'}")
        print(f"原始目录: {self.original_path}")
        print(f"重构目录: {self.refactored_path}")
        print(f"备份目录: {self.backup_path}")
        print()
        
        if not dry_run:
            # 创建备份
            self._create_backup()
        
        # 执行清理步骤
        self._step1_delete_cache_and_temp_files(dry_run)
        self._step2_delete_backup_files(dry_run)
        self._step3_migrate_important_files(dry_run)
        self._step4_merge_overlapping_files(dry_run)
        self._step5_remove_empty_directories(dry_run)
        self._step6_preserve_documentation(dry_run)
        
        # 生成清理报告
        return self._generate_cleanup_report()
    
    def _create_backup(self):
        """创建备份"""
        print("💾 创建备份...")
        
        if self.backup_path.exists():
            shutil.rmtree(self.backup_path)
        
        shutil.copytree(self.original_path, self.backup_path)
        print(f"   备份已创建: {self.backup_path}")
    
    def _step1_delete_cache_and_temp_files(self, dry_run: bool):
        """步骤1: 删除缓存和临时文件"""
        print("🗑️  步骤1: 删除缓存和临时文件")
        
        patterns_to_delete = [
            '**/__pycache__',
            '**/*.pyc',
            '**/*.pyo',
            '**/*_backup.py',
            '**/*.bak',
            '**/*.tmp'
        ]
        
        deleted_count = 0
        for pattern in patterns_to_delete:
            for file_path in self.original_path.glob(pattern):
                try:
                    if file_path.is_file():
                        print(f"   删除文件: {file_path.relative_to(self.original_path)}")
                        if not dry_run:
                            file_path.unlink()
                        deleted_count += 1
                    elif file_path.is_dir():
                        print(f"   删除目录: {file_path.relative_to(self.original_path)}")
                        if not dry_run:
                            shutil.rmtree(file_path)
                        deleted_count += 1
                except Exception as e:
                    self.cleanup_stats['errors'].append(f"删除失败 {file_path}: {e}")
        
        self.cleanup_stats['files_deleted'] += deleted_count
        print(f"   删除了 {deleted_count} 个缓存/临时文件")
    
    def _step2_delete_backup_files(self, dry_run: bool):
        """步骤2: 删除备份文件"""
        print("🗑️  步骤2: 删除备份文件")
        
        backup_files = [
            'aerodynamics/blade_element_backup.py',
            'physics/corrections_backup.py'
        ]
        
        deleted_count = 0
        for backup_file in backup_files:
            file_path = self.original_path / backup_file
            if file_path.exists():
                print(f"   删除备份: {backup_file}")
                if not dry_run:
                    file_path.unlink()
                deleted_count += 1
        
        self.cleanup_stats['files_deleted'] += deleted_count
        print(f"   删除了 {deleted_count} 个备份文件")
    
    def _step3_migrate_important_files(self, dry_run: bool):
        """步骤3: 迁移重要文件"""
        print("📦 步骤3: 迁移重要文件")
        
        # 检查final_integration_test.py
        final_test_path = self.original_path / "tests/final_integration_test.py"
        if final_test_path.exists():
            target_path = self.refactored_path / "tests/validation/test_final_integration.py"
            print(f"   迁移重要测试: {final_test_path.name}")
            
            if not dry_run:
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(final_test_path, target_path)
            
            self.cleanup_stats['files_migrated'] += 1
        
        # 检查其他重要文件
        important_files_to_check = [
            'validation/complete_feature_parity_test.py',
            'validation/validation_suite.py',
            'data/airfoils',  # 整个目录
        ]
        
        migrated_count = 0
        for important_file in important_files_to_check:
            source_path = self.original_path / important_file
            if source_path.exists():
                # 确定目标路径
                if 'validation' in important_file:
                    target_path = self.refactored_path / "tests/validation" / source_path.name
                elif 'data' in important_file:
                    target_path = self.refactored_path / "data" / source_path.name
                else:
                    continue
                
                print(f"   迁移: {important_file}")
                
                if not dry_run:
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    if source_path.is_dir():
                        if target_path.exists():
                            shutil.rmtree(target_path)
                        shutil.copytree(source_path, target_path)
                    else:
                        shutil.copy2(source_path, target_path)
                
                migrated_count += 1
        
        self.cleanup_stats['files_migrated'] += migrated_count
        print(f"   迁移了 {migrated_count} 个重要文件")
    
    def _step4_merge_overlapping_files(self, dry_run: bool):
        """步骤4: 合并重叠文件"""
        print("🔀 步骤4: 检查重叠文件")
        
        # 检查需要合并的文件
        overlapping_files = [
            ('simple_bemt.py', 'bemt/solver.py'),
            ('tests/test_data_config.py', 'tests/test_data_config.py'),
        ]
        
        merge_count = 0
        for original_file, refactored_file in overlapping_files:
            original_path = self.original_path / original_file
            refactored_path = self.refactored_path / refactored_file
            
            if original_path.exists() and refactored_path.exists():
                print(f"   检查重叠: {original_file}")
                
                # 简单的文件大小比较
                original_size = original_path.stat().st_size
                refactored_size = refactored_path.stat().st_size
                
                if original_size != refactored_size:
                    print(f"     文件大小不同: 原始={original_size}, 重构={refactored_size}")
                    print(f"     建议人工检查合并")
                else:
                    print(f"     文件大小相同，可能已同步")
                
                merge_count += 1
        
        self.cleanup_stats['files_merged'] += merge_count
        print(f"   检查了 {merge_count} 个重叠文件")
    
    def _step5_remove_empty_directories(self, dry_run: bool):
        """步骤5: 删除空目录"""
        print("📁 步骤5: 删除空目录")
        
        removed_count = 0
        # 从最深层开始删除空目录
        for root, dirs, files in os.walk(self.original_path, topdown=False):
            root_path = Path(root)
            if root_path != self.original_path:  # 不删除根目录
                try:
                    if not any(root_path.iterdir()):  # 目录为空
                        print(f"   删除空目录: {root_path.relative_to(self.original_path)}")
                        if not dry_run:
                            root_path.rmdir()
                        removed_count += 1
                except OSError:
                    pass  # 目录不为空或其他错误
        
        self.cleanup_stats['directories_removed'] += removed_count
        print(f"   删除了 {removed_count} 个空目录")
    
    def _step6_preserve_documentation(self, dry_run: bool):
        """步骤6: 保留文档"""
        print("📚 步骤6: 整理文档")
        
        # 创建文档归档目录
        doc_archive_path = self.original_path / "docs_archive"
        
        doc_files = [
            'FINAL_ORGANIZATION_COMPLETION_REPORT.md',
            'PROJECT_COMPLETION_SUMMARY.md',
            'FINAL_VALIDATION_REPORT.md',
            'advice1.md'
        ]
        
        preserved_count = 0
        for doc_file in doc_files:
            source_path = self.original_path / doc_file
            if source_path.exists():
                target_path = doc_archive_path / doc_file
                print(f"   归档文档: {doc_file}")
                
                if not dry_run:
                    doc_archive_path.mkdir(exist_ok=True)
                    shutil.move(str(source_path), str(target_path))
                
                preserved_count += 1
        
        self.cleanup_stats['files_preserved'] += preserved_count
        print(f"   归档了 {preserved_count} 个文档文件")
    
    def _generate_cleanup_report(self) -> Dict[str, Any]:
        """生成清理报告"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'cleanup_stats': self.cleanup_stats,
            'summary': {
                'total_operations': sum([
                    self.cleanup_stats['files_deleted'],
                    self.cleanup_stats['files_migrated'],
                    self.cleanup_stats['files_merged'],
                    self.cleanup_stats['files_preserved'],
                    self.cleanup_stats['directories_removed']
                ]),
                'errors_count': len(self.cleanup_stats['errors'])
            }
        }
        
        return report
    
    def print_cleanup_report(self, report: Dict[str, Any]):
        """打印清理报告"""
        
        print("\n📊 清理操作报告")
        print("=" * 60)
        
        stats = report['cleanup_stats']
        print(f"删除文件: {stats['files_deleted']}")
        print(f"迁移文件: {stats['files_migrated']}")
        print(f"合并检查: {stats['files_merged']}")
        print(f"保留文档: {stats['files_preserved']}")
        print(f"删除空目录: {stats['directories_removed']}")
        print(f"总操作数: {report['summary']['total_operations']}")
        
        if stats['errors']:
            print(f"\n❌ 错误 ({len(stats['errors'])}):")
            for error in stats['errors']:
                print(f"   {error}")
        else:
            print(f"\n✅ 无错误")


def main():
    """主函数"""
    
    # 路径设置
    base_path = Path(__file__).parent.parent.parent
    original_path = base_path / "bemt_medium_fidelity_validation"
    refactored_path = base_path / "bemt_refactored"
    
    print("🧹 系统性清理工具")
    print("=" * 60)
    
    # 创建清理器
    cleaner = SystematicCleaner(str(original_path), str(refactored_path))
    
    # 先执行预览
    print("🔍 执行预览模式...")
    report = cleaner.execute_cleanup(dry_run=True)
    cleaner.print_cleanup_report(report)
    
    # 询问是否执行实际清理
    print(f"\n❓ 是否执行实际清理? (y/N): ", end="")
    response = input().strip().lower()
    
    if response == 'y':
        print(f"\n⚡ 执行实际清理...")
        actual_report = cleaner.execute_cleanup(dry_run=False)
        cleaner.print_cleanup_report(actual_report)
        print(f"\n🎉 清理完成！")
    else:
        print(f"\n🚫 取消清理操作")
    
    return report


if __name__ == "__main__":
    report = main()
