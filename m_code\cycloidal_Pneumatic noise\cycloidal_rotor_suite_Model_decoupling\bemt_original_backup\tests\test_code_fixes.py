#!/usr/bin/env python3
"""
代码修复验证测试
验证之前识别的问题是否已经修复
"""

import sys
import os
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestCodeFixes(unittest.TestCase):
    """代码修复验证测试类"""
    
    def setUp(self):
        """测试设置"""
        self.test_config = {
            'R_rotor': 0.4,
            'B': 4,
            'c': 0.07,
            'omega_rotor': 120.0,
            'rho': 1.225,
            'bemt_n_elements': 10,
            'enable_tip_loss': True,
            'enable_hub_loss': True,
            'max_iterations': 100,
            'tolerance': 1e-6
        }
    
    def test_solver_factory_fix(self):
        """测试SolverFactory接口修复"""
        print("\n🔧 测试SolverFactory接口修复...")
        
        try:
            from utils.config import ConfigManager
            from core.solver_factory import SolverFactory
            
            # 创建配置管理器
            config_manager = ConfigManager(self.test_config)
            
            # 创建求解器工厂
            factory = SolverFactory()
            
            # 测试创建求解器 - 修复后应该只需要两个参数
            solver = factory.create_solver('bemt_medium', config_manager.to_dict())
            
            self.assertIsNotNone(solver)
            print("   ✅ SolverFactory接口修复成功")
            
        except Exception as e:
            self.fail(f"SolverFactory接口修复失败: {e}")
    
    def test_blade_class_fix(self):
        """测试Blade类定义修复"""
        print("\n🔧 测试Blade类定义修复...")
        
        try:
            from aerodynamics.blade_element import Blade, BladeElement
            
            # 创建叶片实例
            blade = Blade(blade_id=0, config=self.test_config)
            
            # 验证叶片属性
            self.assertIsNotNone(blade.elements)
            self.assertGreater(len(blade.elements), 0)
            self.assertEqual(blade.blade_id, 0)
            
            # 验证叶素类型
            for element in blade.elements:
                self.assertIsInstance(element, BladeElement)
            
            print(f"   ✅ Blade类修复成功，包含 {len(blade.elements)} 个叶素")
            
        except Exception as e:
            self.fail(f"Blade类定义修复失败: {e}")
    
    def test_physical_corrections_fix(self):
        """测试物理修正接口修复"""
        print("\n🔧 测试物理修正接口修复...")
        
        try:
            from physics.corrections import UnifiedPhysicalCorrections
            
            # 创建物理修正实例
            corrections = UnifiedPhysicalCorrections(self.test_config)
            
            # 测试获取启用的修正
            enabled_corrections = corrections.get_enabled_corrections()
            self.assertIsInstance(enabled_corrections, list)
            
            # 测试应用所有修正
            test_data = {
                'Cl': 0.5,
                'Cd': 0.02,
                'alpha': 0.1,
                'r_R': 0.8
            }
            
            result = corrections.apply_all(test_data)
            self.assertIsInstance(result, dict)
            self.assertIn('Cl', result)
            self.assertIn('Cd', result)
            
            print(f"   ✅ 物理修正修复成功，启用的修正: {enabled_corrections}")
            
        except Exception as e:
            self.fail(f"物理修正接口修复失败: {e}")
    
    def test_config_validation_fix(self):
        """测试配置验证修复"""
        print("\n🔧 测试配置验证修复...")
        
        try:
            from utils.config import ConfigManager
            
            # 测试有效配置
            valid_config = ConfigManager(self.test_config)
            # 验证配置对象创建成功
            self.assertIsNotNone(valid_config)
            
            # 测试配置访问
            config_dict = valid_config.to_dict()
            self.assertIsInstance(config_dict, dict)
            self.assertIn('R_rotor', config_dict)
            
            # 测试无效配置处理
            try:
                invalid_config = ConfigManager({'invalid_param': 'test'})
                # 如果没有抛出异常，说明配置系统有默认值处理
                self.assertIsNotNone(invalid_config)
            except Exception:
                pass  # 抛出异常也是预期的行为
            
            print("   ✅ 配置验证修复成功")
            
        except Exception as e:
            self.fail(f"配置验证修复失败: {e}")
    
    def test_blade_element_kinematics_fix(self):
        """测试BladeElement运动学更新修复"""
        print("\n🔧 测试BladeElement运动学更新修复...")
        
        try:
            from aerodynamics.blade_element import BladeElement
            
            # 创建叶素实例 - 使用正确的参数名
            element = BladeElement(
                element_id=0,
                radius=0.5,
                chord=self.test_config['c'],
                twist=0.1,
                config=self.test_config
            )
            
            # 验证叶素创建成功
            self.assertEqual(element.element_id, 0)
            self.assertEqual(element.radius, 0.5)
            self.assertEqual(element.chord, self.test_config['c'])
            
            # 测试运动学更新方法
            if hasattr(element, 'update_kinematics'):
                element.update_kinematics(
                    azimuth=0.5,
                    collective=0.1,
                    cyclic_lateral=0.0,
                    cyclic_longitudinal=0.0
                )
                print("   ✅ BladeElement运动学更新修复成功")
            else:
                print("   ⚠️  BladeElement.update_kinematics方法仍需实现")
            
        except Exception as e:
            self.fail(f"BladeElement运动学更新修复失败: {e}")
    
    def test_integration_workflow(self):
        """测试整体集成工作流程"""
        print("\n🔧 测试整体集成工作流程...")
        
        try:
            from utils.config import ConfigManager
            from core.solver_factory import SolverFactory
            from aerodynamics.blade_element import Blade
            from physics.corrections import UnifiedPhysicalCorrections
            
            # 1. 创建配置
            config_manager = ConfigManager(self.test_config)
            self.assertIsNotNone(config_manager)
            
            # 2. 创建求解器
            factory = SolverFactory()
            solver = factory.create_solver('bemt_medium', config_manager.to_dict())
            self.assertIsNotNone(solver)
            
            # 3. 创建叶片
            blade = Blade(0, self.test_config)
            self.assertGreater(len(blade.elements), 0)
            
            # 4. 创建物理修正
            corrections = UnifiedPhysicalCorrections(self.test_config)
            enabled = corrections.get_enabled_corrections()
            self.assertIsInstance(enabled, list)
            
            print("   ✅ 整体集成工作流程测试成功")
            
        except Exception as e:
            self.fail(f"整体集成工作流程测试失败: {e}")


class TestFixesRunner:
    """测试运行器"""
    
    def __init__(self):
        self.results = {}
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始验证代码修复")
        print("=" * 60)
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(TestCodeFixes)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)
        
        # 输出总结
        print("\n" + "=" * 60)
        print("📋 修复验证总结")
        print("=" * 60)
        
        total_tests = result.testsRun
        failures = len(result.failures)
        errors = len(result.errors)
        passed = total_tests - failures - errors
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed}")
        print(f"失败: {failures}")
        print(f"错误: {errors}")
        
        if failures == 0 and errors == 0:
            print("🎉 所有修复验证通过！")
            return True
        else:
            print("⚠️  仍有问题需要修复")
            
            if result.failures:
                print("\n失败的测试:")
                for test, traceback in result.failures:
                    print(f"  - {test}: {traceback}")
            
            if result.errors:
                print("\n错误的测试:")
                for test, traceback in result.errors:
                    print(f"  - {test}: {traceback}")
            
            return False


def main():
    """主函数"""
    runner = TestFixesRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n✨ 所有代码修复验证完成！")
    else:
        print("\n🔧 请根据测试结果继续修复代码")
    
    return success


if __name__ == "__main__":
    main()