"""
叶素和桨叶建模
=============

完整复刻原始模块的叶素建模功能，包含几何定义、运动学计算和气动力计算。

核心功能：
- 叶素几何参数化
- 运动学状态更新
- 有效攻角计算
- 气动力系数计算
- 动态失速模型集成
- 粘性修正

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import warnings

from aerodynamics.dynamic_stall import LeishmanBeddoesModel
from aerodynamics.airfoil_database import AirfoilDatabase
from utils.error_handling import validate_input, safe_divide, safe_sqrt


class BladeElement:
    """
    单个叶素类
    
    表示桨叶上的一个离散单元，包含几何、运动学和气动力学属性。
    """
    
    def __init__(self, element_id: int, radius: float, chord: float, 
                 twist: float = 0.0, config: Optional[Dict[str, Any]] = None):
        """
        初始化叶素
        
        Args:
            element_id: 叶素ID
            radius: 径向位置 [m]
            chord: 弦长 [m]
            twist: 扭转角 [rad]
            config: 配置参数
        """
        # 基本属性
        self.element_id = element_id
        self.radius = radius
        self.chord = chord
        self.twist = twist
        self.config = config or {}
        
        # 几何属性
        self.span_width = 0.0  # 展向宽度，由上级设置
        self.area = 0.0  # 叶素面积
        self.airfoil_name = self.config.get('airfoil_name', 'NACA0012')
        
        # 运动学状态
        self.position = np.zeros(3)  # 位置 [x, y, z]
        self.velocity = np.zeros(3)  # 速度 [vx, vy, vz]
        self.angular_velocity = 0.0  # 角速度 [rad/s]
        self.rotation_matrix = np.eye(3)  # 姿态矩阵
        
        # 气动状态
        self.alpha_eff = 0.0  # 有效攻角 [rad]
        self.V_rel = np.zeros(3)  # 相对速度 [m/s]
        self.circulation = 0.0  # 环量 [m²/s]
        
        # 气动力
        self.force = np.zeros(3)  # 力 [N]
        self.moment = np.zeros(3)  # 力矩 [N·m]
        self.lift_force = 0.0  # 升力 [N]
        self.drag_force = 0.0  # 阻力 [N]
        
        # 气动系数
        self.Cl = 0.0  # 升力系数
        self.Cd = 0.0  # 阻力系数
        self.Cm = 0.0  # 力矩系数
        
        # 动态失速模型
        self.lb_model = None
        self.previous_alpha = 0.0
        self.previous_time = 0.0
        self.alpha_history = []
        self.time_history = []
        
        # 初始化动态失速模型
        if self.config.get('enable_dynamic_stall', False):
            self._initialize_dynamic_stall_model()
        
        # 翼型数据库
        self.airfoil_database = None
        if self.config.get('enable_airfoil_database', True):
            try:
                self.airfoil_database = AirfoilDatabase(
                    data_dir=self.config.get('airfoil_data_dir')
                )
            except Exception as e:
                warnings.warn(f"翼型数据库初始化失败: {e}")
        
        print(f"叶素 {element_id} 初始化完成 (r={radius:.3f}m, c={chord:.3f}m)")
    
    def _initialize_dynamic_stall_model(self):
        """初始化动态失速模型"""
        try:
            self.lb_model = LeishmanBeddoesModel(
                airfoil_name=self.airfoil_name,
                config=self.config
            )
        except Exception as e:
            warnings.warn(f"动态失速模型初始化失败: {e}")
            self.lb_model = None
    
    def update_kinematics(self, t: float, blade_angle: float, omega_rotor: float,
                         precone_angle: float = 0.0, pitch_bias_angle: float = 0.0):
        """
        更新运动学状态
        
        Args:
            t: 当前时间 [s]
            blade_angle: 桨叶角度 [rad]
            omega_rotor: 转子角速度 [rad/s]
            precone_angle: 锥角 [rad]
            pitch_bias_angle: 桨距偏置角 [rad]
        """
        # 当前方位角
        theta = omega_rotor * t + blade_angle
        
        # 基础径向位置
        r_hub = np.array([self.radius, 0.0, 0.0])
        
        # 应用锥角变换
        if abs(precone_angle) > 1e-6:
            R_precone = self._rotation_matrix_y(precone_angle)
            r_precone = R_precone @ r_hub
        else:
            R_precone = np.eye(3)
            r_precone = r_hub.copy()
        
        # 应用方位角旋转
        R_azimuth = self._rotation_matrix_z(theta)
        r_azimuth = R_azimuth @ r_precone
        
        # 更新位置
        self.position = r_azimuth
        
        # 计算速度 (ω × r)
        omega_vec = np.array([0.0, 0.0, omega_rotor])
        self.velocity = np.cross(omega_vec, self.position)
        
        # 更新姿态矩阵
        R_pitch = self._rotation_matrix_x(self.twist + pitch_bias_angle)
        self.rotation_matrix = R_azimuth @ R_precone @ R_pitch
        
        self.angular_velocity = omega_rotor
    
    def _rotation_matrix_x(self, angle: float) -> np.ndarray:
        """绕x轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[1, 0, 0], [0, c, -s], [0, s, c]])
    
    def _rotation_matrix_y(self, angle: float) -> np.ndarray:
        """绕y轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, 0, s], [0, 1, 0], [-s, 0, c]])
    
    def _rotation_matrix_z(self, angle: float) -> np.ndarray:
        """绕z轴旋转矩阵"""
        c, s = np.cos(angle), np.sin(angle)
        return np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]])
    
    def calculate_relative_velocity(self, V_inf: np.ndarray) -> np.ndarray:
        """
        计算相对速度
        
        Args:
            V_inf: 来流速度 [vx, vy, vz]
            
        Returns:
            相对速度向量
        """
        self.V_rel = V_inf - self.velocity
        return self.V_rel
    
    def calculate_effective_angle_of_attack(self, blade_pitch_angle: Optional[float] = None) -> float:
        """
        计算有效攻角
        
        Args:
            blade_pitch_angle: 桨叶俯仰角 [rad]
            
        Returns:
            有效攻角 [rad]
        """
        # 计算相对速度分量
        V_tangential = np.sqrt(self.V_rel[0]**2 + self.V_rel[1]**2)
        V_axial = abs(self.V_rel[2])
        
        # 几何攻角
        if V_tangential > 1e-8:
            geometric_alpha = np.arctan2(V_axial, V_tangential)
        else:
            geometric_alpha = 0.0
        
        # 桨叶俯仰角
        if blade_pitch_angle is not None:
            pitch = blade_pitch_angle
        else:
            pitch = 0.0
        
        # 有效攻角 = 几何攻角 + 桨叶俯仰角 + 扭转角
        self.alpha_eff = geometric_alpha + pitch + self.twist
        
        return self.alpha_eff


class Blade:
    """
    完整桨叶模型
    
    包含多个叶素的桨叶，管理叶素的创建、更新和载荷计算。
    """
    
    def __init__(self, blade_id: int, config: Dict[str, Any]):
        """
        初始化桨叶
        
        Args:
            blade_id: 桨叶ID
            config: 配置参数
        """
        self.blade_id = blade_id
        self.config = config
        
        # 几何参数
        self.R_rotor = config.get('R_rotor', 0.3)
        self.c_root = config.get('c', 0.1)
        self.N_elements = config.get('bemt_n_elements', 20)
        self.taper_ratio = config.get('blade_taper', 1.0)
        self.twist_total = np.radians(config.get('blade_twist_deg', 0.0))
        
        # 创建叶素
        self.elements = self._create_blade_elements()
        
        # 总载荷
        self.total_force = np.zeros(3)
        self.total_moment = np.zeros(3)
        self.total_circulation = 0.0
        
        print(f"桨叶 {blade_id} 初始化完成: {self.N_elements} 个叶素")
    
    def _create_blade_elements(self) -> List[BladeElement]:
        """创建叶素"""
        elements = []
        
        for i in range(self.N_elements):
            # 径向位置（余弦分布）
            eta = np.cos(np.pi * (i + 0.5) / self.N_elements)
            r = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta + 1) / 2
            
            # 局部弦长（锥度变化）
            r_norm = (r - 0.2 * self.R_rotor) / (0.8 * self.R_rotor)
            chord = self.c_root * (1.0 - r_norm * (1.0 - self.taper_ratio))
            
            # 局部扭转角
            twist = self.twist_total * r_norm
            
            # 创建叶素
            element = BladeElement(i, r, chord, twist, self.config)
            
            # 设置展向宽度
            if i == 0:
                span_width = r - 0.2 * self.R_rotor
            elif i == self.N_elements - 1:
                span_width = self.R_rotor - r
            else:
                # 相邻叶素中点间距
                eta_prev = np.cos(np.pi * i / self.N_elements)
                eta_next = np.cos(np.pi * (i + 1) / self.N_elements)
                r_prev = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta_prev + 1) / 2
                r_next = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta_next + 1) / 2
                span_width = abs(r_next - r_prev) / 2
            
            element.span_width = span_width
            element.area = chord * span_width
            
            elements.append(element)
        
        return elements
    
    def update_kinematics(self, t: float, blade_angle: float, omega_rotor: float,
                         precone_angle: float = 0.0, pitch_bias_angle: float = 0.0):
        """更新所有叶素的运动学状态"""
        for element in self.elements:
            element.update_kinematics(t, blade_angle, omega_rotor, 
                                    precone_angle, pitch_bias_angle)