"""
尾迹模型
=======

实现不同的尾迹模型，用于计算尾迹几何和诱导效应。

核心功能：
- 预设尾迹模型
- 自由尾迹模型
- 尾迹几何计算
- 诱导速度计算

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from abc import ABC, abstractmethod
import warnings


class WakeModel(ABC):
    """尾迹模型基类"""
    
    @abstractmethod
    def update_wake_geometry(self, blade_positions: np.ndarray, 
                           circulation: np.ndarray, dt: float):
        """
        更新尾迹几何
        
        Args:
            blade_positions: 桨叶位置
            circulation: 环量分布
            dt: 时间步长
        """
        pass
    
    @abstractmethod
    def calculate_induced_velocity(self, target_points: np.ndarray) -> np.ndarray:
        """
        计算诱导速度
        
        Args:
            target_points: 目标点位置
            
        Returns:
            诱导速度向量
        """
        pass
    
    @abstractmethod
    def get_wake_info(self) -> Dict[str, Any]:
        """获取尾迹信息"""
        pass


class PrescribedWake(WakeModel):
    """
    预设尾迹模型
    
    使用预定义的尾迹几何形状。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化预设尾迹模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.R_rotor = config.get('R_rotor', 0.3)
        self.omega_rotor = config.get('omega_rotor', 100.0)
        self.B = config.get('B', 4)  # 桨叶数
        
        # 尾迹参数
        self.wake_age_max = config.get('wake_age_max', 2.0)  # 最大尾迹年龄 [转]
        self.wake_pitch = config.get('wake_pitch', 0.1)  # 尾迹螺距
        self.wake_contraction = config.get('wake_contraction', 0.95)  # 尾迹收缩率
        
        # 尾迹几何存储
        self.wake_filaments = []  # 尾迹丝
        self.circulation_history = []  # 环量历史
        
        # 时间参数
        self.current_time = 0.0
        self.time_step = 0.001
        
        print("预设尾迹模型初始化完成")
        print(f"  最大尾迹年龄: {self.wake_age_max:.1f} 转")
        print(f"  尾迹螺距: {self.wake_pitch:.3f}")
    
    def update_wake_geometry(self, blade_positions: np.ndarray, 
                           circulation: np.ndarray, dt: float):
        """
        更新预设尾迹几何
        
        使用螺旋线模型描述尾迹几何
        """
        self.current_time += dt
        self.time_step = dt
        
        # 计算当前转角
        current_azimuth = self.omega_rotor * self.current_time
        
        # 为每个桨叶创建尾迹点
        for blade_idx in range(self.B):
            blade_phase = blade_idx * 2 * np.pi / self.B
            
            # 创建新的尾迹段
            wake_segment = self._create_wake_segment(
                blade_phase, current_azimuth, circulation[blade_idx::self.B]
            )
            
            # 添加到尾迹丝
            if len(self.wake_filaments) <= blade_idx:
                self.wake_filaments.append([])
            
            self.wake_filaments[blade_idx].append(wake_segment)
        
        # 清理过老的尾迹
        self._cleanup_old_wake()
        
        # 更新环量历史
        self.circulation_history.append(circulation.copy())
        max_history = int(self.wake_age_max * 2 * np.pi / (self.omega_rotor * dt))
        if len(self.circulation_history) > max_history:
            self.circulation_history.pop(0)
    
    def _create_wake_segment(self, blade_phase: float, current_azimuth: float,
                           blade_circulation: np.ndarray) -> Dict[str, Any]:
        """创建尾迹段"""
        n_radial = len(blade_circulation)
        
        # 径向位置
        r_positions = np.linspace(0.2 * self.R_rotor, self.R_rotor, n_radial)
        
        # 尾迹点位置（螺旋线）
        wake_points = []
        wake_circulation = []
        
        for i, (r, gamma) in enumerate(zip(r_positions, blade_circulation)):
            # 螺旋线参数
            theta = current_azimuth + blade_phase
            
            # 尾迹收缩
            r_wake = r * self.wake_contraction
            
            # 位置计算
            x = r_wake * np.cos(theta)
            y = r_wake * np.sin(theta)
            z = -self.wake_pitch * theta / (2 * np.pi)  # 向下螺旋
            
            wake_points.append([x, y, z])
            wake_circulation.append(gamma)
        
        return {
            'points': np.array(wake_points),
            'circulation': np.array(wake_circulation),
            'age': 0.0,
            'azimuth': current_azimuth
        }
    
    def _cleanup_old_wake(self):
        """清理过老的尾迹"""
        max_age_radians = self.wake_age_max * 2 * np.pi
        current_azimuth = self.omega_rotor * self.current_time
        
        for blade_idx in range(len(self.wake_filaments)):
            # 过滤过老的尾迹段
            self.wake_filaments[blade_idx] = [
                segment for segment in self.wake_filaments[blade_idx]
                if (current_azimuth - segment['azimuth']) < max_age_radians
            ]
    
    def calculate_induced_velocity(self, target_points: np.ndarray) -> np.ndarray:
        """
        计算诱导速度
        
        使用Biot-Savart定律计算尾迹诱导速度
        """
        n_targets = target_points.shape[0]
        induced_velocity = np.zeros((n_targets, 3))
        
        # 遍历所有尾迹丝
        for blade_filaments in self.wake_filaments:
            for segment in blade_filaments:
                wake_points = segment['points']
                circulation = segment['circulation']
                
                # 计算每个尾迹段的贡献
                for i in range(len(wake_points) - 1):
                    # 尾迹段端点
                    p1 = wake_points[i]
                    p2 = wake_points[i + 1]
                    gamma = circulation[i]
                    
                    # 计算诱导速度贡献
                    dv = self._biot_savart_segment(target_points, p1, p2, gamma)
                    induced_velocity += dv
        
        return induced_velocity
    
    def _biot_savart_segment(self, target_points: np.ndarray, 
                           p1: np.ndarray, p2: np.ndarray, gamma: float) -> np.ndarray:
        """
        计算直线段的Biot-Savart诱导速度
        
        Args:
            target_points: 目标点 [N x 3]
            p1, p2: 线段端点 [3]
            gamma: 环量强度
            
        Returns:
            诱导速度 [N x 3]
        """
        # 线段向量
        dl = p2 - p1
        dl_mag = np.linalg.norm(dl)
        
        if dl_mag < 1e-10:
            return np.zeros_like(target_points)
        
        dl_unit = dl / dl_mag
        
        # 计算每个目标点的诱导速度
        n_targets = target_points.shape[0]
        dv = np.zeros((n_targets, 3))
        
        for i, target in enumerate(target_points):
            # 从线段中点到目标点的向量
            r_mid = 0.5 * (p1 + p2)
            r_vec = target - r_mid
            r_mag = np.linalg.norm(r_vec)
            
            if r_mag < 1e-6:  # 避免奇点
                continue
            
            # Biot-Savart公式
            cross_product = np.cross(dl, r_vec)
            dv[i] = gamma / (4 * np.pi) * cross_product / (r_mag**3) * dl_mag
        
        return dv
    
    def get_wake_info(self) -> Dict[str, Any]:
        """获取尾迹信息"""
        total_segments = sum(len(filament) for filament in self.wake_filaments)
        
        return {
            'model_type': 'prescribed_wake',
            'total_segments': total_segments,
            'wake_age_max': self.wake_age_max,
            'wake_pitch': self.wake_pitch,
            'wake_contraction': self.wake_contraction,
            'current_time': self.current_time
        }


class FreeWake(WakeModel):
    """
    自由尾迹模型
    
    考虑尾迹的自由变形和相互作用。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化自由尾迹模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.R_rotor = config.get('R_rotor', 0.3)
        self.omega_rotor = config.get('omega_rotor', 100.0)
        self.B = config.get('B', 4)
        
        # 自由尾迹参数
        self.wake_age_max = config.get('wake_age_max', 1.5)
        self.relaxation_factor = config.get('wake_relaxation', 0.3)
        self.core_radius = config.get('vortex_core_radius', 0.01)
        
        # 尾迹点存储
        self.wake_points = []  # 尾迹点位置
        self.wake_circulation = []  # 尾迹环量
        self.wake_velocities = []  # 尾迹点速度
        self.wake_ages = []  # 尾迹年龄
        
        # 迭代参数
        self.max_iterations = config.get('wake_iterations', 5)
        self.convergence_tolerance = config.get('wake_tolerance', 1e-4)
        
        print("自由尾迹模型初始化完成")
        print(f"  松弛因子: {self.relaxation_factor:.2f}")
        print(f"  涡核半径: {self.core_radius:.4f}m")
    
    def update_wake_geometry(self, blade_positions: np.ndarray, 
                           circulation: np.ndarray, dt: float):
        """
        更新自由尾迹几何
        
        使用迭代方法求解尾迹变形
        """
        # 添加新的尾迹点
        self._emit_new_wake_points(blade_positions, circulation)
        
        # 迭代求解尾迹几何
        for iteration in range(self.max_iterations):
            # 计算尾迹点的诱导速度
            induced_velocities = self._calculate_wake_induced_velocities()
            
            # 更新尾迹点位置
            old_positions = [points.copy() for points in self.wake_points]
            self._update_wake_positions(induced_velocities, dt)
            
            # 检查收敛性
            if self._check_wake_convergence(old_positions):
                break
        
        # 更新尾迹年龄并清理
        self._update_wake_ages(dt)
        self._cleanup_old_wake_points()
    
    def _emit_new_wake_points(self, blade_positions: np.ndarray, 
                            circulation: np.ndarray):
        """发射新的尾迹点"""
        n_radial = blade_positions.shape[1] if blade_positions.ndim > 1 else 1
        
        for blade_idx in range(self.B):
            # 桨叶尾缘位置
            if blade_positions.ndim > 1:
                trailing_edge = blade_positions[blade_idx]
            else:
                # 简化的位置计算
                blade_phase = blade_idx * 2 * np.pi / self.B
                azimuth = self.omega_rotor * self.current_time + blade_phase
                trailing_edge = np.array([
                    self.R_rotor * np.cos(azimuth),
                    self.R_rotor * np.sin(azimuth),
                    0.0
                ])
            
            # 添加新尾迹点
            self.wake_points.append(trailing_edge.copy())
            self.wake_circulation.append(circulation[blade_idx] if len(circulation) > blade_idx else 0.0)
            self.wake_velocities.append(np.zeros(3))
            self.wake_ages.append(0.0)
    
    def _calculate_wake_induced_velocities(self) -> List[np.ndarray]:
        """计算尾迹点的诱导速度"""
        n_points = len(self.wake_points)
        induced_velocities = []
        
        for i in range(n_points):
            target_point = self.wake_points[i]
            induced_vel = np.zeros(3)
            
            # 计算其他尾迹点的诱导效应
            for j in range(n_points):
                if i == j:
                    continue
                
                source_point = self.wake_points[j]
                gamma = self.wake_circulation[j]
                
                # 点涡诱导速度
                r_vec = target_point - source_point
                r_mag = np.linalg.norm(r_vec)
                
                if r_mag > self.core_radius:
                    # Rankine涡核模型
                    induced_vel += gamma / (2 * np.pi * r_mag**2) * np.array([
                        -r_vec[1], r_vec[0], 0.0
                    ])
            
            induced_velocities.append(induced_vel)
        
        return induced_velocities
    
    def _update_wake_positions(self, induced_velocities: List[np.ndarray], dt: float):
        """更新尾迹点位置"""
        for i in range(len(self.wake_points)):
            # 总速度 = 诱导速度 + 对流速度
            total_velocity = induced_velocities[i] + self._get_convection_velocity(i)
            
            # 位置更新（带松弛）
            new_position = self.wake_points[i] + total_velocity * dt
            self.wake_points[i] = (self.relaxation_factor * new_position + 
                                 (1 - self.relaxation_factor) * self.wake_points[i])
            
            # 更新速度
            self.wake_velocities[i] = total_velocity
    
    def _get_convection_velocity(self, point_index: int) -> np.ndarray:
        """获取对流速度"""
        # 简化的对流速度（可以包含来流、诱导速度等）
        return np.array([0.0, 0.0, -1.0])  # 简单的向下对流
    
    def _check_wake_convergence(self, old_positions: List[np.ndarray]) -> bool:
        """检查尾迹收敛性"""
        if len(old_positions) != len(self.wake_points):
            return False
        
        max_displacement = 0.0
        for i in range(len(self.wake_points)):
            displacement = np.linalg.norm(self.wake_points[i] - old_positions[i])
            max_displacement = max(max_displacement, displacement)
        
        return max_displacement < self.convergence_tolerance
    
    def _update_wake_ages(self, dt: float):
        """更新尾迹年龄"""
        for i in range(len(self.wake_ages)):
            self.wake_ages[i] += dt
    
    def _cleanup_old_wake_points(self):
        """清理过老的尾迹点"""
        max_age = self.wake_age_max * 2 * np.pi / self.omega_rotor
        
        # 过滤过老的点
        indices_to_keep = [i for i, age in enumerate(self.wake_ages) if age < max_age]
        
        self.wake_points = [self.wake_points[i] for i in indices_to_keep]
        self.wake_circulation = [self.wake_circulation[i] for i in indices_to_keep]
        self.wake_velocities = [self.wake_velocities[i] for i in indices_to_keep]
        self.wake_ages = [self.wake_ages[i] for i in indices_to_keep]
    
    def calculate_induced_velocity(self, target_points: np.ndarray) -> np.ndarray:
        """
        计算自由尾迹的诱导速度
        
        使用点涡模型
        """
        n_targets = target_points.shape[0]
        induced_velocity = np.zeros((n_targets, 3))
        
        for i, target in enumerate(target_points):
            for j, wake_point in enumerate(self.wake_points):
                gamma = self.wake_circulation[j]
                
                # 点涡诱导速度
                r_vec = target - wake_point
                r_mag = np.linalg.norm(r_vec)
                
                if r_mag > self.core_radius:
                    # 使用Rankine涡核模型
                    induced_velocity[i] += gamma / (2 * np.pi * r_mag**2) * np.array([
                        -r_vec[1], r_vec[0], 0.0
                    ])
        
        return induced_velocity
    
    def get_wake_info(self) -> Dict[str, Any]:
        """获取尾迹信息"""
        return {
            'model_type': 'free_wake',
            'total_points': len(self.wake_points),
            'relaxation_factor': self.relaxation_factor,
            'core_radius': self.core_radius,
            'max_iterations': self.max_iterations,
            'average_age': np.mean(self.wake_ages) if self.wake_ages else 0.0
        }


class SimplifiedWake(WakeModel):
    """
    简化尾迹模型
    
    用于快速计算的简化尾迹模型。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化简化尾迹模型"""
        self.config = config
        self.R_rotor = config.get('R_rotor', 0.3)
        self.omega_rotor = config.get('omega_rotor', 100.0)
        
        # 简化参数
        self.wake_skew_angle = config.get('wake_skew_angle', 0.0)  # 尾迹倾斜角
        self.wake_contraction_ratio = config.get('wake_contraction_ratio', 0.9)
        
        print("简化尾迹模型初始化完成")
    
    def update_wake_geometry(self, blade_positions: np.ndarray, 
                           circulation: np.ndarray, dt: float):
        """更新简化尾迹几何（无需实际更新）"""
        pass
    
    def calculate_induced_velocity(self, target_points: np.ndarray) -> np.ndarray:
        """
        计算简化尾迹诱导速度
        
        使用简单的解析模型
        """
        n_targets = target_points.shape[0]
        induced_velocity = np.zeros((n_targets, 3))
        
        # 简化的诱导速度计算
        for i, target in enumerate(target_points):
            r = np.sqrt(target[0]**2 + target[1]**2)
            
            if r < self.R_rotor:
                # 桨盘内的诱导速度
                v_induced = 5.0 * (1.0 - r / self.R_rotor)  # 简化公式
                induced_velocity[i, 2] = -v_induced  # 向下
        
        return induced_velocity
    
    def get_wake_info(self) -> Dict[str, Any]:
        """获取尾迹信息"""
        return {
            'model_type': 'simplified_wake',
            'wake_skew_angle': self.wake_skew_angle,
            'wake_contraction_ratio': self.wake_contraction_ratio
        }


def create_wake_model(model_type: str, config: Dict[str, Any]) -> WakeModel:
    """
    创建尾迹模型
    
    Args:
        model_type: 模型类型
        config: 配置参数
        
    Returns:
        尾迹模型实例
    """
    models = {
        'prescribed': PrescribedWake,
        'free': FreeWake,
        'simplified': SimplifiedWake
    }
    
    if model_type not in models:
        available_types = list(models.keys())
        raise ValueError(f"不支持的尾迹模型: {model_type}. 可用类型: {available_types}")
    
    return models[model_type](config)