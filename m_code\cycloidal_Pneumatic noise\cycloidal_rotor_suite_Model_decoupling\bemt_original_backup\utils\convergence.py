#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Convergence Monitor - Medium Fidelity BEMT
收敛监控器 - 中保真度BEMT

This module provides convergence monitoring and acceleration for BEMT iterations:
- Residual tracking and analysis
- Convergence detection
- Adaptive relaxation factor
- Stagnation detection
- Performance metrics

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
import warnings
from .math_utils import aitken_acceleration, moving_average, exponential_smoothing

class ConvergenceMonitor:
    """
    收敛监控器
    
    监控BEMT迭代过程的收敛性，提供自适应控制和加速功能：
    - 残差历史跟踪
    - 收敛性检测
    - 自适应松弛因子
    - 停滞检测
    - 性能统计
    """
    
    def __init__(self, 
                 tolerance: float = 1e-6,
                 max_iterations: int = 200,
                 min_iterations: int = 5,
                 stagnation_threshold: int = 20,
                 adaptive_relaxation: bool = True,
                 **kwargs):
        """
        初始化收敛监控器
        
        Parameters:
        -----------
        tolerance : float
            收敛容差
        max_iterations : int
            最大迭代次数
        min_iterations : int
            最小迭代次数
        stagnation_threshold : int
            停滞检测阈值
        adaptive_relaxation : bool
            是否启用自适应松弛因子
        """
        
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.min_iterations = min_iterations
        self.stagnation_threshold = stagnation_threshold
        self.adaptive_relaxation = adaptive_relaxation
        
        # 收敛历史
        self.residual_history = []
        self.relaxation_history = []
        self.variable_history = {}
        
        # 当前状态
        self.iteration = 0
        self.current_residual = float('inf')
        self.is_converged = False
        self.is_stagnant = False
        self.convergence_rate = 0.0
        
        # 自适应参数
        self.initial_relaxation = kwargs.get('initial_relaxation', 0.6)
        self.min_relaxation = kwargs.get('min_relaxation', 0.1)
        self.max_relaxation = kwargs.get('max_relaxation', 0.9)
        self.current_relaxation = self.initial_relaxation
        
        # 性能统计
        self.stats = {
            'total_iterations': 0,
            'convergence_time': 0.0,
            'final_residual': 0.0,
            'convergence_rate': 0.0,
            'stagnation_count': 0
        }
        
        print(f"收敛监控器初始化完成")
        print(f"  收敛容差: {tolerance:.2e}")
        print(f"  最大迭代: {max_iterations}")
        print(f"  自适应松弛: {'启用' if adaptive_relaxation else '禁用'}")
    
    def reset(self):
        """重置监控器状态"""
        
        self.residual_history.clear()
        self.relaxation_history.clear()
        self.variable_history.clear()
        
        self.iteration = 0
        self.current_residual = float('inf')
        self.is_converged = False
        self.is_stagnant = False
        self.convergence_rate = 0.0
        self.current_relaxation = self.initial_relaxation
    
    def update(self, residual: float, variables: Optional[Dict[str, np.ndarray]] = None) -> bool:
        """
        更新收敛状态
        
        Parameters:
        -----------
        residual : float
            当前残差
        variables : dict, optional
            当前变量值
            
        Returns:
        --------
        converged : bool
            是否收敛
        """
        
        self.iteration += 1
        self.current_residual = residual
        
        # 记录历史
        self.residual_history.append(residual)
        self.relaxation_history.append(self.current_relaxation)
        
        if variables:
            for name, value in variables.items():
                if name not in self.variable_history:
                    self.variable_history[name] = []
                self.variable_history[name].append(value.copy())
        
        # 检查收敛性
        self._check_convergence()
        
        # 检查停滞
        self._check_stagnation()
        
        # 更新自适应松弛因子
        if self.adaptive_relaxation:
            self._update_relaxation_factor()
        
        # 计算收敛率
        self._compute_convergence_rate()
        
        return self.is_converged
    
    def _check_convergence(self):
        """检查收敛性"""
        
        # 基本收敛检查
        if (self.current_residual < self.tolerance and 
            self.iteration >= self.min_iterations):
            self.is_converged = True
            return
        
        # 最大迭代次数检查
        if self.iteration >= self.max_iterations:
            self.is_converged = True  # 强制收敛
            warnings.warn(f"达到最大迭代次数 {self.max_iterations}，强制收敛")
            return
        
        self.is_converged = False
    
    def _check_stagnation(self):
        """检查停滞"""
        
        if len(self.residual_history) < self.stagnation_threshold:
            self.is_stagnant = False
            return
        
        # 检查最近几次迭代的残差变化
        recent_residuals = self.residual_history[-self.stagnation_threshold:]
        
        # 计算相对变化
        relative_changes = []
        for i in range(1, len(recent_residuals)):
            if recent_residuals[i-1] > 0:
                rel_change = abs(recent_residuals[i] - recent_residuals[i-1]) / recent_residuals[i-1]
                relative_changes.append(rel_change)
        
        if relative_changes:
            avg_change = np.mean(relative_changes)
            self.is_stagnant = avg_change < self.tolerance * 10
        else:
            self.is_stagnant = False
        
        if self.is_stagnant:
            self.stats['stagnation_count'] += 1
    
    def _update_relaxation_factor(self):
        """更新自适应松弛因子"""
        
        if len(self.residual_history) < 3:
            return
        
        # 分析收敛趋势
        recent_residuals = self.residual_history[-3:]
        
        # 如果残差在减小，可以增加松弛因子
        if recent_residuals[-1] < recent_residuals[-2] < recent_residuals[-3]:
            self.current_relaxation = min(self.current_relaxation * 1.05, self.max_relaxation)
        
        # 如果残差在增加或振荡，减小松弛因子
        elif recent_residuals[-1] > recent_residuals[-2]:
            self.current_relaxation = max(self.current_relaxation * 0.9, self.min_relaxation)
        
        # 如果停滞，尝试调整松弛因子
        if self.is_stagnant:
            if self.current_relaxation > 0.5:
                self.current_relaxation = max(self.current_relaxation * 0.8, self.min_relaxation)
            else:
                self.current_relaxation = min(self.current_relaxation * 1.2, self.max_relaxation)
    
    def _compute_convergence_rate(self):
        """计算收敛率"""
        
        if len(self.residual_history) < 5:
            self.convergence_rate = 0.0
            return
        
        # 使用最近几次迭代计算收敛率
        recent_residuals = np.array(self.residual_history[-5:])
        
        # 过滤掉零值和负值
        positive_residuals = recent_residuals[recent_residuals > 1e-15]
        
        if len(positive_residuals) < 2:
            self.convergence_rate = 0.0
            return
        
        # 计算对数残差的斜率
        log_residuals = np.log(positive_residuals)
        iterations = np.arange(len(log_residuals))
        
        if len(iterations) > 1:
            # 线性拟合
            coeffs = np.polyfit(iterations, log_residuals, 1)
            self.convergence_rate = -coeffs[0]  # 负斜率表示收敛
        else:
            self.convergence_rate = 0.0
    
    def get_relaxation_factor(self) -> float:
        """获取当前松弛因子"""
        return self.current_relaxation
    
    def get_convergence_info(self) -> Dict[str, Any]:
        """获取收敛信息"""
        
        return {
            'iteration': self.iteration,
            'current_residual': self.current_residual,
            'is_converged': self.is_converged,
            'is_stagnant': self.is_stagnant,
            'convergence_rate': self.convergence_rate,
            'relaxation_factor': self.current_relaxation,
            'residual_history': self.residual_history.copy(),
            'relaxation_history': self.relaxation_history.copy()
        }
    
    def predict_convergence_iterations(self) -> Optional[int]:
        """预测收敛所需迭代次数"""
        
        if self.convergence_rate <= 0 or self.current_residual <= self.tolerance:
            return None
        
        # 基于当前收敛率预测
        remaining_reduction = np.log(self.tolerance / self.current_residual)
        predicted_iterations = int(remaining_reduction / self.convergence_rate)
        
        return max(1, predicted_iterations)
    
    def apply_acceleration(self, variable_name: str) -> Optional[np.ndarray]:
        """
        应用收敛加速
        
        Parameters:
        -----------
        variable_name : str
            变量名称
            
        Returns:
        --------
        accelerated_value : ndarray or None
            加速后的值
        """
        
        if variable_name not in self.variable_history:
            return None
        
        history = self.variable_history[variable_name]
        
        if len(history) < 3:
            return None
        
        # 对每个分量应用Aitken加速
        last_values = np.array(history[-3:])
        accelerated = np.zeros_like(last_values[-1])
        
        for i in range(len(accelerated)):
            sequence = last_values[:, i]
            accelerated[i] = aitken_acceleration(sequence)
        
        return accelerated
    
    def get_smoothed_residuals(self, method: str = 'moving_average', **kwargs) -> np.ndarray:
        """
        获取平滑的残差历史
        
        Parameters:
        -----------
        method : str
            平滑方法 ('moving_average', 'exponential')
        **kwargs : dict
            平滑参数
            
        Returns:
        --------
        smoothed : ndarray
            平滑后的残差
        """
        
        if not self.residual_history:
            return np.array([])
        
        residuals = np.array(self.residual_history)
        
        if method == 'moving_average':
            window_size = kwargs.get('window_size', 5)
            return moving_average(residuals, window_size)
        elif method == 'exponential':
            alpha = kwargs.get('alpha', 0.3)
            return exponential_smoothing(residuals, alpha)
        else:
            return residuals
    
    def finalize(self, convergence_time: float = 0.0):
        """完成收敛监控，更新统计信息"""
        
        self.stats.update({
            'total_iterations': self.iteration,
            'convergence_time': convergence_time,
            'final_residual': self.current_residual,
            'convergence_rate': self.convergence_rate
        })
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def print_convergence_summary(self):
        """打印收敛摘要"""
        
        print(f"\n收敛监控摘要:")
        print(f"=" * 40)
        print(f"总迭代次数: {self.iteration}")
        print(f"最终残差:   {self.current_residual:.2e}")
        print(f"收敛状态:   {'✅ 收敛' if self.is_converged else '❌ 未收敛'}")
        print(f"收敛率:     {self.convergence_rate:.3f}")
        print(f"松弛因子:   {self.current_relaxation:.3f}")
        
        if self.is_stagnant:
            print(f"⚠️  检测到停滞")
        
        if self.stats['convergence_time'] > 0:
            print(f"收敛时间:   {self.stats['convergence_time']:.3f} s")

# 工厂函数
def create_convergence_monitor(tolerance: float = 1e-6,
                             max_iterations: int = 200,
                             adaptive_relaxation: bool = True,
                             **kwargs) -> ConvergenceMonitor:
    """创建收敛监控器的工厂函数"""
    return ConvergenceMonitor(tolerance, max_iterations, adaptive_relaxation=adaptive_relaxation, **kwargs)
