"""
高级收敛策略
===========

提供高级收敛控制策略。

核心功能：
- 自适应容差
- Aitken加速
- 线搜索算法

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List


class AdvancedConvergenceController:
    """高级收敛控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化高级收敛控制器
        
        Args:
            config: 配置字典，包含收敛参数
        """
        self.config = config
        self.method = config.get('method', 'aitken')
        self.solution_history = []
        self.residual_history = []
        self.tolerance = config.get('tolerance', 1e-6)
        self.max_iterations = config.get('max_iterations', 100)
        
        # 原有属性
        self.base_tolerance = config.get('base_tolerance', 1e-4)
        self.adaptive_tolerance = config.get('adaptive_tolerance', True)
        self.aitken_acceleration = config.get('aitken_acceleration', False)
        self.line_search = config.get('line_search', False)
        
        # 历史数据
        self.tolerance_history = []
        
        # 当前容差
        self.current_tolerance = self.base_tolerance
    
    def check_convergence(self, residual: float, iteration: int) -> bool:
        """
        检查收敛性
        
        Args:
            residual: 当前残差
            iteration: 迭代次数
            
        Returns:
            是否收敛
        """
        # 记录历史
        self.residual_history.append(residual)
        
        # 自适应调整容差
        if self.adaptive_tolerance:
            self._update_tolerance(iteration)
        
        # 检查收敛
        converged = residual < self.current_tolerance
        
        return converged
    
    def _update_tolerance(self, iteration: int):
        """自适应更新容差"""
        if len(self.residual_history) < 3:
            return
        
        # 计算收敛率
        recent_residuals = self.residual_history[-3:]
        if recent_residuals[-2] > 0:
            convergence_rate = recent_residuals[-1] / recent_residuals[-2]
            
            # 根据收敛率调整容差
            if convergence_rate > 0.9:  # 收敛慢
                self.current_tolerance = min(self.base_tolerance * 2, 1e-2)
            elif convergence_rate < 0.1:  # 收敛快
                self.current_tolerance = max(self.base_tolerance * 0.5, 1e-6)
        
        self.tolerance_history.append(self.current_tolerance)
    
    def apply_aitken_acceleration(self, x_current: np.ndarray, 
                                x_previous: np.ndarray, 
                                x_prev_prev: np.ndarray) -> np.ndarray:
        """应用Aitken加速"""
        if not self.aitken_acceleration:
            return x_current
        
        # Aitken Δ²方法
        delta1 = x_current - x_previous
        delta2 = x_previous - x_prev_prev
        delta_delta = delta1 - delta2
        
        # 避免除零
        mask = np.abs(delta_delta) > 1e-15
        x_accelerated = x_current.copy()
        
        if np.any(mask):
            x_accelerated[mask] = x_prev_prev[mask] - (delta2[mask]**2) / delta_delta[mask]
        
        return x_accelerated
    
    def line_search_step(self, x_current: np.ndarray, direction: np.ndarray,
                        objective_func, alpha_init: float = 1.0) -> float:
        """线搜索步长"""
        if not self.line_search:
            return alpha_init
        
        # 简化的线搜索
        alpha = alpha_init
        c1 = 1e-4  # Armijo条件参数
        
        f_current = objective_func(x_current)
        
        for _ in range(10):  # 最多10次尝试
            x_new = x_current + alpha * direction
            f_new = objective_func(x_new)
            
            # Armijo条件
            if f_new <= f_current + c1 * alpha * np.dot(direction, direction):
                break
            
            alpha *= 0.5
        
        return alpha
    
    def get_convergence_info(self) -> Dict[str, Any]:
        """获取收敛信息"""
        return {
            'current_tolerance': self.current_tolerance,
            'base_tolerance': self.base_tolerance,
            'adaptive_tolerance': self.adaptive_tolerance,
            'aitken_acceleration': self.aitken_acceleration,
            'line_search': self.line_search,
            'residual_history': self.residual_history[-10:],  # 最近10个
            'tolerance_history': self.tolerance_history[-10:]
        } 
   
    def accelerate_convergence(self, x_old, x_new, iteration):
        """加速收敛"""
        if self.method == 'aitken' and len(self.solution_history) >= 2:
            # Aitken加速
            if len(self.solution_history) >= 2:
                x_prev_prev = self.solution_history[-2]
                x_previous = self.solution_history[-1]
                x_accelerated = self.apply_aitken_acceleration(x_new, x_previous, x_prev_prev)
            else:
                x_accelerated = x_new
        elif self.method == 'relaxation':
            # 松弛法
            relax_factor = self.config.get('relaxation_factor', 0.5)
            x_accelerated = (1 - relax_factor) * x_old + relax_factor * x_new
        else:
            x_accelerated = x_new
        
        # 更新历史
        self.solution_history.append(x_new.copy())
        if len(self.solution_history) > 10:  # 保持历史长度
            self.solution_history.pop(0)
        
        return x_accelerated
    
    def check_convergence(self, residuals):
        """检查收敛"""
        if len(residuals) == 0:
            return False
        
        current_residual = residuals[-1]
        tolerance = self.config.get('tolerance', 1e-6)
        
        # 更新残差历史
        self.residual_history.append(current_residual)
        if len(self.residual_history) > 20:
            self.residual_history.pop(0)
        
        # 检查收敛
        converged = current_residual < tolerance
        
        # 检查停滞
        if len(self.residual_history) >= 5:
            recent_residuals = self.residual_history[-5:]
            if max(recent_residuals) - min(recent_residuals) < tolerance * 0.1:
                converged = True  # 认为已收敛
        
        return converged