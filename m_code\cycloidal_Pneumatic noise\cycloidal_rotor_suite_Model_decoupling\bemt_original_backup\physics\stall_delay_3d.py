#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D失速延迟模型
=============

基于原始cycloidal_rotor_suite的3D失速延迟效应实现。

参考文献:
[1] <PERSON>, Z., and Seli<PERSON>, M. S. "A 3-D Stall-Delay Model for Horizontal Axis Wind Turbine Performance Prediction."
    AIAA Paper 98-0021, 1998.
[2] <PERSON>rrigan, J. J., and Schillings, J. J. "Empirical Model for Stall Delay Due to Rotation."
    AHS Aeromechanics Specialists Conference, 1994.

作者: Augment Agent
日期: 2025-07-24
"""

import numpy as np
from typing import Dict, Optional, Tuple, Any
import warnings


class DuSelig3DStallDelayModel:
    """
    Du-Selig 3D失速延迟模型
    
    基于原始cycloidal_rotor_suite的完整3D失速延迟实现
    """
    
    def __init__(self, 
                 rotor_radius: float = 1.0,
                 hub_radius: float = 0.1,
                 num_blades: int = 4,
                 model_parameters: Optional[Dict[str, float]] = None):
        """
        初始化3D失速延迟模型
        
        参数:
        ----
        rotor_radius : float
            旋翼半径 [m]
        hub_radius : float
            桂毂半径 [m]
        num_blades : int
            桨叶数量
        model_parameters : Dict, optional
            模型参数
        """
        self.rotor_radius = rotor_radius
        self.hub_radius = hub_radius
        self.num_blades = num_blades
        
        # 默认模型参数
        default_params = {
            'a1': 2.0,           # Du-Selig模型参数a1
            'a2': 1.0,           # Du-Selig模型参数a2
            'c1': 0.125,         # 修正系数c1
            'c2': 0.5,           # 修正系数c2
            'c3': 1.0,           # 修正系数c3
            'r_ref': 0.75,       # 参考径向位置
            'alpha_ref': 0.25,   # 参考失速攻角 [rad]
            'cl_max_2d': 1.4,    # 2D最大升力系数
            'aspect_ratio': 10.0, # 有效展弦比
            'taper_ratio': 1.0,  # 锥度比
        }
        
        # 合并用户参数
        self.params = default_params.copy()
        if model_parameters:
            self.params.update(model_parameters)
        
        # 计算派生参数
        self.aspect_ratio_eff = self._compute_effective_aspect_ratio()
        
        print("✅ Du-Selig 3D失速延迟模型初始化完成")
        print(f"   旋翼半径: {rotor_radius:.2f}m")
        print(f"   有效展弦比: {self.aspect_ratio_eff:.1f}")
    
    def compute_3d_stall_delay(self, 
                              r_stations: np.ndarray,
                              alpha_2d: np.ndarray,
                              cl_2d: np.ndarray,
                              cd_2d: np.ndarray,
                              omega: float,
                              velocity: float,
                              chord_distribution: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
        """
        计算3D失速延迟效应
        
        参数:
        ----
        r_stations : np.ndarray
            径向站位 [m]
        alpha_2d : np.ndarray
            2D攻角 [rad]
        cl_2d : np.ndarray
            2D升力系数
        cd_2d : np.ndarray
            2D阻力系数
        omega : float
            角速度 [rad/s]
        velocity : float
            来流速度 [m/s]
        chord_distribution : np.ndarray
            弦长分布 [m]
            
        返回:
        ----
        cl_3d : np.ndarray
            3D修正后的升力系数
        cd_3d : np.ndarray
            3D修正后的阻力系数
        stall_info : Dict[str, Any]
            失速延迟信息
        """
        # 无量纲径向位置
        r_R = r_stations / self.rotor_radius
        
        # 计算局部雷诺数
        local_reynolds = self._compute_local_reynolds(
            r_stations, omega, velocity, chord_distribution
        )
        
        # 计算3D失速延迟因子
        stall_delay_factor = self._compute_stall_delay_factor(
            r_R, alpha_2d, local_reynolds, chord_distribution
        )
        
        # 计算修正后的失速攻角
        alpha_stall_3d = self._compute_3d_stall_angle(
            r_R, alpha_2d, stall_delay_factor
        )
        
        # 应用3D修正
        cl_3d, cd_3d = self._apply_3d_corrections(
            alpha_2d, cl_2d, cd_2d, alpha_stall_3d, stall_delay_factor
        )
        
        # 失速延迟信息
        stall_info = {
            'stall_delay_factor': stall_delay_factor,
            'alpha_stall_3d': alpha_stall_3d,
            'local_reynolds': local_reynolds,
            'max_delay_factor': np.max(stall_delay_factor),
            'stalled_stations': np.sum(alpha_2d > alpha_stall_3d)
        }
        
        return cl_3d, cd_3d, stall_info
    
    def _compute_effective_aspect_ratio(self) -> float:
        """计算有效展弦比"""
        
        # 旋翼的有效展弦比
        rotor_area = np.pi * (self.rotor_radius**2 - self.hub_radius**2)
        effective_span = 2 * (self.rotor_radius - self.hub_radius)
        
        # 考虑桨叶数量的影响
        AR_eff = (effective_span**2 / rotor_area) * self.num_blades / 2
        
        return AR_eff
    
    def _compute_local_reynolds(self, 
                               r_stations: np.ndarray,
                               omega: float,
                               velocity: float,
                               chord_distribution: np.ndarray) -> np.ndarray:
        """计算局部雷诺数"""
        
        # 局部相对速度
        V_local = np.sqrt((omega * r_stations)**2 + velocity**2)
        
        # 空气运动粘度（标准条件）
        nu = 1.5e-5  # m²/s
        
        # 局部雷诺数
        Re_local = V_local * chord_distribution / nu
        
        return Re_local
    
    def _compute_stall_delay_factor(self, 
                                   r_R: np.ndarray,
                                   alpha_2d: np.ndarray,
                                   local_reynolds: np.ndarray,
                                   chord_distribution: np.ndarray) -> np.ndarray:
        """
        计算Du-Selig失速延迟因子
        """
        
        # 局部实度比
        sigma_local = self.num_blades * chord_distribution / (2 * np.pi * r_R * self.rotor_radius)
        
        # Du-Selig失速延迟因子
        # Δα_stall = f(r/R, c/r, Re)
        
        stall_delay_factor = np.zeros_like(r_R)
        
        for i, (r, alpha, Re, chord) in enumerate(zip(r_R, alpha_2d, local_reynolds, chord_distribution)):
            if r > 0.1:  # 避免桂毂附近的数值问题
                # 径向位置效应
                r_effect = self.params['a1'] * (r / self.params['r_ref'])**self.params['a2']
                
                # 弦长比效应
                c_r_ratio = chord / (r * self.rotor_radius)
                chord_effect = self.params['c1'] * c_r_ratio**self.params['c2']
                
                # 雷诺数效应
                Re_effect = 1.0 + self.params['c3'] * np.log10(Re / 1e6)
                Re_effect = np.clip(Re_effect, 0.5, 2.0)
                
                # 实度比效应
                solidity_effect = 1.0 + 0.5 * sigma_local[i]
                
                # 综合失速延迟因子
                delay_factor = r_effect * chord_effect * Re_effect * solidity_effect
                
                # 限制延迟因子范围
                stall_delay_factor[i] = np.clip(delay_factor, 1.0, 3.0)
            else:
                stall_delay_factor[i] = 1.0
        
        return stall_delay_factor
    
    def _compute_3d_stall_angle(self, 
                               r_R: np.ndarray,
                               alpha_2d: np.ndarray,
                               stall_delay_factor: np.ndarray) -> np.ndarray:
        """计算3D失速攻角"""
        
        # 2D失速攻角（参考值）
        alpha_stall_2d = self.params['alpha_ref']
        
        # 3D失速攻角
        alpha_stall_3d = alpha_stall_2d * stall_delay_factor
        
        # 考虑径向位置的影响
        radial_correction = 1.0 + 0.2 * (1.0 - r_R)
        alpha_stall_3d *= radial_correction
        
        return alpha_stall_3d
    
    def _apply_3d_corrections(self, 
                             alpha_2d: np.ndarray,
                             cl_2d: np.ndarray,
                             cd_2d: np.ndarray,
                             alpha_stall_3d: np.ndarray,
                             stall_delay_factor: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用3D修正"""
        
        cl_3d = cl_2d.copy()
        cd_3d = cd_2d.copy()
        
        for i, (alpha, cl_2, cd_2, alpha_stall, delay_factor) in enumerate(
            zip(alpha_2d, cl_2d, cd_2d, alpha_stall_3d, stall_delay_factor)):
            
            if abs(alpha) > alpha_stall:
                # 失速区域的3D修正
                
                # 升力系数修正
                if alpha > alpha_stall:
                    # 正失速
                    excess_angle = alpha - alpha_stall
                    cl_reduction = 1.0 - 0.5 * (excess_angle / 0.1)**2
                    cl_3d[i] = cl_2 * max(0.3, cl_reduction)
                else:
                    # 负失速
                    excess_angle = alpha_stall - alpha
                    cl_reduction = 1.0 - 0.5 * (excess_angle / 0.1)**2
                    cl_3d[i] = cl_2 * max(0.3, cl_reduction)
                
                # 阻力系数修正
                cd_increase = 1.0 + 0.3 * (abs(alpha) - alpha_stall) / 0.1
                cd_3d[i] = cd_2 * cd_increase
                
            else:
                # 附着流区域的3D增强
                if delay_factor > 1.0:
                    # 升力增强
                    cl_enhancement = 1.0 + 0.1 * (delay_factor - 1.0)
                    cl_3d[i] = cl_2 * cl_enhancement
                    
                    # 阻力略微减少
                    cd_reduction = 1.0 - 0.05 * (delay_factor - 1.0)
                    cd_3d[i] = cd_2 * max(0.8, cd_reduction)
        
        return cl_3d, cd_3d
    
    def compute_rotational_augmentation(self, 
                                      r_stations: np.ndarray,
                                      alpha: np.ndarray,
                                      omega: float,
                                      velocity: float) -> np.ndarray:
        """
        计算旋转增强效应
        
        基于Corrigan-Schillings模型
        """
        
        # 无量纲径向位置
        r_R = r_stations / self.rotor_radius
        
        # 局部速度比
        speed_ratio = (omega * r_stations) / velocity
        
        # 旋转增强因子
        augmentation_factor = np.ones_like(r_R)
        
        for i, (r, a, sr) in enumerate(zip(r_R, alpha, speed_ratio)):
            if r > 0.2 and sr > 1.0:  # 只在外径和高速比区域应用
                # Corrigan-Schillings公式
                # ΔCl = f(r/R, Ωr/V, α)
                
                radial_factor = (r - 0.2) / 0.8  # 径向权重
                speed_factor = min(sr / 5.0, 1.0)  # 速度比权重
                angle_factor = np.sin(2 * a)  # 攻角权重
                
                augmentation = 0.2 * radial_factor * speed_factor * angle_factor
                augmentation_factor[i] = 1.0 + augmentation
        
        return augmentation_factor
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        
        return {
            'model_type': 'Du-Selig 3D Stall Delay',
            'parameters': self.params.copy(),
            'rotor_geometry': {
                'radius': self.rotor_radius,
                'hub_radius': self.hub_radius,
                'num_blades': self.num_blades,
                'aspect_ratio_eff': self.aspect_ratio_eff
            }
        }


# 导出接口
__all__ = [
    'DuSelig3DStallDelayModel'
]
