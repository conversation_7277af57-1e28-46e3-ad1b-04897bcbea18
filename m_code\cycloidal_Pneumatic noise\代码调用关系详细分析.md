# 🔍 旋翼仿真系统 - 代码调用关系详细分析

## 📋 主要入口点和调用链

### 1️⃣ 主程序入口调用链

```mermaid
graph TD
    A[main.py] --> B[cyclone_sim/simulation.py]
    B --> C[CycloneSimulation.__init__]
    C --> D[config_loader.py]
    D --> E[SimulationConfig]
    E --> F[_initialize_modules]
    
    F --> G[_initialize_multi_fidelity_solvers]
    F --> H[_initialize_legacy_solvers]
    F --> I[_initialize_acoustics]
    
    G --> J[MultiFidelitySolverFactory]
    H --> K[LegacySolverFactory]
    I --> L[FWHSolver + NoiseAnalyzer]
```

### 2️⃣ BEMT求解器详细调用链

```mermaid
graph TD
    A[BEMTSolver.__init__] --> B[ConfigManager]
    B --> C[_initialize_subsystems]
    
    C --> D[RotorGeometry]
    C --> E[AirfoilDatabase]
    C --> F[UnifiedPhysicalCorrections]
    C --> G[ConvergenceController]
    C --> H[TimeIntegrator]
    C --> I[PerformanceCalculator]
    
    J[solve_step] --> K[_update_blade_kinematics]
    K --> L[_solve_bemt_iteration]
    
    L --> M[_solve_blade_elements]
    M --> N[BladeElement.calculate_relative_velocity]
    M --> O[_calculate_pitch_angle]
    M --> P[_get_static_coefficients]
    M --> Q[AirfoilDatabase.get_coefficients]
    
    P --> R[_calculate_element_loads]
    R --> S[_update_induced_velocities]
    S --> T[_calculate_residual]
    T --> U[ConvergenceController.check_convergence]
    
    U -->|收敛| V[_apply_physical_corrections]
    U -->|未收敛| W[_apply_relaxation]
    W --> L
    
    V --> X[UnifiedPhysicalCorrections.apply_all]
    X --> Y[PerformanceCalculator.calculate_performance]
```

### 3️⃣ 配置管理系统调用关系

```mermaid
graph LR
    A[config.yaml] --> B[ConfigManager.load_from_file]
    B --> C[yaml.safe_load]
    C --> D[_validate_config]
    
    D --> E[_validate_basic_parameters]
    E --> F[ValidationError检查]
    
    D --> G[_optimize_bemt_config]
    G --> H[自动参数设置]
    
    I[ConfigManager.get] --> J[参数查询]
    I --> K[ConfigManager.set]
    I --> L[ConfigManager.to_dict]
```

### 4️⃣ 物理修正系统调用关系

```mermaid
graph TD
    A[UnifiedPhysicalCorrections.apply_all] --> B[TipLossCorrection.apply]
    A --> C[HubLossCorrection.apply]
    A --> D[ViscousEffectsCorrection.apply]
    A --> E[CompressibilityCorrection.apply]
    
    B --> F[Prandtl叶尖损失公式]
    B --> G[Goldstein修正]
    
    C --> H[桨毂损失计算]
    
    D --> I[雷诺数效应]
    D --> J[边界层修正]
    
    E --> K[马赫数修正]
    E --> L[压缩性系数]
```

### 5️⃣ 动态失速模型调用关系

```mermaid
graph TD
    A[LeishmanBeddoesModel.calculate_coefficients] --> B[_update_states]
    B --> C[_calculate_circulatory_response]
    B --> D[_calculate_impulsive_response]
    B --> E[_calculate_separation_point]
    
    C --> F[环量力时间常数]
    D --> G[冲量力时间常数]
    E --> H[分离点动态]
    
    F --> I[RK4时间积分]
    G --> I
    H --> I
    
    I --> J[_calculate_vortex_shedding]
    J --> K[前缘涡强度]
    K --> L[最终系数输出]
```

### 6️⃣ 时间积分器调用关系

```mermaid
graph LR
    A[TimeIntegrator.integrate] --> B{积分方法选择}
    
    B -->|euler| C[EulerMethod.integrate]
    B -->|rk4| D[RungeKutta4Method.integrate]
    B -->|adams| E[AdamsBashforth2Method.integrate]
    B -->|adaptive| F[AdaptiveRungeKuttaMethod.integrate]
    
    C --> G[y + dt * dydt]
    D --> H[RK4四步计算]
    E --> I[Adams-Bashforth公式]
    F --> J[误差估计和步长调整]
```

### 7️⃣ 声学分析调用关系

```mermaid
graph TD
    A[FWHSolver.solve] --> B[_compute_thickness_sources]
    A --> C[_compute_loading_sources]
    A --> D[_compute_quadrupole_sources]
    
    B --> E[表面法向速度]
    C --> F[表面载荷分布]
    D --> G[四极子强度]
    
    E --> H[_integrate_acoustic_sources]
    F --> H
    G --> H
    
    H --> I[远场声压计算]
    I --> J[NoiseAnalyzer.analyze]
    
    J --> K[频域分析]
    J --> L[时域分析]
    J --> M[声学指标计算]
```

### 8️⃣ 验证框架调用关系

```mermaid
graph TD
    A[ValidationFramework.run_validation] --> B[_load_experimental_data]
    A --> C[_execute_simulation]
    A --> D[_calculate_error_metrics]
    
    B --> E[实验数据解析]
    C --> F[CycloneSimulation.run]
    D --> G[MAE/RMSE/MAPE计算]
    
    G --> H[_quantify_uncertainties]
    H --> I[网格收敛性分析]
    H --> J[参数敏感性分析]
    H --> K[Monte Carlo分析]
    
    I --> L[_generate_validation_report]
    J --> L
    K --> L
    
    L --> M[HTML报告生成]
    L --> N[Markdown报告生成]
    L --> O[LaTeX报告生成]
```

## 🔧 关键函数调用序列

### BEMT求解器完整调用序列

```python
# 1. 初始化阶段
BEMTSolver.__init__(config)
├── ConfigManager(config)
├── _initialize_subsystems()
│   ├── RotorGeometry(config)
│   ├── AirfoilDatabase(data_dir)
│   ├── UnifiedPhysicalCorrections(config)
│   ├── ConvergenceController(tolerance, max_iter)
│   ├── TimeIntegrator(method, config)
│   └── PerformanceCalculator(config)
├── _initialize_solver_state()
└── _generate_radial_positions()

# 2. 求解阶段
solve_step(t, dt)
├── validate_input(t, dt)
├── _update_blade_kinematics(t)
│   └── blade.update_kinematics(t, blade_angle, omega)
├── _solve_bemt_iteration(t, dt)
│   ├── convergence_controller.reset()
│   └── while not converged:
│       ├── _solve_blade_elements(blade, blade_idx, azimuth, t, dt)
│       │   ├── element.calculate_relative_velocity(V_inf)
│       │   ├── _calculate_pitch_angle(azimuth, r_R)
│       │   ├── element.calculate_effective_angle_of_attack(pitch)
│       │   ├── _get_static_coefficients(alpha_eff, radius)
│       │   │   └── airfoil_database.get_coefficients(airfoil, alpha, Re)
│       │   └── _calculate_element_loads(element, V_rel, alpha, Cl, Cd, Cm)
│       ├── _update_induced_velocities(forces, circulation)
│       ├── _calculate_residual(v_old, v_new)
│       └── convergence_controller.check_convergence(residual)
├── _apply_physical_corrections(result)
│   └── physics_corrections.apply_all(correction_input)
│       ├── tip_loss.apply(input_data)
│       ├── hub_loss.apply(input_data)
│       └── viscous_effects.apply(input_data)
├── performance_calculator.calculate_performance(result, geometry, t)
└── _update_history(result)
```

### 声学分析完整调用序列

```python
# 声学求解器调用序列
FWHSolver.solve(surface_data, observer_positions)
├── _validate_input_data(surface_data)
├── _compute_thickness_sources(surface_data)
│   ├── _calculate_surface_normal_velocity()
│   └── _apply_thickness_source_formula()
├── _compute_loading_sources(surface_data)
│   ├── _calculate_surface_pressure_distribution()
│   └── _apply_loading_source_formula()
├── _compute_quadrupole_sources(surface_data)
│   └── _calculate_quadrupole_strength()
├── _integrate_acoustic_sources(sources, observer_positions)
│   ├── _calculate_retarded_time()
│   ├── _apply_doppler_correction()
│   └── _integrate_over_surface()
└── NoiseAnalyzer.analyze(acoustic_pressure)
    ├── _compute_frequency_spectrum()
    ├── _calculate_sound_pressure_level()
    ├── _compute_directivity_pattern()
    └── _generate_acoustic_metrics()
```

### 验证框架完整调用序列

```python
# 验证案例执行序列
ValidationFramework.run_validation(case_name, computed_results)
├── _load_validation_case(case_name)
├── _load_experimental_data(case_name)
├── _calculate_error_metrics(computed, experimental)
│   ├── _calculate_mae(computed, experimental)
│   ├── _calculate_rmse(computed, experimental)
│   ├── _calculate_mape(computed, experimental)
│   └── _calculate_correlation_coefficient(computed, experimental)
├── _quantify_uncertainties(case_name, sensitivity_analysis)
│   ├── _grid_convergence_study()
│   ├── _parameter_sensitivity_analysis()
│   └── _monte_carlo_uncertainty_analysis()
├── _assess_academic_grade(error_metrics)
└── _generate_validation_report(case_name, results)
    ├── _create_html_report()
    ├── _create_markdown_report()
    └── _create_latex_report()
```

## 📊 数据流向分析

### 主要数据结构流向

```mermaid
graph LR
    A[config.yaml] --> B[ConfigManager]
    B --> C[BEMTSolver]
    
    C --> D[blade_kinematics]
    D --> E[relative_velocity]
    E --> F[airfoil_coefficients]
    F --> G[element_loads]
    G --> H[induced_velocities]
    
    H --> I[convergence_check]
    I -->|收敛| J[performance_metrics]
    I -->|未收敛| K[relaxation_update]
    K --> E
    
    J --> L[acoustic_sources]
    L --> M[far_field_noise]
    M --> N[validation_results]
    N --> O[final_report]
```

### 关键数据结构定义

```python
# 配置数据结构
config = {
    'R_rotor': float,           # 转子半径
    'B': int,                   # 桨叶数
    'omega_rotor': float,       # 角速度
    'fidelity': str,            # 保真度级别
    'enable_*': bool,           # 各种开关
}

# 求解结果数据结构
result = {
    'forces': np.ndarray,       # 力向量 [B, 3]
    'moments': np.ndarray,      # 力矩向量 [B, 3]
    'circulation': np.ndarray,  # 环量分布
    'performance': {
        'thrust': float,
        'power': float,
        'torque': float,
        'figure_of_merit': float,
        'CT': float,
        'CP': float
    },
    'convergence_info': {
        'converged': bool,
        'iterations': int,
        'residual': float
    }
}

# 验证结果数据结构
validation_result = {
    'error_metrics': {
        'mae': float,
        'rmse': float,
        'mape': float,
        'correlation': float
    },
    'academic_grade': str,      # A+, A, B+, B, C, D
    'uncertainty_analysis': {
        'grid_convergence': dict,
        'parameter_sensitivity': dict,
        'monte_carlo': dict
    }
}
```

这个详细的调用关系分析展示了整个旋翼仿真系统的内部工作机制，从最高层的用户接口到最底层的数值计算，形成了一个完整的、模块化的仿真框架。