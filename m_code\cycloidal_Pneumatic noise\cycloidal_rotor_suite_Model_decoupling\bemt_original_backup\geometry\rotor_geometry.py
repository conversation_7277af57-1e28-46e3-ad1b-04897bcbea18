#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D Rotor Geometry Manager - Medium Fidelity
3D旋翼几何管理器 - 中保真度

This module implements comprehensive 3D rotor geometry management for medium-fidelity BEMT:
- Multi-blade rotor configuration
- Rotor disk geometry
- Blade positioning and orientation
- Collective and cyclic pitch control
- Geometric transformations

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings

try:
    from .blade_geometry_3d import BladeGeometry3D, create_blade_geometry_3d
except ImportError:
    warnings.warn("无法导入BladeGeometry3D，将使用简化实现")

class RotorGeometry3D:
    """
    3D旋翼几何管理器
    
    管理多桨叶旋翼的完整几何配置：
    - 桨叶几何定义
    - 旋翼盘几何
    - 桨叶定位和方向
    - 桨距控制
    - 几何变换
    """
    
    def __init__(self, 
                 rotor_type: str = "conventional",
                 radius: float = 1.0,
                 num_blades: int = 4,
                 hub_radius: float = None,
                 rotor_config: Optional[Dict] = None,
                 **kwargs):
        """
        初始化3D旋翼几何管理器
        
        Parameters:
        -----------
        rotor_type : str
            旋翼类型 ('conventional', 'cycloidal', 'coaxial')
        radius : float
            旋翼半径 [m]
        num_blades : int
            桨叶数量
        hub_radius : float, optional
            桂毂半径 [m]
        rotor_config : dict, optional
            旋翼配置字典
        **kwargs : dict
            额外参数
        """
        
        self.rotor_type = rotor_type
        self.radius = radius
        self.num_blades = num_blades
        self.hub_radius = hub_radius or 0.1 * radius
        
        # 旋翼配置
        self.config = rotor_config or self._get_default_config()
        
        # 桨叶几何
        self.blade_geometry = self._create_blade_geometry(**kwargs)
        
        # 桨叶方位角
        self.blade_azimuths = np.linspace(0, 2*np.pi, num_blades, endpoint=False)
        
        # 桨距控制
        self.collective_pitch = 0.0  # [deg]
        self.cyclic_pitch = {'A1': 0.0, 'B1': 0.0}  # [deg]
        
        # 旋翼盘几何
        self._compute_disk_geometry()
        
        # 几何变换矩阵
        self._initialize_transformations()
        
        print(f"3D旋翼几何管理器初始化完成")
        print(f"  旋翼类型: {self.rotor_type}")
        print(f"  半径: {self.radius:.3f} m")
        print(f"  桨叶数: {self.num_blades}")
        print(f"  盘面积: {self.disk_area:.4f} m²")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认旋翼配置"""
        
        if self.rotor_type == "conventional":
            return {
                'rotation_direction': 'ccw',  # 逆时针
                'shaft_tilt': 0.0,           # 轴倾斜角 [deg]
                'coning_angle': 0.0,         # 锥度角 [deg]
                'flapping_hinge': 0.0,       # 挥舞铰位置 [m]
                'lead_lag_hinge': 0.0,       # 摆振铰位置 [m]
                'pitch_horn_offset': 0.0     # 桨距杆偏移 [m]
            }
        elif self.rotor_type == "cycloidal":
            return {
                'rotation_direction': 'cw',   # 顺时针
                'blade_span': 0.5,           # 桨叶展长 [m]
                'pitch_amplitude': 20.0,     # 桨距幅值 [deg]
                'pitch_phase': 0.0,          # 桨距相位 [deg]
                'eccentricity': 0.0          # 偏心距 [m]
            }
        else:
            return {}
    
    def _create_blade_geometry(self, **kwargs) -> BladeGeometry3D:
        """创建桨叶几何"""
        
        try:
            return create_blade_geometry_3d(
                radius=self.radius,
                hub_radius=self.hub_radius,
                **kwargs
            )
        except:
            # 简化实现
            return self._create_simple_blade_geometry(**kwargs)
    
    def _create_simple_blade_geometry(self, **kwargs):
        """创建简化桨叶几何"""
        
        class SimpleBlade:
            def __init__(self, radius, hub_radius, **kw):
                self.radius = radius
                self.hub_radius = hub_radius
                self.n_stations = kw.get('n_stations', 20)
                
                # 径向站位
                self.r_stations = np.linspace(hub_radius, radius, self.n_stations)
                self.r_R = self.r_stations / radius
                
                # 几何分布
                self.chord_stations = np.linspace(0.15, 0.05, self.n_stations)
                self.twist_stations = np.linspace(15.0, -5.0, self.n_stations)
                self.thickness_stations = np.linspace(0.15, 0.08, self.n_stations)
                self.dr_stations = np.full(self.n_stations, 0.8 * radius / self.n_stations)
                
                # 翼型
                self.airfoil_names = ['naca0012'] * self.n_stations
                
                # 几何特性
                self.blade_area = np.trapz(self.chord_stations, self.r_stations)
                self.mean_chord = self.blade_area / (radius - hub_radius)
            
            def get_station_properties(self, i):
                return {
                    'r': self.r_stations[i],
                    'r_R': self.r_R[i],
                    'chord': self.chord_stations[i],
                    'twist': self.twist_stations[i],
                    'thickness': self.thickness_stations[i],
                    'dr': self.dr_stations[i],
                    'airfoil_name': self.airfoil_names[i],
                    'airfoil_params': {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64}
                }
            
            def get_airfoil_at_station(self, i):
                return self.airfoil_names[i]
            
            def get_airfoil_params_at_station(self, i):
                return {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64}
        
        return SimpleBlade(self.radius, self.hub_radius, **kwargs)
    
    def _compute_disk_geometry(self):
        """计算旋翼盘几何"""
        
        # 旋翼盘面积
        self.disk_area = np.pi * (self.radius**2 - self.hub_radius**2)
        
        # 实度比
        total_blade_area = self.num_blades * self.blade_geometry.blade_area
        self.solidity = total_blade_area / self.disk_area
        
        # 等效半径
        self.equivalent_radius = np.sqrt(self.disk_area / np.pi)
        
        # 桨叶间距
        self.blade_spacing = 2 * np.pi / self.num_blades
        
        print(f"旋翼盘几何:")
        print(f"  盘面积: {self.disk_area:.4f} m²")
        print(f"  实度比: {self.solidity:.4f}")
        print(f"  等效半径: {self.equivalent_radius:.3f} m")
    
    def _initialize_transformations(self):
        """初始化几何变换"""
        
        # 旋转方向
        self.rotation_sign = -1 if self.config.get('rotation_direction') == 'ccw' else 1
        
        # 轴倾斜变换矩阵
        shaft_tilt = np.radians(self.config.get('shaft_tilt', 0.0))
        self.shaft_tilt_matrix = self._create_rotation_matrix('y', shaft_tilt)
        
        # 锥度角变换
        self.coning_angle = np.radians(self.config.get('coning_angle', 0.0))
    
    def _create_rotation_matrix(self, axis: str, angle: float) -> np.ndarray:
        """创建旋转矩阵"""
        
        c, s = np.cos(angle), np.sin(angle)
        
        if axis.lower() == 'x':
            return np.array([[1, 0, 0], [0, c, -s], [0, s, c]])
        elif axis.lower() == 'y':
            return np.array([[c, 0, s], [0, 1, 0], [-s, 0, c]])
        elif axis.lower() == 'z':
            return np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]])
        else:
            return np.eye(3)
    
    def get_blade_position(self, blade_index: int, azimuth: float = None) -> Dict[str, Any]:
        """获取桨叶位置信息"""
        
        if not (0 <= blade_index < self.num_blades):
            raise IndexError(f"桨叶索引超出范围: {blade_index}")
        
        # 桨叶方位角
        if azimuth is None:
            blade_azimuth = self.blade_azimuths[blade_index]
        else:
            blade_azimuth = azimuth
        
        # 桨叶位置矢量
        x = self.radius * np.cos(blade_azimuth)
        y = self.radius * np.sin(blade_azimuth) * self.rotation_sign
        z = 0.0  # 简化：不考虑锥度角
        
        # 桨叶方向矢量
        dx = -np.sin(blade_azimuth)
        dy = np.cos(blade_azimuth) * self.rotation_sign
        dz = 0.0
        
        return {
            'azimuth': np.degrees(blade_azimuth),
            'position': np.array([x, y, z]),
            'direction': np.array([dx, dy, dz]),
            'blade_index': blade_index
        }
    
    def compute_blade_pitch(self, blade_index: int, azimuth: float = None) -> float:
        """计算桨叶桨距角"""
        
        # 基础桨距（集体桨距）
        pitch = self.collective_pitch
        
        # 周期桨距（循环桨距）
        if azimuth is None:
            azimuth = self.blade_azimuths[blade_index]
        
        pitch += self.cyclic_pitch['A1'] * np.cos(azimuth)
        pitch += self.cyclic_pitch['B1'] * np.sin(azimuth)
        
        # 循环翼特殊处理
        if self.rotor_type == "cycloidal":
            pitch_amplitude = self.config.get('pitch_amplitude', 20.0)
            pitch_phase = np.radians(self.config.get('pitch_phase', 0.0))
            pitch += pitch_amplitude * np.sin(azimuth + pitch_phase)
        
        return pitch
    
    def update_pitch_control(self, 
                           collective: Optional[float] = None,
                           cyclic_A1: Optional[float] = None,
                           cyclic_B1: Optional[float] = None):
        """更新桨距控制"""
        
        if collective is not None:
            self.collective_pitch = collective
        
        if cyclic_A1 is not None:
            self.cyclic_pitch['A1'] = cyclic_A1
        
        if cyclic_B1 is not None:
            self.cyclic_pitch['B1'] = cyclic_B1
        
        print(f"桨距控制已更新:")
        print(f"  集体桨距: {self.collective_pitch:.2f}°")
        print(f"  循环桨距: A1={self.cyclic_pitch['A1']:.2f}°, B1={self.cyclic_pitch['B1']:.2f}°")
    
    def get_station_geometry(self, station_index: int, blade_index: int = 0) -> Dict[str, Any]:
        """获取指定站位的几何信息"""
        
        # 桨叶几何
        blade_props = self.blade_geometry.get_station_properties(station_index)
        
        # 桨叶位置
        blade_pos = self.get_blade_position(blade_index)
        
        # 桨距角
        pitch_angle = self.compute_blade_pitch(blade_index)
        
        # 合成几何信息
        geometry_info = {
            **blade_props,
            'blade_azimuth': blade_pos['azimuth'],
            'blade_position': blade_pos['position'],
            'pitch_angle': pitch_angle,
            'effective_twist': blade_props['twist'] + pitch_angle
        }
        
        return geometry_info
    
    def get_rotor_summary(self) -> Dict[str, Any]:
        """获取旋翼摘要信息"""
        
        blade_summary = self.blade_geometry.get_geometry_summary() if hasattr(self.blade_geometry, 'get_geometry_summary') else {}
        
        return {
            'rotor_type': self.rotor_type,
            'radius': self.radius,
            'hub_radius': self.hub_radius,
            'num_blades': self.num_blades,
            'disk_area': self.disk_area,
            'solidity': self.solidity,
            'equivalent_radius': self.equivalent_radius,
            'rotation_direction': self.config.get('rotation_direction', 'ccw'),
            'collective_pitch': self.collective_pitch,
            'cyclic_pitch': self.cyclic_pitch,
            'blade_geometry': blade_summary
        }
    
    def update_geometry(self, **kwargs):
        """更新几何参数"""
        
        updated = False
        
        # 更新旋翼参数
        if 'radius' in kwargs:
            self.radius = kwargs['radius']
            updated = True
        
        if 'num_blades' in kwargs:
            self.num_blades = kwargs['num_blades']
            self.blade_azimuths = np.linspace(0, 2*np.pi, self.num_blades, endpoint=False)
            updated = True
        
        # 更新桨叶几何
        blade_kwargs = {k: v for k, v in kwargs.items() 
                       if k.startswith(('chord', 'twist', 'thickness'))}
        if blade_kwargs and hasattr(self.blade_geometry, 'update_geometry'):
            self.blade_geometry.update_geometry(**blade_kwargs)
            updated = True
        
        if updated:
            self._compute_disk_geometry()
            print("旋翼几何参数已更新")
    
    def validate_geometry(self) -> Dict[str, bool]:
        """验证几何参数"""
        
        validation = {
            'radius_positive': self.radius > 0,
            'hub_radius_valid': 0 < self.hub_radius < self.radius,
            'num_blades_valid': self.num_blades >= 2,
            'solidity_reasonable': 0.01 < self.solidity < 0.5,
            'disk_area_positive': self.disk_area > 0
        }
        
        all_valid = all(validation.values())
        
        if not all_valid:
            print("⚠️  几何验证失败:")
            for check, result in validation.items():
                if not result:
                    print(f"    {check}: {result}")
        else:
            print("✅ 几何验证通过")
        
        return validation

# 工厂函数
def create_rotor_geometry_3d(rotor_type: str = "conventional",
                           radius: float = 1.0,
                           num_blades: int = 4,
                           hub_radius: float = None,
                           rotor_config: Optional[Dict] = None,
                           **kwargs) -> RotorGeometry3D:
    """创建3D旋翼几何管理器的工厂函数"""
    return RotorGeometry3D(rotor_type, radius, num_blades, hub_radius, rotor_config, **kwargs)

# 预定义配置
CONVENTIONAL_ROTOR_CONFIG = {
    'rotation_direction': 'ccw',
    'shaft_tilt': 0.0,
    'coning_angle': 2.0,
    'flapping_hinge': 0.05,
    'lead_lag_hinge': 0.05
}

CYCLOIDAL_ROTOR_CONFIG = {
    'rotation_direction': 'cw',
    'blade_span': 0.5,
    'pitch_amplitude': 25.0,
    'pitch_phase': 0.0,
    'eccentricity': 0.0
}
