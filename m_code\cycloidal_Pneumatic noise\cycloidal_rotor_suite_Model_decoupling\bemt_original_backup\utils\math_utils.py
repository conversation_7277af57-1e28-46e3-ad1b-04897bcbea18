"""
数学工具函数
===========

提供BEMT求解器需要的数学工具函数。

核心功能：
- 旋转矩阵计算
- 插值函数
- 数值微分
- 数据平滑

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Tuple, Optional, Union
from scipy import interpolate


def rotation_matrix(axis: str, angle: float) -> np.ndarray:
    """
    计算旋转矩阵
    
    Args:
        axis: 旋转轴 ('x', 'y', 'z')
        angle: 旋转角度 [rad]
        
    Returns:
        3x3旋转矩阵
    """
    c, s = np.cos(angle), np.sin(angle)
    
    if axis.lower() == 'x':
        return np.array([[1, 0, 0],
                        [0, c, -s],
                        [0, s, c]])
    elif axis.lower() == 'y':
        return np.array([[c, 0, s],
                        [0, 1, 0],
                        [-s, 0, c]])
    elif axis.lower() == 'z':
        return np.array([[c, -s, 0],
                        [s, c, 0],
                        [0, 0, 1]])
    else:
        raise ValueError(f"不支持的旋转轴: {axis}")


def interpolate_1d(x: np.ndarray, y: np.ndarray, x_new: Union[float, np.ndarray],
                  kind: str = 'linear', bounds_error: bool = False) -> Union[float, np.ndarray]:
    """
    一维插值
    
    Args:
        x: 已知x坐标
        y: 已知y坐标
        x_new: 新的x坐标
        kind: 插值类型
        bounds_error: 是否在边界外报错
        
    Returns:
        插值结果
    """
    f = interpolate.interp1d(x, y, kind=kind, bounds_error=bounds_error, 
                           fill_value='extrapolate')
    return f(x_new)


def interpolate_2d(x: np.ndarray, y: np.ndarray, z: np.ndarray,
                  x_new: Union[float, np.ndarray], y_new: Union[float, np.ndarray],
                  method: str = 'linear') -> Union[float, np.ndarray]:
    """
    二维插值
    
    Args:
        x, y: 已知坐标网格
        z: 已知函数值
        x_new, y_new: 新的坐标点
        method: 插值方法
        
    Returns:
        插值结果
    """
    from scipy.interpolate import griddata
    
    # 将网格坐标转换为点坐标
    points = np.column_stack([x.ravel(), y.ravel()])
    values = z.ravel()
    
    # 新的查询点
    if np.isscalar(x_new) and np.isscalar(y_new):
        xi = np.array([[x_new, y_new]])
    else:
        xi = np.column_stack([np.array(x_new).ravel(), np.array(y_new).ravel()])
    
    return griddata(points, values, xi, method=method)


def calculate_derivatives(x: np.ndarray, y: np.ndarray, order: int = 1) -> np.ndarray:
    """
    计算数值导数
    
    Args:
        x: 自变量数组
        y: 因变量数组
        order: 导数阶数
        
    Returns:
        导数数组
    """
    if order == 1:
        return np.gradient(y, x)
    elif order == 2:
        dy_dx = np.gradient(y, x)
        return np.gradient(dy_dx, x)
    else:
        raise ValueError(f"不支持的导数阶数: {order}")


def smooth_data(y: np.ndarray, window_length: int = 5, polyorder: int = 2) -> np.ndarray:
    """
    数据平滑
    
    Args:
        y: 输入数据
        window_length: 窗口长度
        polyorder: 多项式阶数
        
    Returns:
        平滑后的数据
    """
    from scipy.signal import savgol_filter
    
    if len(y) < window_length:
        return y
    
    return savgol_filter(y, window_length, polyorder)


def cross_product(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """计算向量叉积"""
    return np.cross(a, b)


def dot_product(a: np.ndarray, b: np.ndarray) -> float:
    """计算向量点积"""
    return np.dot(a, b)


def normalize_vector(v: np.ndarray) -> np.ndarray:
    """向量归一化"""
    norm = np.linalg.norm(v)
    if norm < 1e-15:
        return np.zeros_like(v)
    return v / norm


def angle_between_vectors(v1: np.ndarray, v2: np.ndarray) -> float:
    """计算两向量间夹角"""
    v1_norm = normalize_vector(v1)
    v2_norm = normalize_vector(v2)
    cos_angle = np.clip(np.dot(v1_norm, v2_norm), -1.0, 1.0)
    return np.arccos(cos_angle)


def euler_to_rotation_matrix(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    欧拉角转旋转矩阵
    
    Args:
        roll: 滚转角 [rad]
        pitch: 俯仰角 [rad]
        yaw: 偏航角 [rad]
        
    Returns:
        3x3旋转矩阵
    """
    R_x = rotation_matrix('x', roll)
    R_y = rotation_matrix('y', pitch)
    R_z = rotation_matrix('z', yaw)
    
    return R_z @ R_y @ R_x


def rotation_matrix_to_euler(R: np.ndarray) -> Tuple[float, float, float]:
    """
    旋转矩阵转欧拉角
    
    Args:
        R: 3x3旋转矩阵
        
    Returns:
        roll, pitch, yaw [rad]
    """
    # 提取欧拉角
    pitch = np.arcsin(-R[2, 0])
    
    if np.cos(pitch) > 1e-6:
        roll = np.arctan2(R[2, 1], R[2, 2])
        yaw = np.arctan2(R[1, 0], R[0, 0])
    else:
        # 万向锁情况
        roll = 0.0
        yaw = np.arctan2(-R[0, 1], R[1, 1])
    
    return roll, pitch, yaw