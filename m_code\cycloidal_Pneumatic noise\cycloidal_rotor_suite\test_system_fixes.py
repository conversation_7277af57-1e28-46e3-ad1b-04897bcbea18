#!/usr/bin/env python3
"""
系统修复验证脚本
===============

验证所有修复是否成功，测试系统的核心功能。
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("1. 配置加载测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        
        # 测试基本配置
        basic_config = {
            'n_rpm': 300.0,
            'B': 3,
            'c': 0.06,
            'R_rotor': 0.3,
            'rho': 1.225,
            'enable_3d_uvlm': False,
            'solver_fidelity': 'low',
            'dt_sim': 0.01,
            'T_buildup': 0.05,
            'T_acoustic_record': 0.05,
            'run_acoustics': False,
            'pitch_amplitude': 10.0,
            'pitch_amplitude_top': 10.0,
            'pitch_amplitude_bottom': 10.0,
            'rotor_type': 'cycloidal',
        }
        
        config = ConfigLoader.from_dict(basic_config)
        print("✅ 基本配置加载成功")
        print(f"   RPM: {config.n_rpm}, 桨叶数: {config.B}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_solver_creation():
    """测试求解器创建"""
    print("\n" + "=" * 60)
    print("2. 求解器创建测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
        
        # 创建配置
        config_dict = {
            'n_rpm': 300.0,
            'B': 3,
            'c': 0.06,
            'R_rotor': 0.3,
            'rho': 1.225,
            'enable_3d_uvlm': False,
            'solver_fidelity': 'low',
            'dt_sim': 0.01,
            'T_buildup': 0.05,
            'T_acoustic_record': 0.05,
            'run_acoustics': False,
            'pitch_amplitude': 10.0,
            'pitch_amplitude_top': 10.0,
            'pitch_amplitude_bottom': 10.0,
            'rotor_type': 'cycloidal',
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 测试不同求解器
        solver_types = ['BEMT', 'UVLM', 'LiftingLine']
        
        for solver_type in solver_types:
            try:
                solver = SolverFactory.create_solver(solver_type, config)
                print(f"✅ {solver_type} 求解器创建成功")
                
                # 测试求解器信息
                if hasattr(solver, 'get_solver_info'):
                    info = solver.get_solver_info()
                    print(f"   类型: {info.get('solver_type', 'unknown')}")
                
            except Exception as e:
                print(f"❌ {solver_type} 求解器创建失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 求解器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simulation_creation():
    """测试仿真创建"""
    print("\n" + "=" * 60)
    print("3. 仿真创建测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.simulation import CycloneSimulation
        
        # 创建最小配置
        config_dict = {
            'n_rpm': 300.0,
            'B': 3,
            'c': 0.06,
            'R_rotor': 0.3,
            'rho': 1.225,
            'enable_3d_uvlm': False,
            'solver_fidelity': 'low',
            'dt_sim': 0.01,
            'T_buildup': 0.05,
            'T_acoustic_record': 0.05,
            'run_acoustics': False,
            'pitch_amplitude': 10.0,
            'pitch_amplitude_top': 10.0,
            'pitch_amplitude_bottom': 10.0,
            'rotor_type': 'cycloidal',
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 创建仿真实例
        simulation = CycloneSimulation(config, output_dir="test_output")
        print("✅ 仿真实例创建成功")
        
        # 检查初始化状态
        if hasattr(simulation, 'is_initialized') and simulation.is_initialized:
            print("✅ 仿真初始化完成")
        else:
            print("⚠️  仿真初始化状态未知")
        
        # 检查关键组件
        components = ['aero_solver', 'wake_system', 'data_manager']
        for component in components:
            if hasattr(simulation, component):
                print(f"✅ {component} 组件存在")
            else:
                print(f"⚠️  缺少 {component} 组件")
        
        return True
        
    except Exception as e:
        print(f"❌ 仿真创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_modules():
    """测试学术验证模块"""
    print("\n" + "=" * 60)
    print("4. 学术验证模块测试")
    print("=" * 60)
    
    try:
        # 测试案例模块
        from academic_validation_pro.cases.caradonna_tung import create_caradonna_tung_case
        from academic_validation_pro.cases.hart_ii import create_hart_ii_case
        
        case1 = create_caradonna_tung_case()
        case2 = create_hart_ii_case()
        
        print("✅ Caradonna-Tung案例创建成功")
        print("✅ HART II案例创建成功")
        
        # 测试分析模块
        from academic_validation_pro.analysis.error_metrics import ValidationMetrics
        
        metrics = ValidationMetrics()
        print("✅ 验证度量模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 学术验证模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_workflow():
    """测试基本工作流程"""
    print("\n" + "=" * 60)
    print("5. 基本工作流程测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
        
        # 创建配置
        config_dict = {
            'n_rpm': 300.0,
            'B': 3,
            'c': 0.06,
            'R_rotor': 0.3,
            'rho': 1.225,
            'enable_3d_uvlm': False,
            'solver_fidelity': 'low',
            'dt_sim': 0.01,
            'T_buildup': 0.05,
            'T_acoustic_record': 0.05,
            'run_acoustics': False,
            'pitch_amplitude': 10.0,
            'pitch_amplitude_top': 10.0,
            'pitch_amplitude_bottom': 10.0,
            'rotor_type': 'cycloidal',
        }
        
        config = ConfigLoader.from_dict(config_dict)
        print("✅ 配置创建成功")
        
        # 创建求解器
        solver = SolverFactory.create_solver('BEMT', config)
        print("✅ 求解器创建成功")
        
        # 测试单步求解
        if hasattr(solver, 'solve_step'):
            try:
                result = solver.solve_step(0.0, 0.01)
                print("✅ 单步求解成功")
                
                if 'performance' in result:
                    perf = result['performance']
                    print(f"   推力: {perf.get('thrust', 0):.3f} N")
                    print(f"   功率: {perf.get('power', 0):.3f} W")
                
            except Exception as e:
                print(f"⚠️  单步求解失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 开始系统修复验证测试...")
    print(f"项目路径: {project_root}")
    print()
    
    # 运行所有测试
    tests = [
        test_config_loading,
        test_solver_creation,
        test_simulation_creation,
        test_validation_modules,
        test_basic_workflow,
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 生成测试报告
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    test_names = [
        "配置加载测试",
        "求解器创建测试",
        "仿真创建测试",
        "学术验证模块测试",
        "基本工作流程测试",
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name:20s} {status}")
    
    print("-" * 80)
    print(f"总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统修复成功！")
        print("   系统现在可以正常使用。")
        return 0
    else:
        print(f"\n⚠️  {total-passed} 个测试失败，系统仍需进一步修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())