#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified Import Management - Medium Fidelity BEMT
统一导入管理 - 中保真度BEMT

This module provides unified import management for the medium-fidelity BEMT module.
It handles optional dependencies and provides fallback implementations.

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import sys
import warnings
from typing import Any, Optional

# 标准库导入
import numpy as np
import time
import json
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

# 尝试导入可选依赖
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    warnings.warn("matplotlib未安装，可视化功能将不可用")

try:
    import scipy
    from scipy import interpolate, optimize
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    warnings.warn("scipy未安装，将使用简化的数值方法")

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    warnings.warn("pandas未安装，数据处理功能将受限")

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False
    warnings.warn("PyYAML未安装，YAML配置文件支持将不可用")

# 导出所有常用的导入
__all__ = [
    # 标准库
    'np', 'time', 'json', 'Path', 'dataclass',
    'Dict', 'List', 'Optional', 'Tuple', 'Any', 'Union', 'Callable',
    
    # 可选依赖标志
    'HAS_MATPLOTLIB', 'HAS_SCIPY', 'HAS_PANDAS', 'HAS_YAML',
    
    # 可选依赖（如果可用）
    'plt', 'interpolate', 'optimize', 'pd', 'yaml',
    
    # 工具函数
    'safe_import', 'check_dependencies', 'get_dependency_info'
]

# 为不可用的模块提供占位符
if not HAS_MATPLOTLIB:
    plt = None

if not HAS_SCIPY:
    interpolate = None
    optimize = None

if not HAS_PANDAS:
    pd = None

if not HAS_YAML:
    yaml = None

def safe_import(module_name: str, package: Optional[str] = None) -> Optional[Any]:
    """
    安全导入模块
    
    Parameters:
    -----------
    module_name : str
        模块名称
    package : str, optional
        包名称
        
    Returns:
    --------
    module : Any or None
        导入的模块，如果失败则返回None
    """
    try:
        if package:
            return __import__(f"{package}.{module_name}", fromlist=[module_name])
        else:
            return __import__(module_name)
    except ImportError as e:
        warnings.warn(f"无法导入模块 {module_name}: {str(e)}")
        return None

def check_dependencies() -> Dict[str, bool]:
    """
    检查依赖项状态
    
    Returns:
    --------
    status : dict
        依赖项状态字典
    """
    return {
        'numpy': True,  # 必需依赖
        'matplotlib': HAS_MATPLOTLIB,
        'scipy': HAS_SCIPY,
        'pandas': HAS_PANDAS,
        'yaml': HAS_YAML
    }

def get_dependency_info() -> Dict[str, Any]:
    """
    获取依赖项详细信息
    
    Returns:
    --------
    info : dict
        依赖项详细信息
    """
    info = {
        'python_version': sys.version,
        'numpy_version': np.__version__,
        'dependencies': check_dependencies()
    }
    
    # 添加可选依赖的版本信息
    if HAS_MATPLOTLIB:
        info['matplotlib_version'] = plt.matplotlib.__version__
    
    if HAS_SCIPY:
        info['scipy_version'] = scipy.__version__
    
    if HAS_PANDAS:
        info['pandas_version'] = pd.__version__
    
    if HAS_YAML:
        info['yaml_version'] = getattr(yaml, '__version__', 'unknown')
    
    return info

def print_dependency_status():
    """打印依赖项状态"""
    print("BEMT中保真度模块依赖项状态:")
    print("=" * 40)
    
    deps = check_dependencies()
    for name, available in deps.items():
        status = "✅ 可用" if available else "❌ 不可用"
        print(f"{name:12}: {status}")
    
    print("\n详细信息:")
    info = get_dependency_info()
    print(f"Python版本: {info['python_version']}")
    print(f"NumPy版本:  {info['numpy_version']}")
    
    if HAS_MATPLOTLIB:
        print(f"Matplotlib: {info['matplotlib_version']}")
    if HAS_SCIPY:
        print(f"SciPy版本:  {info['scipy_version']}")
    if HAS_PANDAS:
        print(f"Pandas版本: {info['pandas_version']}")
    if HAS_YAML:
        print(f"PyYAML版本: {info['yaml_version']}")

# 数学常数和函数
PI = np.pi
DEG2RAD = np.pi / 180.0
RAD2DEG = 180.0 / np.pi

def deg2rad(degrees: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """角度转弧度"""
    return degrees * DEG2RAD

def rad2deg(radians: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """弧度转角度"""
    return radians * RAD2DEG

# 物理常数
GRAVITY = 9.81          # 重力加速度 [m/s²]
AIR_DENSITY_SL = 1.225  # 海平面空气密度 [kg/m³]
AIR_TEMP_SL = 288.15    # 海平面温度 [K]
AIR_PRESSURE_SL = 101325.0  # 海平面压力 [Pa]
SOUND_SPEED_SL = 343.0  # 海平面音速 [m/s]
AIR_VISCOSITY_SL = 1.716e-5  # 海平面动力粘度 [Pa·s]

# 数值常数
SMALL_NUMBER = 1e-12    # 小数值
LARGE_NUMBER = 1e12     # 大数值
DEFAULT_TOLERANCE = 1e-6  # 默认收敛容差

# 导出物理常数
__all__.extend([
    'PI', 'DEG2RAD', 'RAD2DEG', 'deg2rad', 'rad2deg',
    'GRAVITY', 'AIR_DENSITY_SL', 'AIR_TEMP_SL', 'AIR_PRESSURE_SL',
    'SOUND_SPEED_SL', 'AIR_VISCOSITY_SL',
    'SMALL_NUMBER', 'LARGE_NUMBER', 'DEFAULT_TOLERANCE'
])

# 初始化时检查关键依赖
if __name__ == "__main__":
    print_dependency_status()
else:
    # 静默检查numpy（必需依赖）
    try:
        import numpy as np
        if not hasattr(np, 'version'):
            raise ImportError("NumPy版本过旧")
    except ImportError as e:
        raise ImportError(f"BEMT中保真度模块需要NumPy: {str(e)}")
