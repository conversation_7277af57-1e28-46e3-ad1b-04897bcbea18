"""
错误处理和验证
=============

提供BEMT求解器的错误处理、输入验证和安全数学运算功能。

核心功能：
- 自定义异常类
- 输入验证函数
- 安全数学运算
- 错误恢复机制
- 警告管理

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Any, Optional, Union, Callable
import warnings
import functools
import traceback


# 自定义异常类
class BEMTError(Exception):
    """BEMT求解器基础异常"""
    pass


class ConvergenceError(BEMTError):
    """收敛失败异常"""
    def __init__(self, message: str, residual: float = None, iterations: int = None):
        super().__init__(message)
        self.residual = residual
        self.iterations = iterations


class ValidationError(BEMTError):
    """输入验证异常"""
    def __init__(self, message: str, parameter_name: str = None, parameter_value: Any = None):
        super().__init__(message)
        self.parameter_name = parameter_name
        self.parameter_value = parameter_value


class PhysicsError(BEMTError):
    """物理模型异常"""
    pass


class NumericalError(BEMTError):
    """数值计算异常"""
    pass


class ConfigurationError(BEMTError):
    """配置错误异常"""
    pass


# 输入验证函数
def validate_input(value: Any, name: str, 
                  value_type: type = None,
                  min_value: float = None,
                  max_value: float = None,
                  must_be_positive: bool = False,
                  must_be_finite: bool = True,
                  allowed_values: list = None) -> None:
    """
    验证输入参数
    
    Args:
        value: 要验证的值
        name: 参数名称
        value_type: 期望的类型
        min_value: 最小值
        max_value: 最大值
        must_be_positive: 是否必须为正数
        must_be_finite: 是否必须为有限数
        allowed_values: 允许的值列表
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 类型检查
    if value_type is not None and not isinstance(value, value_type):
        raise ValidationError(
            f"参数 {name} 类型错误，期望 {value_type.__name__}，实际 {type(value).__name__}",
            name, value
        )
    
    # 数值验证
    if isinstance(value, (int, float, np.number)):
        # 有限性检查
        if must_be_finite and not np.isfinite(value):
            raise ValidationError(f"参数 {name} 必须是有限数", name, value)
        
        # 正数检查
        if must_be_positive and value <= 0:
            raise ValidationError(f"参数 {name} 必须是正数", name, value)
        
        # 范围检查
        if min_value is not None and value < min_value:
            raise ValidationError(f"参数 {name} 不能小于 {min_value}", name, value)
        
        if max_value is not None and value > max_value:
            raise ValidationError(f"参数 {name} 不能大于 {max_value}", name, value)
    
    # 数组验证
    elif isinstance(value, np.ndarray):
        if must_be_finite and not np.all(np.isfinite(value)):
            raise ValidationError(f"参数 {name} 包含非有限值", name, value)
        
        if must_be_positive and np.any(value <= 0):
            raise ValidationError(f"参数 {name} 必须全部为正数", name, value)
        
        if min_value is not None and np.any(value < min_value):
            raise ValidationError(f"参数 {name} 不能小于 {min_value}", name, value)
        
        if max_value is not None and np.any(value > max_value):
            raise ValidationError(f"参数 {name} 不能大于 {max_value}", name, value)
    
    # 允许值检查
    if allowed_values is not None and value not in allowed_values:
        raise ValidationError(
            f"参数 {name} 值 {value} 不在允许范围内: {allowed_values}",
            name, value
        )


def validate_array_shape(array: np.ndarray, name: str, 
                        expected_shape: tuple = None,
                        min_dimensions: int = None,
                        max_dimensions: int = None) -> None:
    """
    验证数组形状
    
    Args:
        array: 要验证的数组
        name: 参数名称
        expected_shape: 期望的形状
        min_dimensions: 最小维度数
        max_dimensions: 最大维度数
    """
    if not isinstance(array, np.ndarray):
        raise ValidationError(f"参数 {name} 必须是numpy数组", name, array)
    
    if expected_shape is not None and array.shape != expected_shape:
        raise ValidationError(
            f"参数 {name} 形状错误，期望 {expected_shape}，实际 {array.shape}",
            name, array
        )
    
    if min_dimensions is not None and array.ndim < min_dimensions:
        raise ValidationError(
            f"参数 {name} 维度不足，至少需要 {min_dimensions} 维",
            name, array
        )
    
    if max_dimensions is not None and array.ndim > max_dimensions:
        raise ValidationError(
            f"参数 {name} 维度过多，最多允许 {max_dimensions} 维",
            name, array
        )


# 安全数学运算
def safe_divide(numerator: Union[float, np.ndarray], 
               denominator: Union[float, np.ndarray],
               default_value: float = 0.0,
               min_denominator: float = 1e-15) -> Union[float, np.ndarray]:
    """
    安全除法运算
    
    Args:
        numerator: 分子
        denominator: 分母
        default_value: 分母为零时的默认值
        min_denominator: 最小分母值
        
    Returns:
        除法结果
    """
    # 处理标量情况
    if np.isscalar(denominator):
        if abs(denominator) < min_denominator:
            return default_value
        return numerator / denominator
    
    # 处理数组情况
    result = np.full_like(denominator, default_value, dtype=float)
    valid_mask = np.abs(denominator) >= min_denominator
    
    if np.any(valid_mask):
        if np.isscalar(numerator):
            result[valid_mask] = numerator / denominator[valid_mask]
        else:
            result[valid_mask] = numerator[valid_mask] / denominator[valid_mask]
    
    return result


def safe_sqrt(value: Union[float, np.ndarray], 
             default_value: float = 0.0) -> Union[float, np.ndarray]:
    """
    安全开方运算
    
    Args:
        value: 输入值
        default_value: 负数时的默认值
        
    Returns:
        开方结果
    """
    if np.isscalar(value):
        return np.sqrt(max(0.0, value)) if value >= 0 else default_value
    
    result = np.full_like(value, default_value, dtype=float)
    valid_mask = value >= 0
    
    if np.any(valid_mask):
        result[valid_mask] = np.sqrt(value[valid_mask])
    
    return result


def safe_log(value: Union[float, np.ndarray],
            default_value: float = -10.0,
            min_value: float = 1e-15) -> Union[float, np.ndarray]:
    """
    安全对数运算
    
    Args:
        value: 输入值
        default_value: 非正数时的默认值
        min_value: 最小有效值
        
    Returns:
        对数结果
    """
    if np.isscalar(value):
        return np.log(max(min_value, value)) if value > 0 else default_value
    
    result = np.full_like(value, default_value, dtype=float)
    valid_mask = value > min_value
    
    if np.any(valid_mask):
        result[valid_mask] = np.log(value[valid_mask])
    
    return result


def safe_arcsin(value: Union[float, np.ndarray],
               default_value: float = 0.0) -> Union[float, np.ndarray]:
    """
    安全反正弦运算
    
    Args:
        value: 输入值
        default_value: 超出范围时的默认值
        
    Returns:
        反正弦结果
    """
    if np.isscalar(value):
        if -1.0 <= value <= 1.0:
            return np.arcsin(value)
        else:
            return default_value
    
    # 限制到有效范围
    clipped_value = np.clip(value, -1.0, 1.0)
    return np.arcsin(clipped_value)


def safe_arccos(value: Union[float, np.ndarray],
               default_value: float = 0.0) -> Union[float, np.ndarray]:
    """
    安全反余弦运算
    
    Args:
        value: 输入值
        default_value: 超出范围时的默认值
        
    Returns:
        反余弦结果
    """
    if np.isscalar(value):
        if -1.0 <= value <= 1.0:
            return np.arccos(value)
        else:
            return default_value
    
    # 限制到有效范围
    clipped_value = np.clip(value, -1.0, 1.0)
    return np.arccos(clipped_value)


# 错误处理装饰器
def handle_errors(default_return=None, 
                 exceptions=(Exception,),
                 log_errors=True):
    """
    错误处理装饰器
    
    Args:
        default_return: 出错时的默认返回值
        exceptions: 要捕获的异常类型
        log_errors: 是否记录错误
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                if log_errors:
                    error_msg = f"函数 {func.__name__} 执行失败: {str(e)}"
                    warnings.warn(error_msg)
                    
                    # 在调试模式下打印完整的错误堆栈
                    if hasattr(args[0], 'config') and args[0].config.get('debug_mode', False):
                        traceback.print_exc()
                
                return default_return
        
        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3, 
                    delay: float = 0.1,
                    exceptions=(Exception,)):
    """
    失败重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟
        exceptions: 要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        warnings.warn(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，重试中...")
                        if delay > 0:
                            import time
                            time.sleep(delay)
                    else:
                        break
            
            # 所有重试都失败了
            raise last_exception
        
        return wrapper
    return decorator


# 警告管理
class WarningManager:
    """警告管理器"""
    
    _warning_counts = {}
    _max_warnings = 10
    
    @classmethod
    def numerical_warning(cls, message: str, category=UserWarning):
        """发出数值计算警告"""
        cls._issue_warning(message, category, "numerical")
    
    @classmethod
    def physics_warning(cls, message: str, category=UserWarning):
        """发出物理模型警告"""
        cls._issue_warning(message, category, "physics")
    
    @classmethod
    def convergence_warning(cls, message: str, category=UserWarning):
        """发出收敛警告"""
        cls._issue_warning(message, category, "convergence")
    
    @classmethod
    def _issue_warning(cls, message: str, category, warning_type: str):
        """发出警告（带频率控制）"""
        key = f"{warning_type}:{message}"
        
        if key not in cls._warning_counts:
            cls._warning_counts[key] = 0
        
        cls._warning_counts[key] += 1
        
        if cls._warning_counts[key] <= cls._max_warnings:
            warnings.warn(message, category)
        elif cls._warning_counts[key] == cls._max_warnings + 1:
            warnings.warn(f"警告 '{message}' 已达到最大次数，后续将被抑制", category)
    
    @classmethod
    def reset_warning_counts(cls):
        """重置警告计数"""
        cls._warning_counts.clear()
    
    @classmethod
    def set_max_warnings(cls, max_warnings: int):
        """设置最大警告次数"""
        cls._max_warnings = max_warnings
    
    @classmethod
    def get_warning_summary(cls) -> dict:
        """获取警告摘要"""
        return cls._warning_counts.copy()


# 收敛检查函数
def check_convergence(residual: float, tolerance: float, 
                     iteration: int, max_iterations: int) -> bool:
    """
    检查收敛性
    
    Args:
        residual: 当前残差
        tolerance: 收敛容差
        iteration: 当前迭代次数
        max_iterations: 最大迭代次数
        
    Returns:
        是否收敛
        
    Raises:
        ConvergenceError: 达到最大迭代次数但未收敛
    """
    if residual < tolerance:
        return True
    
    if iteration >= max_iterations:
        raise ConvergenceError(
            f"达到最大迭代次数 {max_iterations}，残差 {residual:.2e} > 容差 {tolerance:.2e}",
            residual, iteration
        )
    
    return False


# 数值稳定性检查
def check_numerical_stability(values: np.ndarray, 
                            name: str = "values",
                            max_value: float = 1e6,
                            min_value: float = -1e6) -> bool:
    """
    检查数值稳定性
    
    Args:
        values: 要检查的数值
        name: 数值名称
        max_value: 最大允许值
        min_value: 最小允许值
        
    Returns:
        是否稳定
        
    Raises:
        NumericalError: 数值不稳定时抛出
    """
    if not np.all(np.isfinite(values)):
        raise NumericalError(f"{name} 包含非有限值 (NaN或Inf)")
    
    if np.any(values > max_value) or np.any(values < min_value):
        raise NumericalError(f"{name} 超出数值稳定范围 [{min_value}, {max_value}]")
    
    return True


# 物理合理性检查
def check_physical_validity(Cl: float, Cd: float, alpha_deg: float) -> bool:
    """
    检查气动系数的物理合理性
    
    Args:
        Cl: 升力系数
        Cd: 阻力系数
        alpha_deg: 攻角 [度]
        
    Returns:
        是否物理合理
        
    Raises:
        PhysicsError: 物理不合理时抛出
    """
    # 升力系数合理性
    if abs(Cl) > 5.0:
        raise PhysicsError(f"升力系数 {Cl:.3f} 超出合理范围 [-5, 5]")
    
    # 阻力系数合理性
    if Cd < 0 or Cd > 2.0:
        raise PhysicsError(f"阻力系数 {Cd:.3f} 超出合理范围 [0, 2]")
    
    # 攻角合理性
    if abs(alpha_deg) > 90:
        WarningManager.physics_warning(f"攻角 {alpha_deg:.1f}° 可能过大")
    
    return True