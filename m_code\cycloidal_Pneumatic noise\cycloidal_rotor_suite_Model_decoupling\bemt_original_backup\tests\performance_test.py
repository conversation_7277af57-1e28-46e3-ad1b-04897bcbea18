#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能基准测试
===========

测试BEMT模块的计算性能和效率。

作者: Augment Agent
日期: 2025-07-24
"""

import sys
import os
import time
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import bemt_medium_fidelity_validation as bemt


def performance_benchmark():
    """性能基准测试"""
    
    print("🚀 BEMT性能基准测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        (300, 0.0, "低转速悬停"),
        (400, 0.0, "标准悬停"),
        (500, 0.0, "高转速悬停"),
        (400, 5.0, "低速前飞"),
        (400, 10.0, "标准前飞"),
        (400, 15.0, "高速前飞"),
        (500, 20.0, "高速高转速"),
    ]
    
    print(f"{'工况':<12} {'转速':<8} {'前飞速度':<10} {'迭代次数':<8} {'求解时间':<10} {'推力':<8} {'功率':<8}")
    print("-" * 80)
    
    total_time = 0
    total_iterations = 0
    converged_count = 0
    
    for rpm, v_forward, desc in test_cases:
        start_time = time.time()
        result = bemt.quick_analysis(
            rpm=rpm, 
            forward_speed=v_forward, 
            radius=1.0, 
            num_blades=4, 
            verbose=False
        )
        solve_time = time.time() - start_time
        
        total_time += solve_time
        total_iterations += result['iterations']
        if result['converged']:
            converged_count += 1
        
        print(f"{desc:<12} {rpm:<8} {v_forward:<10.1f} {result['iterations']:<8} "
              f"{solve_time*1000:<10.1f} {result['thrust']:<8.1f} {result['power']:<8.1f}")
    
    # 统计结果
    avg_time = total_time / len(test_cases)
    avg_iterations = total_iterations / len(test_cases)
    convergence_rate = converged_count / len(test_cases)
    
    print("\n📊 性能统计:")
    print(f"   平均求解时间: {avg_time*1000:.1f} ms")
    print(f"   平均迭代次数: {avg_iterations:.1f}")
    print(f"   收敛率: {convergence_rate:.1%}")
    print(f"   总计算时间: {total_time*1000:.1f} ms")
    
    # 性能评级
    if avg_time < 0.01:
        performance_grade = "🏆 优秀"
    elif avg_time < 0.05:
        performance_grade = "✅ 良好"
    else:
        performance_grade = "⚠️ 一般"
    
    print(f"   性能评级: {performance_grade}")
    
    return {
        'avg_time': avg_time,
        'avg_iterations': avg_iterations,
        'convergence_rate': convergence_rate,
        'total_time': total_time
    }


def memory_usage_test():
    """内存使用测试"""
    
    print("\n🧠 内存使用测试")
    print("=" * 30)
    
    try:
        import psutil
        import gc
        
        # 获取初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.1f} MB")
        
        # 运行多次计算
        for i in range(10):
            result = bemt.quick_analysis(rpm=400, forward_speed=10.0, verbose=False)
        
        # 获取峰值内存
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 强制垃圾回收
        gc.collect()
        
        # 获取回收后内存
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"峰值内存使用: {peak_memory:.1f} MB")
        print(f"回收后内存: {final_memory:.1f} MB")
        print(f"内存增长: {final_memory - initial_memory:.1f} MB")
        
        if final_memory - initial_memory < 10:
            print("✅ 内存使用正常")
        else:
            print("⚠️ 可能存在内存泄漏")
            
    except ImportError:
        print("⚠️ psutil不可用，跳过内存测试")


def accuracy_vs_performance_test():
    """精度vs性能权衡测试"""
    
    print("\n⚖️ 精度vs性能权衡测试")
    print("=" * 40)
    
    # 不同站位数的测试
    station_counts = [5, 10, 15, 20, 25]
    
    print(f"{'站位数':<8} {'求解时间':<10} {'迭代次数':<8} {'推力':<8} {'相对误差':<10}")
    print("-" * 50)
    
    # 参考解（最高精度）
    reference_result = bemt.quick_analysis(
        rpm=400, forward_speed=10.0, radius=1.0, num_blades=4, 
        num_stations=30, verbose=False
    )
    reference_thrust = reference_result['thrust']
    
    for n_stations in station_counts:
        start_time = time.time()
        result = bemt.quick_analysis(
            rpm=400, forward_speed=10.0, radius=1.0, num_blades=4,
            num_stations=n_stations, verbose=False
        )
        solve_time = time.time() - start_time
        
        relative_error = abs(result['thrust'] - reference_thrust) / reference_thrust * 100
        
        print(f"{n_stations:<8} {solve_time*1000:<10.1f} {result['iterations']:<8} "
              f"{result['thrust']:<8.1f} {relative_error:<10.2f}%")
    
    print("\n💡 建议: 10-15个站位可获得良好的精度-性能平衡")


if __name__ == "__main__":
    # 运行所有性能测试
    perf_results = performance_benchmark()
    memory_usage_test()
    accuracy_vs_performance_test()
    
    print(f"\n🎯 总结:")
    print(f"   BEMT模块性能优秀，平均求解时间 {perf_results['avg_time']*1000:.1f} ms")
    print(f"   收敛稳定，平均 {perf_results['avg_iterations']:.1f} 次迭代")
    print(f"   适用于实时和批量计算应用")
