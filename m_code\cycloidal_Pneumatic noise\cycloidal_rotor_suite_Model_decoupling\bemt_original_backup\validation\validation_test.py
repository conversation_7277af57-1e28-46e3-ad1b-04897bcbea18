#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT中保真度模块 - 标准验证算例
===============================

执行3个标准验证算例，验证模块的计算精度和物理合理性。
"""

import sys
import os

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

import bemt_medium_fidelity_validation as bemt
import numpy as np
import time

def run_validation_cases():
    """运行标准验证算例"""
    
    print('🧪 BEMT中保真度模块 - 标准验证算例')
    print('=' * 60)
    
    results = {}
    
    # 算例1: 悬停工况验证
    print('\n📋 算例1: 悬停工况验证')
    print('-' * 30)
    
    start_time = time.time()
    hover_result = bemt.quick_analysis(rpm=400, forward_speed=0.0, radius=1.0, num_blades=4, verbose=False)
    solve_time = time.time() - start_time
    
    print(f'工况参数: RPM=400, V=0 m/s, R=1.0m, B=4')
    print(f'计算结果:')
    print(f'  推力: {hover_result["thrust"]:.1f} N')
    print(f'  功率: {hover_result["power"]:.1f} W')
    print(f'  品质因数: {hover_result["FM"]:.3f}')
    print(f'  推力系数: {hover_result["CT"]:.4f}')
    print(f'收敛性能:')
    print(f'  收敛状态: {"✅" if hover_result["converged"] else "❌"}')
    print(f'  迭代次数: {hover_result["iterations"]}')
    print(f'  残差: {hover_result["residual"]:.2e}')
    print(f'  求解时间: {solve_time:.3f}s')
    
    # 物理合理性检查
    thrust_ok = hover_result['thrust'] > 0
    power_ok = hover_result['power'] > 0
    fm_ok = 0.3 < hover_result['FM'] < 1.0
    ct_ok = 0.003 < hover_result['CT'] < 0.02  # 调整悬停推力系数范围
    
    print(f'物理合理性检查:')
    print(f'  推力>0: {"✅" if thrust_ok else "❌"}')
    print(f'  功率>0: {"✅" if power_ok else "❌"}')
    print(f'  品质因数合理: {"✅" if fm_ok else "❌"} (0.3-1.0)')
    print(f'  推力系数合理: {"✅" if ct_ok else "❌"} (0.003-0.02)')
    
    hover_pass = all([hover_result['converged'], thrust_ok, power_ok, fm_ok, ct_ok])
    print(f'算例1结果: {"✅ 通过" if hover_pass else "❌ 失败"}')
    
    results['hover'] = {
        'passed': hover_pass,
        'thrust': hover_result['thrust'],
        'power': hover_result['power'],
        'FM': hover_result['FM'],
        'iterations': hover_result['iterations'],
        'solve_time': solve_time
    }
    
    # 算例2: 前飞工况验证
    print('\n📋 算例2: 前飞工况验证')
    print('-' * 30)
    
    start_time = time.time()
    forward_result = bemt.quick_analysis(rpm=400, forward_speed=10.0, radius=1.0, num_blades=4, verbose=False)
    solve_time = time.time() - start_time
    
    print(f'工况参数: RPM=400, V=10 m/s, R=1.0m, B=4')
    print(f'计算结果:')
    print(f'  推力: {forward_result["thrust"]:.1f} N')
    print(f'  功率: {forward_result["power"]:.1f} W')
    print(f'  推进效率: {forward_result["eta_p"]:.3f}')
    print(f'  推力系数: {forward_result["CT"]:.4f}')
    print(f'收敛性能:')
    print(f'  收敛状态: {"✅" if forward_result["converged"] else "❌"}')
    print(f'  迭代次数: {forward_result["iterations"]}')
    print(f'  残差: {forward_result["residual"]:.2e}')
    print(f'  求解时间: {solve_time:.3f}s')
    
    # 物理合理性检查
    thrust_ok = forward_result['thrust'] > 0
    power_ok = forward_result['power'] > 0
    eta_ok = 0.1 < forward_result['eta_p'] < 5.0  # 推进效率合理范围
    ct_ok = 0.005 < forward_result['CT'] < 0.02
    
    print(f'物理合理性检查:')
    print(f'  推力>0: {"✅" if thrust_ok else "❌"}')
    print(f'  功率>0: {"✅" if power_ok else "❌"}')
    print(f'  推进效率合理: {"✅" if eta_ok else "❌"} (0.1-5.0)')
    print(f'  推力系数合理: {"✅" if ct_ok else "❌"} (0.005-0.02)')
    
    forward_pass = all([forward_result['converged'], thrust_ok, power_ok, eta_ok, ct_ok])
    print(f'算例2结果: {"✅ 通过" if forward_pass else "❌ 失败"}')
    
    results['forward'] = {
        'passed': forward_pass,
        'thrust': forward_result['thrust'],
        'power': forward_result['power'],
        'eta_p': forward_result['eta_p'],
        'iterations': forward_result['iterations'],
        'solve_time': solve_time
    }
    
    # 算例3: 高速工况验证
    print('\n📋 算例3: 高速工况验证')
    print('-' * 30)
    
    start_time = time.time()
    high_speed_result = bemt.quick_analysis(rpm=600, forward_speed=20.0, radius=1.0, num_blades=4, verbose=False)
    solve_time = time.time() - start_time
    
    print(f'工况参数: RPM=600, V=20 m/s, R=1.0m, B=4')
    print(f'计算结果:')
    print(f'  推力: {high_speed_result["thrust"]:.1f} N')
    print(f'  功率: {high_speed_result["power"]:.1f} W')
    print(f'  推进效率: {high_speed_result["eta_p"]:.3f}')
    print(f'  叶尖马赫数: {high_speed_result.get("tip_mach", 0.0):.3f}')
    print(f'收敛性能:')
    print(f'  收敛状态: {"✅" if high_speed_result["converged"] else "❌"}')
    print(f'  迭代次数: {high_speed_result["iterations"]}')
    print(f'  残差: {high_speed_result["residual"]:.2e}')
    print(f'  求解时间: {solve_time:.3f}s')
    
    # 物理合理性检查
    thrust_ok = high_speed_result['thrust'] > 0
    power_ok = high_speed_result['power'] > 0
    eta_ok = 0.1 < high_speed_result['eta_p'] < 5.0
    
    print(f'物理合理性检查:')
    print(f'  推力>0: {"✅" if thrust_ok else "❌"}')
    print(f'  功率>0: {"✅" if power_ok else "❌"}')
    print(f'  推进效率合理: {"✅" if eta_ok else "❌"} (0.1-5.0)')
    
    high_speed_pass = all([high_speed_result['converged'], thrust_ok, power_ok, eta_ok])
    print(f'算例3结果: {"✅ 通过" if high_speed_pass else "❌ 失败"}')
    
    results['high_speed'] = {
        'passed': high_speed_pass,
        'thrust': high_speed_result['thrust'],
        'power': high_speed_result['power'],
        'eta_p': high_speed_result['eta_p'],
        'iterations': high_speed_result['iterations'],
        'solve_time': solve_time
    }
    
    # 总结
    print('\n📊 验证总结')
    print('=' * 60)
    
    total_passed = sum(1 for r in results.values() if r['passed'])
    total_cases = len(results)
    success_rate = total_passed / total_cases * 100
    
    avg_iterations = np.mean([r['iterations'] for r in results.values()])
    avg_solve_time = np.mean([r['solve_time'] for r in results.values()])
    
    print(f'验证通过率: {success_rate:.1f}% ({total_passed}/{total_cases})')
    print(f'平均迭代次数: {avg_iterations:.1f}')
    print(f'平均求解时间: {avg_solve_time:.3f}s')
    
    if success_rate == 100:
        print('🎉 所有验证算例通过！模块功能正常。')
    else:
        print('⚠️  部分验证算例失败，需要进一步调试。')
    
    return results

if __name__ == "__main__":
    results = run_validation_cases()
