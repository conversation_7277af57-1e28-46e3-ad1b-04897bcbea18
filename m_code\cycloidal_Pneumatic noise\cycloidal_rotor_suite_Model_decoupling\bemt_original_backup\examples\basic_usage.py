"""
基本使用示例
===========

展示BEMT中保真度模块的基本使用方法。

功能演示：
- 求解器创建和配置
- 基本求解流程
- 结果分析和可视化
- 性能监控

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any
import time

# 导入BEMT模块
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.bemt_solver import BEMTSolver
from core.solver_factory import SolverFactory
from utils.config import ConfigManager
from utils.error_handling import ValidationError


def run_basic_example() -> Dict[str, Any]:
    """
    运行基本使用示例
    
    Returns:
        示例结果字典
    """
    print("=" * 60)
    print("BEMT中保真度模块 - 基本使用示例")
    print("=" * 60)
    
    # 1. 创建配置
    print("\n1. 创建配置...")
    config = create_basic_config()
    print(f"   配置参数: {len(config.to_dict())} 个")
    
    # 2. 创建求解器
    print("\n2. 创建BEMT求解器...")
    solver = create_solver(config)
    print(f"   求解器类型: {solver.solver_type}")
    print(f"   保真度级别: {solver.fidelity_level}")
    
    # 3. 运行仿真
    print("\n3. 运行时域仿真...")
    results = run_time_simulation(solver)
    print(f"   仿真步数: {len(results['time_history'])}")
    print(f"   平均求解时间: {results['average_solve_time']:.4f}s")
    
    # 4. 分析结果
    print("\n4. 分析结果...")
    analysis = analyze_results(results)
    print(f"   平均推力: {analysis['mean_thrust']:.2f} N")
    print(f"   平均功率: {analysis['mean_power']:.2f} W")
    print(f"   品质因数: {analysis['mean_figure_of_merit']:.3f}")
    
    # 5. 生成报告
    print("\n5. 生成性能报告...")
    report = generate_report(solver, results, analysis)
    
    # 6. 可视化结果（可选）
    if True:  # 设置为True以启用绘图
        print("\n6. 生成可视化图表...")
        create_plots(results, analysis)
    
    print("\n" + "=" * 60)
    print("基本示例运行完成！")
    print("=" * 60)
    
    return {
        'config': config.to_dict(),
        'results': results,
        'analysis': analysis,
        'report': report
    }


def create_basic_config() -> ConfigManager:
    """创建基本配置"""
    config_dict = {
        # 基本参数
        'R_rotor': 0.5,          # 转子半径 [m]
        'B': 4,                  # 桨叶数
        'c': 0.08,               # 弦长 [m]
        'omega_rotor': 150.0,    # 角速度 [rad/s]
        'rho': 1.225,            # 空气密度 [kg/m³]
        
        # BEMT参数
        'bemt_n_elements': 20,
        'bemt_max_iterations': 50,
        'bemt_tolerance': 1e-4,
        'relaxation_factor': 0.5,
        
        # 物理模型
        'enable_tip_loss': True,
        'enable_hub_loss': True,
        'enable_dynamic_stall': False,  # 基本示例中禁用
        
        # 转子类型
        'rotor_type': 'cycloidal',
        'pitch_amplitude': 12.0,  # 度
        'pitch_phase_offset': 0.0,
        'pitch_bias_angle': 2.0,
        
        # 仿真参数
        'dt': 0.002,
        'application_type': 'general'
    }
    
    return ConfigManager(config_dict)


def create_solver(config: ConfigManager) -> BEMTSolver:
    """创建求解器"""
    try:
        # 使用工厂创建求解器
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        # 验证配置
        if not solver.validate_configuration():
            raise ValidationError("求解器配置验证失败")
        
        return solver
        
    except Exception as e:
        print(f"求解器创建失败: {e}")
        raise


def run_time_simulation(solver: BEMTSolver, 
                       t_end: float = 0.5, 
                       dt: float = None) -> Dict[str, Any]:
    """运行时域仿真"""
    if dt is None:
        dt = solver.config.get('dt', 0.002)
    
    # 初始化存储
    time_history = []
    thrust_history = []
    power_history = []
    torque_history = []
    solve_times = []
    convergence_history = []
    
    # 时间循环
    t = 0.0
    step_count = 0
    
    print(f"   仿真时间: 0.0 - {t_end:.2f}s, 步长: {dt:.4f}s")
    
    while t < t_end:
        step_start_time = time.time()
        
        try:
            # 执行求解步
            result = solver.solve_step(t, dt)
            
            # 记录结果
            time_history.append(t)
            thrust_history.append(result['performance']['thrust'])
            power_history.append(result['performance']['power'])
            torque_history.append(result['performance']['torque'])
            convergence_history.append(result['convergence_info']['converged'])
            
            # 记录求解时间
            solve_time = time.time() - step_start_time
            solve_times.append(solve_time)
            
            # 进度显示
            if step_count % 50 == 0:
                progress = t / t_end * 100
                print(f"   进度: {progress:.1f}% (t={t:.3f}s)")
            
        except Exception as e:
            print(f"   求解步失败 (t={t:.3f}s): {e}")
            break
        
        t += dt
        step_count += 1
    
    # 计算统计信息
    convergence_rate = sum(convergence_history) / len(convergence_history) * 100
    
    return {
        'time_history': np.array(time_history),
        'thrust_history': np.array(thrust_history),
        'power_history': np.array(power_history),
        'torque_history': np.array(torque_history),
        'solve_times': np.array(solve_times),
        'convergence_history': convergence_history,
        'convergence_rate': convergence_rate,
        'average_solve_time': np.mean(solve_times),
        'total_steps': step_count
    }


def analyze_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """分析仿真结果"""
    thrust = results['thrust_history']
    power = results['power_history']
    torque = results['torque_history']
    
    # 基本统计
    analysis = {
        'mean_thrust': np.mean(thrust),
        'std_thrust': np.std(thrust),
        'max_thrust': np.max(thrust),
        'min_thrust': np.min(thrust),
        
        'mean_power': np.mean(power),
        'std_power': np.std(power),
        'max_power': np.max(power),
        'min_power': np.min(power),
        
        'mean_torque': np.mean(torque),
        'std_torque': np.std(torque),
    }
    
    # 基本参数
    rho = 1.225  # 空气密度
    omega = 150.0  # rad/s
    R = 0.5  # m
    tip_speed = omega * R
    A = np.pi * R**2  # 桨盘面积
    
    # 计算品质因数
    if analysis['mean_power'] > 1e-6:
        # 简化的品质因数计算
        ideal_power = analysis['mean_thrust']**1.5 / np.sqrt(2 * rho * A)
        analysis['mean_figure_of_merit'] = ideal_power / analysis['mean_power']
    else:
        analysis['mean_figure_of_merit'] = 0.0
    
    # 性能系数
    analysis['mean_CT'] = analysis['mean_thrust'] / (rho * A * tip_speed**2)
    analysis['mean_CP'] = analysis['mean_power'] / (rho * A * tip_speed**3)
    
    # 振动分析
    analysis['thrust_vibration'] = analysis['std_thrust'] / analysis['mean_thrust'] if analysis['mean_thrust'] > 0 else 0
    analysis['power_vibration'] = analysis['std_power'] / analysis['mean_power'] if analysis['mean_power'] > 0 else 0
    
    return analysis


def generate_report(solver: BEMTSolver, results: Dict[str, Any], 
                   analysis: Dict[str, Any]) -> str:
    """生成性能报告"""
    report = f"""
BEMT中保真度模块 - 基本示例报告
================================

求解器信息:
- 类型: {solver.solver_type}
- 保真度: {solver.fidelity_level}
- 桨叶数: {solver.B}
- 叶素数: {solver.n_elements}

仿真参数:
- 总步数: {results['total_steps']}
- 收敛率: {results['convergence_rate']:.1f}%
- 平均求解时间: {results['average_solve_time']:.4f}s

性能结果:
- 平均推力: {analysis['mean_thrust']:.2f} ± {analysis['std_thrust']:.2f} N
- 平均功率: {analysis['mean_power']:.2f} ± {analysis['std_power']:.2f} W
- 平均转矩: {analysis['mean_torque']:.3f} ± {analysis['std_torque']:.3f} N·m

无量纲系数:
- 推力系数 CT: {analysis['mean_CT']:.4f}
- 功率系数 CP: {analysis['mean_CP']:.4f}
- 品质因数 FM: {analysis['mean_figure_of_merit']:.3f}

振动水平:
- 推力振动: {analysis['thrust_vibration']:.1%}
- 功率振动: {analysis['power_vibration']:.1%}

求解器性能统计:
{solver.get_performance_stats()}
"""
    
    return report


def create_plots(results: Dict[str, Any], analysis: Dict[str, Any]):
    """创建可视化图表"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('BEMT中保真度模块 - 基本示例结果', fontsize=14)
        
        time = results['time_history']
        
        # 推力时间历程
        axes[0, 0].plot(time, results['thrust_history'], 'b-', linewidth=1.5)
        axes[0, 0].axhline(analysis['mean_thrust'], color='r', linestyle='--', alpha=0.7)
        axes[0, 0].set_xlabel('时间 [s]')
        axes[0, 0].set_ylabel('推力 [N]')
        axes[0, 0].set_title('推力时间历程')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 功率时间历程
        axes[0, 1].plot(time, results['power_history'], 'g-', linewidth=1.5)
        axes[0, 1].axhline(analysis['mean_power'], color='r', linestyle='--', alpha=0.7)
        axes[0, 1].set_xlabel('时间 [s]')
        axes[0, 1].set_ylabel('功率 [W]')
        axes[0, 1].set_title('功率时间历程')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 推力-功率关系
        axes[1, 0].scatter(results['thrust_history'], results['power_history'], 
                          alpha=0.6, s=10)
        axes[1, 0].set_xlabel('推力 [N]')
        axes[1, 0].set_ylabel('功率 [W]')
        axes[1, 0].set_title('推力-功率关系')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 求解时间分布
        axes[1, 1].hist(results['solve_times'] * 1000, bins=20, alpha=0.7, color='orange')
        axes[1, 1].axvline(analysis.get('mean_solve_time', np.mean(results['solve_times'])) * 1000, 
                          color='r', linestyle='--')
        axes[1, 1].set_xlabel('求解时间 [ms]')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('求解时间分布')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = 'bemt_basic_example_results.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"   图表已保存: {output_file}")
        
        # 显示图片（如果在交互环境中）
        plt.show()
        
    except Exception as e:
        print(f"   绘图失败: {e}")


def main():
    """主函数"""
    try:
        # 运行基本示例
        example_results = run_basic_example()
        
        # 打印报告
        print("\n" + example_results['report'])
        
        return example_results
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()