"""
🔧 统一导入管理工具
用于处理可选依赖和动态导入，避免重复的导入逻辑
"""

import importlib
import warnings
from typing import Any, Optional, Dict, List


class ImportManager:
    """统一导入管理器"""
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._failed_imports: List[str] = []
    
    def safe_import(self, module_path: str, class_name: Optional[str] = None, 
                   fallback: Any = None, silent: bool = False) -> Any:
        """
        安全导入模块或类
        
        Args:
            module_path: 模块路径
            class_name: 类名（可选）
            fallback: 导入失败时的备用值
            silent: 是否静默失败
            
        Returns:
            导入的模块或类，失败时返回fallback
        """
        cache_key = f"{module_path}.{class_name}" if class_name else module_path
        
        # 检查缓存
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # 检查是否已知失败
        if cache_key in self._failed_imports:
            return fallback
        
        try:
            module = importlib.import_module(module_path)
            
            if class_name:
                result = getattr(module, class_name)
            else:
                result = module
            
            # 缓存成功的导入
            self._cache[cache_key] = result
            return result
            
        except (ImportError, AttributeError) as e:
            # 记录失败的导入
            self._failed_imports.append(cache_key)
            
            if not silent:
                warnings.warn(
                    f"Failed to import {cache_key}: {e}. Using fallback.",
                    ImportWarning
                )
            
            return fallback
    
    def check_optional_dependency(self, package_name: str) -> bool:
        """检查可选依赖是否可用"""
        try:
            importlib.import_module(package_name)
            return True
        except ImportError:
            return False
    
    def get_import_status(self) -> Dict[str, Any]:
        """获取导入状态报告"""
        return {
            'successful_imports': len(self._cache),
            'failed_imports': len(self._failed_imports),
            'failed_modules': self._failed_imports.copy()
        }


# 全局导入管理器实例
_import_manager = ImportManager()

# 便捷函数
def safe_import(module_path: str, class_name: Optional[str] = None, 
               fallback: Any = None, silent: bool = False) -> Any:
    """便捷的安全导入函数"""
    return _import_manager.safe_import(module_path, class_name, fallback, silent)

def check_dependency(package_name: str) -> bool:
    """检查依赖是否可用"""
    return _import_manager.check_optional_dependency(package_name)

def get_import_report() -> Dict[str, Any]:
    """获取导入状态报告"""
    return _import_manager.get_import_status()


# 常用的可选导入
def get_torch():
    """获取PyTorch，如果不可用则返回None"""
    return safe_import('torch', silent=True)

def get_cuda_support():
    """检查CUDA支持"""
    torch = get_torch()
    if torch is None:
        return False
    return torch.cuda.is_available()

def get_adaptive_interpolator():
    """获取自适应翼型插值器"""
    return safe_import(
        'cyclone_sim.core.aerodynamics.adaptive_airfoil_interpolator',
        'AdaptiveAirfoilInterpolator',
        fallback=None,
        silent=True
    )

def get_gpu_solver():
    """获取GPU求解器"""
    return safe_import(
        'cyclone_sim.core.solvers.gpu_solver',
        'GPUSolver',
        fallback=None,
        silent=True
    )


# 导入验证装饰器
def require_dependency(package_name: str):
    """装饰器：要求特定依赖"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not check_dependency(package_name):
                raise ImportError(
                    f"Function {func.__name__} requires {package_name} "
                    f"but it is not available."
                )
            return func(*args, **kwargs)
        return wrapper
    return decorator


# 可选依赖装饰器
def optional_dependency(package_name: str, fallback_result=None):
    """装饰器：可选依赖，不可用时返回fallback"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not check_dependency(package_name):
                warnings.warn(
                    f"Function {func.__name__} requires {package_name} "
                    f"but it is not available. Returning fallback.",
                    ImportWarning
                )
                return fallback_result
            return func(*args, **kwargs)
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试导入管理器
    print("Import Manager Test")
    print("=" * 40)
    
    # 测试成功导入
    numpy = safe_import('numpy')
    print(f"NumPy import: {'Success' if numpy else 'Failed'}")
    
    # 测试失败导入
    fake_module = safe_import('fake_module', fallback='fallback_value')
    print(f"Fake module import: {fake_module}")
    
    # 测试依赖检查
    print(f"NumPy available: {check_dependency('numpy')}")
    print(f"PyTorch available: {check_dependency('torch')}")
    
    # 导入状态报告
    report = get_import_report()
    print(f"Import report: {report}")
