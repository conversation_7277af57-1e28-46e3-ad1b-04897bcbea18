"""
HART II验证案例
==============

高级旋翼声学测试(HART II)验证案例，用于验证气动声学耦合计算。

参考文献:
<PERSON> der <PERSON>, B. G., et al. (2003). The HART-II test: rotor wakes and 
aeroacoustics with higher-harmonic pitch control (HHC) inputs.
"""

import numpy as np
from typing import Dict, List, Tuple, Any

class HartIICase:
    """HART II验证案例"""
    
    def __init__(self):
        self.case_name = "HART-II"
        self.description = "Higher Harmonic Control rotor test"
        
        # 实验参数
        self.rotor_radius = 2.0  # m
        self.blade_count = 4
        self.chord = 0.121  # m
        self.advance_ratio = 0.15
        
    def get_config(self) -> Dict[str, Any]:
        """获取验证案例配置"""
        return {
            'case_name': self.case_name,
            'R_rotor': self.rotor_radius,
            'B': self.blade_count,
            'c': self.chord,
            'advance_ratio': self.advance_ratio,
            'solver_type': 'UVLM',
            'enable_acoustics': True,
            'enable_validation': True,
        }

def create_hart_ii_case():
    """创建HART II验证案例"""
    return HartIICase()