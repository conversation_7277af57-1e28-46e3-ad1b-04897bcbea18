"""
收敛控制器
=========

实现BEMT求解器的收敛控制算法，包含多种收敛判据和加速方法。

核心功能：
- 多种收敛判据（绝对、相对、混合）
- Aitken加速方法
- 自适应松弛因子
- 收敛历史监控
- 发散检测和处理

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import List, Optional, Dict, Any
import warnings


class ConvergenceController:
    """
    收敛控制器
    
    管理BEMT迭代求解的收敛过程，提供多种收敛判据和加速方法。
    """
    
    def __init__(self, tolerance: float = 1e-4, max_iterations: int = 100,
                 relaxation_factor: float = 0.5, enable_aitken: bool = False):
        """
        初始化收敛控制器
        
        Args:
            tolerance: 收敛容差
            max_iterations: 最大迭代次数
            relaxation_factor: 松弛因子
            enable_aitken: 是否启用Aitken加速
        """
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.relaxation_factor = relaxation_factor
        self.enable_aitken = enable_aitken
        
        # 收敛历史
        self.residual_history: List[float] = []
        self.convergence_rate_history: List[float] = []
        
        # Aitken加速相关
        self.aitken_history: List[np.ndarray] = []
        self.aitken_factor = 1.0
        
        # 自适应松弛因子
        self.adaptive_relaxation = True
        self.min_relaxation = 0.1
        self.max_relaxation = 0.9
        
        # 发散检测
        self.divergence_threshold = 1e6
        self.stagnation_threshold = 1e-12
        self.stagnation_count = 0
        
        # 统计信息
        self.total_iterations = 0
        self.convergence_failures = 0
        
        print(f"收敛控制器初始化完成")
        print(f"  容差: {tolerance:.2e}")
        print(f"  最大迭代: {max_iterations}")
        print(f"  松弛因子: {relaxation_factor}")
        print(f"  Aitken加速: {'启用' if enable_aitken else '禁用'}")
    
    def reset(self):
        """重置收敛状态"""
        self.residual_history.clear()
        self.convergence_rate_history.clear()
        self.aitken_history.clear()
        self.aitken_factor = 1.0
        self.stagnation_count = 0
    
    def check_convergence(self, residual: float) -> bool:
        """
        检查是否收敛
        
        Args:
            residual: 当前残差
            
        Returns:
            是否收敛
        """
        # 记录残差历史
        self.residual_history.append(residual)
        
        # 基本收敛判据
        converged = residual < self.tolerance
        
        if converged:
            return True
        
        # 发散检测 (仅在严重发散时警告)
        if self._check_divergence(residual):
            if residual > 1.0:  # 只有在残差很大时才警告
                warnings.warn(f"检测到发散，残差: {residual:.2e}")
            return False
        
        # 停滞检测
        if self._check_stagnation():
            warnings.warn("检测到收敛停滞")
            return False
        
        # 更新收敛率
        self._update_convergence_rate()
        
        # 自适应松弛因子
        if self.adaptive_relaxation:
            self._update_relaxation_factor()
        
        return False
    
    def _check_divergence(self, residual: float) -> bool:
        """检测发散"""
        if residual > self.divergence_threshold:
            return True
        
        # 检查残差是否持续增长
        if len(self.residual_history) >= 5:
            recent_residuals = self.residual_history[-5:]
            if all(recent_residuals[i] < recent_residuals[i+1] 
                   for i in range(len(recent_residuals)-1)):
                return True
        
        return False
    
    def _check_stagnation(self) -> bool:
        """检测收敛停滞"""
        if len(self.residual_history) < 3:
            return False
        
        # 检查残差变化是否过小
        recent_change = abs(self.residual_history[-1] - self.residual_history[-2])
        if recent_change < self.stagnation_threshold:
            self.stagnation_count += 1
        else:
            self.stagnation_count = 0
        
        return self.stagnation_count > 10
    
    def _update_convergence_rate(self):
        """更新收敛率"""
        if len(self.residual_history) >= 3:
            r_n = self.residual_history[-1]
            r_n1 = self.residual_history[-2]
            r_n2 = self.residual_history[-3]
            
            if r_n2 > 1e-15 and r_n1 > 1e-15:
                # 计算收敛率
                rate = np.log(r_n / r_n1) / np.log(r_n1 / r_n2)
                self.convergence_rate_history.append(rate)
    
    def _update_relaxation_factor(self):
        """自适应更新松弛因子"""
        if len(self.residual_history) < 3:
            return
        
        # 基于收敛率调整松弛因子
        if len(self.convergence_rate_history) > 0:
            avg_rate = np.mean(self.convergence_rate_history[-5:])
            
            if avg_rate > 0.8:  # 收敛慢
                self.relaxation_factor = min(self.max_relaxation, 
                                           self.relaxation_factor * 1.1)
            elif avg_rate < 0.3:  # 可能振荡
                self.relaxation_factor = max(self.min_relaxation,
                                           self.relaxation_factor * 0.9)
    
    def apply_aitken_acceleration(self, x_current: np.ndarray, 
                                x_previous: np.ndarray) -> np.ndarray:
        """
        应用Aitken加速方法
        
        Args:
            x_current: 当前迭代值
            x_previous: 前一次迭代值
            
        Returns:
            加速后的值
        """
        if not self.enable_aitken:
            return x_current
        
        self.aitken_history.append(x_current.copy())
        
        if len(self.aitken_history) < 3:
            return x_current
        
        # Aitken Δ²方法
        x_n = self.aitken_history[-1]
        x_n1 = self.aitken_history[-2]
        x_n2 = self.aitken_history[-3]
        
        delta1 = x_n1 - x_n2
        delta2 = x_n - x_n1
        delta_delta = delta2 - delta1
        
        # 避免除零
        mask = np.abs(delta_delta) > 1e-15
        
        x_accelerated = x_current.copy()
        if np.any(mask):
            x_accelerated[mask] = x_n2[mask] - (delta1[mask]**2) / delta_delta[mask]
        
        # 限制加速幅度
        max_change = 0.5 * np.abs(x_current - x_previous)
        change = x_accelerated - x_current
        change = np.clip(change, -max_change, max_change)
        
        return x_current + change
    
    def get_convergence_info(self) -> Dict[str, Any]:
        """获取收敛信息"""
        info = {
            'current_residual': self.residual_history[-1] if self.residual_history else float('inf'),
            'tolerance': self.tolerance,
            'iterations': len(self.residual_history),
            'max_iterations': self.max_iterations,
            'relaxation_factor': self.relaxation_factor,
            'residual_history': self.residual_history.copy(),
            'convergence_rate': (self.convergence_rate_history[-1] 
                               if self.convergence_rate_history else None),
            'stagnation_count': self.stagnation_count
        }
        
        return info
    
    def estimate_remaining_iterations(self) -> Optional[int]:
        """估计剩余迭代次数"""
        if len(self.residual_history) < 3:
            return None
        
        if len(self.convergence_rate_history) == 0:
            return None
        
        current_residual = self.residual_history[-1]
        avg_rate = np.mean(self.convergence_rate_history[-3:])
        
        if avg_rate <= 0 or avg_rate >= 1:
            return None
        
        # 估计剩余迭代次数
        remaining = np.log(self.tolerance / current_residual) / np.log(avg_rate)
        return max(0, int(remaining))
    
    def suggest_tolerance_adjustment(self) -> Optional[float]:
        """建议容差调整"""
        if len(self.residual_history) < 10:
            return None
        
        # 如果收敛过慢，建议放宽容差
        if len(self.residual_history) > 0.8 * self.max_iterations:
            current_residual = self.residual_history[-1]
            if current_residual > self.tolerance:
                return current_residual * 2
        
        # 如果收敛过快，建议收紧容差
        if len(self.residual_history) < 0.2 * self.max_iterations:
            return self.tolerance * 0.5
        
        return None
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        if not self.residual_history:
            return {}
        
        metrics = {
            'convergence_efficiency': len(self.residual_history) / self.max_iterations,
            'final_residual': self.residual_history[-1],
            'residual_reduction': (self.residual_history[0] / self.residual_history[-1] 
                                 if len(self.residual_history) > 1 else 1.0),
            'average_convergence_rate': (np.mean(self.convergence_rate_history) 
                                       if self.convergence_rate_history else 0.0)
        }
        
        return metrics


class AdaptiveConvergenceController(ConvergenceController):
    """
    自适应收敛控制器
    
    基于收敛历史自动调整参数的高级收敛控制器。
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 自适应参数
        self.tolerance_history: List[float] = []
        self.success_rate_window = 10
        self.adjustment_factor = 1.2
        
        # 性能监控
        self.performance_history: List[Dict] = []
    
    def adaptive_tolerance_adjustment(self):
        """自适应调整容差"""
        if len(self.performance_history) < self.success_rate_window:
            return
        
        # 计算最近的成功率
        recent_performance = self.performance_history[-self.success_rate_window:]
        success_rate = sum(1 for p in recent_performance if p['converged']) / len(recent_performance)
        
        if success_rate < 0.7:  # 成功率过低，放宽容差
            new_tolerance = self.tolerance * self.adjustment_factor
            self.tolerance = min(new_tolerance, 1e-2)  # 设置上限
            print(f"自适应调整容差: {self.tolerance:.2e} (放宽)")
            
        elif success_rate > 0.95:  # 成功率很高，收紧容差
            new_tolerance = self.tolerance / self.adjustment_factor
            self.tolerance = max(new_tolerance, 1e-8)  # 设置下限
            print(f"自适应调整容差: {self.tolerance:.2e} (收紧)")
        
        self.tolerance_history.append(self.tolerance)
    
    def record_performance(self, converged: bool, iterations: int, final_residual: float):
        """记录性能数据"""
        performance = {
            'converged': converged,
            'iterations': iterations,
            'final_residual': final_residual,
            'tolerance': self.tolerance,
            'efficiency': iterations / self.max_iterations
        }
        
        self.performance_history.append(performance)
        
        # 限制历史长度
        if len(self.performance_history) > 100:
            self.performance_history.pop(0)
        
        # 定期调整参数
        if len(self.performance_history) % self.success_rate_window == 0:
            self.adaptive_tolerance_adjustment()


class MultiCriteriaConvergence:
    """
    多判据收敛控制器
    
    使用多个收敛判据的组合来提高收敛判断的可靠性。
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 各种判据的权重
        self.criteria_weights = {
            'residual': config.get('residual_weight', 0.4),
            'relative_change': config.get('relative_weight', 0.3),
            'force_balance': config.get('force_weight', 0.2),
            'energy_balance': config.get('energy_weight', 0.1)
        }
        
        # 各判据的容差
        self.tolerances = {
            'residual': config.get('residual_tolerance', 1e-4),
            'relative_change': config.get('relative_tolerance', 1e-3),
            'force_balance': config.get('force_tolerance', 1e-3),
            'energy_balance': config.get('energy_tolerance', 1e-2)
        }
        
        # 历史数据
        self.criteria_history = {key: [] for key in self.criteria_weights.keys()}
    
    def evaluate_convergence(self, current_state: Dict[str, Any], 
                           previous_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估多判据收敛状态
        
        Args:
            current_state: 当前状态
            previous_state: 前一状态
            
        Returns:
            收敛评估结果
        """
        criteria_values = {}
        criteria_satisfied = {}
        
        # 残差判据
        residual = current_state.get('residual', float('inf'))
        criteria_values['residual'] = residual
        criteria_satisfied['residual'] = residual < self.tolerances['residual']
        
        # 相对变化判据
        if 'forces' in current_state and 'forces' in previous_state:
            relative_change = self._calculate_relative_change(
                current_state['forces'], previous_state['forces']
            )
            criteria_values['relative_change'] = relative_change
            criteria_satisfied['relative_change'] = relative_change < self.tolerances['relative_change']
        
        # 力平衡判据
        if 'forces' in current_state:
            force_balance = self._calculate_force_balance(current_state['forces'])
            criteria_values['force_balance'] = force_balance
            criteria_satisfied['force_balance'] = force_balance < self.tolerances['force_balance']
        
        # 能量平衡判据
        if 'power' in current_state and 'thrust' in current_state:
            energy_balance = self._calculate_energy_balance(
                current_state['power'], current_state['thrust']
            )
            criteria_values['energy_balance'] = energy_balance
            criteria_satisfied['energy_balance'] = energy_balance < self.tolerances['energy_balance']
        
        # 计算综合收敛指标
        convergence_score = self._calculate_convergence_score(
            criteria_values, criteria_satisfied
        )
        
        # 更新历史
        for key, value in criteria_values.items():
            self.criteria_history[key].append(value)
        
        return {
            'converged': convergence_score > 0.8,
            'convergence_score': convergence_score,
            'criteria_values': criteria_values,
            'criteria_satisfied': criteria_satisfied
        }
    
    def _calculate_relative_change(self, current: np.ndarray, previous: np.ndarray) -> float:
        """计算相对变化"""
        if np.allclose(previous, 0):
            return np.linalg.norm(current)
        return np.linalg.norm(current - previous) / np.linalg.norm(previous)
    
    def _calculate_force_balance(self, forces: np.ndarray) -> float:
        """计算力平衡"""
        # 简化的力平衡检查
        total_force = np.sum(forces, axis=0)
        return np.linalg.norm(total_force)
    
    def _calculate_energy_balance(self, power: float, thrust: float) -> float:
        """计算能量平衡"""
        # 简化的能量平衡检查
        if thrust <= 0:
            return float('inf')
        
        ideal_power = thrust**1.5 / np.sqrt(2 * 1.225)  # 理想功率
        return abs(power - ideal_power) / ideal_power
    
    def _calculate_convergence_score(self, values: Dict[str, float], 
                                   satisfied: Dict[str, bool]) -> float:
        """计算综合收敛分数"""
        score = 0.0
        total_weight = 0.0
        
        for criterion, weight in self.criteria_weights.items():
            if criterion in satisfied:
                if satisfied[criterion]:
                    score += weight
                total_weight += weight
        
        return score / total_weight if total_weight > 0 else 0.0