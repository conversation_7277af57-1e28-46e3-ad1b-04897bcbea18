#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构功能测试脚本
===============

测试BEMT模块重构后的新功能。

作者: Augment Agent
日期: 2025-07-24
"""

import sys
import os
import time

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_time_stepping():
    """测试时间步进功能"""
    print("🕐 测试时间步进功能")
    print("-" * 30)
    
    from simple_bemt import SimpleBEMT
    
    solver = SimpleBEMT(radius=1.0, num_blades=4)
    
    # 测试非定常求解
    start_time = time.time()
    result = solver.solve_unsteady(
        t_span=(0.0, 0.05),
        dt=0.005,
        rpm=400,
        forward_speed=10.0,
        integration_method='rk4'
    )
    solve_time = time.time() - start_time
    
    print(f"   时间范围: 0.0-0.05s")
    print(f"   时间步长: 0.005s")
    print(f"   积分方法: RK4")
    print(f"   总步数: {result['total_steps']}")
    print(f"   求解时间: {solve_time:.3f}s")
    print(f"   最终推力: {result['final_state']['thrust']:.1f}N")
    print(f"   平均推力: {result['thrust_history'].mean():.1f}N")
    
    return True


def test_extended_airfoils():
    """测试扩展翼型数据库"""
    print("\n📊 测试扩展翼型数据库")
    print("-" * 30)
    
    from aerodynamics.airfoil_interpolator import EnhancedAirfoilInterpolator
    
    # 测试线性插值
    linear_interp = EnhancedAirfoilInterpolator(interpolation_method='linear')
    airfoils = linear_interp.get_available_airfoils()
    
    print(f"   可用翼型数量: {len(airfoils)}")
    print(f"   翼型列表: {', '.join(airfoils[:6])}...")
    
    # 测试三次样条插值
    spline_interp = EnhancedAirfoilInterpolator(interpolation_method='cubic_spline')
    
    # 测试不同翼型
    test_airfoils = ['naca0012', 'naca4412', 'clark_y', 's1223']
    
    print(f"\n   插值精度对比 (5度攻角):")
    print(f"   {'翼型':<12} {'线性Cl':<8} {'样条Cl':<8} {'线性Cd':<8} {'样条Cd':<8}")
    print("   " + "-" * 50)
    
    for airfoil in test_airfoils:
        cl_linear, cd_linear = linear_interp.interpolate(airfoil, 5.0)
        cl_spline, cd_spline = spline_interp.interpolate(airfoil, 5.0)
        
        print(f"   {airfoil:<12} {cl_linear:<8.3f} {cl_spline:<8.3f} {cd_linear:<8.4f} {cd_spline:<8.4f}")
    
    # 测试雷诺数修正
    print(f"\n   雷诺数修正测试 (NACA0012, 5度):")
    cl_low, cd_low = spline_interp.interpolate('naca0012', 5.0, Re=1e4)
    cl_ref, cd_ref = spline_interp.interpolate('naca0012', 5.0, Re=1e6)
    cl_high, cd_high = spline_interp.interpolate('naca0012', 5.0, Re=1e7)
    
    print(f"   Re=1e4: Cl={cl_low:.3f}, Cd={cd_low:.4f}")
    print(f"   Re=1e6: Cl={cl_ref:.3f}, Cd={cd_ref:.4f}")
    print(f"   Re=1e7: Cl={cl_high:.3f}, Cd={cd_high:.4f}")
    
    return True


def test_unified_solver():
    """测试统一求解器接口"""
    print("\n🔧 测试统一求解器接口")
    print("-" * 30)
    
    try:
        from core.bemt_solver import UnifiedBEMTSolver, TimeSteppingBEMTSolver
        
        # 测试统一求解器
        unified_solver = UnifiedBEMTSolver(solver_type='main', radius=1.0, num_blades=4)
        
        result = unified_solver.solve(rpm=400, forward_speed=10.0)
        
        print(f"   统一求解器测试:")
        print(f"   推力: {result['thrust']:.1f}N")
        print(f"   功率: {result['power']:.1f}W")
        print(f"   收敛: {'✅' if result['converged'] else '❌'}")
        
        # 获取求解器信息
        info = unified_solver.get_solver_info()
        print(f"   求解器类型: {info['type']}")
        print(f"   求解器类: {info['class']}")
        
        # 测试时间步进求解器
        time_solver = TimeSteppingBEMTSolver(base_solver='main', radius=1.0, num_blades=4)
        
        unsteady_result = time_solver.solve_unsteady(
            t_span=(0.0, 0.02),
            dt=0.005,
            rpm=400,
            forward_speed=10.0
        )
        
        print(f"\n   时间步进求解器测试:")
        print(f"   总步数: {unsteady_result['total_steps']}")
        print(f"   积分方法: {unsteady_result['integration_method']}")
        
        return True
        
    except ImportError as e:
        print(f"   ⚠️ 统一求解器不可用: {e}")
        return False


def test_config_management():
    """测试配置管理系统"""
    print("\n⚙️ 测试配置管理系统")
    print("-" * 30)
    
    try:
        from utils.config import ConfigManager, get_global_config
        
        # 测试配置管理器
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        print(f"   默认配置:")
        print(f"   最大迭代次数: {config.max_iterations}")
        print(f"   收敛容差: {config.tolerance:.2e}")
        print(f"   时间积分方法: {config.time_integration_method}")
        print(f"   插值方法: {config.interpolation_method}")
        
        # 测试配置更新
        config_manager.update_config(
            max_iterations=150,
            time_integration_method='rk4'
        )
        
        updated_config = config_manager.get_config()
        print(f"\n   更新后配置:")
        print(f"   最大迭代次数: {updated_config.max_iterations}")
        print(f"   时间积分方法: {updated_config.time_integration_method}")
        
        return True
        
    except ImportError as e:
        print(f"   ⚠️ 配置管理不可用: {e}")
        return False


def test_validation_suite():
    """测试验证套件"""
    print("\n🧪 测试验证套件")
    print("-" * 30)
    
    from validation_suite import run_validation
    
    # 运行完整验证
    start_time = time.time()
    results = run_validation()
    validation_time = time.time() - start_time
    
    print(f"   验证时间: {validation_time:.2f}s")
    print(f"   通过率: {results.get('success_rate', 0):.1f}%")
    print(f"   通过测试: {results.get('passed', 0)}/{results.get('total', 0)}")
    
    return results.get('success_rate', 0) >= 90


def run_all_tests():
    """运行所有重构功能测试"""
    print("🚀 BEMT重构功能测试套件")
    print("=" * 50)
    
    tests = [
        ("时间步进功能", test_time_stepping),
        ("扩展翼型数据库", test_extended_airfoils),
        ("统一求解器接口", test_unified_solver),
        ("配置管理系统", test_config_management),
        ("验证测试套件", test_validation_suite),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n📊 测试总结:")
    print(f"   通过率: {passed/total*100:.1f}% ({passed}/{total})")
    
    if passed == total:
        print("🎉 所有重构功能测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed, total


if __name__ == "__main__":
    run_all_tests()
