#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced BEMT Solver - Medium Fidelity
增强BEMT求解器 - 中保真度

This module implements an enhanced BEMT solver with medium-fidelity physics:
- 3D rotational effects (Coriolis, centrifugal)
- Simplified dynamic stall (Leishman-Beddoes)
- Non-uniform inflow modeling
- Wake skew effects
- Advanced tip/hub loss corrections

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import time
import warnings

# 导入工具模块
from ..utilities import (safe_divide, safe_sqrt, BEMTError, BEMTConvergenceError,
                        SimpleConvergenceMonitor, validate_positive, deg2rad, rad2deg)

# 导入几何和物理模型
try:
    from ..geometry.rotor_geometry import RotorGeometry3D
    from ..physics.dynamic_stall import SimplifiedLeishmanBeddoes
    from ..physics.rotational_effects import CoriolisEffects, CentrifugalEffects
    from ..physics.corrections import AdvancedCorrections
    from ..aerodynamics.airfoil_interpolator import EnhancedAirfoilInterpolator
except ImportError as e:
    warnings.warn(f"部分模块导入失败: {e}，将使用简化实现")

@dataclass
class SolverSettings:
    """求解器设置"""
    max_iterations: int = 200
    tolerance: float = 1e-7
    relaxation_factor: float = 0.6
    
    # 中保真度特有设置
    enable_3d_effects: bool = True
    enable_dynamic_stall: bool = True
    enable_wake_skew: bool = True
    enable_non_uniform_inflow: bool = True
    
    # 自适应特性
    adaptive_relaxation: bool = True
    adaptive_mesh: bool = False
    multi_grid: bool = False
    
    # 性能设置
    enable_caching: bool = True
    parallel_computation: bool = False

class FlightCondition:
    """飞行条件类 - 中保真度增强版"""
    
    def __init__(self, 
                 rpm: float,
                 forward_speed: float = 0.0,
                 lateral_speed: float = 0.0,
                 vertical_speed: float = 0.0,
                 density: float = 1.225,
                 temperature: float = 288.15,
                 pressure: float = 101325.0,
                 **kwargs):
        """
        初始化飞行条件
        
        Parameters:
        -----------
        rpm : float
            转速 [rpm]
        forward_speed : float
            前飞速度 [m/s]
        lateral_speed : float  
            侧向速度 [m/s]
        vertical_speed : float
            垂直速度 [m/s]
        density : float
            空气密度 [kg/m³]
        temperature : float
            温度 [K]
        pressure : float
            压力 [Pa]
        """
        self.rpm = rpm
        self.omega = rpm * np.pi / 30.0  # 角速度 [rad/s]
        
        # 速度矢量
        self.V_inf = np.array([forward_speed, lateral_speed, vertical_speed])
        self.V_inf_magnitude = np.linalg.norm(self.V_inf)
        
        # 大气条件
        self.rho = density
        self.T = temperature
        self.p = pressure
        
        # 计算音速和马赫数
        self.a = np.sqrt(1.4 * 287.0 * temperature)  # 音速 [m/s]
        self.M_inf = self.V_inf_magnitude / self.a if self.a > 0 else 0.0
        
        # 运动粘度 (Sutherland公式)
        self.mu = self._calculate_viscosity(temperature)
        self.nu = self.mu / density
        
        # 存储额外参数
        self.extra_params = kwargs
    
    def _calculate_viscosity(self, T: float) -> float:
        """计算动力粘度 (Sutherland公式)"""
        mu_ref = 1.716e-5  # 参考粘度 [Pa·s]
        T_ref = 273.15     # 参考温度 [K]
        S = 110.4          # Sutherland常数 [K]
        
        return mu_ref * (T / T_ref)**1.5 * (T_ref + S) / (T + S)
    
    def get_reynolds_number(self, chord: float, relative_velocity: float) -> float:
        """计算雷诺数"""
        return self.rho * relative_velocity * chord / self.mu
    
    def get_mach_number(self, relative_velocity: float) -> float:
        """计算马赫数"""
        return relative_velocity / self.a if self.a > 0 else 0.0

class EnhancedBEMTSolver:
    """
    增强BEMT求解器 - 中保真度
    
    特性:
    - 3D旋转效应建模
    - 简化动态失速模型
    - 非均匀流入计算
    - 尾迹倾斜效应
    - 自适应收敛算法
    """
    
    def __init__(self, 
                 geometry,  # RotorGeometry3D
                 settings: Optional[SolverSettings] = None,
                 **kwargs):
        """
        初始化增强BEMT求解器
        
        Parameters:
        -----------
        geometry : RotorGeometry3D
            3D旋翼几何
        settings : SolverSettings, optional
            求解器设置
        **kwargs : dict
            额外参数
        """
        
        self.geometry = geometry
        self.settings = settings or SolverSettings(**kwargs)
        
        # 初始化物理模型
        self._initialize_physics_models()

        # 初始化数值工具
        self._initialize_numerical_tools()

        # 缓存系统
        self._cache = {} if self.settings.enable_caching else None

        # 性能统计
        self.performance_stats = {
            'solve_time': 0.0,
            'iterations': 0,
            'convergence_history': [],
            'physics_model_time': {}
        }

        print(f"增强BEMT求解器初始化完成")
        print(f"  3D效应: {'启用' if self.settings.enable_3d_effects else '禁用'}")
        print(f"  动态失速: {'启用' if self.settings.enable_dynamic_stall else '禁用'}")
        print(f"  非均匀流入: {'启用' if self.settings.enable_non_uniform_inflow else '禁用'}")
    
    def _initialize_physics_models(self):
        """初始化物理模型"""

        # 翼型插值器
        try:
            self.airfoil_interpolator = EnhancedAirfoilInterpolator()
        except:
            self.airfoil_interpolator = self._create_simple_airfoil_interpolator()

        # 动态失速模型
        if self.settings.enable_dynamic_stall:
            try:
                self.dynamic_stall = SimplifiedLeishmanBeddoes()
            except:
                self.dynamic_stall = self._create_simple_dynamic_stall()
        else:
            self.dynamic_stall = None

        # 3D旋转效应
        if self.settings.enable_3d_effects:
            try:
                self.coriolis_effects = CoriolisEffects(self.geometry)
                self.centrifugal_effects = CentrifugalEffects(self.geometry)
            except:
                self.coriolis_effects = self._create_simple_coriolis_effects()
                self.centrifugal_effects = self._create_simple_centrifugal_effects()
        else:
            self.coriolis_effects = None
            self.centrifugal_effects = None

        # 高级修正模型
        try:
            self.corrections = AdvancedCorrections(self.geometry)
        except:
            self.corrections = self._create_simple_corrections()

    def _initialize_numerical_tools(self):
        """初始化数值工具"""

        # 收敛监控器
        try:
            self.convergence_monitor = SimpleConvergenceMonitor(
                tolerance=self.settings.tolerance,
                max_iterations=self.settings.max_iterations
            )
        except:
            self.convergence_monitor = self._create_simple_convergence_monitor()

        # 自适应松弛因子
        self.relaxation_factor = self.settings.relaxation_factor
        self.relaxation_history = []
    
    def solve(self, flight_condition: FlightCondition, **kwargs) -> Dict[str, Any]:
        """
        求解BEMT方程组
        
        Parameters:
        -----------
        flight_condition : FlightCondition
            飞行条件
        **kwargs : dict
            求解选项
            
        Returns:
        --------
        results : dict
            求解结果
        """
        
        start_time = time.time()
        
        try:
            # 1. 预处理
            self._preprocess(flight_condition, **kwargs)
            
            # 2. 初始化求解变量
            solution_vars = self._initialize_solution(flight_condition)
            
            # 3. 迭代求解
            converged, final_residual = self._iterative_solve(
                solution_vars, flight_condition, **kwargs
            )
            
            # 4. 后处理
            results = self._postprocess(solution_vars, flight_condition, **kwargs)
            
            # 5. 添加求解信息
            solve_time = time.time() - start_time
            results.update({
                'solver_info': {
                    'converged': converged,
                    'final_residual': final_residual,
                    'iterations': getattr(self.convergence_monitor, 'iteration', 0),
                    'solve_time': solve_time,
                    'fidelity_level': 'medium'
                }
            })
            
            # 更新性能统计
            self.performance_stats.update({
                'solve_time': solve_time,
                'iterations': getattr(self.convergence_monitor, 'iteration', 0),
                'convergence_history': getattr(self.convergence_monitor, 'residual_history', [])
            })
            
            return results
            
        except Exception as e:
            raise BEMTError(f"BEMT求解失败: {str(e)}")
    
    def _preprocess(self, flight_condition: FlightCondition, **kwargs):
        """预处理"""
        
        # 计算无量纲参数
        if hasattr(self.geometry, 'radius'):
            self.advance_ratio = (flight_condition.V_inf[0] / 
                                 (flight_condition.omega * self.geometry.radius))
        else:
            self.advance_ratio = 0.0
        
        # 清空缓存 (如果需要)
        if self._cache is not None and kwargs.get('clear_cache', False):
            self._cache.clear()
    
    def _initialize_solution(self, flight_condition: FlightCondition) -> Dict[str, np.ndarray]:
        """初始化求解变量"""

        # 从几何对象获取径向站位数
        if hasattr(self.geometry, 'blade_geometry') and hasattr(self.geometry.blade_geometry, 'n_stations'):
            n_stations = self.geometry.blade_geometry.n_stations
        elif hasattr(self.geometry, 'n_stations'):
            n_stations = self.geometry.n_stations
        else:
            n_stations = 20  # 默认值，与几何模块一致

        # 基于动量理论的初始估计
        if self.advance_ratio < 0.1:  # 悬停或低速前飞
            lambda_initial = np.full(n_stations, 0.05)  # 初始诱导速度比
            alpha_initial = np.full(n_stations, 5.0)    # 初始攻角 [deg]
        else:
            lambda_initial = np.full(n_stations, 0.02)
            alpha_initial = np.full(n_stations, 2.0)

        solution_vars = {
            'lambda_i': lambda_initial.copy(),      # 诱导速度比
            'alpha': alpha_initial.copy(),          # 攻角 [deg]
            'phi': np.zeros(n_stations),            # 流入角 [deg]
            'Cl': np.zeros(n_stations),             # 升力系数
            'Cd': np.zeros(n_stations),             # 阻力系数
            'dT': np.zeros(n_stations),             # 微元推力
            'dQ': np.zeros(n_stations),             # 微元扭矩
        }

        return solution_vars

    def _iterative_solve(self, solution_vars: Dict[str, np.ndarray],
                        flight_condition: FlightCondition, **kwargs) -> Tuple[bool, float]:
        """迭代求解主循环"""

        # 重置收敛监控器
        self.convergence_monitor.reset()

        max_iterations = self.settings.max_iterations
        tolerance = self.settings.tolerance

        # 获取径向站位信息
        n_stations = len(solution_vars['lambda_i'])

        # 从几何对象获取径向站位
        if hasattr(self.geometry, 'blade_geometry') and hasattr(self.geometry.blade_geometry, 'r_stations'):
            r_stations = self.geometry.blade_geometry.r_stations
        elif hasattr(self.geometry, 'r_stations'):
            r_stations = self.geometry.r_stations
        else:
            # 回退到默认分布
            radius = getattr(self.geometry, 'radius', 1.0)
            hub_radius = getattr(self.geometry, 'hub_radius', 0.1)
            r_stations = np.linspace(hub_radius, radius, n_stations)

        for iteration in range(max_iterations):
            # 保存上一步解
            old_vars = {key: val.copy() for key, val in solution_vars.items()}

            try:
                # 1. 计算流入角和攻角
                self._compute_inflow_angles(solution_vars, flight_condition, r_stations)

                # 2. 应用3D旋转效应修正
                if self.settings.enable_3d_effects:
                    self._apply_3d_effects(solution_vars, flight_condition, r_stations)

                # 3. 计算翼型气动系数
                self._compute_airfoil_coefficients(solution_vars, flight_condition, r_stations)

                # 4. 应用动态失速修正
                if self.settings.enable_dynamic_stall:
                    self._apply_dynamic_stall(solution_vars, flight_condition, r_stations)

                # 5. 应用物理修正
                self._apply_physical_corrections(solution_vars, flight_condition, r_stations)

                # 6. 计算载荷分布
                self._compute_load_distribution(solution_vars, flight_condition, r_stations)

                # 7. 更新诱导速度
                self._update_induced_velocity(solution_vars, flight_condition, r_stations)

                # 8. 应用松弛因子
                current_relaxation = self.convergence_monitor.get_relaxation_factor()
                self._apply_relaxation(solution_vars, old_vars, current_relaxation)

                # 9. 检查收敛性
                residual = self._compute_residual(solution_vars, old_vars)

                # 更新收敛监控器
                converged = self.convergence_monitor.update(residual, solution_vars)

                if converged:
                    return True, residual

            except Exception as e:
                raise BEMTError(f"迭代 {iteration} 计算失败: {str(e)}")

        # 达到最大迭代次数
        final_residual = self.convergence_monitor.current_residual
        if final_residual < tolerance * 10:  # 放宽容差
            warnings.warn(f"求解器在 {max_iterations} 次迭代后收敛（放宽容差）")
            return True, final_residual
        else:
            raise BEMTConvergenceError(
                f"求解器在 {max_iterations} 次迭代后未收敛",
                iterations=max_iterations,
                residual=final_residual
            )

    def _compute_inflow_angles(self, solution_vars: Dict[str, np.ndarray],
                              flight_condition: FlightCondition, r_stations: np.ndarray):
        """计算流入角和攻角"""

        # 旋翼半径
        R = getattr(self.geometry, 'radius', 1.0)

        # 轴向和切向速度分量
        V_axial = flight_condition.V_inf[0] + solution_vars['lambda_i'] * flight_condition.omega * R
        V_tangential = flight_condition.omega * r_stations

        # 流入角（使用numpy的arctan2）
        solution_vars['phi'] = np.degrees(np.arctan2(V_axial, V_tangential))

        # 获取桨叶扭转角
        if hasattr(self.geometry, 'blade_geometry') and hasattr(self.geometry.blade_geometry, 'twist_stations'):
            twist_stations = self.geometry.blade_geometry.twist_stations
        else:
            # 简化：线性扭转分布
            n_stations = len(solution_vars['lambda_i'])
            twist_stations = np.linspace(15.0, -5.0, n_stations)  # 根部15°到叶尖-5°

        # 攻角 = 桨叶扭转角 - 流入角
        solution_vars['alpha'] = twist_stations - solution_vars['phi']

    def _apply_3d_effects(self, solution_vars: Dict[str, np.ndarray],
                         flight_condition: FlightCondition, r_stations: np.ndarray):
        """应用3D旋转效应修正"""

        if self.coriolis_effects is not None:
            try:
                # 使用完整的科里奥利效应模型
                corrections = self.coriolis_effects.compute_correction(
                    solution_vars, flight_condition, r_stations
                )
                solution_vars['alpha'] += corrections['alpha_correction']
                solution_vars['lambda_i'] *= corrections['inflow_correction']
            except:
                # 回退到简化实现
                coriolis_correction = 0.05 * np.sin(np.radians(solution_vars['alpha']))
                solution_vars['lambda_i'] *= (1 + coriolis_correction)

        if self.centrifugal_effects is not None:
            try:
                # 使用完整的离心效应模型
                corrections = self.centrifugal_effects.compute_correction(
                    solution_vars, flight_condition, r_stations
                )
                solution_vars['alpha'] += corrections['alpha_stall_correction']
            except:
                # 回退到简化实现
                centrifugal_correction = 0.02 * np.cos(np.radians(solution_vars['alpha']))
                solution_vars['alpha'] += centrifugal_correction

    def _compute_airfoil_coefficients(self, solution_vars: Dict[str, np.ndarray],
                                     flight_condition: FlightCondition, r_stations: np.ndarray):
        """计算翼型气动系数"""

        n_stations = len(solution_vars['alpha'])

        # 初始化系数数组
        solution_vars['Cl'] = np.zeros(n_stations)
        solution_vars['Cd'] = np.zeros(n_stations)

        # 计算每个站位的气动系数
        for i in range(n_stations):
            alpha = solution_vars['alpha'][i]

            # 获取翼型名称
            if hasattr(self.geometry, 'blade_geometry'):
                airfoil_name = self.geometry.blade_geometry.get_airfoil_at_station(i)
            else:
                airfoil_name = 'naca0012'

            # 计算雷诺数和马赫数
            chord = getattr(self.geometry.blade_geometry, 'chord_stations', [0.1])[min(i, len(getattr(self.geometry.blade_geometry, 'chord_stations', [0.1]))-1)]
            V_rel = np.sqrt((flight_condition.V_inf[0] + solution_vars['lambda_i'][i] * flight_condition.omega * r_stations[i])**2 +
                           (flight_condition.omega * r_stations[i])**2)

            Re = flight_condition.get_reynolds_number(chord, V_rel)
            M = flight_condition.get_mach_number(V_rel)

            # 使用翼型插值器
            try:
                Cl, Cd = self.airfoil_interpolator.interpolate(airfoil_name, alpha, Re, M)
                solution_vars['Cl'][i] = Cl
                solution_vars['Cd'][i] = Cd
            except:
                # 回退到简化计算
                alpha_rad = np.radians(alpha)
                solution_vars['Cl'][i] = 2 * np.pi * alpha_rad
                solution_vars['Cd'][i] = 0.01 + 0.1 * alpha_rad**2

                # 失速限制
                if abs(alpha_rad) > np.radians(15.0):
                    solution_vars['Cl'][i] = np.sign(alpha_rad) * 1.2
                    solution_vars['Cd'][i] = 0.5 + 0.3 * abs(alpha_rad)

    def _apply_dynamic_stall(self, solution_vars: Dict[str, np.ndarray],
                            flight_condition: FlightCondition, r_stations: np.ndarray):
        """应用动态失速修正"""

        if self.dynamic_stall is None:
            return

        # 计算攻角变化率（简化）
        dt = 0.01  # 假设时间步长

        for i in range(len(solution_vars['alpha'])):
            alpha = solution_vars['alpha'][i]
            Cl_static = solution_vars['Cl'][i]
            Cd_static = solution_vars['Cd'][i]

            try:
                # 使用完整的动态失速模型
                airfoil_params = {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64}
                Cl_dyn, Cd_dyn, _, _ = self.dynamic_stall.compute_dynamic_coefficients(
                    alpha, Cl_static, Cd_static, 0.0, 0.0, dt, airfoil_params
                )
                solution_vars['Cl'][i] = Cl_dyn
                solution_vars['Cd'][i] = Cd_dyn
            except:
                # 回退到简化实现
                alpha_rad = np.radians(alpha)
                dynamic_factor = 1.0 + 0.1 * np.sin(2 * alpha_rad)
                solution_vars['Cl'][i] *= dynamic_factor
                solution_vars['Cd'][i] *= (1.0 + 0.05 * abs(dynamic_factor - 1.0))

    def _apply_physical_corrections(self, solution_vars: Dict[str, np.ndarray],
                                   flight_condition: FlightCondition, r_stations: np.ndarray):
        """应用物理修正"""

        # 获取几何参数
        R = getattr(self.geometry, 'radius', 1.0)
        num_blades = getattr(self.geometry, 'num_blades', 4)
        hub_radius = getattr(self.geometry, 'hub_radius', 0.1)

        # 1. 叶尖损失修正（Prandtl模型）
        tip_loss_factor = self._compute_tip_loss_correction(r_stations, R, num_blades, solution_vars['phi'])

        # 2. 桂毂损失修正
        hub_loss_factor = self._compute_hub_loss_correction(r_stations, hub_radius, num_blades, solution_vars['phi'])

        # 3. 组合损失因子
        total_loss_factor = tip_loss_factor * hub_loss_factor

        # 4. 应用修正
        solution_vars['Cl'] *= np.maximum(total_loss_factor, 0.05)
        solution_vars['Cd'] *= np.maximum(total_loss_factor, 0.05)

        # 5. 压缩性修正（简化）
        if hasattr(flight_condition, 'get_mach_number'):
            self._apply_compressibility_correction(solution_vars, flight_condition, r_stations)

    def _compute_tip_loss_correction(self, r_stations: np.ndarray, R: float,
                                   num_blades: int, phi: np.ndarray) -> np.ndarray:
        """计算叶尖损失修正因子"""

        tip_loss_factor = np.ones_like(r_stations)

        for i, (r, phi_val) in enumerate(zip(r_stations, phi)):
            if r < 0.98 * R and abs(phi_val) > 1e-6:
                # Prandtl叶尖损失公式
                f_arg = num_blades * (R - r) / (2 * r * abs(np.sin(phi_val)))
                f_arg = np.clip(f_arg, 0.01, 100.0)  # 限制范围

                tip_loss_factor[i] = (2 / np.pi) * np.arccos(np.exp(-f_arg))
            elif r >= 0.98 * R:
                # 叶尖区域强烈衰减
                tip_loss_factor[i] = 0.1

        return np.clip(tip_loss_factor, 0.05, 1.0)

    def _compute_hub_loss_correction(self, r_stations: np.ndarray, hub_radius: float,
                                   num_blades: int, phi: np.ndarray) -> np.ndarray:
        """计算桂毂损失修正因子"""

        hub_loss_factor = np.ones_like(r_stations)

        for i, (r, phi_val) in enumerate(zip(r_stations, phi)):
            if r < 1.5 * hub_radius and abs(phi_val) > 1e-6:
                # 桂毂损失公式
                f_arg = num_blades * (r - hub_radius) / (2 * hub_radius * abs(np.sin(phi_val)))
                f_arg = np.clip(f_arg, 0.01, 100.0)

                hub_loss_factor[i] = (2 / np.pi) * np.arccos(np.exp(-f_arg))
            elif r <= hub_radius:
                hub_loss_factor[i] = 0.1

        return np.clip(hub_loss_factor, 0.05, 1.0)

    def _apply_compressibility_correction(self, solution_vars: Dict[str, np.ndarray],
                                        flight_condition: FlightCondition, r_stations: np.ndarray):
        """应用压缩性修正"""

        # 简化的压缩性修正
        omega = flight_condition.omega

        for i, r in enumerate(r_stations):
            # 计算当地马赫数
            V_tip = omega * r
            M_local = V_tip / 343.0  # 假设音速343 m/s

            if M_local > 0.3:  # 需要压缩性修正
                # Prandtl-Glauert修正
                beta = safe_sqrt(1 - M_local**2, 0.1)
                solution_vars['Cl'][i] /= beta
                solution_vars['Cd'][i] *= (1 + 0.1 * M_local**2)  # 简化的阻力修正

    def _compute_load_distribution(self, solution_vars: Dict[str, np.ndarray],
                                  flight_condition: FlightCondition, r_stations: np.ndarray):
        """计算载荷分布"""

        # 几何参数
        R = getattr(self.geometry, 'radius', 1.0)
        num_blades = getattr(self.geometry, 'num_blades', 4)
        n_stations = len(solution_vars['lambda_i'])

        # 获取弦长分布
        if hasattr(self.geometry, 'blade_geometry') and hasattr(self.geometry.blade_geometry, 'chord_stations'):
            chord_stations = self.geometry.blade_geometry.chord_stations
            # 确保长度匹配
            if len(chord_stations) != n_stations:
                # 插值到正确的站位数
                r_original = np.linspace(0, 1, len(chord_stations))
                r_target = np.linspace(0, 1, n_stations)
                chord_stations = np.interp(r_target, r_original, chord_stations)
        else:
            # 简化：线性锥度分布
            chord_stations = np.linspace(0.15, 0.05, n_stations)

        # 径向微元长度
        dr_stations = np.diff(np.concatenate([[r_stations[0] * 0.8], r_stations]))

        # 相对速度
        V_axial = flight_condition.V_inf[0] + solution_vars['lambda_i'] * flight_condition.omega * R
        V_tangential = flight_condition.omega * r_stations
        V_rel = safe_sqrt(V_axial**2 + V_tangential**2)

        # 动压
        q = 0.5 * flight_condition.rho * V_rel**2

        # 流入角
        phi_rad = np.radians(solution_vars['phi'])

        # 微元推力和扭矩
        solution_vars['dT'] = (num_blades * q * chord_stations *
                              (solution_vars['Cl'] * np.cos(phi_rad) -
                               solution_vars['Cd'] * np.sin(phi_rad)) * dr_stations)

        solution_vars['dQ'] = (num_blades * q * chord_stations *
                              (solution_vars['Cl'] * np.sin(phi_rad) +
                               solution_vars['Cd'] * np.cos(phi_rad)) *
                              r_stations * dr_stations)

    def _update_induced_velocity(self, solution_vars: Dict[str, np.ndarray],
                                flight_condition: FlightCondition, r_stations: np.ndarray):
        """更新诱导速度（动量理论）"""

        # 几何参数
        R = getattr(self.geometry, 'radius', 1.0)
        n_stations = len(solution_vars['lambda_i'])

        # 环形面积
        dr_stations = np.diff(np.concatenate([[r_stations[0] * 0.8], r_stations]))
        dA = 2 * np.pi * r_stations * dr_stations

        # 局部推力系数
        rho_omega_R_squared = flight_condition.rho * (flight_condition.omega * R)**2
        CT_local = safe_divide(solution_vars['dT'], rho_omega_R_squared * dA, 0.0)

        # 动量理论诱导速度比
        mu = self.advance_ratio
        lambda_i_new = np.zeros_like(CT_local)

        for i, ct in enumerate(CT_local):
            ct = max(ct, 0.0)  # 确保非负

            if mu < 0.1:  # 悬停或低速前飞
                lambda_i_new[i] = safe_sqrt(ct / 2.0)
            else:  # 前飞
                # 求解二次方程：lambda_i^2 + mu*lambda_i - ct/2 = 0
                discriminant = mu**2 + 2*ct
                if discriminant >= 0:
                    lambda_i_new[i] = (-mu + safe_sqrt(discriminant)) / 2.0
                else:
                    lambda_i_new[i] = 0.0

        # 限制诱导速度比的范围
        lambda_i_new = np.clip(lambda_i_new, 0.0, 0.5)

        solution_vars['lambda_i'] = lambda_i_new

    def _apply_relaxation(self, solution_vars: Dict[str, np.ndarray],
                         old_vars: Dict[str, np.ndarray], relaxation_factor: float):
        """应用松弛因子"""

        # 主要变量的松弛
        for key in ['lambda_i', 'alpha']:
            if key in solution_vars and key in old_vars:
                solution_vars[key] = (relaxation_factor * solution_vars[key] +
                                    (1 - relaxation_factor) * old_vars[key])

        # 气动系数的松弛（较小的松弛因子）
        coeff_relaxation = min(relaxation_factor, 0.3)
        for key in ['Cl', 'Cd']:
            if key in solution_vars and key in old_vars:
                solution_vars[key] = (coeff_relaxation * solution_vars[key] +
                                    (1 - coeff_relaxation) * old_vars[key])

    def _compute_residual(self, solution_vars: Dict[str, np.ndarray],
                         old_vars: Dict[str, np.ndarray]) -> float:
        """计算残差"""

        residuals = []

        # 诱导速度残差（主要收敛指标）
        if 'lambda_i' in solution_vars and 'lambda_i' in old_vars:
            lambda_diff = np.abs(solution_vars['lambda_i'] - old_vars['lambda_i'])
            # 相对残差
            lambda_ref = np.maximum(np.abs(old_vars['lambda_i']), 1e-6)
            lambda_residual = np.max(lambda_diff / lambda_ref)
            residuals.append(lambda_residual)

        # 攻角残差
        if 'alpha' in solution_vars and 'alpha' in old_vars:
            alpha_diff = np.abs(solution_vars['alpha'] - old_vars['alpha'])
            alpha_residual = np.max(alpha_diff)
            residuals.append(alpha_residual * 0.1)  # 降低权重

        # 升力系数残差
        if 'Cl' in solution_vars and 'Cl' in old_vars:
            cl_diff = np.abs(solution_vars['Cl'] - old_vars['Cl'])
            cl_ref = np.maximum(np.abs(old_vars['Cl']), 0.1)
            cl_residual = np.max(cl_diff / cl_ref)
            residuals.append(cl_residual * 0.05)  # 较小权重

        return max(residuals) if residuals else 0.0

    def _postprocess(self, solution_vars: Dict[str, np.ndarray],
                    flight_condition: FlightCondition, **kwargs) -> Dict[str, Any]:
        """后处理计算总体性能"""

        # 计算总推力和功率
        thrust = np.sum(solution_vars['dT'])
        power = flight_condition.omega * np.sum(solution_vars['dQ'])

        # 计算无量纲系数
        R = getattr(self.geometry, 'radius', 1.0)
        A = np.pi * R**2  # 旋翼盘面积

        CT = thrust / (flight_condition.rho * (flight_condition.omega * R)**2 * A)
        CP = power / (flight_condition.rho * (flight_condition.omega * R)**3 * A)

        # 品质因数（悬停时）
        if self.advance_ratio < 0.1:
            FM = CT**1.5 / (np.sqrt(2) * CP) if CP > 0 else 0.0
        else:
            FM = 0.0

        results = {
            'thrust': thrust,
            'power': power,
            'CT': CT,
            'CP': CP,
            'FM': FM,
            'advance_ratio': self.advance_ratio,
            'radial_distributions': {
                'r_stations': np.linspace(0.2, 1.0, len(solution_vars['lambda_i'])),
                'lambda_i': solution_vars['lambda_i'],
                'alpha': solution_vars['alpha'],
                'Cl': solution_vars['Cl'],
                'Cd': solution_vars['Cd'],
                'dT': solution_vars['dT'],
                'dQ': solution_vars['dQ']
            }
        }

        return results

    # ========== 简化的物理模型创建方法 ==========

    def _create_simple_airfoil_interpolator(self):
        """创建简化的翼型插值器"""
        class SimpleAirfoilInterpolator:
            def interpolate(self, airfoil_name, alpha, Re, M):
                """简化的翼型插值"""
                alpha_rad = np.radians(alpha)
                Cl = 2 * np.pi * alpha_rad
                Cd = 0.01 + 0.1 * alpha_rad**2
                return Cl, Cd

        return SimpleAirfoilInterpolator()

    def _create_simple_dynamic_stall(self):
        """创建简化的动态失速模型"""
        class SimpleDynamicStall:
            def compute_dynamic_coefficients(self, alpha, Cl_static, Cd_static,
                                           x1, x2, dt, airfoil_params):
                """简化的动态失速计算"""
                dynamic_factor = 1.0 + 0.1 * np.sin(2 * np.radians(alpha))
                Cl_dyn = Cl_static * dynamic_factor
                Cd_dyn = Cd_static * (1.0 + 0.05 * abs(dynamic_factor - 1.0))
                return Cl_dyn, Cd_dyn, x1, x2

        return SimpleDynamicStall()

    def _create_simple_coriolis_effects(self):
        """创建简化的科里奥利效应模型"""
        class SimpleCoriolisEffects:
            def compute_correction(self, solution_vars, flight_condition, geometry):
                """简化的科里奥利效应计算"""
                return 0.05 * np.sin(np.radians(solution_vars['alpha']))

        return SimpleCoriolisEffects()

    def _create_simple_centrifugal_effects(self):
        """创建简化的离心效应模型"""
        class SimpleCentrifugalEffects:
            def compute_correction(self, solution_vars, flight_condition, geometry):
                """简化的离心效应计算"""
                return 0.02 * np.cos(np.radians(solution_vars['alpha']))

        return SimpleCentrifugalEffects()

    def _create_simple_inflow_model(self):
        """创建简化的非均匀流入模型"""
        class SimpleInflowModel:
            def compute_induced_velocity(self, solution_vars, flight_condition, geometry):
                """简化的非均匀流入计算"""
                return solution_vars['lambda_i']  # 暂时返回原值

        return SimpleInflowModel()

    def _create_simple_corrections(self):
        """创建简化的修正模型"""
        class SimpleCorrections:
            def compute_tip_loss_correction(self, r_stations, radius, num_blades):
                """简化的叶尖损失修正"""
                return np.ones_like(r_stations)  # 暂时不修正

            def compute_hub_loss_correction(self, r_stations, hub_radius, num_blades):
                """简化的桂毂损失修正"""
                return np.ones_like(r_stations)  # 暂时不修正

        return SimpleCorrections()

    def _create_simple_convergence_monitor(self):
        """创建简化的收敛监控器"""
        class SimpleConvergenceMonitor:
            def __init__(self):
                self.iteration = 0
                self.residual_history = []
                self.current_residual = 0.0

            def reset(self):
                self.iteration = 0
                self.residual_history = []
                self.current_residual = 0.0

            def update(self, residual):
                self.iteration += 1
                self.current_residual = residual
                self.residual_history.append(residual)
                return False  # 简化实现，不检查收敛

            def is_converged(self):
                return False  # 简化实现，总是返回False

        return SimpleConvergenceMonitor()

# 简化的几何类（如果导入失败时使用）
class SimpleRotorGeometry3D:
    """简化的3D旋翼几何类"""

    def __init__(self, radius=1.0, num_blades=4, chord=0.1, **kwargs):
        self.radius = radius
        self.num_blades = num_blades
        self.chord = chord
        self.hub_radius = kwargs.get('hub_radius', 0.1 * radius)

        # 径向站位
        n_stations = kwargs.get('n_stations', 10)
        self.r_stations = np.linspace(0.2 * radius, radius, n_stations)
        self.chord_stations = np.full(n_stations, chord)
        self.twist_stations = np.zeros(n_stations)  # 无扭转
        self.dr_stations = np.full(n_stations, 0.8 * radius / n_stations)

    def update_geometry(self):
        """更新几何参数"""
        pass

    def get_airfoil_at_station(self, i):
        """获取指定站位的翼型名称"""
        return "naca0012"

    def get_airfoil_params_at_station(self, i):
        """获取指定站位的翼型参数"""
        return {'A1': 0.165, 'A2': 0.125, 'b1': 0.8, 'b2': 0.64}

# 注意：BEMTMediumError已在utils.error_handling中定义
