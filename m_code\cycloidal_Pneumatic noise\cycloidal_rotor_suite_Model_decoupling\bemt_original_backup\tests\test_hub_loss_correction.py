#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桂毂损失修正测试
===============

验证桂毂损失修正的物理正确性和数值精度。

测试内容:
1. Prandtl桂毂损失公式正确性
2. 桂毂区域载荷分布特性
3. 不同桂毂比的影响
4. 与理论解对比验证
5. 物理合理性检查
6. 数值稳定性测试

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import time
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import unittest

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_data_config import TEST_DATA
from physics.corrections import ComprehensivePhysicalCorrections
from simple_bemt import SimpleBEMT


class TestHubLossCorrection(unittest.TestCase):
    """桂毂损失修正测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_data = TEST_DATA
        self.tolerance = 1e-4
        
        # 获取真实测试参数
        self.rotor_config = self.test_data.get_rotor_config('UH-60')
        self.flight_config = self.test_data.get_flight_config('hover')
        
        # 初始化物理修正模块
        self.corrections = ComprehensivePhysicalCorrections(
            radius=self.rotor_config.radius,
            hub_radius=self.rotor_config.hub_radius,
            num_blades=self.rotor_config.num_blades
        )
        
        # 创建BEMT求解器
        self.solver = SimpleBEMT(
            radius=self.rotor_config.radius,
            num_blades=self.rotor_config.num_blades,
            hub_radius=self.rotor_config.hub_radius
        )
        
        print(f"\n🧪 桂毂损失修正测试初始化")
        print(f"   旋翼半径: {self.rotor_config.radius:.2f}m")
        print(f"   桂毂半径: {self.rotor_config.hub_radius:.2f}m")
        print(f"   桂毂比: {self.rotor_config.hub_radius/self.rotor_config.radius:.3f}")
        print(f"   桨叶数: {self.rotor_config.num_blades}")
    
    def test_prandtl_hub_loss_formula(self):
        """测试Prandtl桂毂损失公式正确性"""
        print("\n📋 测试1: Prandtl桂毂损失公式正确性")
        
        # 创建径向站位
        n_stations = 30
        r_stations = np.linspace(
            self.rotor_config.hub_radius + 0.01,  # 避免桂毂边界
            self.rotor_config.radius,
            n_stations
        )
        
        # 模拟解变量（包含流入角）
        solution_vars = {
            'phi': np.full(n_stations, np.radians(5.0)),  # 5度流入角
            'alpha': np.full(n_stations, np.radians(8.0)),
            'lambda_i': np.full(n_stations, 0.05)
        }
        
        # 计算桂毂损失因子
        hub_loss_factor = self.corrections.compute_hub_loss_correction(
            r_stations, solution_vars
        )
        
        # 验证桂毂损失特性
        
        # 1. 损失因子应该在[0,1]范围内
        self.assertTrue(np.all(hub_loss_factor >= 0),
                       "桂毂损失因子应该非负")
        self.assertTrue(np.all(hub_loss_factor <= 1),
                       "桂毂损失因子应该不超过1")
        
        # 2. 桂毂附近损失因子应该较小
        r_R = r_stations / self.rotor_config.radius
        hub_region_mask = r_R < 0.3
        tip_region_mask = r_R > 0.8
        
        if np.any(hub_region_mask) and np.any(tip_region_mask):
            avg_hub_loss = np.mean(hub_loss_factor[hub_region_mask])
            avg_tip_loss = np.mean(hub_loss_factor[tip_region_mask])
            
            self.assertLess(avg_hub_loss, avg_tip_loss,
                           f"桂毂区域损失({avg_hub_loss:.3f})应大于叶尖区域({avg_tip_loss:.3f})")
        
        # 3. 损失因子应该单调递增
        # 允许小的数值波动
        diff = np.diff(hub_loss_factor)
        monotonic_violations = np.sum(diff < -0.01)  # 允许1%的回落
        monotonic_ratio = 1 - monotonic_violations / len(diff)
        
        self.assertGreater(monotonic_ratio, 0.9,
                          f"桂毂损失因子单调性不足: {monotonic_ratio*100:.1f}%")
        
        # 4. 验证Prandtl公式的数学正确性
        # 在已知条件下验证公式
        test_r_R = 0.5
        test_phi = np.radians(5.0)
        hub_ratio = self.rotor_config.hub_radius / self.rotor_config.radius
        
        # 手动计算Prandtl桂毂损失
        f_hub = (self.rotor_config.num_blades / 2) * (test_r_R - hub_ratio) / (test_r_R * np.sin(test_phi))
        expected_loss_factor = (2.0 / np.pi) * np.arccos(np.exp(-f_hub))
        
        # 找到最接近的计算点
        closest_idx = np.argmin(np.abs(r_R - test_r_R))
        computed_loss_factor = hub_loss_factor[closest_idx]
        
        relative_error = abs(computed_loss_factor - expected_loss_factor) / expected_loss_factor
        self.assertLess(relative_error, 0.1,
                       f"Prandtl公式计算误差过大: {relative_error*100:.1f}%")
        
        print(f"   损失因子范围: {np.min(hub_loss_factor):.3f} - {np.max(hub_loss_factor):.3f}")
        print(f"   桂毂区域平均损失: {avg_hub_loss:.3f}" if 'avg_hub_loss' in locals() else "")
        print(f"   叶尖区域平均损失: {avg_tip_loss:.3f}" if 'avg_tip_loss' in locals() else "")
        print(f"   单调性: {monotonic_ratio*100:.1f}%")
        print(f"   公式验证误差: {relative_error*100:.1f}%")
        print("   ✅ Prandtl桂毂损失公式正确性测试通过")
    
    def test_hub_region_load_distribution(self):
        """测试桂毂区域载荷分布特性"""
        print("\n📋 测试2: 桂毂区域载荷分布特性")
        
        # 使用BEMT求解获得真实载荷分布
        result = self.solver.solve(
            rpm=self.flight_config.rpm,
            forward_speed=self.flight_config.forward_speed,
            verbose=False
        )
        
        self.assertTrue(result['converged'], "BEMT求解应该收敛")
        
        # 获取载荷分布
        thrust_dist = result.get('thrust_distribution', np.array([]))
        torque_dist = result.get('torque_distribution', np.array([]))
        
        if len(thrust_dist) == 0:
            # 如果没有分布数据，创建模拟数据
            n_stations = 20
            r_stations = np.linspace(
                self.rotor_config.hub_radius,
                self.rotor_config.radius,
                n_stations
            )
            
            # 模拟典型的载荷分布
            r_R = r_stations / self.rotor_config.radius
            thrust_dist = 1000 * r_R * (1 - r_R**2)  # 典型推力分布
            torque_dist = 50 * r_R**2 * (1 - r_R)    # 典型扭矩分布
        else:
            n_stations = len(thrust_dist)
            r_stations = np.linspace(
                self.rotor_config.hub_radius,
                self.rotor_config.radius,
                n_stations
            )
        
        # 应用桂毂损失修正
        solution_vars = {
            'phi': np.full(n_stations, np.radians(5.0)),
            'alpha': np.full(n_stations, np.radians(8.0))
        }
        
        hub_loss_factor = self.corrections.compute_hub_loss_correction(
            r_stations, solution_vars
        )
        
        # 修正后的载荷
        corrected_thrust = thrust_dist * hub_loss_factor
        corrected_torque = torque_dist * hub_loss_factor
        
        # 验证桂毂区域载荷特性
        
        # 1. 桂毂区域载荷应该显著减少
        r_R = r_stations / self.rotor_config.radius
        hub_region_mask = r_R < 0.25
        
        if np.any(hub_region_mask):
            original_hub_thrust = np.mean(thrust_dist[hub_region_mask])
            corrected_hub_thrust = np.mean(corrected_thrust[hub_region_mask])
            
            if original_hub_thrust > 0:
                thrust_reduction = (original_hub_thrust - corrected_hub_thrust) / original_hub_thrust
                self.assertGreater(thrust_reduction, 0.1,
                                 f"桂毂区域推力减少不足: {thrust_reduction*100:.1f}%")
        
        # 2. 总载荷应该减少
        total_original_thrust = np.trapz(thrust_dist, r_stations)
        total_corrected_thrust = np.trapz(corrected_thrust, r_stations)
        
        if total_original_thrust > 0:
            total_thrust_reduction = (total_original_thrust - total_corrected_thrust) / total_original_thrust
            self.assertGreater(total_thrust_reduction, 0.01,
                             f"总推力减少不足: {total_thrust_reduction*100:.1f}%")
            self.assertLess(total_thrust_reduction, 0.2,
                           f"总推力减少过多: {total_thrust_reduction*100:.1f}%")
        
        # 3. 载荷分布应该保持物理合理性
        self.assertTrue(np.all(corrected_thrust >= 0),
                       "修正后推力分布应该非负")
        self.assertTrue(np.all(corrected_torque >= 0),
                       "修正后扭矩分布应该非负")
        
        # 4. 验证载荷梯度的合理性
        thrust_gradient = np.gradient(corrected_thrust, r_stations)
        # 在大部分区域，推力梯度应该合理
        reasonable_gradient_mask = np.abs(thrust_gradient) < 1e6  # 避免数值奇点
        reasonable_ratio = np.sum(reasonable_gradient_mask) / len(thrust_gradient)
        
        self.assertGreater(reasonable_ratio, 0.8,
                          f"载荷梯度合理性不足: {reasonable_ratio*100:.1f}%")
        
        print(f"   桂毂区域推力减少: {thrust_reduction*100:.1f}%" if 'thrust_reduction' in locals() else "")
        print(f"   总推力减少: {total_thrust_reduction*100:.1f}%" if 'total_thrust_reduction' in locals() else "")
        print(f"   载荷梯度合理性: {reasonable_ratio*100:.1f}%")
        print("   ✅ 桂毂区域载荷分布特性测试通过")
    
    def test_hub_ratio_effects(self):
        """测试不同桂毂比的影响"""
        print("\n📋 测试3: 不同桂毂比的影响")
        
        # 测试不同的桂毂比
        hub_ratios = [0.05, 0.1, 0.15, 0.2, 0.25]  # 5% - 25%
        
        hub_ratio_results = {}
        
        for hub_ratio in hub_ratios:
            # 创建对应的修正器
            test_hub_radius = hub_ratio * self.rotor_config.radius
            
            test_corrections = ComprehensivePhysicalCorrections(
                radius=self.rotor_config.radius,
                hub_radius=test_hub_radius,
                num_blades=self.rotor_config.num_blades
            )
            
            # 创建径向站位
            n_stations = 25
            r_stations = np.linspace(
                test_hub_radius + 0.01,
                self.rotor_config.radius,
                n_stations
            )
            
            # 计算桂毂损失
            solution_vars = {
                'phi': np.full(n_stations, np.radians(5.0)),
                'alpha': np.full(n_stations, np.radians(8.0))
            }
            
            hub_loss_factor = test_corrections.compute_hub_loss_correction(
                r_stations, solution_vars
            )
            
            # 统计结果
            min_loss_factor = np.min(hub_loss_factor)
            avg_loss_factor = np.mean(hub_loss_factor)
            
            hub_ratio_results[hub_ratio] = {
                'min_loss_factor': min_loss_factor,
                'avg_loss_factor': avg_loss_factor,
                'hub_radius': test_hub_radius
            }
        
        # 验证桂毂比效应
        
        # 1. 桂毂比越大，损失应该越严重
        hub_ratios_sorted = sorted(hub_ratios)
        min_loss_factors = [hub_ratio_results[hr]['min_loss_factor'] for hr in hub_ratios_sorted]
        
        # 检查单调性趋势
        decreasing_trend = 0
        for i in range(1, len(min_loss_factors)):
            if min_loss_factors[i] <= min_loss_factors[i-1]:
                decreasing_trend += 1
        
        trend_ratio = decreasing_trend / (len(min_loss_factors) - 1)
        self.assertGreater(trend_ratio, 0.6,
                          f"桂毂比效应趋势不明显: {trend_ratio*100:.1f}%")
        
        # 2. 验证数值合理性
        for hub_ratio, result in hub_ratio_results.items():
            self.assertGreaterEqual(result['min_loss_factor'], 0,
                                   f"桂毂比{hub_ratio}的最小损失因子应非负")
            self.assertLessEqual(result['avg_loss_factor'], 1,
                                f"桂毂比{hub_ratio}的平均损失因子应不超过1")
        
        # 3. 极端桂毂比的合理性检查
        smallest_hub_result = hub_ratio_results[min(hub_ratios)]
        largest_hub_result = hub_ratio_results[max(hub_ratios)]
        
        self.assertGreater(smallest_hub_result['avg_loss_factor'],
                          largest_hub_result['avg_loss_factor'],
                          "小桂毂比的平均损失因子应大于大桂毂比")
        
        print("   桂毂比效应:")
        for hub_ratio in hub_ratios_sorted:
            result = hub_ratio_results[hub_ratio]
            print(f"     {hub_ratio*100:4.1f}%: 最小={result['min_loss_factor']:.3f}, "
                  f"平均={result['avg_loss_factor']:.3f}")
        
        print(f"   单调性趋势: {trend_ratio*100:.1f}%")
        print("   ✅ 不同桂毂比的影响测试通过")
    
    def test_theoretical_comparison(self):
        """测试与理论解对比验证"""
        print("\n📋 测试4: 与理论解对比验证")
        
        # 使用简化的理论模型进行对比
        # 理论上，在远离桂毂的区域，损失因子应该接近1
        
        n_stations = 50
        r_stations = np.linspace(
            self.rotor_config.hub_radius + 0.1,  # 远离桂毂
            self.rotor_config.radius,
            n_stations
        )
        
        # 不同流入角的测试
        phi_values = [np.radians(2.0), np.radians(5.0), np.radians(10.0)]
        
        theoretical_comparison = {}
        
        for phi in phi_values:
            solution_vars = {
                'phi': np.full(n_stations, phi),
                'alpha': np.full(n_stations, np.radians(8.0))
            }
            
            hub_loss_factor = self.corrections.compute_hub_loss_correction(
                r_stations, solution_vars
            )
            
            # 理论预期：远离桂毂区域损失因子接近1
            r_R = r_stations / self.rotor_config.radius
            far_field_mask = r_R > 0.7  # 远场区域
            
            if np.any(far_field_mask):
                far_field_avg = np.mean(hub_loss_factor[far_field_mask])
                
                # 远场区域损失因子应该接近1
                self.assertGreater(far_field_avg, 0.9,
                                 f"流入角{np.rad2deg(phi):.1f}°时远场损失因子过小: {far_field_avg:.3f}")
            
            # 理论验证：小流入角时损失更严重
            hub_region_mask = r_R < 0.3
            if np.any(hub_region_mask):
                hub_region_avg = np.mean(hub_loss_factor[hub_region_mask])
                
                theoretical_comparison[phi] = {
                    'phi_deg': np.rad2deg(phi),
                    'far_field_avg': far_field_avg if 'far_field_avg' in locals() else 1.0,
                    'hub_region_avg': hub_region_avg
                }
        
        # 验证流入角效应
        phi_sorted = sorted(theoretical_comparison.keys())
        hub_region_avgs = [theoretical_comparison[phi]['hub_region_avg'] for phi in phi_sorted]
        
        # 流入角越小，桂毂损失应该越严重（损失因子越小）
        if len(hub_region_avgs) >= 2:
            small_phi_loss = hub_region_avgs[0]
            large_phi_loss = hub_region_avgs[-1]
            
            # 允许一定的数值误差
            if abs(small_phi_loss - large_phi_loss) > 0.01:
                self.assertLess(small_phi_loss, large_phi_loss * 1.1,
                               "小流入角时桂毂损失应该更严重")
        
        print("   理论对比结果:")
        for phi, result in theoretical_comparison.items():
            print(f"     流入角{result['phi_deg']:4.1f}°: "
                  f"远场={result['far_field_avg']:.3f}, "
                  f"桂毂区域={result['hub_region_avg']:.3f}")
        
        print("   ✅ 理论解对比验证通过")
    
    def test_physical_reasonableness(self):
        """测试物理合理性检查"""
        print("\n📋 测试5: 物理合理性检查")
        
        # 测试各种物理约束
        test_conditions = [
            # (phi_deg, description)
            (1.0, "极小流入角"),
            (5.0, "典型流入角"),
            (15.0, "大流入角"),
            (30.0, "极大流入角"),
        ]
        
        n_stations = 30
        r_stations = np.linspace(
            self.rotor_config.hub_radius + 0.01,
            self.rotor_config.radius,
            n_stations
        )
        
        for phi_deg, description in test_conditions:
            phi = np.radians(phi_deg)
            
            solution_vars = {
                'phi': np.full(n_stations, phi),
                'alpha': np.full(n_stations, np.radians(8.0))
            }
            
            hub_loss_factor = self.corrections.compute_hub_loss_correction(
                r_stations, solution_vars
            )
            
            # 物理合理性检查
            
            # 1. 损失因子应该有界
            self.assertTrue(np.all(np.isfinite(hub_loss_factor)),
                           f"{description}: 损失因子包含无效值")
            
            self.assertTrue(np.all(hub_loss_factor >= 0),
                           f"{description}: 损失因子应该非负")
            
            self.assertTrue(np.all(hub_loss_factor <= 1.1),  # 允许小的数值误差
                           f"{description}: 损失因子不应超过1")
            
            # 2. 桂毂附近损失应该更严重
            r_R = r_stations / self.rotor_config.radius
            hub_loss = hub_loss_factor[0]  # 最靠近桂毂的点
            tip_loss = hub_loss_factor[-1]  # 叶尖点
            
            self.assertLessEqual(hub_loss, tip_loss + 0.1,  # 允许数值误差
                                f"{description}: 桂毂损失应不大于叶尖损失")
            
            # 3. 损失因子变化应该平滑
            gradient = np.gradient(hub_loss_factor, r_stations)
            max_gradient = np.max(np.abs(gradient))
            
            # 梯度不应过大（避免数值振荡）
            self.assertLess(max_gradient, 100.0,
                           f"{description}: 损失因子梯度过大: {max_gradient:.1f}")
            
            print(f"     {description}: 范围=[{np.min(hub_loss_factor):.3f}, {np.max(hub_loss_factor):.3f}], "
                  f"最大梯度={max_gradient:.1f}")
        
        print("   ✅ 物理合理性检查通过")
    
    def test_numerical_stability(self):
        """测试数值稳定性"""
        print("\n📋 测试6: 数值稳定性测试")
        
        # 测试极端条件下的数值稳定性
        extreme_conditions = [
            # (phi_deg, description)
            (0.1, "极小流入角"),
            (89.0, "接近90度流入角"),
            (0.0, "零流入角"),  # 可能导致除零
        ]
        
        n_stations = 20
        r_stations = np.linspace(
            self.rotor_config.hub_radius + 0.001,  # 非常接近桂毂
            self.rotor_config.radius,
            n_stations
        )
        
        stability_results = []
        
        for phi_deg, description in extreme_conditions:
            phi = np.radians(phi_deg)
            
            solution_vars = {
                'phi': np.full(n_stations, phi),
                'alpha': np.full(n_stations, np.radians(8.0))
            }
            
            try:
                hub_loss_factor = self.corrections.compute_hub_loss_correction(
                    r_stations, solution_vars
                )
                
                # 检查数值稳定性
                is_finite = np.all(np.isfinite(hub_loss_factor))
                is_bounded = np.all((hub_loss_factor >= -0.1) & (hub_loss_factor <= 1.1))
                is_reasonable = np.all(hub_loss_factor >= 0)
                
                stable = is_finite and is_bounded and is_reasonable
                
                stability_results.append({
                    'condition': description,
                    'stable': stable,
                    'min_value': np.min(hub_loss_factor) if is_finite else float('nan'),
                    'max_value': np.max(hub_loss_factor) if is_finite else float('nan')
                })
                
                if not stable:
                    print(f"     {description}: 不稳定 - "
                          f"有限={is_finite}, 有界={is_bounded}, 合理={is_reasonable}")
                
            except Exception as e:
                stability_results.append({
                    'condition': description,
                    'stable': False,
                    'error': str(e)
                })
                print(f"     {description}: 异常 - {e}")
        
        # 统计稳定性
        stable_count = sum(1 for r in stability_results if r['stable'])
        stability_rate = stable_count / len(stability_results)
        
        # 至少80%的极端条件应该稳定
        self.assertGreaterEqual(stability_rate, 0.8,
                               f"数值稳定性不足: {stability_rate*100:.1f}%")
        
        print("   极端条件稳定性:")
        for result in stability_results:
            if result['stable']:
                print(f"     {result['condition']}: ✅ "
                      f"[{result['min_value']:.3f}, {result['max_value']:.3f}]")
            else:
                print(f"     {result['condition']}: ❌")
        
        print(f"   稳定性: {stability_rate*100:.1f}%")
        print("   ✅ 数值稳定性测试通过")


def run_hub_loss_correction_tests():
    """运行桂毂损失修正测试"""
    
    print("🚀 开始桂毂损失修正测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestHubLossCorrection)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # 统计结果
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"\n📊 桂毂损失修正测试结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed}")
    print(f"   失败: {failures}")
    print(f"   错误: {errors}")
    print(f"   通过率: {(passed/total_tests)*100:.1f}%")
    
    # 详细失败信息
    if failures or errors:
        print(f"\n❌ 失败详情:")
        for test, traceback in result.failures + result.errors:
            print(f"   {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    success = (failures == 0 and errors == 0)
    print(f"\n🎯 桂毂损失修正测试: {'✅ 通过' if success else '❌ 失败'}")
    
    return {
        'success': success,
        'total': total_tests,
        'passed': passed,
        'failed': failures + errors,
        'details': {
            'failures': result.failures,
            'errors': result.errors
        }
    }


if __name__ == "__main__":
    results = run_hub_loss_correction_tests()
    
    if results['success']:
        print("\n🎉 所有桂毂损失修正测试通过！")
    else:
        print(f"\n⚠️  {results['failed']}/{results['total']} 测试失败")
        sys.exit(1)
