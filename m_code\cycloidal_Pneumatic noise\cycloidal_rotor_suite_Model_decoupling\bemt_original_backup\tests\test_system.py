"""
系统测试脚本
===========

测试BEMT中保真度模块的基本功能是否正常工作。

测试内容：
- 模块导入测试
- 配置管理测试
- 求解器创建测试
- 基本求解测试
- 错误处理测试

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import traceback
import numpy as np
from typing import Dict, Any


def test_module_imports():
    """测试模块导入"""
    print("1. 测试模块导入...")
    
    try:
        # 测试核心模块导入
        from .core.bemt_solver import BEMTSolver
        from .core.solver_factory import SolverFactory
        from .core.convergence import ConvergenceController
        from .core.time_integration import TimeIntegrator
        from .core.performance_calculator import PerformanceCalculator
        print("   ✅ 核心模块导入成功")
        
        # 测试气动力学模块导入
        from .aerodynamics.blade_element import BladeElement, Blade
        from .aerodynamics.dynamic_stall import LeishmanBeddoesModel
        from .aerodynamics.airfoil_database import AirfoilDatabase
        print("   ✅ 气动力学模块导入成功")
        
        # 测试物理修正模块导入
        from .physics.corrections import UnifiedPhysicalCorrections
        print("   ✅ 物理修正模块导入成功")
        
        # 测试工具模块导入
        from .utils.config import ConfigManager
        from .utils.error_handling import ValidationError, validate_input
        print("   ✅ 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 未知错误: {e}")
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n2. 测试配置管理器...")
    
    try:
        from .utils.config import ConfigManager
        
        # 创建默认配置
        config = ConfigManager()
        print("   ✅ 默认配置创建成功")
        
        # 测试参数获取
        R_rotor = config.get('R_rotor')
        B = config.get('B')
        print(f"   ✅ 参数获取成功: R_rotor={R_rotor}, B={B}")
        
        # 测试参数设置
        config.set('R_rotor', 0.5)
        config.set('B', 6)
        print("   ✅ 参数设置成功")
        
        # 测试配置验证
        config.update({'omega_rotor': 150.0, 'c': 0.08})
        print("   ✅ 配置更新成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置管理器测试失败: {e}")
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n3. 测试错误处理...")
    
    try:
        from .utils.error_handling import (
            ValidationError, validate_input, safe_divide, safe_sqrt
        )
        
        # 测试输入验证
        validate_input(5.0, 'test_param', value_type=float, must_be_positive=True)
        print("   ✅ 输入验证成功")
        
        # 测试安全数学运算
        result1 = safe_divide(10.0, 2.0)
        result2 = safe_divide(10.0, 0.0, default_value=999.0)
        result3 = safe_sqrt(4.0)
        result4 = safe_sqrt(-1.0, default_value=0.0)
        
        print(f"   ✅ 安全数学运算成功: {result1}, {result2}, {result3}, {result4}")
        
        # 测试异常处理
        try:
            validate_input(-1.0, 'negative_param', must_be_positive=True)
            print("   ❌ 异常处理失败：应该抛出异常")
            return False
        except ValidationError:
            print("   ✅ 异常处理成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {e}")
        traceback.print_exc()
        return False


def test_blade_element():
    """测试叶素模块"""
    print("\n4. 测试叶素模块...")
    
    try:
        from .aerodynamics.blade_element import BladeElement, Blade
        
        # 创建叶素
        config = {'rho': 1.225, 'enable_dynamic_stall': False}
        element = BladeElement(0, 0.3, 0.08, 0.0, config)
        print("   ✅ 叶素创建成功")
        
        # 测试运动学更新
        element.update_kinematics(0.0, 0.0, 100.0)
        print("   ✅ 运动学更新成功")
        
        # 测试相对速度计算
        V_inf = np.array([0.0, 0.0, -5.0])
        V_rel = element.calculate_relative_velocity(V_inf)
        print(f"   ✅ 相对速度计算成功: {np.linalg.norm(V_rel):.2f} m/s")
        
        # 测试有效攻角计算
        alpha_eff = element.calculate_effective_angle_of_attack()
        print(f"   ✅ 有效攻角计算成功: {np.degrees(alpha_eff):.2f}°")
        
        # 创建完整桨叶
        blade_config = {
            'R_rotor': 0.5, 'c': 0.08, 'bemt_n_elements': 10,
            'blade_taper': 1.0, 'blade_twist_deg': 0.0
        }
        blade = Blade(0, blade_config)
        print(f"   ✅ 桨叶创建成功: {len(blade.elements)} 个叶素")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 叶素模块测试失败: {e}")
        traceback.print_exc()
        return False


def test_airfoil_database():
    """测试翼型数据库"""
    print("\n5. 测试翼型数据库...")
    
    try:
        from .aerodynamics.airfoil_database import AirfoilDatabase
        
        # 创建翼型数据库
        database = AirfoilDatabase()
        print("   ✅ 翼型数据库创建成功")
        
        # 测试系数查询
        Cl, Cd, Cm = database.get_coefficients('NACA0012', 8.0, 100000)
        print(f"   ✅ 系数查询成功: Cl={Cl:.3f}, Cd={Cd:.4f}, Cm={Cm:.3f}")
        
        # 测试翼型列表
        airfoil_list = database.get_airfoil_list()
        print(f"   ✅ 可用翼型: {len(airfoil_list)} 个")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 翼型数据库测试失败: {e}")
        traceback.print_exc()
        return False


def test_physical_corrections():
    """测试物理修正"""
    print("\n6. 测试物理修正...")
    
    try:
        from .physics.corrections import UnifiedPhysicalCorrections
        
        # 创建物理修正系统
        config = {
            'enable_tip_loss': True,
            'enable_hub_loss': True,
            'enable_viscous_effects': False
        }
        corrections = UnifiedPhysicalCorrections(config)
        print("   ✅ 物理修正系统创建成功")
        
        # 测试修正应用
        input_data = {
            'r': 0.4,
            'R': 0.5,
            'B': 4,
            'phi': np.radians(10),
            'omega': 100.0,
            'azimuth': 0.0,
            'Cl': 0.8,
            'Cd': 0.02,
            'alpha': np.radians(8)
        }
        
        corrected_data = corrections.apply_all(input_data)
        print(f"   ✅ 修正应用成功: Cl={corrected_data['Cl']:.3f}")
        
        # 测试启用的修正列表
        enabled_corrections = corrections.get_enabled_corrections()
        print(f"   ✅ 启用的修正: {enabled_corrections}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 物理修正测试失败: {e}")
        traceback.print_exc()
        return False


def test_solver_creation():
    """测试求解器创建"""
    print("\n7. 测试求解器创建...")
    
    try:
        from .core.solver_factory import SolverFactory
        
        # 创建求解器工厂
        factory = SolverFactory()
        print("   ✅ 求解器工厂创建成功")
        
        # 创建基本配置
        config = {
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 150.0,
            'rho': 1.225,
            'bemt_n_elements': 15,
            'bemt_max_iterations': 50,
            'bemt_tolerance': 1e-4,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 12.0
        }
        
        # 创建求解器
        solver = factory.create_solver('bemt_medium', config)
        print("   ✅ BEMT求解器创建成功")
        
        # 验证求解器属性
        print(f"   ✅ 求解器类型: {solver.solver_type}")
        print(f"   ✅ 保真度级别: {solver.fidelity_level}")
        print(f"   ✅ 桨叶数: {solver.B}")
        print(f"   ✅ 叶素数: {solver.n_elements}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 求解器创建测试失败: {e}")
        traceback.print_exc()
        return False


def test_basic_solve():
    """测试基本求解"""
    print("\n8. 测试基本求解...")
    
    try:
        from .core.solver_factory import SolverFactory
        
        # 创建求解器
        config = {
            'R_rotor': 0.3,
            'B': 4,
            'c': 0.06,
            'omega_rotor': 120.0,
            'rho': 1.225,
            'bemt_n_elements': 10,
            'bemt_max_iterations': 30,
            'bemt_tolerance': 1e-3,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 10.0,
            'enable_tip_loss': True,
            'enable_hub_loss': False,
            'enable_dynamic_stall': False
        }
        
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config)
        
        # 执行单步求解
        t = 0.0
        dt = 0.005
        result = solver.solve_step(t, dt)
        
        print("   ✅ 单步求解成功")
        
        # 检查结果
        performance = result['performance']
        thrust = performance['thrust']
        power = performance['power']
        convergence_info = result['convergence_info']
        
        print(f"   ✅ 推力: {thrust:.3f} N")
        print(f"   ✅ 功率: {power:.3f} W")
        print(f"   ✅ 收敛: {'是' if convergence_info['converged'] else '否'}")
        print(f"   ✅ 迭代次数: {convergence_info['iterations']}")
        
        # 检查结果合理性
        if thrust > 0 and power > 0 and np.isfinite(thrust) and np.isfinite(power):
            print("   ✅ 结果合理性检查通过")
            return True
        else:
            print("   ❌ 结果合理性检查失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 基本求解测试失败: {e}")
        traceback.print_exc()
        return False


def test_time_simulation():
    """测试时域仿真"""
    print("\n9. 测试时域仿真...")
    
    try:
        from .core.solver_factory import SolverFactory
        
        # 创建简化配置
        config = {
            'R_rotor': 0.25,
            'B': 3,
            'c': 0.05,
            'omega_rotor': 100.0,
            'rho': 1.225,
            'bemt_n_elements': 8,
            'bemt_max_iterations': 20,
            'bemt_tolerance': 5e-3,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 8.0,
            'enable_tip_loss': False,
            'enable_hub_loss': False,
            'enable_dynamic_stall': False
        }
        
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config)
        
        # 运行短时间仿真
        t_end = 0.05
        dt = 0.01
        t = 0.0
        step_count = 0
        
        thrust_history = []
        power_history = []
        
        while t < t_end and step_count < 10:  # 限制最大步数
            result = solver.solve_step(t, dt)
            
            thrust_history.append(result['performance']['thrust'])
            power_history.append(result['performance']['power'])
            
            t += dt
            step_count += 1
        
        print(f"   ✅ 时域仿真成功: {step_count} 步")
        
        # 检查结果变化
        if len(thrust_history) > 1:
            thrust_mean = np.mean(thrust_history)
            thrust_std = np.std(thrust_history)
            print(f"   ✅ 推力统计: {thrust_mean:.3f} ± {thrust_std:.3f} N")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 时域仿真测试失败: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("BEMT中保真度模块 - 系统测试")
    print("=" * 60)
    
    tests = [
        test_module_imports,
        test_config_manager,
        test_error_handling,
        test_blade_element,
        test_airfoil_database,
        test_physical_corrections,
        test_solver_creation,
        test_basic_solve,
        test_time_simulation
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return False


def main():
    """主函数"""
    try:
        success = run_all_tests()
        return 0 if success else 1
    except Exception as e:
        print(f"测试运行失败: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())