#!/usr/bin/env python3

from aerodynamics.blade_element import Blade, BladeElement

config = {
    'R_rotor': 0.3, 
    'B': 4, 
    'c': 0.1, 
    'bemt_n_elements': 3
}

print("Testing BladeElement...")
element = BladeElement(0, 0.2, 0.1, 0.0, config)
print(f"Has update_kinematics: {hasattr(element, 'update_kinematics')}")

try:
    element.update_kinematics(0.0, 0.0, 100.0)
    print("✅ BladeElement.update_kinematics call successful")
except Exception as e:
    print(f"❌ Error calling BladeElement.update_kinematics: {e}")

print("\nTesting Blade...")
blade = Blade(0, config)
print(f"Blade created with {len(blade.elements)} elements")

try:
    blade.update_kinematics(0.0, 0.0, 100.0)
    print("✅ Blade.update_kinematics call successful")
except Exception as e:
    print(f"❌ Error calling Blade.update_kinematics: {e}")

print("\nTesting BEMT solver import...")
try:
    from core.bemt_solver import BEMTSolver
    print("✅ BEMTSolver import successful")
except Exception as e:
    print(f"❌ Error importing BEMTSolver: {e}")