#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT中保真度模块 - 最终整合测试 (重构版)
=====================================

验证重构后的系统能够正常工作，包括：
1. 模块导入测试
2. 基本功能测试  
3. 核心算法验证
4. 性能基准测试
5. 与原始版本的功能对等性验证

作者: Augment Agent (重构版)
日期: 2025-07-28
"""

import sys
import os
import numpy as np
import time
import traceback
from pathlib import Path

# 添加重构模块路径
bemt_refactored_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(bemt_refactored_path.parent))  # 添加包含bemt_refactored的目录

def print_header():
    """打印测试标题"""
    print("=" * 80)
    print("🧪 BEMT中保真度模块 - 最终整合测试 (重构版)")
    print("=" * 80)
    print()

def test_module_imports():
    """测试重构模块导入"""
    print("1. 重构模块导入测试")
    print("-" * 50)
    
    import_results = {}
    
    # 测试重构后的模块导入
    modules_to_test = [
        ("bemt_refactored", "SimpleBEMT"),
        ("bemt_refactored.bemt.solver", "SimpleBEMT"),
        ("bemt_refactored.physics.aerodynamics", "EnhancedAirfoilInterpolator"),
        ("bemt_refactored.physics.corrections", "ComprehensivePhysicalCorrections"),
        ("bemt_refactored.geometry.rotor", "RotorGeometry"),
        ("bemt_refactored.utils.config", "BEMTConfig"),
        ("bemt_refactored.utils.gpu_acceleration", "get_gpu_manager"),
    ]
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            import_results[module_name] = "✅ 成功"
            print(f"   ✅ {module_name}.{class_name}")
        except Exception as e:
            import_results[module_name] = f"❌ 失败: {e}"
            print(f"   ❌ {module_name}.{class_name}: {e}")
    
    success_rate = sum(1 for result in import_results.values() if "成功" in result) / len(import_results)
    print(f"\n   导入成功率: {success_rate:.1%}")
    
    return import_results

def test_basic_functionality():
    """测试基本功能"""
    print("\n2. 基本功能测试")
    print("-" * 50)
    
    try:
        import bemt_refactored as bemt
        
        # 测试求解器创建
        print("   测试求解器创建...")
        solver = bemt.create_solver('UH-60', enable_gpu=False)
        print("   ✅ 求解器创建成功")
        
        # 测试基本求解
        print("   测试基本求解...")
        result = solver.solve(rpm=258, forward_speed=0, verbose=False)
        
        if result['converged']:
            print(f"   ✅ 求解成功: 推力={result['thrust']:.0f}N, 功率={result['power']/1000:.1f}kW")
        else:
            print(f"   ⚠️  求解未收敛: 迭代={result['iterations']}")
        
        # 测试配置系统
        print("   测试配置系统...")
        config = bemt.BEMTConfig.create_preset('standard')
        print(f"   ✅ 配置创建成功: {config}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_core_algorithms():
    """测试核心算法"""
    print("\n3. 核心算法验证")
    print("-" * 50)
    
    try:
        import bemt_refactored as bemt
        
        # UH-60黑鹰直升机参数
        uh60_params = {
            'radius': 8.18,      # 旋翼半径 [m]
            'hub_radius': 0.61,  # 桂毂半径 [m]
            'num_blades': 4,     # 桨叶数量
            'rpm': 258,          # 转速 [rpm]
            'forward_speed': 77.2,  # 前飞速度 [m/s] (150 knots)
            'density': 1.225     # 空气密度 [kg/m³]
        }
        
        print(f"   使用UH-60参数进行验证...")
        print(f"   旋翼半径: {uh60_params['radius']:.2f}m")
        print(f"   转速: {uh60_params['rpm']} RPM")
        print(f"   前飞速度: {uh60_params['forward_speed']:.1f} m/s")
        
        # 创建高保真度求解器
        solver = bemt.SimpleBEMT(
            radius=uh60_params['radius'],
            hub_radius=uh60_params['hub_radius'],
            num_blades=uh60_params['num_blades'],
            num_stations=30,
            enable_gpu=False,
            enable_adaptive_mesh=True
        )
        
        # 求解
        start_time = time.time()
        result = solver.solve(
            rpm=uh60_params['rpm'],
            forward_speed=uh60_params['forward_speed'],
            density=uh60_params['density'],
            verbose=False
        )
        solve_time = time.time() - start_time
        
        # 验证结果合理性
        thrust = result['thrust']
        power = result['power']
        ct = result['ct']
        cp = result['cp']
        
        print(f"   求解时间: {solve_time:.3f}s")
        print(f"   推力: {thrust:.0f} N")
        print(f"   功率: {power/1000:.1f} kW")
        print(f"   推力系数: {ct:.4f}")
        print(f"   功率系数: {cp:.4f}")
        
        # 合理性检查
        reasonable_results = True
        
        # UH-60典型推力范围: 60,000-80,000 N
        if not (50000 <= thrust <= 100000):
            print(f"   ⚠️  推力超出合理范围: {thrust:.0f}N")
            reasonable_results = False
        
        # UH-60典型功率范围: 1000-2000 kW
        if not (800 <= power/1000 <= 3000):
            print(f"   ⚠️  功率超出合理范围: {power/1000:.1f}kW")
            reasonable_results = False
        
        if reasonable_results:
            print("   ✅ 算法验证通过，结果在合理范围内")
        else:
            print("   ⚠️  算法验证警告，结果可能需要调整")
        
        return result
        
    except Exception as e:
        print(f"   ❌ 核心算法测试失败: {e}")
        traceback.print_exc()
        return None

def test_performance_benchmark():
    """性能基准测试"""
    print("\n4. 性能基准测试")
    print("-" * 50)
    
    try:
        import bemt_refactored as bemt
        
        # 不同站位数量的性能测试
        station_counts = [10, 20, 50]
        performance_results = {}
        
        for num_stations in station_counts:
            print(f"   测试 {num_stations} 个站位...")
            
            solver = bemt.SimpleBEMT(
                radius=8.18,
                num_blades=4,
                num_stations=num_stations,
                enable_gpu=False
            )
            
            start_time = time.time()
            result = solver.solve(rpm=258, forward_speed=50, verbose=False)
            solve_time = time.time() - start_time
            
            performance_results[num_stations] = {
                'solve_time': solve_time,
                'converged': result['converged'],
                'iterations': result['iterations']
            }
            
            print(f"     时间: {solve_time:.3f}s, 迭代: {result['iterations']}, "
                  f"收敛: {'✅' if result['converged'] else '❌'}")
        
        # 性能分析
        print(f"\n   性能分析:")
        base_time = performance_results[10]['solve_time']
        for num_stations, perf in performance_results.items():
            speedup = base_time / perf['solve_time'] if perf['solve_time'] > 0 else 1.0
            print(f"     {num_stations:2d} 站位: {perf['solve_time']:.3f}s "
                  f"(相对速度: {speedup:.1f}x)")
        
        return performance_results
        
    except Exception as e:
        print(f"   ❌ 性能测试失败: {e}")
        traceback.print_exc()
        return {}

def test_feature_parity():
    """功能对等性测试"""
    print("\n5. 功能对等性验证")
    print("-" * 50)
    
    try:
        import bemt_refactored as bemt
        
        # 测试所有主要功能
        features_to_test = [
            ("GPU加速", lambda: bemt.get_gpu_manager().use_gpu),
            ("自适应网格", lambda: hasattr(bemt, 'AdaptiveMeshRefinement')),
            ("动态失速", lambda: hasattr(bemt, 'CompleteLeishmanBeddoesModel')),
            ("物理修正", lambda: hasattr(bemt, 'ComprehensivePhysicalCorrections')),
            ("配置管理", lambda: hasattr(bemt, 'BEMTConfig')),
            ("验证功能", lambda: callable(getattr(bemt, 'run_validation', None))),
        ]
        
        feature_results = {}
        for feature_name, test_func in features_to_test:
            try:
                available = test_func()
                feature_results[feature_name] = available
                status = "✅ 可用" if available else "❌ 不可用"
                print(f"   {feature_name}: {status}")
            except Exception as e:
                feature_results[feature_name] = False
                print(f"   {feature_name}: ❌ 错误 - {e}")
        
        available_features = sum(1 for available in feature_results.values() if available)
        total_features = len(feature_results)
        parity_rate = available_features / total_features
        
        print(f"\n   功能对等性: {parity_rate:.1%} ({available_features}/{total_features})")
        
        return feature_results
        
    except Exception as e:
        print(f"   ❌ 功能对等性测试失败: {e}")
        traceback.print_exc()
        return {}

def run_comprehensive_test():
    """运行综合测试"""
    print_header()
    
    # 执行所有测试
    test_results = {}
    
    test_results['imports'] = test_module_imports()
    test_results['basic_functionality'] = test_basic_functionality()
    test_results['core_algorithms'] = test_core_algorithms()
    test_results['performance'] = test_performance_benchmark()
    test_results['feature_parity'] = test_feature_parity()
    
    # 生成总结报告
    print("\n" + "=" * 80)
    print("📊 测试总结报告")
    print("=" * 80)
    
    # 导入测试总结
    import_success_rate = sum(1 for result in test_results['imports'].values() 
                             if "成功" in result) / len(test_results['imports'])
    print(f"模块导入成功率: {import_success_rate:.1%}")
    
    # 基本功能测试
    basic_success = test_results['basic_functionality']
    print(f"基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    
    # 核心算法测试
    algorithm_success = test_results['core_algorithms'] is not None
    print(f"核心算法验证: {'✅ 通过' if algorithm_success else '❌ 失败'}")
    
    # 性能测试
    performance_success = len(test_results['performance']) > 0
    print(f"性能基准测试: {'✅ 通过' if performance_success else '❌ 失败'}")
    
    # 功能对等性
    if test_results['feature_parity']:
        parity_rate = sum(1 for available in test_results['feature_parity'].values() 
                         if available) / len(test_results['feature_parity'])
        print(f"功能对等性: {parity_rate:.1%}")
    
    # 总体评估
    overall_success = all([
        import_success_rate >= 0.8,
        basic_success,
        algorithm_success,
        performance_success
    ])
    
    print(f"\n🎯 总体评估: {'🎉 重构成功！' if overall_success else '⚠️  需要进一步优化'}")
    
    return test_results

if __name__ == "__main__":
    results = run_comprehensive_test()
