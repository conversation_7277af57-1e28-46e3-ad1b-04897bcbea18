#!/usr/bin/env python3
"""
验证框架 - 测试运行器
====================

统一的测试运行和管理系统，支持多阶段验证流程。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class ValidationPhase(Enum):
    """验证阶段枚举"""
    FOUNDATION = "foundation"
    INTEGRATION = "integration"
    ACCURACY = "accuracy"
    PERFORMANCE = "performance"
    ACADEMIC = "academic"

class TestStatus(Enum):
    """测试状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"

@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    phase: ValidationPhase
    status: TestStatus
    execution_time: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    
class TestRunner:
    """验证测试运行器"""
    
    def __init__(self, output_dir: str = "validation_results"):
        """
        初始化测试运行器
        
        Args:
            output_dir: 结果输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        # 测试注册表
        self.test_registry: Dict[ValidationPhase, List[Callable]] = {
            phase: [] for phase in ValidationPhase
        }
        
        # 测试结果
        self.results: List[TestResult] = []
        
        # 统计信息
        self.stats = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': 0,
            'skipped': 0,
            'total_time': 0.0
        }
        
        self.logger.info("验证测试运行器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "validation.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def register_test(self, phase: ValidationPhase, test_func: Callable, 
                     name: Optional[str] = None):
        """
        注册测试函数
        
        Args:
            phase: 验证阶段
            test_func: 测试函数
            name: 测试名称（可选）
        """
        if name is None:
            name = test_func.__name__
        
        # 为测试函数添加元数据
        test_func._test_name = name
        test_func._test_phase = phase
        
        self.test_registry[phase].append(test_func)
        self.logger.info(f"注册测试: {name} (阶段: {phase.value})")
    
    def run_phase(self, phase: ValidationPhase, 
                  stop_on_failure: bool = False) -> List[TestResult]:
        """
        运行指定阶段的测试
        
        Args:
            phase: 验证阶段
            stop_on_failure: 是否在失败时停止
            
        Returns:
            测试结果列表
        """
        self.logger.info(f"开始运行验证阶段: {phase.value}")
        phase_results = []
        
        tests = self.test_registry[phase]
        if not tests:
            self.logger.warning(f"阶段 {phase.value} 没有注册的测试")
            return phase_results
        
        for test_func in tests:
            result = self._run_single_test(test_func)
            phase_results.append(result)
            self.results.append(result)
            
            # 更新统计
            self._update_stats(result)
            
            # 检查是否需要停止
            if stop_on_failure and result.status in [TestStatus.FAILED, TestStatus.ERROR]:
                self.logger.error(f"测试失败，停止执行: {result.test_name}")
                break
        
        self.logger.info(f"阶段 {phase.value} 完成，共 {len(phase_results)} 个测试")
        return phase_results
    
    def _run_single_test(self, test_func: Callable) -> TestResult:
        """
        运行单个测试
        
        Args:
            test_func: 测试函数
            
        Returns:
            测试结果
        """
        test_name = getattr(test_func, '_test_name', test_func.__name__)
        phase = getattr(test_func, '_test_phase', ValidationPhase.FOUNDATION)
        
        self.logger.info(f"运行测试: {test_name}")
        
        start_time = time.time()
        
        try:
            # 运行测试
            test_result = test_func()
            execution_time = time.time() - start_time
            
            # 解析测试结果
            if test_result is True or test_result is None:
                status = TestStatus.PASSED
                error_message = None
                details = None
            elif isinstance(test_result, dict):
                status = TestStatus.PASSED if test_result.get('passed', True) else TestStatus.FAILED
                error_message = test_result.get('error_message')
                details = test_result.get('details')
            else:
                status = TestStatus.FAILED
                error_message = f"意外的测试结果类型: {type(test_result)}"
                details = {'result': str(test_result)}
            
            self.logger.info(f"测试 {test_name} 完成: {status.value} ({execution_time:.3f}s)")
            
        except Exception as e:
            execution_time = time.time() - start_time
            status = TestStatus.ERROR
            error_message = str(e)
            details = {'traceback': traceback.format_exc()}
            
            self.logger.error(f"测试 {test_name} 出错: {error_message}")
        
        return TestResult(
            test_name=test_name,
            phase=phase,
            status=status,
            execution_time=execution_time,
            error_message=error_message,
            details=details
        )
    
    def _update_stats(self, result: TestResult):
        """更新统计信息"""
        self.stats['total_tests'] += 1
        self.stats['total_time'] += result.execution_time
        
        if result.status == TestStatus.PASSED:
            self.stats['passed'] += 1
        elif result.status == TestStatus.FAILED:
            self.stats['failed'] += 1
        elif result.status == TestStatus.ERROR:
            self.stats['errors'] += 1
        elif result.status == TestStatus.SKIPPED:
            self.stats['skipped'] += 1
    
    def run_all_phases(self, stop_on_failure: bool = False) -> Dict[ValidationPhase, List[TestResult]]:
        """
        运行所有验证阶段
        
        Args:
            stop_on_failure: 是否在失败时停止
            
        Returns:
            按阶段分组的测试结果
        """
        self.logger.info("开始运行完整验证流程")
        all_results = {}
        
        for phase in ValidationPhase:
            phase_results = self.run_phase(phase, stop_on_failure)
            all_results[phase] = phase_results
            
            # 检查是否需要停止
            if stop_on_failure and any(r.status in [TestStatus.FAILED, TestStatus.ERROR] 
                                     for r in phase_results):
                self.logger.error(f"阶段 {phase.value} 有失败测试，停止后续阶段")
                break
        
        self._save_results()
        self._print_summary()
        
        return all_results
    
    def _save_results(self):
        """保存测试结果"""
        # 保存详细结果
        results_file = self.output_dir / "test_results.json"
        
        results_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'statistics': self.stats,
            'results': [
                {
                    'test_name': r.test_name,
                    'phase': r.phase.value,
                    'status': r.status.value,
                    'execution_time': r.execution_time,
                    'error_message': r.error_message,
                    'details': r.details
                }
                for r in self.results
            ]
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"测试结果已保存到: {results_file}")
    
    def _print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🧪 验证测试摘要")
        print("="*60)
        
        print(f"总测试数: {self.stats['total_tests']}")
        print(f"通过: {self.stats['passed']} ✅")
        print(f"失败: {self.stats['failed']} ❌")
        print(f"错误: {self.stats['errors']} 💥")
        print(f"跳过: {self.stats['skipped']} ⏭️")
        print(f"总耗时: {self.stats['total_time']:.2f}s")
        
        # 按阶段统计
        print("\n📊 按阶段统计:")
        for phase in ValidationPhase:
            phase_results = [r for r in self.results if r.phase == phase]
            if phase_results:
                passed = sum(1 for r in phase_results if r.status == TestStatus.PASSED)
                total = len(phase_results)
                print(f"  {phase.value}: {passed}/{total} 通过")
        
        # 失败测试详情
        failed_tests = [r for r in self.results if r.status in [TestStatus.FAILED, TestStatus.ERROR]]
        if failed_tests:
            print("\n❌ 失败测试详情:")
            for result in failed_tests:
                print(f"  - {result.test_name} ({result.phase.value}): {result.error_message}")
        
        # 总体评估
        success_rate = self.stats['passed'] / max(self.stats['total_tests'], 1) * 100
        print(f"\n🎯 总体通过率: {success_rate:.1f}%")
        
        if success_rate >= 95:
            print("🎉 验证结果优秀！")
        elif success_rate >= 80:
            print("✅ 验证结果良好")
        elif success_rate >= 60:
            print("⚠️ 验证结果一般，需要改进")
        else:
            print("❌ 验证结果不佳，需要重点修复")

def test_decorator(phase: ValidationPhase, name: Optional[str] = None):
    """
    测试装饰器
    
    Args:
        phase: 验证阶段
        name: 测试名称
    """
    def decorator(func):
        func._test_phase = phase
        func._test_name = name or func.__name__
        return func
    return decorator

# 全局测试运行器实例
_global_runner = None

def get_test_runner() -> TestRunner:
    """获取全局测试运行器实例"""
    global _global_runner
    if _global_runner is None:
        _global_runner = TestRunner()
    return _global_runner

def register_test(phase: ValidationPhase, name: Optional[str] = None):
    """注册测试的装饰器"""
    def decorator(func):
        runner = get_test_runner()
        runner.register_test(phase, func, name)
        return func
    return decorator

if __name__ == "__main__":
    # 示例用法
    runner = TestRunner()
    
    @register_test(ValidationPhase.FOUNDATION, "示例基础测试")
    def example_foundation_test():
        """示例基础功能测试"""
        print("运行基础功能测试...")
        return True
    
    @register_test(ValidationPhase.INTEGRATION, "示例集成测试")
    def example_integration_test():
        """示例集成测试"""
        print("运行集成测试...")
        return {"passed": True, "details": {"message": "集成测试通过"}}
    
    # 运行所有测试
    results = runner.run_all_phases()