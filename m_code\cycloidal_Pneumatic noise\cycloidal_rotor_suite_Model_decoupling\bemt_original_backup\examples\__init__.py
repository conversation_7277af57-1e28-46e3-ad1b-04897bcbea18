"""
示例和验证模块
=============

包含BEMT中保真度模块的使用示例和验证算例。

模块内容：
- basic_usage.py: 基本使用示例
- uh60_validation.py: UH-60黑鹰验证算例
- cycloidal_rotor_example.py: 循环翼转子示例
- performance_benchmark.py: 性能基准测试

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from .basic_usage import run_basic_example

# 其他示例模块待实现
# from .uh60_validation import run_uh60_validation
# from .cycloidal_rotor_example import run_cycloidal_example
# from .performance_benchmark import run_performance_benchmark

__all__ = [
    'run_basic_example',
    # 'run_uh60_validation', 
    # 'run_cycloidal_example',
    # 'run_performance_benchmark'
]