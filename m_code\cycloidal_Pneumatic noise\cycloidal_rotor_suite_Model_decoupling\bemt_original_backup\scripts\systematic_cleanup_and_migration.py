#!/usr/bin/env python3
"""
BEMT中保真度模块 - 系统性清理和迁移脚本
=====================================

基于重构工作，对原始目录进行系统性的文件整理和清理：
1. 扫描和分析所有文件
2. 分类并迁移有价值的文件到重构版本
3. 清理冗余和过期文件
4. 验证迁移后的功能完整性

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import os
import shutil
import sys
from pathlib import Path
import json
from typing import Dict, List, Tuple, Any
import hashlib

class FileAnalyzer:
    """文件分析器"""
    
    def __init__(self, original_dir: str, refactored_dir: str):
        self.original_dir = Path(original_dir)
        self.refactored_dir = Path(refactored_dir)
        self.file_analysis = {}
        self.migration_plan = {}
        
    def analyze_file_type(self, file_path: Path) -> str:
        """分析文件类型"""
        if file_path.suffix == '.py':
            content = self.read_file_safely(file_path)
            if not content:
                return 'empty_python'
                
            # 分析Python文件类型
            if 'test_' in file_path.name or file_path.parent.name == 'tests':
                return 'test_file'
            elif file_path.parent.name == 'examples':
                return 'example_file'
            elif 'solver' in file_path.name.lower():
                return 'core_solver'
            elif file_path.parent.name in ['core', 'bemt']:
                return 'core_module'
            elif file_path.parent.name in ['utils', 'physics', 'geometry', 'aerodynamics']:
                return 'support_module'
            elif 'validation' in file_path.name.lower():
                return 'validation_file'
            elif '__init__' in file_path.name:
                return 'init_file'
            else:
                return 'python_script'
                
        elif file_path.suffix == '.md':
            return 'documentation'
        elif file_path.suffix in ['.txt', '.cfg', '.ini']:
            return 'config_file'
        elif file_path.suffix == '.pyc':
            return 'compiled_python'
        elif file_path.name == '__pycache__':
            return 'cache_directory'
        else:
            return 'other_file'
    
    def read_file_safely(self, file_path: Path) -> str:
        """安全读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""
    
    def get_file_hash(self, file_path: Path) -> str:
        """获取文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return ""
    
    def analyze_all_files(self) -> Dict[str, Any]:
        """分析所有文件"""
        print("🔍 开始文件分析...")
        
        analysis_result = {
            'total_files': 0,
            'file_types': {},
            'files_by_category': {},
            'duplicate_files': [],
            'empty_files': [],
            'large_files': []
        }
        
        for root, dirs, files in os.walk(self.original_dir):
            # 跳过缓存目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                file_path = Path(root) / file
                relative_path = file_path.relative_to(self.original_dir)
                
                # 分析文件
                file_type = self.analyze_file_type(file_path)
                file_size = file_path.stat().st_size if file_path.exists() else 0
                file_hash = self.get_file_hash(file_path)
                
                # 记录分析结果
                file_info = {
                    'path': str(relative_path),
                    'full_path': str(file_path),
                    'type': file_type,
                    'size': file_size,
                    'hash': file_hash,
                    'parent_dir': file_path.parent.name
                }
                
                self.file_analysis[str(relative_path)] = file_info
                analysis_result['total_files'] += 1
                
                # 按类型分类
                if file_type not in analysis_result['file_types']:
                    analysis_result['file_types'][file_type] = 0
                analysis_result['file_types'][file_type] += 1
                
                if file_type not in analysis_result['files_by_category']:
                    analysis_result['files_by_category'][file_type] = []
                analysis_result['files_by_category'][file_type].append(str(relative_path))
                
                # 检查特殊情况
                if file_size == 0:
                    analysis_result['empty_files'].append(str(relative_path))
                elif file_size > 100000:  # 100KB
                    analysis_result['large_files'].append({
                        'path': str(relative_path),
                        'size': file_size
                    })
        
        # 检查重复文件
        hash_to_files = {}
        for file_info in self.file_analysis.values():
            if file_info['hash'] and file_info['type'] != 'compiled_python':
                if file_info['hash'] not in hash_to_files:
                    hash_to_files[file_info['hash']] = []
                hash_to_files[file_info['hash']].append(file_info['path'])
        
        for file_hash, files in hash_to_files.items():
            if len(files) > 1:
                analysis_result['duplicate_files'].append({
                    'hash': file_hash,
                    'files': files
                })
        
        return analysis_result
    
    def create_migration_plan(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """创建迁移计划"""
        print("📋 创建迁移计划...")
        
        migration_plan = {
            'migrate_to_refactored': [],
            'delete_files': [],
            'keep_in_original': [],
            'merge_files': [],
            'special_handling': []
        }
        
        # 定义迁移规则
        migration_rules = {
            'test_file': 'migrate_to_tests',
            'example_file': 'migrate_to_examples',
            'validation_file': 'migrate_to_validation',
            'documentation': 'migrate_to_docs',
            'config_file': 'migrate_to_root',
            'compiled_python': 'delete',
            'cache_directory': 'delete',
            'empty_python': 'delete'
        }
        
        for file_path, file_info in self.file_analysis.items():
            file_type = file_info['type']
            action = migration_rules.get(file_type, 'evaluate')
            
            if action == 'delete':
                migration_plan['delete_files'].append({
                    'path': file_path,
                    'reason': f'{file_type} - 自动清理',
                    'type': file_type
                })
            elif action.startswith('migrate_to_'):
                target_dir = action.replace('migrate_to_', '')
                migration_plan['migrate_to_refactored'].append({
                    'source': file_path,
                    'target_dir': target_dir,
                    'type': file_type,
                    'action': 'copy'
                })
            elif file_type in ['core_module', 'support_module']:
                # 检查是否在重构版本中已存在
                if self.check_exists_in_refactored(file_path):
                    migration_plan['keep_in_original'].append({
                        'path': file_path,
                        'reason': '重构版本中已存在对应文件',
                        'type': file_type
                    })
                else:
                    migration_plan['migrate_to_refactored'].append({
                        'source': file_path,
                        'target_dir': 'corresponding_module',
                        'type': file_type,
                        'action': 'copy_and_update_imports'
                    })
            elif file_type == 'core_solver':
                migration_plan['special_handling'].append({
                    'path': file_path,
                    'reason': '核心求解器需要特别处理',
                    'type': file_type,
                    'action': 'manual_review'
                })
            else:
                migration_plan['keep_in_original'].append({
                    'path': file_path,
                    'reason': '需要进一步评估',
                    'type': file_type
                })
        
        return migration_plan
    
    def check_exists_in_refactored(self, original_path: str) -> bool:
        """检查文件是否在重构版本中已存在"""
        # 简化的检查逻辑
        file_name = Path(original_path).name
        
        # 在重构版本中搜索同名文件
        for root, dirs, files in os.walk(self.refactored_dir):
            if file_name in files:
                return True
        return False

class FileMigrator:
    """文件迁移器"""
    
    def __init__(self, original_dir: str, refactored_dir: str):
        self.original_dir = Path(original_dir)
        self.refactored_dir = Path(refactored_dir)
        self.migration_log = []
    
    def execute_migration_plan(self, migration_plan: Dict[str, Any]) -> Dict[str, Any]:
        """执行迁移计划"""
        print("🚀 开始执行迁移...")
        
        results = {
            'migrated_files': 0,
            'deleted_files': 0,
            'kept_files': 0,
            'errors': []
        }
        
        # 1. 删除文件
        for item in migration_plan['delete_files']:
            try:
                file_path = self.original_dir / item['path']
                if file_path.exists():
                    if file_path.is_dir():
                        shutil.rmtree(file_path)
                    else:
                        file_path.unlink()
                    results['deleted_files'] += 1
                    self.migration_log.append(f"删除: {item['path']} - {item['reason']}")
                    print(f"   ✅ 删除: {item['path']}")
            except Exception as e:
                error_msg = f"删除失败 {item['path']}: {e}"
                results['errors'].append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # 2. 迁移文件到重构版本
        for item in migration_plan['migrate_to_refactored']:
            try:
                source_path = self.original_dir / item['source']
                target_dir = self.refactored_dir / item['target_dir']
                
                # 创建目标目录
                target_dir.mkdir(parents=True, exist_ok=True)
                
                # 确定目标文件路径
                target_path = target_dir / Path(item['source']).name
                
                # 复制文件
                if source_path.exists():
                    shutil.copy2(source_path, target_path)
                    results['migrated_files'] += 1
                    self.migration_log.append(f"迁移: {item['source']} -> {target_path}")
                    print(f"   ✅ 迁移: {item['source']} -> {item['target_dir']}")
                    
                    # 如果需要更新导入路径
                    if item.get('action') == 'copy_and_update_imports':
                        self.update_import_paths(target_path)
                        
            except Exception as e:
                error_msg = f"迁移失败 {item['source']}: {e}"
                results['errors'].append(error_msg)
                print(f"   ❌ {error_msg}")
        
        # 3. 记录保留的文件
        results['kept_files'] = len(migration_plan['keep_in_original'])
        
        return results
    
    def update_import_paths(self, file_path: Path):
        """更新文件中的导入路径"""
        if file_path.suffix != '.py':
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新导入路径
            updated_content = content.replace(
                'from bemt_medium_fidelity_validation',
                'from bemt_refactored'
            ).replace(
                'import bemt_medium_fidelity_validation',
                'import bemt_refactored'
            )
            
            if updated_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                print(f"     🔧 更新导入路径: {file_path.name}")
                
        except Exception as e:
            print(f"     ⚠️  更新导入路径失败 {file_path.name}: {e}")

def print_analysis_report(analysis_result: Dict[str, Any]):
    """打印分析报告"""
    print("\n" + "=" * 80)
    print("📊 文件分析报告")
    print("=" * 80)
    
    print(f"\n📈 总体统计:")
    print(f"   总文件数: {analysis_result['total_files']}")
    
    print(f"\n📂 文件类型分布:")
    for file_type, count in sorted(analysis_result['file_types'].items()):
        print(f"   {file_type:20s}: {count:3d} 个")
    
    if analysis_result['duplicate_files']:
        print(f"\n🔄 重复文件 ({len(analysis_result['duplicate_files'])} 组):")
        for dup in analysis_result['duplicate_files'][:5]:  # 只显示前5组
            print(f"   重复组: {', '.join(dup['files'])}")
    
    if analysis_result['empty_files']:
        print(f"\n📭 空文件 ({len(analysis_result['empty_files'])} 个):")
        for empty_file in analysis_result['empty_files'][:10]:  # 只显示前10个
            print(f"   {empty_file}")
    
    if analysis_result['large_files']:
        print(f"\n📦 大文件 ({len(analysis_result['large_files'])} 个):")
        for large_file in analysis_result['large_files'][:5]:  # 只显示前5个
            size_kb = large_file['size'] / 1024
            print(f"   {large_file['path']:40s}: {size_kb:.1f} KB")

def print_migration_plan(migration_plan: Dict[str, Any]):
    """打印迁移计划"""
    print("\n" + "=" * 80)
    print("📋 迁移计划")
    print("=" * 80)
    
    print(f"\n🚀 迁移到重构版本 ({len(migration_plan['migrate_to_refactored'])} 个):")
    for item in migration_plan['migrate_to_refactored'][:10]:  # 只显示前10个
        print(f"   {item['source']:40s} -> {item['target_dir']}")
    
    print(f"\n🗑️  删除文件 ({len(migration_plan['delete_files'])} 个):")
    for item in migration_plan['delete_files'][:10]:  # 只显示前10个
        print(f"   {item['path']:40s} - {item['reason']}")
    
    print(f"\n📁 保留在原位置 ({len(migration_plan['keep_in_original'])} 个):")
    for item in migration_plan['keep_in_original'][:10]:  # 只显示前10个
        print(f"   {item['path']:40s} - {item['reason']}")
    
    if migration_plan['special_handling']:
        print(f"\n⚠️  特殊处理 ({len(migration_plan['special_handling'])} 个):")
        for item in migration_plan['special_handling']:
            print(f"   {item['path']:40s} - {item['reason']}")

def main():
    """主函数"""
    print("=" * 80)
    print("🧹 BEMT中保真度模块 - 系统性清理和迁移")
    print("=" * 80)
    
    # 设置路径
    original_dir = "."  # 当前目录（bemt_medium_fidelity_validation）
    refactored_dir = "../bemt_refactored"
    
    try:
        # 1. 文件分析
        analyzer = FileAnalyzer(original_dir, refactored_dir)
        analysis_result = analyzer.analyze_all_files()
        print_analysis_report(analysis_result)
        
        # 2. 创建迁移计划
        migration_plan = analyzer.create_migration_plan(analysis_result)
        print_migration_plan(migration_plan)
        
        # 3. 用户确认
        print(f"\n❓ 是否执行迁移计划？")
        print(f"   - 将迁移 {len(migration_plan['migrate_to_refactored'])} 个文件到重构版本")
        print(f"   - 将删除 {len(migration_plan['delete_files'])} 个文件")
        print(f"   - 将保留 {len(migration_plan['keep_in_original'])} 个文件在原位置")
        
        # 自动执行（在脚本中）
        print(f"\\n🚀 开始执行迁移...")
        
        # 4. 执行迁移
        migrator = FileMigrator(original_dir, refactored_dir)
        migration_results = migrator.execute_migration_plan(migration_plan)
        
        # 5. 生成报告
        print(f"\n" + "=" * 80)
        print("📋 迁移完成报告")
        print("=" * 80)
        print(f"✅ 迁移文件: {migration_results['migrated_files']} 个")
        print(f"🗑️  删除文件: {migration_results['deleted_files']} 个")
        print(f"📁 保留文件: {migration_results['kept_files']} 个")
        
        if migration_results['errors']:
            print(f"❌ 错误: {len(migration_results['errors'])} 个")
            for error in migration_results['errors'][:5]:
                print(f"   {error}")
        
        # 保存详细日志
        log_file = Path("migration_log.json")
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump({
                'analysis_result': analysis_result,
                'migration_plan': migration_plan,
                'migration_results': migration_results,
                'migration_log': migrator.migration_log
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细日志已保存到: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)