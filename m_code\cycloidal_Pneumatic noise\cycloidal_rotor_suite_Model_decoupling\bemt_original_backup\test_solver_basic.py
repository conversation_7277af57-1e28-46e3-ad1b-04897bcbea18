#!/usr/bin/env python3
"""
BEMT求解器基础功能测试
====================

测试BEMT求解器的核心功能，确保基本算例能够成功运行。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
import numpy as np

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config import ConfigManager
from core.solver_factory import SolverFactory


def test_basic_solver():
    """测试基本求解器功能"""
    print("=" * 60)
    print("BEMT求解器基础功能测试")
    print("=" * 60)
    
    try:
        # 1. 创建简化配置
        print("\n1. 创建配置...")
        config = ConfigManager({
            # 基本参数
            'R_rotor': 0.3,          # 转子半径 [m]
            'B': 3,                  # 桨叶数
            'c': 0.06,               # 弦长 [m]
            'omega_rotor': 100.0,    # 角速度 [rad/s]
            'rho': 1.225,            # 空气密度 [kg/m³]
            
            # BEMT参数
            'bemt_n_elements': 10,   # 减少叶素数量
            'bemt_max_iterations': 30,
            'bemt_tolerance': 5e-4,  # 放宽容差
            'relaxation_factor': 0.7, # 增加松弛因子
            
            # 物理模型
            'enable_tip_loss': True,
            'enable_hub_loss': False,  # 简化模型
            'enable_dynamic_stall': False,
            
            # 转子类型
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 8.0,   # 减小俯仰幅值
            'pitch_phase_offset': 0.0,
            'pitch_bias_angle': 0.0,
            
            # 仿真参数
            'dt': 0.01,
            'application_type': 'general'
        })
        print(f"   ✅ 配置创建成功，参数数量: {len(config.to_dict())}")
        
        # 2. 创建求解器
        print("\n2. 创建求解器...")
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        print(f"   ✅ 求解器创建成功")
        print(f"   - 类型: {solver.solver_type}")
        print(f"   - 保真度: {solver.fidelity_level}")
        print(f"   - 桨叶数: {solver.B}")
        print(f"   - 叶素数: {solver.n_elements}")
        
        # 3. 单步求解测试
        print("\n3. 单步求解测试...")
        t = 0.0
        dt = 0.01
        
        start_time = time.time()
        result = solver.solve_step(t, dt)
        solve_time = time.time() - start_time
        
        print(f"   ✅ 单步求解成功")
        print(f"   - 求解时间: {solve_time:.4f}s")
        print(f"   - 收敛状态: {'成功' if result['convergence_info']['converged'] else '失败'}")
        print(f"   - 迭代次数: {result['convergence_info']['iterations']}")
        print(f"   - 残差: {result['convergence_info']['residual']:.2e}")
        
        # 4. 性能结果
        performance = result['performance']
        print(f"\n4. 性能结果:")
        print(f"   - 推力: {performance['thrust']:.3f} N")
        print(f"   - 功率: {performance['power']:.3f} W")
        print(f"   - 转矩: {performance['torque']:.4f} N·m")
        print(f"   - 品质因数: {performance['figure_of_merit']:.3f}")
        print(f"   - 推力系数: {performance['CT']:.5f}")
        print(f"   - 功率系数: {performance['CP']:.5f}")
        
        # 5. 多步求解测试
        print(f"\n5. 多步求解测试...")
        n_steps = 10
        dt = 0.01
        
        thrust_history = []
        power_history = []
        solve_times = []
        convergence_count = 0
        
        for i in range(n_steps):
            t = i * dt
            step_start = time.time()
            
            try:
                result = solver.solve_step(t, dt)
                step_time = time.time() - step_start
                
                thrust_history.append(result['performance']['thrust'])
                power_history.append(result['performance']['power'])
                solve_times.append(step_time)
                
                if result['convergence_info']['converged']:
                    convergence_count += 1
                    
            except Exception as e:
                print(f"   ⚠️  步骤 {i} 失败: {e}")
                break
        
        if len(thrust_history) > 0:
            print(f"   ✅ 多步求解完成")
            print(f"   - 成功步数: {len(thrust_history)}/{n_steps}")
            print(f"   - 收敛率: {convergence_count/len(thrust_history)*100:.1f}%")
            print(f"   - 平均推力: {np.mean(thrust_history):.3f} ± {np.std(thrust_history):.3f} N")
            print(f"   - 平均功率: {np.mean(power_history):.3f} ± {np.std(power_history):.3f} W")
            print(f"   - 平均求解时间: {np.mean(solve_times):.4f}s")
        
        # 6. 求解器统计
        print(f"\n6. 求解器统计:")
        stats = solver.get_performance_stats()
        print(f"   - 总迭代次数: {stats.get('total_iterations', 0)}")
        print(f"   - 收敛失败次数: {stats.get('convergence_failures', 0)}")
        print(f"   - 平均求解时间: {stats.get('average_solve_time', 0):.4f}s")
        
        print(f"\n" + "=" * 60)
        print("✅ 基础功能测试通过！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_sweep():
    """测试参数扫描"""
    print("\n" + "=" * 60)
    print("参数扫描测试")
    print("=" * 60)
    
    try:
        # 测试不同转速的影响
        omega_values = [80.0, 100.0, 120.0]
        
        print("\n转速扫描测试:")
        print("转速[rad/s]  推力[N]  功率[W]  FM     收敛")
        print("-" * 45)
        
        for omega in omega_values:
            config = ConfigManager({
                'R_rotor': 0.3,
                'B': 3,
                'c': 0.06,
                'omega_rotor': omega,
                'rho': 1.225,
                'bemt_n_elements': 8,
                'bemt_max_iterations': 25,
                'bemt_tolerance': 1e-3,
                'relaxation_factor': 0.8,
                'enable_tip_loss': True,
                'enable_hub_loss': False,
                'enable_dynamic_stall': False,
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 6.0,
                'dt': 0.01
            })
            
            try:
                factory = SolverFactory()
                solver = factory.create_solver('bemt_medium', config.to_dict())
                result = solver.solve_step(0.0, 0.01)
                
                perf = result['performance']
                conv = result['convergence_info']['converged']
                
                print(f"{omega:8.1f}    {perf['thrust']:6.3f}  {perf['power']:6.2f}  "
                      f"{perf['figure_of_merit']:5.3f}  {'✅' if conv else '❌'}")
                
            except Exception as e:
                print(f"{omega:8.1f}    计算失败: {str(e)[:20]}...")
        
        print("\n✅ 参数扫描测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 参数扫描测试失败: {e}")
        return False


def main():
    """主函数"""
    print("开始BEMT求解器基础功能测试...")
    
    # 运行测试
    test_results = []
    
    # 基础功能测试
    test_results.append(test_basic_solver())
    
    # 参数扫描测试
    test_results.append(test_parameter_sweep())
    
    # 总结
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！BEMT求解器基础功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main())