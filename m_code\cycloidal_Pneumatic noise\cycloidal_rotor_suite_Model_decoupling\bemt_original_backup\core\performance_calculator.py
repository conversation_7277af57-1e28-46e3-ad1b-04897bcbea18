"""
性能计算器
=========

计算BEMT求解器的性能参数，包括推力、功率、效率等关键指标。

核心功能：
- 推力和功率计算
- 气动效率分析
- 性能系数计算
- 载荷分布分析
- 实时性能监控

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import warnings


class PerformanceCalculator:
    """
    性能计算器
    
    计算旋翼的各种性能参数和效率指标。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能计算器
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 基本参数
        self.rho = config.get('rho', 1.225)  # 空气密度 [kg/m³]
        self.R_rotor = config.get('R_rotor', 0.3)  # 转子半径 [m]
        self.omega_rotor = config.get('omega_rotor', 100.0)  # 角速度 [rad/s]
        self.B = config.get('B', 4)  # 桨叶数
        
        # 参考值
        self.disk_area = np.pi * self.R_rotor**2
        self.tip_speed = self.omega_rotor * self.R_rotor
        self.solidity = config.get('solidity', 0.1)  # 实度
        
        # 性能历史
        self.performance_history = []
        self.efficiency_history = []
        
        print(f"性能计算器初始化完成")
        print(f"  转子参数: R={self.R_rotor}m, Ω={self.omega_rotor}rad/s")
        print(f"  桨盘面积: {self.disk_area:.4f}m²")
        print(f"  叶尖速度: {self.tip_speed:.1f}m/s")
    
    def calculate_performance(self, result: Dict[str, Any], 
                            geometry: Any, t: float) -> Dict[str, Any]:
        """
        计算完整的性能参数
        
        Args:
            result: BEMT求解结果
            geometry: 几何对象
            t: 当前时间
            
        Returns:
            性能参数字典
        """
        forces = result['forces']
        moments = result['moments']
        circulation = result['circulation']
        
        # 基本力和力矩
        thrust_vector, torque_vector = self._calculate_total_loads(forces, moments)
        
        # 推力和功率
        thrust = self._calculate_thrust(thrust_vector)
        power = self._calculate_power(torque_vector)
        
        # 无量纲系数
        CT = self._calculate_thrust_coefficient(thrust)
        CP = self._calculate_power_coefficient(power)
        CQ = CP / (2 * np.pi)  # 转矩系数
        
        # 效率指标
        efficiency_metrics = self._calculate_efficiency_metrics(thrust, power)
        
        # 载荷分布
        load_distribution = self._calculate_load_distribution(forces, circulation)
        
        # 性能品质因数
        figure_of_merit = self._calculate_figure_of_merit(thrust, power)
        
        # 振动指标
        vibration_metrics = self._calculate_vibration_metrics(forces, moments)
        
        # 组装性能结果
        performance = {
            # 基本参数
            'time': t,
            'thrust': thrust,
            'power': power,
            'torque': np.linalg.norm(torque_vector),
            
            # 无量纲系数
            'CT': CT,
            'CP': CP,
            'CQ': CQ,
            
            # 效率指标
            'figure_of_merit': figure_of_merit,
            'disk_loading': thrust / self.disk_area,
            'power_loading': thrust / power if power > 1e-6 else 0.0,
            
            # 详细效率分析
            'efficiency_metrics': efficiency_metrics,
            
            # 载荷分布
            'load_distribution': load_distribution,
            
            # 振动指标
            'vibration_metrics': vibration_metrics,
            
            # 流场参数
            'tip_speed_ratio': self._calculate_tip_speed_ratio(),
            'advance_ratio': self._calculate_advance_ratio(),
            
            # 诊断信息
            'convergence_quality': result.get('convergence_info', {}).get('residual', 0.0)
        }
        
        # 更新历史
        self._update_performance_history(performance)
        
        return performance
    
    def _calculate_total_loads(self, forces: np.ndarray, 
                             moments: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算总载荷"""
        # 对所有桨叶求和
        total_force = np.sum(forces, axis=0)  # [Fx, Fy, Fz]
        total_moment = np.sum(moments, axis=0)  # [Mx, My, Mz]
        
        return total_force, total_moment
    
    def _calculate_thrust(self, thrust_vector: np.ndarray) -> float:
        """计算推力大小"""
        # 假设推力主要在z方向
        thrust = thrust_vector[2]
        return max(0.0, thrust)  # 推力不能为负
    
    def _calculate_power(self, torque_vector: np.ndarray) -> float:
        """计算功率"""
        # 功率 = 转矩 × 角速度
        torque = torque_vector[2]  # 绕z轴的转矩
        power = abs(torque * self.omega_rotor)
        return power
    
    def _calculate_thrust_coefficient(self, thrust: float) -> float:
        """计算推力系数"""
        # CT = T / (ρ * A * (ΩR)²)
        dynamic_pressure = self.rho * self.disk_area * self.tip_speed**2
        CT = thrust / dynamic_pressure if dynamic_pressure > 1e-15 else 0.0
        return CT
    
    def _calculate_power_coefficient(self, power: float) -> float:
        """计算功率系数"""
        # CP = P / (ρ * A * (ΩR)³)
        reference_power = self.rho * self.disk_area * self.tip_speed**3
        CP = power / reference_power if reference_power > 1e-15 else 0.0
        return CP
    
    def _calculate_efficiency_metrics(self, thrust: float, 
                                    power: float) -> Dict[str, float]:
        """计算效率指标"""
        metrics = {}
        
        # 理想效率（动量理论）
        if thrust > 1e-6:
            ideal_power = thrust**1.5 / np.sqrt(2 * self.rho * self.disk_area)
            metrics['ideal_efficiency'] = ideal_power / power if power > 1e-6 else 0.0
        else:
            metrics['ideal_efficiency'] = 0.0
        
        # 推重比
        weight_estimate = self.config.get('vehicle_weight', 10.0)  # N
        metrics['thrust_to_weight'] = thrust / weight_estimate
        
        # 功率密度
        metrics['power_density'] = power / self.disk_area
        
        # 载荷系数
        metrics['disk_loading'] = thrust / self.disk_area
        
        return metrics
    
    def _calculate_load_distribution(self, forces: np.ndarray, 
                                   circulation: np.ndarray) -> Dict[str, Any]:
        """计算载荷分布"""
        n_elements = len(circulation) // self.B
        
        # 径向载荷分布
        radial_thrust = np.zeros(n_elements)
        radial_torque = np.zeros(n_elements)
        radial_circulation = np.zeros(n_elements)
        
        for i in range(n_elements):
            # 对所有桨叶在该径向位置求平均
            blade_forces = []
            blade_circulations = []
            
            for blade_idx in range(self.B):
                elem_idx = blade_idx * n_elements + i
                if elem_idx < len(circulation):
                    blade_forces.append(forces[blade_idx, 2])  # 推力分量
                    blade_circulations.append(circulation[elem_idx])
            
            if blade_forces:
                radial_thrust[i] = np.mean(blade_forces)
                radial_circulation[i] = np.mean(blade_circulations)
        
        # 计算载荷集中度
        thrust_concentration = self._calculate_load_concentration(radial_thrust)
        
        return {
            'radial_thrust': radial_thrust,
            'radial_torque': radial_torque,
            'radial_circulation': radial_circulation,
            'thrust_concentration': thrust_concentration,
            'max_thrust_location': np.argmax(radial_thrust) / n_elements if n_elements > 0 else 0.0
        }
    
    def _calculate_load_concentration(self, radial_loads: np.ndarray) -> float:
        """计算载荷集中度"""
        if len(radial_loads) == 0 or np.sum(radial_loads) == 0:
            return 0.0
        
        # 使用基尼系数衡量载荷分布的不均匀程度
        sorted_loads = np.sort(radial_loads)
        n = len(sorted_loads)
        cumsum = np.cumsum(sorted_loads)
        
        gini = (2 * np.sum((np.arange(1, n+1) * sorted_loads))) / (n * cumsum[-1]) - (n + 1) / n
        
        return gini
    
    def _calculate_figure_of_merit(self, thrust: float, power: float) -> float:
        """计算品质因数（Figure of Merit）"""
        if power <= 1e-6 or thrust <= 1e-6:
            return 0.0
        
        # 理想功率（动量理论）
        ideal_power = thrust**1.5 / np.sqrt(2 * self.rho * self.disk_area)
        
        # 品质因数 = 理想功率 / 实际功率
        FM = ideal_power / power
        
        # 限制在合理范围内
        return np.clip(FM, 0.0, 1.0)
    
    def _calculate_vibration_metrics(self, forces: np.ndarray, 
                                   moments: np.ndarray) -> Dict[str, float]:
        """计算振动指标"""
        metrics = {}
        
        # 力的不平衡
        force_imbalance = np.std(forces, axis=0)
        metrics['force_imbalance_x'] = force_imbalance[0]
        metrics['force_imbalance_y'] = force_imbalance[1]
        metrics['force_imbalance_z'] = force_imbalance[2]
        
        # 力矩的不平衡
        moment_imbalance = np.std(moments, axis=0)
        metrics['moment_imbalance_x'] = moment_imbalance[0]
        metrics['moment_imbalance_y'] = moment_imbalance[1]
        metrics['moment_imbalance_z'] = moment_imbalance[2]
        
        # 总体振动水平
        metrics['overall_vibration'] = np.sqrt(np.sum(force_imbalance**2) + 
                                             np.sum(moment_imbalance**2))
        
        return metrics
    
    def _calculate_tip_speed_ratio(self) -> float:
        """计算叶尖速比"""
        forward_speed = self.config.get('forward_velocity', 0.0)
        if forward_speed > 1e-6:
            return self.tip_speed / forward_speed
        else:
            return float('inf')  # 悬停状态
    
    def _calculate_advance_ratio(self) -> float:
        """计算前进比"""
        forward_speed = self.config.get('forward_velocity', 0.0)
        return forward_speed / self.tip_speed
    
    def _update_performance_history(self, performance: Dict[str, Any]):
        """更新性能历史"""
        self.performance_history.append(performance.copy())
        
        # 提取效率历史
        self.efficiency_history.append({
            'time': performance['time'],
            'figure_of_merit': performance['figure_of_merit'],
            'ideal_efficiency': performance['efficiency_metrics']['ideal_efficiency'],
            'power_loading': performance['power_loading']
        })
        
        # 限制历史长度
        max_history = self.config.get('max_performance_history', 1000)
        if len(self.performance_history) > max_history:
            self.performance_history.pop(0)
            self.efficiency_history.pop(0)
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.performance_history:
            return {}
        
        # 提取关键参数
        thrusts = [p['thrust'] for p in self.performance_history]
        powers = [p['power'] for p in self.performance_history]
        CTs = [p['CT'] for p in self.performance_history]
        CPs = [p['CP'] for p in self.performance_history]
        FMs = [p['figure_of_merit'] for p in self.performance_history]
        
        stats = {
            'thrust': {
                'mean': np.mean(thrusts),
                'std': np.std(thrusts),
                'min': np.min(thrusts),
                'max': np.max(thrusts)
            },
            'power': {
                'mean': np.mean(powers),
                'std': np.std(powers),
                'min': np.min(powers),
                'max': np.max(powers)
            },
            'CT': {
                'mean': np.mean(CTs),
                'std': np.std(CTs)
            },
            'CP': {
                'mean': np.mean(CPs),
                'std': np.std(CPs)
            },
            'figure_of_merit': {
                'mean': np.mean(FMs),
                'std': np.std(FMs),
                'min': np.min(FMs),
                'max': np.max(FMs)
            }
        }
        
        return stats
    
    def analyze_performance_trends(self) -> Dict[str, Any]:
        """分析性能趋势"""
        if len(self.performance_history) < 10:
            return {'insufficient_data': True}
        
        # 提取时间序列
        times = [p['time'] for p in self.performance_history]
        thrusts = [p['thrust'] for p in self.performance_history]
        powers = [p['power'] for p in self.performance_history]
        FMs = [p['figure_of_merit'] for p in self.performance_history]
        
        # 计算趋势（简单线性回归）
        thrust_trend = self._calculate_trend(times, thrusts)
        power_trend = self._calculate_trend(times, powers)
        FM_trend = self._calculate_trend(times, FMs)
        
        # 性能稳定性
        thrust_stability = 1.0 - (np.std(thrusts[-10:]) / np.mean(thrusts[-10:]))
        power_stability = 1.0 - (np.std(powers[-10:]) / np.mean(powers[-10:]))
        
        return {
            'trends': {
                'thrust_slope': thrust_trend,
                'power_slope': power_trend,
                'figure_of_merit_slope': FM_trend
            },
            'stability': {
                'thrust_stability': thrust_stability,
                'power_stability': power_stability
            },
            'performance_degradation': FM_trend < -0.01  # FM下降超过1%
        }
    
    def _calculate_trend(self, x: List[float], y: List[float]) -> float:
        """计算线性趋势斜率"""
        if len(x) != len(y) or len(x) < 2:
            return 0.0
        
        x_arr = np.array(x)
        y_arr = np.array(y)
        
        # 简单线性回归
        n = len(x_arr)
        slope = (n * np.sum(x_arr * y_arr) - np.sum(x_arr) * np.sum(y_arr)) / \
                (n * np.sum(x_arr**2) - np.sum(x_arr)**2)
        
        return slope
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        if not self.performance_history:
            return "无性能数据"
        
        latest = self.performance_history[-1]
        stats = self.get_performance_statistics()
        trends = self.analyze_performance_trends()
        
        report = f"""
BEMT性能分析报告
===============

当前性能参数：
- 推力: {latest['thrust']:.2f} N
- 功率: {latest['power']:.2f} W
- 推力系数 CT: {latest['CT']:.4f}
- 功率系数 CP: {latest['CP']:.4f}
- 品质因数 FM: {latest['figure_of_merit']:.3f}
- 桨盘载荷: {latest['disk_loading']:.1f} N/m²

统计信息：
- 平均推力: {stats['thrust']['mean']:.2f} ± {stats['thrust']['std']:.2f} N
- 平均功率: {stats['power']['mean']:.2f} ± {stats['power']['std']:.2f} W
- 平均品质因数: {stats['figure_of_merit']['mean']:.3f} ± {stats['figure_of_merit']['std']:.3f}

载荷分布：
- 推力集中度: {latest['load_distribution']['thrust_concentration']:.3f}
- 最大推力位置: {latest['load_distribution']['max_thrust_location']:.2f} (r/R)

振动水平：
- 总体振动: {latest['vibration_metrics']['overall_vibration']:.3f}
- 力不平衡: [{latest['vibration_metrics']['force_imbalance_x']:.2f}, 
             {latest['vibration_metrics']['force_imbalance_y']:.2f}, 
             {latest['vibration_metrics']['force_imbalance_z']:.2f}] N

"""
        
        if not trends.get('insufficient_data', False):
            report += f"""
性能趋势：
- 推力趋势: {trends['trends']['thrust_slope']:.2e} N/s
- 功率趋势: {trends['trends']['power_slope']:.2e} W/s
- FM趋势: {trends['trends']['figure_of_merit_slope']:.2e} /s
- 推力稳定性: {trends['stability']['thrust_stability']:.1%}
- 功率稳定性: {trends['stability']['power_stability']:.1%}
"""
            
            if trends['performance_degradation']:
                report += "\n⚠️ 警告: 检测到性能退化趋势"
        
        return report
    
    def reset_history(self):
        """重置性能历史"""
        self.performance_history.clear()
        self.efficiency_history.clear()
    
    def export_performance_data(self) -> Dict[str, List]:
        """导出性能数据"""
        if not self.performance_history:
            return {}
        
        data = {
            'time': [p['time'] for p in self.performance_history],
            'thrust': [p['thrust'] for p in self.performance_history],
            'power': [p['power'] for p in self.performance_history],
            'CT': [p['CT'] for p in self.performance_history],
            'CP': [p['CP'] for p in self.performance_history],
            'figure_of_merit': [p['figure_of_merit'] for p in self.performance_history],
            'disk_loading': [p['disk_loading'] for p in self.performance_history],
            'power_loading': [p['power_loading'] for p in self.performance_history]
        }
        
        return data