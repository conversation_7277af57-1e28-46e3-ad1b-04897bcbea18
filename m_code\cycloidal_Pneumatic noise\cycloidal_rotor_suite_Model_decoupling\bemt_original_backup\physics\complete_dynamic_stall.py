#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整Leishman-Beddoes动态失速模型
===============================

基于原始cycloidal_rotor_suite的完整12状态L-B动态失速模型实现。

参考文献:
[1] <PERSON><PERSON><PERSON>, J. G., and Bed<PERSON><PERSON>, T. S. "A Semi-Empirical Model for Dynamic Stall."
    Journal of the American Helicopter Society, Vol. 34, No. 3, 1989, pp. 3-17.
[2] <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. "Principles of Helicopter Aerodynamics." Cambridge University Press, 2006.

作者: Augment Agent
日期: 2025-07-24
"""

import numpy as np
from typing import Dict, Optional, Tuple, Any, List
import warnings


class CompleteLeishmanBeddoesModel:
    """
    完整Leishman-Beddoes动态失速模型
    
    基于原始cycloidal_rotor_suite的完整12状态L-B模型实现，包括：
    - 附着流动态响应（2状态）
    - 分离流动态响应（2状态）
    - 前缘涡动态（4状态）
    - 后缘分离动态（2状态）
    - 涡对流和脱落（2状态）
    """
    
    def __init__(self, 
                 airfoil_params: Optional[Dict[str, float]] = None,
                 chord: float = 0.1,
                 mach_number: float = 0.0):
        """
        初始化完整L-B动态失速模型
        
        参数:
        ----
        airfoil_params : Dict, optional
            翼型参数字典
        chord : float
            弦长 [m]
        mach_number : float
            马赫数
        """
        self.chord = chord
        self.mach_number = mach_number
        
        # 默认翼型参数（NACA0012）
        default_params = {
            # 基本翼型参数
            'alpha_0': 0.0,      # 零升攻角 [rad]
            'cl_alpha': 2*np.pi, # 升力线斜率 [1/rad]
            'alpha_ss': 0.25,    # 静态失速攻角 [rad]
            'cl_max': 1.4,       # 最大升力系数
            'cd_0': 0.01,        # 零升阻力系数
            'cm_0': 0.0,         # 零升力矩系数
            
            # L-B模型参数
            'A1': 0.3,           # 不可压缩性参数1
            'A2': 0.7,           # 不可压缩性参数2
            'b1': 0.14,          # 时间常数参数1
            'b2': 0.53,          # 时间常数参数2
            'T_f': 3.0,          # 流动分离时间常数
            'T_v': 6.0,          # 涡脱落时间常数
            'T_vl': 11.0,        # 涡对流时间常数
            'T_p': 1.7,          # 压力滞后时间常数
            'C_N1': 1.0,         # 临界法向力系数
            'C_N_alpha': 2*np.pi, # 法向力线斜率
            'eta': 0.95,         # 恢复因子
            'E0': 0.16,          # 涡强度参数
            'cd_p': 0.2,         # 压差阻力系数
            'S1': 0.04,          # 前缘分离点
            'S2': 0.15,          # 后缘分离点
            'K0': 0.0,           # 涡脱落延迟参数
            'K1': -0.1,          # 涡脱落延迟参数
            'K2': 0.2,           # 涡脱落延迟参数
            'alpha_f': 0.7,      # 分离点函数参数
            'alpha_1': 0.75,     # 分离点函数参数
            'S_f1': 0.04,        # 分离点函数参数
            'S_f2': 0.15,        # 分离点函数参数
        }
        
        # 合并用户参数
        self.params = default_params.copy()
        if airfoil_params:
            self.params.update(airfoil_params)
        
        # 初始化12个状态变量
        self.reset_states()
        
        # 历史数据存储
        self.alpha_history = []
        self.alpha_dot_history = []
        self.time_history = []
        self.state_history = []
        
        print("✅ 完整Leishman-Beddoes动态失速模型初始化完成")
        print(f"   翼型: {airfoil_params.get('name', 'NACA0012') if airfoil_params else 'NACA0012'}")
        print(f"   状态变量数: 12")
        print(f"   弦长: {chord:.3f}m")
        print(f"   马赫数: {mach_number:.2f}")
    
    def reset_states(self):
        """重置所有状态变量"""
        
        # 12个状态变量
        self.states = {
            # 附着流动态响应（2状态）
            'X1': 0.0,           # 环量力滞后状态1
            'X2': 0.0,           # 环量力滞后状态2
            
            # 分离流动态响应（2状态）
            'Y1': 0.0,           # 分离点动态状态1
            'Y2': 0.0,           # 分离点动态状态2
            
            # 前缘涡动态（4状态）
            'X_v1': 0.0,         # 前缘涡状态1
            'X_v2': 0.0,         # 前缘涡状态2
            'X_v3': 0.0,         # 前缘涡状态3
            'X_v4': 0.0,         # 前缘涡状态4
            
            # 后缘分离动态（2状态）
            'Z1': 0.0,           # 后缘分离状态1
            'Z2': 0.0,           # 后缘分离状态2
            
            # 涡对流和脱落（2状态）
            'W1': 0.0,           # 涡对流状态1
            'W2': 0.0,           # 涡对流状态2
        }
        
        # 辅助变量
        self.alpha_prev = 0.0
        self.alpha_dot_prev = 0.0
        self.q_prev = 0.0
        self.time_prev = 0.0
    
    def compute_dynamic_stall(self, 
                            alpha: float, 
                            alpha_dot: float, 
                            velocity: float, 
                            time: float,
                            dt: float) -> Dict[str, float]:
        """
        计算动态失速效应
        
        参数:
        ----
        alpha : float
            攻角 [rad]
        alpha_dot : float
            攻角变化率 [rad/s]
        velocity : float
            相对速度 [m/s]
        time : float
            当前时间 [s]
        dt : float
            时间步长 [s]
            
        返回:
        ----
        coefficients : Dict[str, float]
            动态气动系数
        """
        # 无量纲时间
        s = 2 * velocity * dt / self.chord
        
        # 计算准定常系数
        cl_qs, cd_qs, cm_qs = self._compute_quasi_steady_coefficients(alpha)
        
        # 计算法向力和弦向力系数
        cn_qs = cl_qs * np.cos(alpha) + cd_qs * np.sin(alpha)
        cc_qs = cl_qs * np.sin(alpha) - cd_qs * np.cos(alpha)
        
        # 1. 附着流动态响应
        cn_c, cc_c = self._compute_attached_flow_dynamics(
            alpha, alpha_dot, cn_qs, cc_qs, s
        )
        
        # 2. 分离流动态响应
        cn_f, cc_f = self._compute_separated_flow_dynamics(
            alpha, cn_c, s
        )
        
        # 3. 前缘涡动态
        cn_v, cc_v = self._compute_leading_edge_vortex_dynamics(
            alpha, alpha_dot, cn_f, s
        )
        
        # 4. 后缘分离动态
        cn_te, cc_te = self._compute_trailing_edge_dynamics(
            alpha, cn_f, s
        )
        
        # 5. 涡对流和脱落
        cn_conv, cc_conv = self._compute_vortex_convection(
            alpha, cn_v, s
        )
        
        # 总的法向力和弦向力系数
        cn_total = cn_f + cn_v + cn_te + cn_conv
        cc_total = cc_f + cc_v + cc_te + cc_conv
        
        # 转换回升力和阻力系数
        cl_dynamic = cn_total * np.cos(alpha) - cc_total * np.sin(alpha)
        cd_dynamic = cn_total * np.sin(alpha) + cc_total * np.cos(alpha)
        
        # 力矩系数（简化）
        cm_dynamic = cm_qs + self._compute_dynamic_moment(alpha, cn_total)
        
        # 存储历史数据
        self._update_history(alpha, alpha_dot, time)
        
        return {
            'cl': cl_dynamic,
            'cd': cd_dynamic,
            'cm': cm_dynamic,
            'cn': cn_total,
            'cc': cc_total,
            'cl_qs': cl_qs,
            'cd_qs': cd_qs,
            'separation_point': self._compute_separation_point(alpha),
            'vortex_strength': self.states['X_v1']
        }
    
    def _compute_quasi_steady_coefficients(self, alpha: float) -> Tuple[float, float, float]:
        """计算准定常气动系数"""
        
        # 升力系数
        if abs(alpha) <= self.params['alpha_ss']:
            cl = self.params['cl_alpha'] * (alpha - self.params['alpha_0'])
        else:
            # 失速后的升力
            cl = self.params['cl_max'] * np.sin(2 * alpha)
        
        # 阻力系数
        cd = self.params['cd_0'] + self.params['cd_p'] * np.sin(alpha)**2
        
        # 力矩系数
        cm = self.params['cm_0'] - 0.25 * cl
        
        return cl, cd, cm
    
    def _compute_attached_flow_dynamics(self, 
                                       alpha: float, 
                                       alpha_dot: float, 
                                       cn_qs: float, 
                                       cc_qs: float, 
                                       s: float) -> Tuple[float, float]:
        """计算附着流动态响应"""
        
        # 不可压缩性修正
        cn_alpha = self.params['C_N_alpha']
        
        # 状态方程
        # dX1/ds = -1/T1 * X1 + A1 * alpha_dot
        # dX2/ds = -1/T2 * X2 + A2 * alpha_dot
        
        T1 = self.params['b1'] * self.chord / (2 * 100)  # 简化时间常数
        T2 = self.params['b2'] * self.chord / (2 * 100)
        
        # 更新状态
        self.states['X1'] += s * (-self.states['X1'] / T1 + self.params['A1'] * alpha_dot)
        self.states['X2'] += s * (-self.states['X2'] / T2 + self.params['A2'] * alpha_dot)
        
        # 动态法向力
        cn_c = cn_qs - cn_alpha * (self.states['X1'] + self.states['X2'])
        cc_c = cc_qs  # 弦向力暂时不考虑动态效应
        
        return cn_c, cc_c
    
    def _compute_separated_flow_dynamics(self, 
                                        alpha: float, 
                                        cn_c: float, 
                                        s: float) -> Tuple[float, float]:
        """计算分离流动态响应"""
        
        # 分离点函数
        f_static = self._compute_separation_point(alpha)
        
        # 分离点动态
        T_f = self.params['T_f']
        
        # 状态方程
        self.states['Y1'] += s * (-self.states['Y1'] / T_f + f_static)
        self.states['Y2'] += s * (-self.states['Y2'] / T_f + self.states['Y1'])
        
        f_dynamic = self.states['Y2']
        
        # 分离流修正
        if f_dynamic > 0.7:
            cn_f = cn_c * ((1 + np.sqrt(f_dynamic)) / 2)**2
        else:
            cn_f = cn_c
        
        cc_f = 0.0  # 简化
        
        return cn_f, cc_f
    
    def _compute_leading_edge_vortex_dynamics(self, 
                                            alpha: float, 
                                            alpha_dot: float, 
                                            cn_f: float, 
                                            s: float) -> Tuple[float, float]:
        """计算前缘涡动态"""
        
        # 涡脱落条件
        if abs(alpha) > self.params['alpha_ss'] and alpha_dot > 0:
            vortex_shedding = True
        else:
            vortex_shedding = False
        
        T_v = self.params['T_v']
        T_vl = self.params['T_vl']
        
        if vortex_shedding:
            # 涡强度增长
            self.states['X_v1'] += s * (self.params['E0'] - self.states['X_v1'] / T_v)
            self.states['X_v2'] += s * (self.states['X_v1'] - self.states['X_v2'] / T_vl)
            self.states['X_v3'] += s * (self.states['X_v2'] - self.states['X_v3'] / T_vl)
            self.states['X_v4'] += s * (self.states['X_v3'] - self.states['X_v4'] / T_vl)
        else:
            # 涡强度衰减
            self.states['X_v1'] *= np.exp(-s / T_v)
            self.states['X_v2'] *= np.exp(-s / T_vl)
            self.states['X_v3'] *= np.exp(-s / T_vl)
            self.states['X_v4'] *= np.exp(-s / T_vl)
        
        # 涡贡献
        cn_v = self.states['X_v1'] * np.sin(alpha)
        cc_v = self.states['X_v1'] * np.cos(alpha)
        
        return cn_v, cc_v
    
    def _compute_trailing_edge_dynamics(self, 
                                       alpha: float, 
                                       cn_f: float, 
                                       s: float) -> Tuple[float, float]:
        """计算后缘分离动态"""
        
        # 简化的后缘分离模型
        T_te = 2.0  # 后缘时间常数
        
        # 后缘分离强度
        te_separation = max(0, abs(alpha) - self.params['alpha_ss'])
        
        self.states['Z1'] += s * (-self.states['Z1'] / T_te + te_separation)
        self.states['Z2'] += s * (-self.states['Z2'] / T_te + self.states['Z1'])
        
        cn_te = -0.1 * self.states['Z2'] * np.sign(alpha)
        cc_te = 0.05 * self.states['Z2']
        
        return cn_te, cc_te
    
    def _compute_vortex_convection(self, 
                                  alpha: float, 
                                  cn_v: float, 
                                  s: float) -> Tuple[float, float]:
        """计算涡对流和脱落"""
        
        # 涡对流时间常数
        T_conv = 5.0
        
        # 对流状态
        self.states['W1'] += s * (-self.states['W1'] / T_conv + cn_v)
        self.states['W2'] += s * (-self.states['W2'] / T_conv + self.states['W1'])
        
        # 对流贡献（延迟效应）
        cn_conv = -0.2 * self.states['W2']
        cc_conv = 0.0
        
        return cn_conv, cc_conv
    
    def _compute_separation_point(self, alpha: float) -> float:
        """计算分离点位置"""
        
        alpha_abs = abs(alpha)
        
        if alpha_abs <= self.params['alpha_f']:
            f = 1.0 - 0.3 * np.exp((alpha_abs - self.params['alpha_f']) / 0.006)
        else:
            f = 0.04 + 0.66 * np.exp((self.params['alpha_1'] - alpha_abs) / 0.035)
        
        return np.clip(f, 0.0, 1.0)
    
    def _compute_dynamic_moment(self, alpha: float, cn_total: float) -> float:
        """计算动态力矩修正"""
        
        # 简化的动态力矩模型
        cm_dynamic = -0.1 * cn_total * np.sin(alpha)
        
        return cm_dynamic
    
    def _update_history(self, alpha: float, alpha_dot: float, time: float):
        """更新历史数据"""
        
        self.alpha_history.append(alpha)
        self.alpha_dot_history.append(alpha_dot)
        self.time_history.append(time)
        self.state_history.append(self.states.copy())
        
        # 限制历史长度
        max_history = 1000
        if len(self.alpha_history) > max_history:
            self.alpha_history.pop(0)
            self.alpha_dot_history.pop(0)
            self.time_history.pop(0)
            self.state_history.pop(0)
        
        # 更新前一时刻值
        self.alpha_prev = alpha
        self.alpha_dot_prev = alpha_dot
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取状态信息"""
        
        return {
            'states': self.states.copy(),
            'parameters': self.params.copy(),
            'history_length': len(self.alpha_history),
            'current_separation_point': self._compute_separation_point(self.alpha_prev),
            'vortex_active': self.states['X_v1'] > 0.01
        }


# 导出接口
__all__ = [
    'CompleteLeishmanBeddoesModel'
]
