#!/usr/bin/env python3
"""
基础功能验证 - 求解器工厂测试
============================

验证求解器创建和选择机制的正确性。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "bemt_medium_fidelity_validation"))

from validation_framework.core.test_runner import register_test, ValidationPhase

def test_solver_creation():
    """测试求解器创建功能"""
    try:
        test_results = {}
        
        # 测试中保真度求解器工厂
        try:
            from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
            from bemt_medium_fidelity_validation.utils.config import ConfigManager
            
            # 创建测试配置
            config = {
                'R_rotor': 1.0,
                'B': 4,
                'c': 0.1,
                'omega_rotor': 120.0,
                'rho': 1.225
            }
            
            config_manager = ConfigManager(config)
            factory = SolverFactory()
            
            # 测试不同类型的求解器创建
            solver_types = ['bemt_medium', 'default', 'bemt']
            
            for solver_type in solver_types:
                try:
                    solver = factory.create_solver(solver_type, config_manager.to_dict())
                    
                    # 验证求解器对象
                    assert solver is not None
                    assert hasattr(solver, 'solve') or hasattr(solver, 'solve_step')
                    
                    test_results[f'medium_{solver_type}'] = True
                    
                except Exception as e:
                    test_results[f'medium_{solver_type}'] = f"创建失败: {e}"
            
        except Exception as e:
            test_results['medium_factory'] = f"中保真度工厂测试失败: {e}"
        
        # 测试低保真度求解器工厂
        try:
            sys.path.insert(0, str(project_root / "bemt_low_fidelity_validation"))
            
            # 尝试导入低保真度工厂
            try:
                from core.solver_factory import SolverFactory as LowFidelityFactory
                
                low_factory = LowFidelityFactory()
                low_solver = low_factory.create_solver('bemt_low', {})
                
                test_results['low_fidelity'] = True
                
            except ImportError:
                test_results['low_fidelity'] = "低保真度工厂不存在"
            except Exception as e:
                test_results['low_fidelity'] = f"低保真度工厂测试失败: {e}"
                
        except Exception as e:
            test_results['low_fidelity_import'] = f"低保真度模块导入失败: {e}"
        
        # 测试重构版求解器工厂
        try:
            sys.path.insert(0, str(project_root / "bemt_refactored"))
            
            try:
                from bemt.solver_factory import SolverFactory as RefactoredFactory
                
                ref_factory = RefactoredFactory()
                ref_solver = ref_factory.create_solver('bemt', {})
                
                test_results['refactored'] = True
                
            except ImportError:
                test_results['refactored'] = "重构版工厂不存在"
            except Exception as e:
                test_results['refactored'] = f"重构版工厂测试失败: {e}"
                
        except Exception as e:
            test_results['refactored_import'] = f"重构版模块导入失败: {e}"
        
        # 统计结果
        passed_tests = sum(1 for result in test_results.values() if result is True)
        total_tests = len(test_results)
        
        return {
            'passed': passed_tests >= 1,  # 至少一个工厂能工作
            'details': {
                'test_results': test_results,
                'passed_count': passed_tests,
                'total_count': total_tests
            },
            'error_message': None if passed_tests >= 1 else "所有求解器工厂都无法正常工作"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"求解器创建测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_solver_interface():
    """测试求解器接口一致性"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        # 创建求解器
        config = {
            'R_rotor': 1.0,
            'B': 4,
            'c': 0.1,
            'omega_rotor': 120.0,
            'rho': 1.225
        }
        
        config_manager = ConfigManager(config)
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config_manager.to_dict())
        
        # 检查必要的接口方法
        interface_checks = {}
        
        # 检查solve方法
        if hasattr(solver, 'solve'):
            interface_checks['solve_method'] = True
        else:
            interface_checks['solve_method'] = "缺少solve方法"
        
        # 检查solve_step方法
        if hasattr(solver, 'solve_step'):
            interface_checks['solve_step_method'] = True
        else:
            interface_checks['solve_step_method'] = "缺少solve_step方法"
        
        # 检查初始化方法
        if hasattr(solver, '__init__'):
            interface_checks['init_method'] = True
        else:
            interface_checks['init_method'] = "缺少__init__方法"
        
        # 检查配置属性
        if hasattr(solver, 'config') or hasattr(solver, '_config'):
            interface_checks['config_attribute'] = True
        else:
            interface_checks['config_attribute'] = "缺少配置属性"
        
        # 统计结果
        passed_checks = sum(1 for result in interface_checks.values() if result is True)
        total_checks = len(interface_checks)
        
        return {
            'passed': passed_checks >= 2,  # 至少有2个接口正常
            'details': {
                'interface_checks': interface_checks,
                'passed_count': passed_checks,
                'total_count': total_checks,
                'solver_type': type(solver).__name__
            },
            'error_message': None if passed_checks >= 2 else "求解器接口不完整"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"求解器接口测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_invalid_solver_types():
    """测试无效求解器类型的处理"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        config = {
            'R_rotor': 1.0,
            'B': 4,
            'c': 0.1,
            'omega_rotor': 120.0,
            'rho': 1.225
        }
        
        config_manager = ConfigManager(config)
        factory = SolverFactory()
        
        # 测试无效的求解器类型
        invalid_types = ['invalid_solver', 'nonexistent', '', None]
        error_handling_results = {}
        
        for invalid_type in invalid_types:
            try:
                solver = factory.create_solver(invalid_type, config_manager.to_dict())
                # 如果没有抛出异常，说明错误处理不够严格
                error_handling_results[f'invalid_{invalid_type}'] = "应该抛出异常但没有"
                
            except Exception as e:
                # 预期的异常，说明错误处理正常
                error_handling_results[f'invalid_{invalid_type}'] = True
        
        # 统计结果
        passed_checks = sum(1 for result in error_handling_results.values() if result is True)
        total_checks = len(error_handling_results)
        
        return {
            'passed': passed_checks >= total_checks * 0.5,  # 至少50%的错误处理正常
            'details': {
                'error_handling_results': error_handling_results,
                'passed_count': passed_checks,
                'total_count': total_checks
            },
            'error_message': None if passed_checks >= total_checks * 0.5 else "错误处理不够严格"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"无效求解器类型测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_config_parameter_passing():
    """测试配置参数传递"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        # 创建特定的配置
        test_config = {
            'R_rotor': 2.5,  # 特殊值用于验证
            'B': 6,          # 特殊值用于验证
            'c': 0.15,       # 特殊值用于验证
            'omega_rotor': 150.0,
            'rho': 1.225
        }
        
        config_manager = ConfigManager(test_config)
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config_manager.to_dict())
        
        # 检查配置是否正确传递
        config_checks = {}
        
        # 尝试访问求解器的配置
        solver_config = None
        if hasattr(solver, 'config'):
            solver_config = solver.config
        elif hasattr(solver, '_config'):
            solver_config = solver._config
        elif hasattr(solver, 'get_config'):
            solver_config = solver.get_config()
        
        if solver_config:
            # 检查关键参数是否正确传递
            if isinstance(solver_config, dict):
                if solver_config.get('R_rotor') == 2.5:
                    config_checks['R_rotor'] = True
                else:
                    config_checks['R_rotor'] = f"期望2.5，实际{solver_config.get('R_rotor')}"
                
                if solver_config.get('B') == 6:
                    config_checks['B'] = True
                else:
                    config_checks['B'] = f"期望6，实际{solver_config.get('B')}"
                
                if solver_config.get('c') == 0.15:
                    config_checks['c'] = True
                else:
                    config_checks['c'] = f"期望0.15，实际{solver_config.get('c')}"
            else:
                config_checks['config_format'] = f"配置格式不是字典: {type(solver_config)}"
        else:
            config_checks['config_access'] = "无法访问求解器配置"
        
        # 统计结果
        passed_checks = sum(1 for result in config_checks.values() if result is True)
        total_checks = len(config_checks)
        
        return {
            'passed': passed_checks >= 1,  # 至少一个参数正确传递
            'details': {
                'config_checks': config_checks,
                'passed_count': passed_checks,
                'total_count': total_checks,
                'input_config': test_config,
                'solver_config': solver_config
            },
            'error_message': None if passed_checks >= 1 else "配置参数传递失败"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"配置参数传递测试出错: {e}",
            'details': {'exception': str(e)}
        }

# 注册测试
register_test(ValidationPhase.FOUNDATION, "求解器创建测试")(test_solver_creation)
register_test(ValidationPhase.FOUNDATION, "求解器接口测试")(test_solver_interface)
register_test(ValidationPhase.FOUNDATION, "无效求解器类型测试")(test_invalid_solver_types)
register_test(ValidationPhase.FOUNDATION, "配置参数传递测试")(test_config_parameter_passing)

if __name__ == "__main__":
    # 单独运行求解器工厂测试
    print("🧪 运行求解器工厂测试...")
    
    tests = [
        ("求解器创建", test_solver_creation),
        ("求解器接口", test_solver_interface),
        ("无效求解器类型", test_invalid_solver_types),
        ("配置参数传递", test_config_parameter_passing)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试...")
        result = test_func()
        
        if result['passed']:
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败: {result['error_message']}")
            if 'details' in result:
                print(f"   详情: {result['details']}")
    
    print("\n🎯 求解器工厂测试完成")