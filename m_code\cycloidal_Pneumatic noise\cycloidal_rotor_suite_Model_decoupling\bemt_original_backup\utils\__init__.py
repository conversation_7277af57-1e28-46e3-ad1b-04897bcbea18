"""
工具模块
=======

包含BEMT求解器的各种工具函数和辅助类。

模块内容：
- config.py: 配置管理
- error_handling.py: 错误处理和验证
- math_utils.py: 数学工具函数
- file_utils.py: 文件操作工具
- plotting_utils.py: 绘图工具

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from .config import ConfigManager
from .error_handling import (
    BEMTError, ConvergenceError, ValidationError,
    validate_input, safe_divide, safe_sqrt
)
from .math_utils import (
    rotation_matrix, interpolate_1d, interpolate_2d,
    calculate_derivatives, smooth_data
)
from .file_utils import (
    ensure_directory, save_results, load_results,
    export_to_csv, import_from_csv
)

__all__ = [
    'ConfigManager',
    'BEMTError',
    'ConvergenceError', 
    'ValidationError',
    'validate_input',
    'safe_divide',
    'safe_sqrt',
    'rotation_matrix',
    'interpolate_1d',
    'interpolate_2d',
    'calculate_derivatives',
    'smooth_data',
    'ensure_directory',
    'save_results',
    'load_results',
    'export_to_csv',
    'import_from_csv'
]