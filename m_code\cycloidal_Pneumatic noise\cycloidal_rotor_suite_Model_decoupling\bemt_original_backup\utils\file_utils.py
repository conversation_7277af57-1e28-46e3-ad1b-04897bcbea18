"""
文件操作工具
===========

提供文件操作相关的工具函数。

核心功能：
- 目录管理
- 结果保存和加载
- CSV导入导出

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import os
import json
import csv
import pickle
from typing import Any, Dict, List
import numpy as np


def ensure_directory(path: str):
    """确保目录存在"""
    os.makedirs(path, exist_ok=True)


def save_results(data: Dict[str, Any], filepath: str, format: str = 'json'):
    """保存结果到文件"""
    ensure_directory(os.path.dirname(filepath))
    
    if format == 'json':
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
    elif format == 'pickle':
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    else:
        raise ValueError(f"不支持的格式: {format}")


def load_results(filepath: str, format: str = 'json') -> Dict[str, Any]:
    """从文件加载结果"""
    if format == 'json':
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif format == 'pickle':
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    else:
        raise ValueError(f"不支持的格式: {format}")


def export_to_csv(data: Dict[str, List], filepath: str):
    """导出数据到CSV"""
    ensure_directory(os.path.dirname(filepath))
    
    with open(filepath, 'w', newline='', encoding='utf-8') as f:
        if not data:
            return
        
        fieldnames = list(data.keys())
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        
        # 假设所有列表长度相同
        n_rows = len(list(data.values())[0])
        for i in range(n_rows):
            row = {key: data[key][i] for key in fieldnames}
            writer.writerow(row)


def import_from_csv(filepath: str) -> Dict[str, List]:
    """从CSV导入数据"""
    data = {}
    
    with open(filepath, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            for key, value in row.items():
                if key not in data:
                    data[key] = []
                
                # 尝试转换为数值
                try:
                    data[key].append(float(value))
                except ValueError:
                    data[key].append(value)
    
    return data