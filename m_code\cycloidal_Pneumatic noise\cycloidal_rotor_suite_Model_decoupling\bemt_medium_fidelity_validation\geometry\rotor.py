"""
转子几何模块
===========

定义转子的几何参数和配置。

核心功能：
- 转子几何参数化
- 桨叶分布计算
- 几何验证

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List


class RotorGeometry:
    """转子几何类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化转子几何
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 基本几何参数
        self.R_rotor = config.get('R_rotor', 0.3)  # 转子半径 [m]
        self.B = config.get('B', 4)  # 桨叶数
        self.c = config.get('c', 0.1)  # 弦长 [m]
        self.hub_radius = config.get('hub_radius', 0.05)  # 桂毂半径 [m]
        
        # 桨叶几何参数
        self.blade_taper = config.get('blade_taper', 1.0)  # 锥度比
        self.blade_twist = np.radians(config.get('blade_twist_deg', 0.0))  # 扭转角 [rad]
        self.precone_angle = np.radians(config.get('precone_angle', 0.0))  # 锥角 [rad]
        
        # 计算派生参数
        self.disk_area = np.pi * self.R_rotor**2
        self.solidity = self.B * self.c / (np.pi * self.R_rotor)
        self.aspect_ratio = self.R_rotor / self.c
        
        print(f"转子几何初始化完成:")
        print(f"  半径: {self.R_rotor}m, 桨叶数: {self.B}")
        print(f"  实度: {self.solidity:.3f}, 展弦比: {self.aspect_ratio:.1f}")
    
    def get_radial_positions(self, n_elements: int) -> np.ndarray:
        """
        获取径向位置分布
        
        Args:
            n_elements: 叶素数量
            
        Returns:
            径向位置数组 [m]
        """
        # 使用余弦分布
        positions = []
        for i in range(n_elements):
            eta = np.cos(np.pi * (i + 0.5) / n_elements)
            r = self.hub_radius + (self.R_rotor - self.hub_radius) * (eta + 1) / 2
            positions.append(r)
        
        return np.array(positions)
    
    def get_chord_distribution(self, radial_positions: np.ndarray) -> np.ndarray:
        """
        获取弦长分布
        
        Args:
            radial_positions: 径向位置数组
            
        Returns:
            弦长分布数组 [m]
        """
        r_norm = (radial_positions - self.hub_radius) / (self.R_rotor - self.hub_radius)
        chord_distribution = self.c * (1.0 - r_norm * (1.0 - self.blade_taper))
        
        return chord_distribution
    
    def get_twist_distribution(self, radial_positions: np.ndarray) -> np.ndarray:
        """
        获取扭转角分布
        
        Args:
            radial_positions: 径向位置数组
            
        Returns:
            扭转角分布数组 [rad]
        """
        r_norm = (radial_positions - self.hub_radius) / (self.R_rotor - self.hub_radius)
        twist_distribution = self.blade_twist * r_norm
        
        return twist_distribution
    
    def validate_geometry(self) -> bool:
        """验证几何参数的合理性"""
        if self.R_rotor <= 0:
            print("错误: 转子半径必须大于0")
            return False
        
        if self.B < 2:
            print("错误: 桨叶数必须至少为2")
            return False
        
        if self.c <= 0:
            print("错误: 弦长必须大于0")
            return False
        
        if self.hub_radius >= self.R_rotor:
            print("错误: 桂毂半径必须小于转子半径")
            return False
        
        if self.solidity > 0.5:
            print("警告: 实度过高，可能影响性能")
        
        return True
    
    def get_geometry_info(self) -> Dict[str, Any]:
        """获取几何信息"""
        return {
            'R_rotor': self.R_rotor,
            'B': self.B,
            'c': self.c,
            'hub_radius': self.hub_radius,
            'blade_taper': self.blade_taper,
            'blade_twist_deg': np.degrees(self.blade_twist),
            'precone_angle_deg': np.degrees(self.precone_angle),
            'disk_area': self.disk_area,
            'solidity': self.solidity,
            'aspect_ratio': self.aspect_ratio
        }

def test_rotor_geometry():
    """测试转子几何功能"""
    print("🔧 测试转子几何...")
    
    try:
        # 测试基本创建
        config = {
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'hub_radius': 0.05
        }
        
        geometry = RotorGeometry(config)
        print(f"   ✅ 几何创建成功: R={geometry.R_rotor}m, B={geometry.B}")
        
        # 测试径向位置分布
        positions = geometry.get_radial_positions(10)
        print(f"   ✅ 径向位置生成: {len(positions)} 个点，范围 {positions[0]:.3f}-{positions[-1]:.3f}m")
        
        # 测试弦长分布
        chord_dist = geometry.get_chord_distribution(positions)
        print(f"   ✅ 弦长分布生成: 范围 {chord_dist.min():.3f}-{chord_dist.max():.3f}m")
        
        # 测试扭转角分布
        twist_dist = geometry.get_twist_distribution(positions)
        print(f"   ✅ 扭转角分布生成: 范围 {np.degrees(twist_dist.min()):.1f}-{np.degrees(twist_dist.max()):.1f}°")
        
        # 测试几何验证
        is_valid = geometry.validate_geometry()
        print(f"   ✅ 几何验证: {'通过' if is_valid else '失败'}")
        
        # 测试几何信息
        info = geometry.get_geometry_info()
        print(f"   ✅ 几何信息获取: 实度={info['solidity']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 转子几何测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rotor_geometry()