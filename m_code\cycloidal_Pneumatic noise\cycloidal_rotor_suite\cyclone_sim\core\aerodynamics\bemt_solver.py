"""
叶素动量理论(BEMT)求解器
=======================

实现低保真度的叶素动量理论求解器，适用于快速参数扫描和初步设计。
计算速度快，但精度相对较低，不考虑三维效应和非定常效应。

核心特性：
- 基于经典BEMT理论的快速求解
- 适用于大规模参数扫描
- 计算速度比UVLM快50-100倍
- 支持基本的失速建模
- 不考虑尾迹相互作用

理论基础：
BEMT将桨叶分为多个叶素，每个叶素独立计算气动载荷，
通过动量理论确定诱导速度，迭代求解直至收敛。

适用场景：
- 快速性能评估
- 参数扫描研究
- 初步设计优化
- 实时仿真应用

作者: Augment Agent
日期: 2025-01-09
"""

import warnings
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch

from cyclone_sim.utils.common_utils import (
    load_config_file,
    save_config_file,
    setup_output_directory,
    validate_file_path,
)

# 统一错误处理框架导入
from cyclone_sim.utils.error_handling import (
    ConfigurationError,
    ConvergenceError,
    InputValidator,
    NumericalError,
    PhysicsError,
    ValidationError,
    WarningManager,
    check_convergence,
    handle_errors,
    safe_divide,
    safe_log,
    safe_sqrt,
)

from ..gpu_acceleration import get_gpu_manager, gpu_accelerated
from .base_solver import SOLVER_CAPABILITIES, AerodynamicSolverBase
from .convergence_optimizer import get_convergence_optimizer
from .blade_interaction_model import BladeInteractionModel, CycloidalBladeInteractionIntegrator


class BladeElement:
    """
    叶素对象类 - 用于存储叶素的几何和气动信息

    🔧 修复LB模型索引错误：为每个叶素提供独立的LB模型实例
    """

    def __init__(
        self,
        blade_idx: int,
        elem_idx: int,
        radial_position: float,
        chord: float,
        lb_model=None,
    ):
        """
        初始化叶素对象

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            radial_position: 径向位置 [m]
            chord: 弦长 [m]
            lb_model: Leishman-Beddoes模型实例
        """
        self.blade_idx = blade_idx
        self.elem_idx = elem_idx
        self.radial_position = radial_position
        self.chord = chord
        self.lb_model = lb_model

        # 历史数据存储
        self.alpha_history = []
        self.time_history = []
        self.force_history = []

    def calculate_unsteady_coefficients(
        self, alpha_eff: float, alpha_dot: float, V_local: float, dt: float, t: float
    ) -> Tuple[float, float, float]:
        """
        计算非定常气动力系数

        Args:
            alpha_eff: 有效攻角 [rad]
            alpha_dot: 攻角变化率 [rad/s]
            V_local: 局部速度 [m/s]
            dt: 时间步长 [s]
            t: 当前时间 [s]

        Returns:
            Cl, Cd, Cm: 升力、阻力、力矩系数
        """
        if self.lb_model is not None:
            try:
                # 使用LB模型计算非定常系数
                Cn, Cm = self.lb_model.update_coeffs(
                    alpha_eff=alpha_eff,
                    q=alpha_dot,  # 俯仰角速度近似为攻角变化率
                    V_rel=V_local,
                    dt=dt,
                    radial_pos_r_R=min(
                        1.0, self.radial_position / 1.0
                    ),  # 假设转子半径为1m
                    tip_speed_ratio=0.1,  # 简化的尖速比
                )

                # 转换为升力和阻力系数（简化转换）
                Cl = Cn * np.cos(alpha_eff)
                Cd = Cn * np.sin(alpha_eff) + 0.01  # 基础阻力

                # 存储历史数据
                self.alpha_history.append(alpha_eff)
                self.time_history.append(t)

                return Cl, Cd, Cm

            except Exception as e:
                # LB模型计算失败，使用静态方法
                WarningManager.numerical_warning(
                    f"LB模型计算失败 (桨叶{self.blade_idx}, 叶素{self.elem_idx}): {e}"
                )
                return self._get_static_coefficients(alpha_eff)
        else:
            # 没有LB模型，使用静态方法
            return self._get_static_coefficients(alpha_eff)

    def _get_static_coefficients(self, alpha_eff: float) -> Tuple[float, float, float]:
        """
        静态气动力系数计算（回退方法）

        Args:
            alpha_eff: 有效攻角 [rad]

        Returns:
            Cl, Cd, Cm: 升力、阻力、力矩系数
        """
        # 简化的静态系数计算
        Cl = 2 * np.pi * alpha_eff  # 薄翼理论
        Cd = 0.01 + 0.1 * alpha_eff**2  # 简化阻力
        Cm = -0.25 * Cl  # 简化力矩

        return Cl, Cd, Cm


class BEMTSolver(AerodynamicSolverBase):
    """
    叶素动量理论求解器

    低保真度但高效的气动力学求解器
    """

    def __init__(self, config, wake_system=None, airfoil_database=None):
        """
        初始化BEMT求解器

        Args:
            config: 仿真配置
            wake_system: 尾迹系统（BEMT不使用）
            airfoil_database: 翼型数据库
        """
        # 调用基类构造函数（现在包含统一物理修正系统）
        super().__init__(config, wake_system, airfoil_database)

        # BEMT特有参数
        self.fidelity_level = "low"
        self.computational_cost = "low"

        # 🔧 修复：移除重复代码，统一初始化自适应翼型插值器
        self._initialize_adaptive_interpolator(config)

        # 叶素离散化
        self.n_elements = getattr(config, "bemt_n_elements", 10)
        self.max_iterations = getattr(
            config, "bemt_max_iterations", 50
        )  # 增加最大迭代次数

        # 🔧 优化：实现自适应收敛标准
        # 基于Leishman "Principles of Helicopter Aerodynamics" 和工程实践
        # 使用分层收敛标准：严格模式用于研究，工程模式用于快速计算
        convergence_mode = getattr(
            config, "bemt_convergence_mode", "engineering"
        )  # 'strict' | 'engineering' | 'fast'

        if convergence_mode == "strict":
            self.convergence_tolerance = getattr(
                config, "bemt_tolerance", 1e-4
            )  # 研究级精度
            self.max_iterations = min(self.max_iterations, 100)
        elif convergence_mode == "engineering":
            self.convergence_tolerance = getattr(
                config, "bemt_tolerance", 1e-3
            )  # 工程级精度
            self.max_iterations = min(self.max_iterations, 50)
        else:  # fast mode
            self.convergence_tolerance = getattr(
                config, "bemt_tolerance", 5e-3
            )  # 快速计算
            self.max_iterations = min(self.max_iterations, 20)

        # 自适应收敛参数
        self.convergence_mode = convergence_mode
        self.adaptive_tolerance = True  # 启用自适应容差

        # 物理修正选项配置（现在使用统一系统）
        # 注意：物理修正现在由基类的physics_manager管理

        # === LB动态失速模型配置（基于2011年飞行力学论文） ===
        # 参考: 2011-飞行力学-基于LB动态失速模型的摆线桨气动性能计算方法_唐继伟.pdf
        # 🔧 修复：默认启用动态失速模型以提高物理保真度
        self.use_dynamic_stall_model = getattr(
            config, "enable_dynamic_stall", True
        )  # 默认启用
        self.unsteady_model_name = getattr(
            config, "unsteady_model_name", "leishman_beddoes"
        )  # 默认L-B模型

        # L-B模型参数配置
        self.lb_model_config = {
            "enable_3d_correction": getattr(config, "lb_enable_3d_correction", True),
            "enhanced_mode": getattr(config, "lb_enhanced_mode", True),
            "time_integration_method": getattr(config, "lb_time_integration", "rk4"),
            "convergence_tolerance": getattr(config, "lb_tolerance", 1e-6),
        }

        # 攻角变化率计算配置
        self.alpha_history = {}  # 存储每个叶素的攻角历史
        self.time_history = {}  # 存储时间历史

        if (
            self.use_dynamic_stall_model
            and self.unsteady_model_name == "leishman_beddoes"
        ):
            print("✅ 启用LB动态失速模型")
            print(
                f"   - 3D修正: {'启用' if self.lb_model_config['enable_3d_correction'] else '禁用'}"
            )
            print(
                f"   - 增强模式: {'启用' if self.lb_model_config['enhanced_mode'] else '禁用'}"
            )
            print(f"   - 时间积分: {self.lb_model_config['time_integration_method']}")
            self._initialize_lb_model_history()

        # 添加缺失的span属性
        self.span = getattr(config, "L_span", 1.0)  # 桨叶展长

        # 初始化叶素
        self._initialize_blade_elements()

        # 初始化收敛优化器
        self.convergence_optimizer = get_convergence_optimizer(
            {
                "base_tolerance": self.convergence_tolerance,
                "strict_tolerance": self.convergence_tolerance * 0.2,  # 更严格的容差
                "max_iterations": min(self.max_iterations, 15),  # 限制最大迭代次数
                "relaxation_factor": 0.4,  # 固定松弛因子
                "enable_aitken": False,  # 禁用Aitken加速以提高稳定性
                "use_smart_initial_guess": True,
            }
        )

        # === 气动-声学接口增强（基于2019年AeroacousticAnalysis论文） ===
        # 参考: 2019（重点）-AeroacousticAnalysis... 论文明确了FW-H方程对声源项精度的依赖
        self.enable_detailed_loads_history = getattr(
            config, "enable_detailed_loads_history", False
        )
        self.blade_loads_history = {}  # 存储详细的叶素载荷历史
        self.time_history = []  # 时间历史

        if self.enable_detailed_loads_history:
            print("启用详细载荷历史记录 - 为FW-H声学计算提供高保真度输入")
            self._initialize_loads_history()

        # 🔧 新增：自适应时间步长控制
        self.enable_adaptive_timestep = getattr(
            config, "enable_adaptive_timestep", False
        )
        self.max_alpha_dot = getattr(config, "max_alpha_dot", 50.0)  # rad/s
        self.min_timestep = getattr(config, "min_timestep", 1e-6)
        self.max_timestep = getattr(config, "max_timestep", 0.01)
        self.alpha_history = {}  # 存储每个叶素的攻角历史

        # 🔧 新增：循环翼转子动量理论修正
        self.use_cycloidal_momentum_theory = getattr(
            config, "use_cycloidal_momentum_theory", True
        )

        if self.use_cycloidal_momentum_theory:
            print("✅ 启用循环翼转子专用动量理论")
            self._initialize_cycloidal_momentum_parameters()

        # 🔧 新增：叶片间干扰效应建模
        self.use_blade_interaction = getattr(config, "use_blade_interaction", True)

        if self.use_blade_interaction:
            print("✅ 启用叶片间干扰效应建模")
            self._initialize_blade_interaction_model()

        # 标记为已初始化
        self.is_initialized = True

        print(f"BEMT求解器初始化完成")
        print(f"  桨叶数: {self.B}, 叶素数: {self.n_elements}")
        print(f"  计算成本: 低, 适用于快速扫描")
        print(f"  物理修正: {self._get_enabled_corrections()}")

    def initialize(self):
        """
        实现基类的初始化方法
        """
        if not self.is_initialized:
            self._initialize_blade_elements()
            self.is_initialized = True

    def _initialize_blade_elements(self):
        """
        初始化叶素
        """
        # 🔧 修复数组溢出问题：正确初始化二维叶素数组结构
        self.blade_elements = []

        # 径向位置分布（余弦分布）
        self.r_positions = []
        for i in range(self.n_elements):
            eta = np.cos(np.pi * (i + 0.5) / self.n_elements)
            r = 0.2 * self.R_rotor + 0.8 * self.R_rotor * (eta + 1) / 2
            self.r_positions.append(r)

        self.r_positions = np.array(self.r_positions)

        # 🔧 修复：正确初始化叶素对象的二维数组结构
        # 为每个桨叶创建叶素列表
        for blade_idx in range(self.B):
            blade_elements = []
            for elem_idx in range(self.n_elements):
                # 🔧 修复LB模型索引错误：创建实际的叶素对象
                if (
                    self.use_dynamic_stall_model
                    and self.unsteady_model_name == "leishman_beddoes"
                ):
                    # 创建带有LB模型的叶素对象
                    element = self._create_blade_element_with_lb_model(
                        blade_idx, elem_idx
                    )
                else:
                    # 创建简单的叶素对象（不带LB模型）
                    element = None
                blade_elements.append(element)
            self.blade_elements.append(blade_elements)

        # 初始化诱导速度
        self.induced_velocities = np.zeros(
            (self.B, self.n_elements, 2)
        )  # [axial, tangential]

        # 🚀 GPU加速初始化
        self.gpu_manager = get_gpu_manager(
            {
                "enable_mixed_precision": getattr(
                    self.config, "enable_mixed_precision", True
                ),
                "batch_size": getattr(self.config, "gpu_batch_size", 32),
                "memory_fraction": getattr(self.config, "gpu_memory_fraction", 0.8),
            }
        )

        print(f"叶素初始化完成: {self.B} 个桨叶 x {self.n_elements} 个径向位置")
        print(
            f"叶素数组结构: {len(self.blade_elements)} x {len(self.blade_elements[0]) if self.blade_elements else 0}"
        )
        print(f"🚀 GPU加速: {'启用' if self.gpu_manager.use_gpu else '禁用'}")

    def _initialize_lb_model_history(self):
        """
        初始化L-B模型的攻角历史记录

        为每个叶素创建攻角和时间历史存储
        """
        for blade_idx in range(self.B):
            self.alpha_history[blade_idx] = {}
            self.time_history[blade_idx] = {}

            for elem_idx in range(self.n_elements):
                self.alpha_history[blade_idx][elem_idx] = []
                self.time_history[blade_idx][elem_idx] = []

        print(f"L-B模型历史记录初始化完成: {self.B}桨叶 × {self.n_elements}叶素")

    def _create_blade_element_with_lb_model(self, blade_idx: int, elem_idx: int):
        """
        创建带有LB模型的叶素对象

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引

        Returns:
            element: 叶素对象
        """
        try:
            from .leishman_beddoes import LeishmanBeddoesModel

            # 计算叶素的弦长（这里使用常数弦长，实际应用中可能变化）
            chord = self.c

            # 创建LB模型
            lb_model = LeishmanBeddoesModel(
                chord=chord,
                enhanced_mode=self.lb_model_config["enhanced_mode"],
                enable_3d_correction=self.lb_model_config["enable_3d_correction"],
                config=self.config,
            )

            # 创建叶素对象
            element = BladeElement(
                blade_idx=blade_idx,
                elem_idx=elem_idx,
                radial_position=self.r_positions[elem_idx],
                chord=chord,
                lb_model=lb_model,
            )

            return element

        except Exception as e:
            WarningManager.numerical_warning(
                f"创建叶素{blade_idx}-{elem_idx}的LB模型失败: {e}"
            )
            return None

    def _calculate_alpha_dot(
        self, blade_idx: int, elem_idx: int, alpha_current: float, t_current: float
    ) -> float:
        """
        计算攻角变化率 dα/dt

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            alpha_current: 当前攻角 [rad]
            t_current: 当前时间 [s]

        Returns:
            alpha_dot: 攻角变化率 [rad/s]
        """
        alpha_hist = self.alpha_history[blade_idx][elem_idx]
        time_hist = self.time_history[blade_idx][elem_idx]

        # 存储当前值
        alpha_hist.append(alpha_current)
        time_hist.append(t_current)

        # 保持历史长度不超过3个点（用于数值微分）
        if len(alpha_hist) > 3:
            alpha_hist.pop(0)
            time_hist.pop(0)

        # 计算攻角变化率
        if len(alpha_hist) >= 2:
            # 使用后向差分
            dt = time_hist[-1] - time_hist[-2]
            if dt > 1e-10:
                alpha_dot = (alpha_hist[-1] - alpha_hist[-2]) / dt
            else:
                alpha_dot = 0.0
        else:
            alpha_dot = 0.0

        return alpha_dot

    def _initialize_loads_history(self):
        """
        初始化载荷历史记录数据结构

        为FW-H声学计算提供详细的叶素载荷历史
        """
        self.blade_loads_history = {}

        for blade_idx in range(self.B):
            self.blade_loads_history[f"blade_{blade_idx}"] = {}

            for elem_idx in range(self.n_elements):
                self.blade_loads_history[f"blade_{blade_idx}"][
                    f"element_{elem_idx}"
                ] = {
                    "time": [],
                    "force_normal": [],  # 法向力 [N]
                    "force_tangential": [],  # 切向力 [N]
                    "force_axial": [],  # 轴向力 [N]
                    "moment_pitching": [],  # 俯仰力矩 [N·m]
                    "alpha_eff": [],  # 有效攻角 [rad]
                    "cl": [],  # 升力系数
                    "cd": [],  # 阻力系数
                    "cm": [],  # 力矩系数
                }

    def _record_element_loads(
        self,
        t: float,
        blade_idx: int,
        elem_idx: int,
        force_normal: float,
        force_tangential: float,
        force_axial: float,
        moment_pitching: float,
        alpha_eff: float,
        cl: float,
        cd: float,
        cm: float,
    ):
        """
        记录叶素载荷到历史数据

        Args:
            t: 当前时间 [s]
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            force_normal: 法向力 [N]
            force_tangential: 切向力 [N]
            force_axial: 轴向力 [N]
            moment_pitching: 俯仰力矩 [N·m]
            alpha_eff: 有效攻角 [rad]
            cl, cd, cm: 气动力系数
        """
        if not self.enable_detailed_loads_history:
            return

        element_history = self.blade_loads_history[f"blade_{blade_idx}"][
            f"element_{elem_idx}"
        ]

        element_history["time"].append(t)
        element_history["force_normal"].append(force_normal)
        element_history["force_tangential"].append(force_tangential)
        element_history["force_axial"].append(force_axial)
        element_history["moment_pitching"].append(moment_pitching)
        element_history["alpha_eff"].append(alpha_eff)
        element_history["cl"].append(cl)
        element_history["cd"].append(cd)
        element_history["cm"].append(cm)

    def _initialize_blade_interaction_model(self):
        """
        初始化叶片间干扰效应模型

        为循环翼转子创建叶片间干扰效应建模，包括：
        - 尾迹干扰效应
        - 压力场干扰
        - 端壁效应
        """
        try:
            # 动态导入叶片干扰模型
            from .blade_interaction_model import BladeInteractionModel, CycloidalBladeInteractionIntegrator

            # 创建叶片干扰模型
            self.blade_interaction_model = BladeInteractionModel(
                config=self.config,
                num_blades=self.B,
                rotor_radius=self.R_rotor
            )

            # 创建集成器
            self.blade_interaction_integrator = CycloidalBladeInteractionIntegrator(
                self.blade_interaction_model
            )

            # 初始化叶片状态存储
            self.blade_states = []
            for i in range(self.B):
                self.blade_states.append({
                    'circulation': 0.0,
                    'thrust_coefficient': 0.0,
                    'azimuth': i * 2 * np.pi / self.B
                })

            print("  - 尾迹干扰建模: 启用")
            print("  - 压力场干扰: 启用")
            print("  - 端壁效应: 启用")
            print(f"  - 叶片数量: {self.B}")

        except Exception as e:
            print(f"⚠️ 叶片干扰模型初始化失败: {e}")
            self.use_blade_interaction = False
            self.blade_interaction_model = None
            self.blade_interaction_integrator = None

    def _update_blade_state(self, blade_idx: int, circulation: float, element_force: np.ndarray):
        """
        更新叶片状态信息用于干扰效应计算

        Args:
            blade_idx: 叶片索引
            circulation: 环量
            element_force: 叶素力
        """
        if hasattr(self, 'blade_states') and blade_idx < len(self.blade_states):
            # 更新环量
            self.blade_states[blade_idx]['circulation'] = circulation

            # 计算推力系数（简化计算）
            if len(element_force) >= 3:
                thrust = element_force[2]  # z方向推力
                dynamic_pressure = 0.5 * self.rho * (self.omega_rotor * self.R_rotor) ** 2
                disk_area = np.pi * self.R_rotor ** 2
                self.blade_states[blade_idx]['thrust_coefficient'] = thrust / (dynamic_pressure * disk_area)

            # 更新方位角
            self.blade_states[blade_idx]['azimuth'] = blade_idx * 2 * np.pi / self.B

    def _get_relative_velocity_magnitude(self, azimuth: float, r: float, v_induced: float) -> float:
        """
        计算相对速度大小

        Args:
            azimuth: 方位角 [rad]
            r: 径向位置 [m]
            v_induced: 诱导速度 [m/s]

        Returns:
            V_rel: 相对速度大小 [m/s]
        """
        # 旋转速度分量
        V_rot = self.omega_rotor * r

        # 前飞速度分量（如果有）
        V_forward = getattr(self, 'forward_velocity', 0.0)

        # 合成相对速度
        V_x = V_forward + V_rot * np.sin(azimuth)  # 前向分量
        V_y = V_rot * np.cos(azimuth)  # 侧向分量
        V_z = v_induced  # 诱导速度分量

        V_rel = np.sqrt(V_x**2 + V_y**2 + V_z**2)

        return V_rel

    def solve_core(self, t: float = 0.0, dt: float = 0.001, **kwargs) -> Dict:
        """
        BEMT核心求解逻辑 - 纯算法实现

        Args:
            t: 当前时间 [s]
            dt: 时间步长 [s]
            **kwargs: 其他参数

        Returns:
            原始BEMT求解结果（未应用物理修正）
        """
        # 更新桨叶运动学
        self.update_kinematics(t)

        # 迭代求解诱导速度和载荷（核心BEMT算法）
        forces, moments, circulation = self._solve_bemt_iteration(t)

        # 计算气动系数（用于物理修正）
        Cl, Cd = self._calculate_sectional_coefficients(t)

        return {
            "forces": forces,
            "moments": moments,
            "circulation": circulation,
            "Cl": Cl,
            "Cd": Cd,
            "t": t,
            "dt": dt,
            "convergence_info": {
                "converged": True,
                "iterations": self.max_iterations,
                "residual": 0.0,
            },
        }

    @handle_errors()
    def solve_step(self, t: float, dt: float) -> Dict:
        """
        执行单个时间步的求解 - 使用新的统一流程

        Args:
            t: 当前时间 [s]
            dt: 时间步长 [s]

        Returns:
            step_results: 求解结果字典
        """
        # 输入验证
        InputValidator.validate_scalar(t, "时间t", min_value=0.0)
        InputValidator.validate_scalar(dt, "时间步长dt", must_be_positive=True)

        # 验证输入
        self.validate_inputs(t, dt)

        # 更新当前时间
        self.current_time = t

        # 使用新的统一求解流程（包含物理修正）
        result = self.solve_with_corrections(t=t, dt=dt)

        # 存储历史数据
        self.store_history(result["forces"], result["circulation"])

        return result

    def _solve_bemt_iteration(
        self, t: float
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        BEMT迭代求解 - 实现真正的叶素理论+动量理论迭代收敛

        基于唐博士论文第2章的BEMT理论实现：
        1. 猜测初始诱导速度
        2. 用叶素理论计算气动力
        3. 用动量理论反算诱导速度
        4. 迭代直至收敛

        Args:
            t: 当前时间

        Returns:
            forces, moments, circulation
        """
        forces = np.zeros((self.B, 3))
        moments = np.zeros((self.B, 3))
        circulation = np.zeros(self.B * self.n_elements)

        for blade_idx in range(self.B):
            # 桨叶相位角
            blade_phase = blade_idx * 2 * np.pi / self.B
            theta = self.omega_rotor * t + blade_phase

            blade_force = np.zeros(3)
            blade_moment = np.zeros(3)

            for elem_idx in range(self.n_elements):
                # 🔧 修复数组溢出问题：检查r_positions数组边界
                if elem_idx < len(self.r_positions):
                    r = self.r_positions[elem_idx]
                else:
                    WarningManager.numerical_warning(
                        f"叶素索引 {elem_idx} 超出r_positions数组范围 {len(self.r_positions)}"
                    )
                    continue

                # 迭代求解诱导速度（核心BEMT算法）
                v_induced, converged = self._solve_induced_velocity_iterative(
                    blade_idx, elem_idx, theta, r
                )

                if not converged:
                    WarningManager.numerical_warning(
                        f"叶素 {blade_idx}-{elem_idx} 未收敛"
                    )

                # 计算最终的有效攻角
                alpha_eff = self._calculate_effective_aoa(theta, r, v_induced)

                # === 应用叶片间干扰效应修正 ===
                if self.use_blade_interaction and hasattr(self, 'blade_interaction_integrator'):
                    # 更新当前叶片状态
                    self._update_blade_state(blade_idx, gamma if 'gamma' in locals() else 0.0,
                                           element_force if 'element_force' in locals() else np.zeros(3))

                    # 计算局部速度
                    local_velocity = self._get_relative_velocity_magnitude(theta, r, v_induced)

                    # 应用叶片间干扰修正
                    corrected_velocity, corrected_alpha = self.blade_interaction_integrator.integrate_to_bemt_solver(
                        self, blade_idx, theta, self.blade_states, local_velocity, alpha_eff
                    )

                    # 更新有效攻角
                    alpha_eff = corrected_alpha

                # === 计算气动力系数（支持LB动态失速模型） ===
                if (
                    self.use_dynamic_stall_model
                    and self.unsteady_model_name == "leishman_beddoes"
                ):
                    # 使用LB动态失速模型
                    Cl, Cd, Cm = self._get_unsteady_airfoil_coefficients(
                        blade_idx, elem_idx, alpha_eff, r, t
                    )
                else:
                    # 使用静态查表方法
                    Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
                    Cm = 0.0  # 简化力矩系数

                # 应用物理修正（使用新的统一系统）
                correction_input = {
                    'r': r,
                    'R': self.R_rotor,
                    'B': self.B,
                    'phi': np.arctan2(v_induced, self.omega_rotor * r),  # 入流角
                    'omega': self.omega_rotor,
                    'azimuth': theta,
                    'Cl': Cl,
                    'Cd': Cd,
                    'alpha': alpha_eff
                }

                corrected_result = self.physics_manager.apply_all(correction_input)
                Cl_corrected = corrected_result['Cl']
                Cd_corrected = corrected_result['Cd']

                # 计算叶素载荷
                element_force, element_moment, gamma = self._calculate_element_loads(
                    r, alpha_eff, Cl_corrected, Cd_corrected, v_induced
                )

                # === 记录详细载荷历史（为FW-H声学计算提供输入） ===
                if self.enable_detailed_loads_history:
                    # 分解载荷到法向、切向、轴向分量
                    force_normal = element_force[1]  # y方向为法向
                    force_tangential = element_force[0]  # x方向为切向
                    force_axial = element_force[2]  # z方向为轴向
                    moment_pitching = element_moment[1]  # 绕y轴的俯仰力矩

                    self._record_element_loads(
                        t,
                        blade_idx,
                        elem_idx,
                        force_normal,
                        force_tangential,
                        force_axial,
                        moment_pitching,
                        alpha_eff,
                        Cl_corrected,
                        Cd_corrected,
                        Cm,
                    )

                # 累加到桨叶载荷
                blade_force += element_force
                blade_moment += element_moment

                # 存储环量
                circulation[blade_idx * self.n_elements + elem_idx] = gamma

            forces[blade_idx] = blade_force
            moments[blade_idx] = blade_moment

        return forces, moments, circulation

    def _calculate_sectional_coefficients(self, t: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算截面气动系数，用于物理修正

        Args:
            t: 当前时间 [s]

        Returns:
            Cl, Cd: 升力系数和阻力系数数组
        """
        # 初始化系数数组
        Cl_array = []
        Cd_array = []

        for blade_idx in range(self.B):
            # 桨叶相位角
            blade_phase = blade_idx * 2 * np.pi / self.B
            theta = self.omega_rotor * t + blade_phase

            blade_Cl = []
            blade_Cd = []

            for elem_idx in range(self.n_elements):
                if elem_idx < len(self.r_positions):
                    r = self.r_positions[elem_idx]

                    # 计算有效攻角
                    alpha_eff = self._calculate_effective_aoa(theta, r, 0.0)  # 简化的诱导速度

                    # 获取气动系数
                    if (self.use_dynamic_stall_model and
                        self.unsteady_model_name == "leishman_beddoes"):
                        Cl, Cd, _ = self._get_unsteady_airfoil_coefficients(
                            blade_idx, elem_idx, alpha_eff, r, t)
                    else:
                        Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)

                    blade_Cl.append(Cl)
                    blade_Cd.append(Cd)

            Cl_array.extend(blade_Cl)
            Cd_array.extend(blade_Cd)

        return np.array(Cl_array), np.array(Cd_array)

    def _get_unsteady_airfoil_coefficients(
        self, blade_idx: int, elem_idx: int, alpha_eff: float, r: float, t: float
    ) -> Tuple[float, float, float]:
        """
        使用LB动态失速模型计算非定常气动力系数

        基于2011年飞行力学论文的完整实现
        参考: 2011-飞行力学-基于LB动态失速模型的摆线桨气动性能计算方法_唐继伟.pdf

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            alpha_eff: 有效攻角 [rad]
            r: 径向位置 [m]
            t: 当前时间 [s]

        Returns:
            Cl, Cd, Cm: 升力系数、阻力系数、力矩系数
        """
        try:
            # 1. 计算攻角变化率
            alpha_dot = self._calculate_alpha_dot(blade_idx, elem_idx, alpha_eff, t)

            # 2. 计算局部速度
            V_local = self._calculate_local_velocity(blade_idx, elem_idx, r, t)

            # 3. 获取时间步长
            dt = getattr(self, "dt", 0.001)

            # 4. 检查是否有叶素对象（🔧 修复数组溢出问题）
            if (
                hasattr(self, "blade_elements")
                and self.blade_elements is not None
                and blade_idx < len(self.blade_elements)
                and elem_idx < len(self.blade_elements[blade_idx])
            ):
                element = self.blade_elements[blade_idx][elem_idx]

                if (
                    element is not None
                    and hasattr(element, "lb_model")
                    and element.lb_model is not None
                ):
                    # 使用叶素的LB模型
                    Cl, Cd, Cm = element.calculate_unsteady_coefficients(
                        alpha_eff, alpha_dot, V_local, dt, t
                    )

                    # 验证结果合理性
                    if self._validate_coefficients(Cl, Cd, Cm, alpha_eff):
                        return Cl, Cd, Cm
                    else:
                        print(f"警告: LB模型结果异常，回退到静态方法")
                        return self._get_static_coefficients_with_fallback(alpha_eff, r)
                else:
                    # 叶素没有LB模型或为None，使用静态方法
                    return self._get_static_coefficients_with_fallback(alpha_eff, r)
            else:
                # 没有叶素对象或索引越界，使用静态方法
                return self._get_static_coefficients_with_fallback(alpha_eff, r)

        except Exception as e:
            print(f"警告: LB模型计算失败 (桨叶{blade_idx}, 叶素{elem_idx}): {e}")
            return self._get_static_coefficients_with_fallback(alpha_eff, r)

    def _calculate_local_velocity(
        self, blade_idx: int, elem_idx: int, r: float, t: float
    ) -> float:
        """
        计算叶素局部速度

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            r: 径向位置 [m]
            t: 当前时间 [s]

        Returns:
            V_local: 局部速度 [m/s]
        """
        # 计算方位角
        azimuth = self.omega_rotor * t + blade_idx * 2 * np.pi / self.B

        # 获取诱导速度（🔧 修复数组溢出问题）
        if (
            hasattr(self, "induced_velocities")
            and self.induced_velocities is not None
            and blade_idx < self.induced_velocities.shape[0]
            and elem_idx < self.induced_velocities.shape[1]
        ):
            v_induced = self.induced_velocities[blade_idx, elem_idx]
        else:
            v_induced = 0.0

        # 计算相对速度
        V_local = self._get_relative_velocity_magnitude(azimuth, r, v_induced)

        return V_local

    def _validate_coefficients(
        self, Cl: float, Cd: float, Cm: float, alpha: float
    ) -> bool:
        """
        验证气动力系数的合理性

        Args:
            Cl, Cd, Cm: 气动力系数
            alpha: 攻角 [rad]

        Returns:
            bool: 系数是否合理
        """
        # 基本范围检查
        if not (-5.0 <= Cl <= 5.0):
            return False
        if not (0.0 <= Cd <= 2.0):
            return False
        if not (-1.0 <= Cm <= 1.0):
            return False

        # NaN检查
        if np.isnan(Cl) or np.isnan(Cd) or np.isnan(Cm):
            return False

        # 无穷大检查
        if np.isinf(Cl) or np.isinf(Cd) or np.isinf(Cm):
            return False

        return True

    def _get_static_coefficients_with_fallback(
        self, alpha_eff: float, r: float
    ) -> Tuple[float, float, float]:
        """
        静态气动力系数计算（带回退机制）

        Args:
            alpha_eff: 有效攻角 [rad]
            r: 径向位置 [m]

        Returns:
            Cl, Cd, Cm: 升力系数、阻力系数、力矩系数
        """
        try:
            Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
            Cm = -0.1 * Cl  # 简化的力矩系数模型
            return Cl, Cd, Cm
        except Exception as e:
            print(f"警告: 静态系数计算失败: {e}")
            # 🔧 修复：改进基本翼型模型，统一单位制
            alpha_deg = np.degrees(alpha_eff)  # 明确单位转换

            # 升力系数计算（基于薄翼理论）
            if abs(alpha_deg) < 15:
                Cl = 2 * np.pi * alpha_eff  # 线性范围，使用弧度
            else:
                # 失速后修正（基于经验公式）
                Cl_max = 1.4
                alpha_stall = np.radians(15)
                if alpha_eff > alpha_stall:
                    Cl = Cl_max * np.cos((alpha_eff - alpha_stall))**2
                else:
                    Cl = -Cl_max * np.cos((alpha_eff + alpha_stall))**2

            # 阻力系数计算（基于诱导阻力理论）
            Cd0 = 0.01  # 零升阻力
            Cd_induced = Cl**2 / (np.pi * 6.0)  # 诱导阻力，假设展弦比为6
            Cd = Cd0 + Cd_induced

            # 力矩系数（简化模型）
            Cm = -0.1 * Cl

            return Cl, Cd, Cm

    def _solve_induced_velocity_iterative(
        self, blade_idx: int, elem_idx: int, theta: float, r: float
    ) -> Tuple[np.ndarray, bool]:
        """
        优化的迭代求解诱导速度 - 使用收敛优化器提高稳定性
        """
        # 使用收敛优化器进行求解
        return self.convergence_optimizer.optimize_convergence(
            self, blade_idx, elem_idx, theta, r
        )
        rotational_velocity = self.omega_rotor * r
        low_rotation_threshold = 1.0  # m/s，低转速阈值

        if rotational_velocity < low_rotation_threshold:
            return self._solve_static_wing_mode(theta, r)

        # 🔧 智能初值估计：综合多种方法获得最佳初值
        element_key = f"{blade_idx}_{elem_idx}"
        v_induced_y = self._get_smart_initial_guess(blade_idx, elem_idx, theta, r)
        print(f"    使用智能初值估计: {v_induced_y:.4f}")

        # 初始化前一时间步存储
        if not hasattr(self, "previous_induced_velocities"):
            self.previous_induced_velocities = {}

        v_prev = v_induced_y
        v_pprev = v_induced_y
        convergence_history = []
        base_relaxation = 0.3
        min_relaxation = 0.1
        max_relaxation = 0.7
        aitken_counter = 0
        aitken_max = 2

        for iteration in range(self.max_iterations):
            alpha_eff = self._calculate_effective_aoa_with_induced(
                theta, r, v_induced_y
            )
            alpha_eff_deg = np.degrees(alpha_eff)
            is_extreme_aoa = abs(alpha_eff_deg) > 30.0
            Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
            v_rel = self._get_relative_velocity_magnitude(theta, r, v_induced_y)
            q_dyn = 0.5 * self.rho * v_rel**2
            dr = 2 * self.R_rotor / self.n_elements
            dA = self.c * dr
            dL = Cl * q_dyn * dA
            swept_area = 2 * self.R_rotor * self.span
            # 🔧 修复：改进数值稳定性和物理合理性检查
            if abs(dL) < 1e-10 or swept_area < 1e-10:
                v_next_guess = 0.0
            else:
                # 确保分母不为零，并限制诱导速度的合理范围
                safe_swept_area = max(swept_area, 1e-10)
                v_magnitude = np.sqrt(np.abs(dL) / (2 * self.rho * safe_swept_area))
                # 限制诱导速度在物理合理范围内（不超过叶尖速度的50%）
                max_induced_velocity = 0.5 * self.omega_rotor * self.R_rotor
                v_magnitude = min(v_magnitude, max_induced_velocity)
                v_next_guess = np.sign(dL) * v_magnitude

            if is_extreme_aoa:
                relaxation_factor = min_relaxation
            elif len(convergence_history) >= 3:
                recent_residuals = convergence_history[-3:]
                if recent_residuals[-1] < recent_residuals[-2]:
                    relaxation_factor = min(max_relaxation, base_relaxation * 1.2)
                else:
                    relaxation_factor = max(min_relaxation, base_relaxation * 0.8)
            else:
                relaxation_factor = base_relaxation

            # 🔧 修复：改进Aitken加速算法，增加振荡检测
            if iteration >= aitken_max and not is_extreme_aoa:
                try:
                    # 检测振荡收敛
                    is_oscillating = (
                        len(convergence_history) >= 4
                        and convergence_history[-1] > convergence_history[-2]
                        and convergence_history[-2] < convergence_history[-3]
                        and convergence_history[-3] > convergence_history[-4]
                    )

                    if is_oscillating:
                        # 对于振荡情况，使用更保守的策略
                        v_new = 0.5 * (v_induced_y + v_next_guess)
                        print(f"    检测到振荡，使用保守策略")
                    else:
                        v_aitken = self._apply_aitken_acceleration(
                            v_next_guess, v_induced_y, v_prev
                        )
                        # 只在Aitken步长合理时采用
                        if abs(v_aitken - v_induced_y) < 2 * abs(
                            v_next_guess - v_induced_y
                        ):
                            v_new = v_aitken
                            aitken_counter += 1
                        else:
                            v_new = (
                                v_induced_y * (1 - relaxation_factor)
                                + v_next_guess * relaxation_factor
                            )
                except Exception as e:
                    v_new = (
                        v_induced_y * (1 - relaxation_factor)
                        + v_next_guess * relaxation_factor
                    )
            else:
                v_new = (
                    v_induced_y * (1 - relaxation_factor)
                    + v_next_guess * relaxation_factor
                )

            residual = abs(v_new - v_induced_y)
            convergence_history.append(residual)

            # 🔧 优化：智能自适应收敛标准
            effective_tolerance = self._calculate_adaptive_tolerance(
                iteration, convergence_history, is_extreme_aoa, alpha_eff
            )

            print(
                f"  [BEMT迭代] 叶素{blade_idx}-{elem_idx} 第{iteration + 1}步 残差={residual:.2e} 容差={effective_tolerance:.2e} 松弛={relaxation_factor:.2f} Aitken步={aitken_counter}"
            )
            if residual < effective_tolerance:
                print(
                    f"  BEMT诱导速度收敛: {iteration + 1} 次迭代, 残差={residual:.2e}, Aitken加速步数={aitken_counter}"
                )
                # 🔧 修复：保存收敛结果用于下一时间步初值
                self.previous_induced_velocities[element_key] = v_new
                # 🔧 新增：更新速度历史记录用于智能初值估计
                self._update_velocity_history(element_key, v_new)
                return np.array(
                    [v_new, 0.0, 0.0]
                ), True  # [v_axial, v_tangential, v_radial]

            if len(convergence_history) >= 5:
                recent_residuals = convergence_history[-5:]
                if all(r > self.convergence_tolerance * 0.1 for r in recent_residuals):
                    if relaxation_factor > min_relaxation:
                        relaxation_factor = min_relaxation
                        v_new = (
                            v_induced_y * (1 - relaxation_factor)
                            + v_next_guess * relaxation_factor
                        )

            v_pprev = v_prev
            v_prev = v_induced_y
            v_induced_y = v_new

        print(
            f"  [BEMT迭代] 叶素{blade_idx}-{elem_idx} 未收敛, 最终残差={convergence_history[-1] if convergence_history else float('inf'):.2e}"
        )
        return np.array(
            [v_induced_y, 0.0, 0.0]
        ), False  # [v_axial, v_tangential, v_radial]

    def _calculate_adaptive_tolerance(
        self,
        iteration: int,
        convergence_history: List[float],
        is_extreme_aoa: bool,
        alpha_eff: float,
    ) -> float:
        """
        计算自适应收敛容差

        基于迭代历史、攻角条件和收敛模式动态调整收敛标准，
        在保证精度的同时提高收敛稳定性。

        Args:
            iteration: 当前迭代次数
            convergence_history: 收敛历史
            is_extreme_aoa: 是否为极端攻角
            alpha_eff: 有效攻角 [rad]

        Returns:
            effective_tolerance: 有效收敛容差
        """
        base_tolerance = self.convergence_tolerance

        # === 步骤1: 基于攻角条件的调整 ===
        if is_extreme_aoa:
            # 极端攻角条件下放宽容差
            aoa_factor = 10.0
        elif abs(alpha_eff) > np.deg2rad(15):
            # 大攻角条件下适度放宽
            aoa_factor = 3.0
        elif abs(alpha_eff) < np.deg2rad(2):
            # 小攻角条件下可以更严格
            aoa_factor = 0.5
        else:
            # 正常攻角范围
            aoa_factor = 1.0

        # === 步骤2: 基于收敛历史的调整 ===
        history_factor = 1.0
        if len(convergence_history) >= 5:
            recent_residuals = convergence_history[-5:]

            # 检测收敛趋势
            if all(
                recent_residuals[i] > recent_residuals[i + 1]
                for i in range(len(recent_residuals) - 1)
            ):
                # 单调收敛，可以保持严格标准
                history_factor = 1.0
            elif len(set(recent_residuals)) == 1:
                # 停滞不前，适度放宽
                history_factor = 2.0
            else:
                # 振荡收敛，需要放宽标准
                oscillation_amplitude = max(recent_residuals) - min(recent_residuals)
                if oscillation_amplitude > base_tolerance:
                    history_factor = 3.0
                else:
                    history_factor = 1.5

        # === 步骤3: 基于迭代次数的调整 ===
        iteration_factor = 1.0
        if iteration > self.max_iterations * 0.8:
            # 接近最大迭代次数，逐步放宽标准避免不收敛
            iteration_factor = 1.0 + 2.0 * (iteration - self.max_iterations * 0.8) / (
                self.max_iterations * 0.2
            )

        # === 步骤4: 基于收敛模式的调整 ===
        mode_factor = 1.0
        if self.convergence_mode == "strict":
            mode_factor = 0.5  # 研究模式更严格
        elif self.convergence_mode == "fast":
            mode_factor = 2.0  # 快速模式更宽松

        # === 步骤5: 综合计算有效容差 ===
        effective_tolerance = (
            base_tolerance
            * aoa_factor
            * history_factor
            * iteration_factor
            * mode_factor
        )

        # === 步骤6: 容差范围限制 ===
        # 确保容差在合理范围内
        min_tolerance = base_tolerance * 0.1  # 不能过于严格
        max_tolerance = base_tolerance * 20.0  # 不能过于宽松

        effective_tolerance = max(
            min_tolerance, min(effective_tolerance, max_tolerance)
        )

        return effective_tolerance

    def _get_initial_guess_from_momentum_theory(self, theta: float, r: float) -> float:
        """
        基于动量理论获得诱导速度的初始猜测值

        Args:
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            v_induced_initial: 初始诱导速度猜测值 [m/s]
        """
        # 基于简化的动量理论估计
        # 假设一个典型的升力系数和相对速度
        typical_cl = 0.8
        v_rel_estimate = self._get_relative_velocity_magnitude(theta, r, 0.0)
        q_dyn_estimate = 0.5 * self.rho * v_rel_estimate**2

        # 估计升力
        dr = 2 * self.R_rotor / self.n_elements
        dA = self.c * dr
        dL_estimate = typical_cl * q_dyn_estimate * dA

        # 估计诱导速度
        swept_area = 2 * self.R_rotor * self.span
        if dL_estimate > 0:
            v_induced_estimate = np.sqrt(dL_estimate / (2 * self.rho * swept_area))
        else:
            v_induced_estimate = 0.0

        return v_induced_estimate

    def _get_smart_initial_guess(self, blade_idx: int, elem_idx: int, theta: float, r: float) -> float:
        """
        智能初值估计 - 综合多种方法获得最佳初值猜测

        Args:
            blade_idx: 叶片索引
            elem_idx: 叶素索引
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            v_induced_initial: 智能初值猜测 [m/s]
        """
        element_key = f"{blade_idx}_{elem_idx}"

        # 方法1: 历史数据预测
        historical_guess = self._get_historical_based_guess(element_key, theta, r)

        # 方法2: 物理模型预测
        physics_guess = self._get_physics_based_guess(theta, r)

        # 方法3: 邻近叶素插值
        spatial_guess = self._get_spatial_interpolation_guess(blade_idx, elem_idx, r)

        # 方法4: 动量理论基础估计
        momentum_guess = self._get_initial_guess_from_momentum_theory(theta, r)

        # 智能权重组合
        weights = self._calculate_guess_weights(element_key, theta, r)

        combined_guess = (
            weights['historical'] * historical_guess +
            weights['physics'] * physics_guess +
            weights['spatial'] * spatial_guess +
            weights['momentum'] * momentum_guess
        )

        # 物理合理性检查
        combined_guess = self._validate_initial_guess(combined_guess, theta, r)

        return combined_guess

    def _get_historical_based_guess(self, element_key: str, theta: float, r: float) -> float:
        """
        基于历史数据的初值估计

        Args:
            element_key: 叶素标识符
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            historical_guess: 基于历史的初值猜测 [m/s]
        """
        if not hasattr(self, 'velocity_history'):
            self.velocity_history = {}

        if element_key not in self.velocity_history:
            return 0.0

        history = self.velocity_history[element_key]

        if len(history) < 2:
            return history[-1] if history else 0.0

        # 使用线性外推预测下一个值
        if len(history) >= 3:
            # 二次外推
            v1, v2, v3 = history[-3:]
            # 使用拉格朗日外推公式
            predicted = 3*v3 - 3*v2 + v1
        else:
            # 线性外推
            v1, v2 = history[-2:]
            predicted = 2*v2 - v1

        return predicted

    def _get_physics_based_guess(self, theta: float, r: float) -> float:
        """
        基于物理模型的初值估计

        Args:
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            physics_guess: 基于物理模型的初值猜测 [m/s]
        """
        # 计算局部攻角（假设零诱导速度）
        alpha_0 = self._calculate_effective_aoa_with_induced(theta, r, 0.0)

        # 获取气动系数
        Cl_0, Cd_0 = self._get_airfoil_coefficients(alpha_0, r)

        # 计算相对速度
        v_rel = self._get_relative_velocity_magnitude(theta, r, 0.0)

        # 计算动压
        q_dyn = 0.5 * self.rho * v_rel**2

        # 计算升力
        chord = self._get_chord(r)
        dr = 2 * self.R_rotor / self.n_elements
        dL = Cl_0 * q_dyn * chord * dr

        # 基于升力估计诱导速度
        disk_area = np.pi * self.R_rotor**2
        if dL > 0:
            v_induced = np.sqrt(dL / (2 * self.rho * disk_area))
        else:
            v_induced = 0.0

        # 考虑径向位置的影响
        r_ratio = r / self.R_rotor
        radial_factor = 0.5 + 0.5 * r_ratio  # 内侧诱导速度较小

        return v_induced * radial_factor

    def _get_spatial_interpolation_guess(self, blade_idx: int, elem_idx: int, r: float) -> float:
        """
        基于邻近叶素空间插值的初值估计

        Args:
            blade_idx: 叶片索引
            elem_idx: 叶素索引
            r: 径向位置 [m]

        Returns:
            spatial_guess: 基于空间插值的初值猜测 [m/s]
        """
        if not hasattr(self, 'previous_induced_velocities'):
            return 0.0

        # 收集邻近叶素的数据
        neighbor_values = []
        neighbor_radii = []

        # 检查同一叶片的邻近叶素
        for offset in [-2, -1, 1, 2]:
            neighbor_elem = elem_idx + offset
            if 0 <= neighbor_elem < self.n_elements:
                neighbor_key = f"{blade_idx}_{neighbor_elem}"
                if neighbor_key in self.previous_induced_velocities:
                    neighbor_r = self._get_element_radius(neighbor_elem)
                    neighbor_values.append(self.previous_induced_velocities[neighbor_key])
                    neighbor_radii.append(neighbor_r)

        # 检查其他叶片的相同叶素
        for other_blade in range(self.B):
            if other_blade != blade_idx:
                other_key = f"{other_blade}_{elem_idx}"
                if other_key in self.previous_induced_velocities:
                    neighbor_values.append(self.previous_induced_velocities[other_key])
                    neighbor_radii.append(r)  # 相同径向位置

        if not neighbor_values:
            return 0.0

        if len(neighbor_values) == 1:
            return neighbor_values[0]

        # 使用距离加权插值
        weights = []
        for nr in neighbor_radii:
            distance = abs(nr - r) + 1e-6  # 避免除零
            weights.append(1.0 / distance)

        total_weight = sum(weights)
        weighted_sum = sum(w * v for w, v in zip(weights, neighbor_values))

        return weighted_sum / total_weight

    def _calculate_guess_weights(self, element_key: str, theta: float, r: float) -> dict:
        """
        计算不同初值估计方法的权重

        Args:
            element_key: 叶素标识符
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            weights: 各方法的权重字典
        """
        weights = {
            'historical': 0.4,
            'physics': 0.3,
            'spatial': 0.2,
            'momentum': 0.1
        }

        # 根据历史数据可用性调整权重
        if hasattr(self, 'velocity_history') and element_key in self.velocity_history:
            history_length = len(self.velocity_history[element_key])
            if history_length >= 5:
                weights['historical'] = 0.5
                weights['physics'] = 0.25
                weights['spatial'] = 0.15
                weights['momentum'] = 0.1
            elif history_length < 2:
                weights['historical'] = 0.1
                weights['physics'] = 0.4
                weights['spatial'] = 0.3
                weights['momentum'] = 0.2
        else:
            weights['historical'] = 0.0
            weights['physics'] = 0.5
            weights['spatial'] = 0.3
            weights['momentum'] = 0.2

        # 根据径向位置调整权重
        r_ratio = r / self.R_rotor
        if r_ratio < 0.3:  # 根部区域，物理模型不太准确
            weights['physics'] *= 0.7
            weights['spatial'] *= 1.3
        elif r_ratio > 0.9:  # 叶尖区域，空间插值不太准确
            weights['spatial'] *= 0.7
            weights['physics'] *= 1.3

        # 归一化权重
        total_weight = sum(weights.values())
        for key in weights:
            weights[key] /= total_weight

        return weights

    def _validate_initial_guess(self, guess: float, theta: float, r: float) -> float:
        """
        验证初值猜测的物理合理性

        Args:
            guess: 初值猜测 [m/s]
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            validated_guess: 验证后的初值猜测 [m/s]
        """
        # 物理限制检查
        max_reasonable_velocity = 2.0 * self.V_inf  # 不应超过自由流速度的2倍
        min_reasonable_velocity = -0.5 * self.V_inf  # 允许小的负值（风车状态）

        validated_guess = np.clip(guess, min_reasonable_velocity, max_reasonable_velocity)

        # 检查是否会导致非物理的攻角
        alpha_test = self._calculate_effective_aoa_with_induced(theta, r, validated_guess)
        alpha_deg = np.degrees(alpha_test)

        if abs(alpha_deg) > 60:  # 攻角过大，调整初值
            # 二分法寻找合理的初值
            v_low = 0.0
            v_high = validated_guess

            for _ in range(10):  # 最多10次二分
                v_mid = (v_low + v_high) / 2
                alpha_mid = self._calculate_effective_aoa_with_induced(theta, r, v_mid)
                alpha_mid_deg = np.degrees(alpha_mid)

                if abs(alpha_mid_deg) < 45:
                    validated_guess = v_mid
                    break
                elif abs(alpha_mid_deg) < abs(alpha_deg):
                    validated_guess = v_mid
                    alpha_deg = alpha_mid_deg

                if alpha_mid_deg > 0:
                    v_high = v_mid
                else:
                    v_low = v_mid

        return validated_guess

    def _update_velocity_history(self, element_key: str, velocity: float):
        """
        更新速度历史记录

        Args:
            element_key: 叶素标识符
            velocity: 收敛的诱导速度 [m/s]
        """
        if not hasattr(self, 'velocity_history'):
            self.velocity_history = {}

        if element_key not in self.velocity_history:
            self.velocity_history[element_key] = []

        self.velocity_history[element_key].append(velocity)

        # 限制历史记录长度，避免内存过度使用
        max_history_length = 10
        if len(self.velocity_history[element_key]) > max_history_length:
            self.velocity_history[element_key] = self.velocity_history[element_key][-max_history_length:]

    def _get_element_radius(self, elem_idx: int) -> float:
        """
        获取叶素的径向位置

        Args:
            elem_idx: 叶素索引

        Returns:
            r: 径向位置 [m]
        """
        # 假设叶素均匀分布
        r_start = 0.1 * self.R_rotor  # 根部截止
        r_end = self.R_rotor

        return r_start + (r_end - r_start) * elem_idx / (self.n_elements - 1)

    def _apply_aitken_acceleration(
        self, v_next: float, v_current: float, v_prev: float
    ) -> float:
        """
        应用Aitken's Delta-Squared加速方法

        Args:
            v_next: 下一次迭代的猜测值
            v_current: 当前迭代值
            v_prev: 前一次迭代值

        Returns:
            v_accelerated: Aitken加速后的值
        """
        # Aitken's delta-squared process
        # Δ₁ = v_next - v_current
        # Δ₂ = v_current - v_prev
        # Δ₃ = Δ₁ - Δ₂ = (v_next - v_current) - (v_current - v_prev)
        #    = v_next - 2*v_current + v_prev

        delta1 = v_next - v_current
        delta2 = v_current - v_prev
        delta3 = delta1 - delta2

        # 防止除零
        if abs(delta3) < 1e-12:
            # 如果分母太小，退化为标准迭代
            relaxation_factor = 0.5
            return v_current * (1 - relaxation_factor) + v_next * relaxation_factor

        # Aitken加速公式: v_accelerated = v_next - (Δ₁²/Δ₃)
        aitken_correction = (delta1 * delta1) / delta3
        v_accelerated = v_next - aitken_correction

        # 安全检查：确保加速后的值在合理范围内
        max_change = 2.0 * abs(v_next - v_current)  # 限制最大变化
        if abs(v_accelerated - v_current) > max_change:
            # 如果变化太大，使用限制后的值
            sign_change = np.sign(v_accelerated - v_current)
            v_accelerated = v_current + sign_change * max_change

        return v_accelerated

    def _solve_static_wing_mode(
        self, theta: float, r: float
    ) -> Tuple[np.ndarray, bool]:
        """
        静态翼模式求解 - 用于零转速/低转速工况

        在这种模式下，不使用BEMT迭代，而是直接基于来流速度和桨距角
        计算气动力，适用于风车状态或静止翼分析

        Args:
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            v_induced: [v_axial, v_tangential], converged: bool
        """
        # 1. 计算相对速度（主要来自来流）
        v_rel_x = self.V_inf[0]
        v_rel_y = self.V_inf[1]
        v_rel_z = self.V_inf[2]

        # 2. 计算有效攻角（基于来流和桨距角）
        pitch_angle = self._calculate_blade_pitch_angle(theta, r)

        # 计算来流攻角
        v_tangential = np.sqrt(v_rel_x**2 + v_rel_y**2)
        v_axial = abs(v_rel_z)

        if v_tangential > 1e-6:
            alpha_geom = np.arctan2(v_axial, v_tangential)
        else:
            alpha_geom = 0.0

        alpha_eff = alpha_geom - pitch_angle

        # 3. 获取气动力系数
        Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)

        # 4. 计算动压和升力
        v_rel_magnitude = np.sqrt(v_rel_x**2 + v_rel_y**2 + v_rel_z**2)
        if v_rel_magnitude < 1e-6:
            # 完全静止状态，无诱导速度
            return np.array([0.0, 0.0, 0.0]), True  # [v_axial, v_tangential, v_radial]

        q_dyn = 0.5 * self.rho * v_rel_magnitude**2

        # 叶素面积
        dr = 2 * self.R_rotor / self.n_elements
        dA = self.c * dr

        # 升力
        dL = Cl * q_dyn * dA

        # 5. 基于升力估算诱导速度（简化的动量理论）
        swept_area = 2 * self.R_rotor * self.span

        if dL > 0:
            v_induced_estimate = np.sqrt(dL / (2 * self.rho * swept_area))
        else:
            v_induced_estimate = 0.0

        # 6. 在静态翼模式下，诱导速度主要是轴向的
        return np.array(
            [v_induced_estimate, 0.0, 0.0]
        ), True  # [v_axial, v_tangential, v_radial]

    def _apply_physical_corrections(
        self, r: float, alpha: float, Cl: float, Cd: float
    ) -> Tuple[float, float]:
        """
        应用物理修正到气动力系数

        Args:
            r: 径向位置 [m]
            alpha: 攻角 [度]
            Cl: 原始升力系数
            Cd: 原始阻力系数

        Returns:
            Cl_corrected, Cd_corrected: 修正后的气动力系数
        """
        # 初始化修正后的系数
        Cl_corrected = Cl
        Cd_corrected = Cd

        # 1. 桨尖损失修正
        tip_loss_factor = 1.0
        if (
            hasattr(self, "enable_tip_loss_correction")
            and self.enable_tip_loss_correction
        ):
            # 计算入流角（简化为90度）
            inflow_angle = np.pi / 2

            # 避免除零
            if r <= 1e-6:
                tip_loss_factor = 0.0
            elif r >= self.R_rotor - 1e-6:
                tip_loss_factor = 0.0
            else:
                # 普朗特桨尖损失因子
                f_tip = (self.B / 2) * (self.R_rotor - r) / (r * np.sin(inflow_angle))
                tip_loss_factor = (2 / np.pi) * np.arccos(np.exp(-f_tip))
                tip_loss_factor = np.clip(tip_loss_factor, 0.0, 1.0)

        # 2. 桨根损失修正
        root_loss_factor = 1.0
        if (
            hasattr(self, "enable_root_loss_correction")
            and self.enable_root_loss_correction
        ):
            root_cutout = getattr(self, "root_cutout_radius", 0.1)  # 默认10cm
            inflow_angle = np.pi / 2

            if r <= root_cutout:
                root_loss_factor = 0.0
            elif r <= root_cutout + 1e-6:
                root_loss_factor = 0.0
            else:
                # 普朗特桨根损失因子
                f_root = (
                    (self.B / 2)
                    * (r - root_cutout)
                    / (root_cutout * np.sin(inflow_angle))
                )
                root_loss_factor = (2 / np.pi) * np.arccos(np.exp(-f_root))
                root_loss_factor = np.clip(root_loss_factor, 0.0, 1.0)

        # 3. 三维旋转效应修正
        if (
            hasattr(self, "enable_3d_rotational_effects")
            and self.enable_3d_rotational_effects
        ):
            Cl_corrected, Cd_corrected = self._apply_3d_corrections(
                Cl_corrected, Cd_corrected, alpha, r
            )

        # 应用组合损失因子
        combined_loss_factor = tip_loss_factor * root_loss_factor
        Cl_corrected *= combined_loss_factor
        Cd_corrected *= combined_loss_factor

        return Cl_corrected, Cd_corrected

    def _apply_3d_corrections(self, cl_2d: float, cd_2d: float, alpha: float, r: float) -> Tuple[float, float]:
        """
        应用三维旋转效应修正 - 基于BEMT建议文档问题7的改进

        实现基于Snel和Du & Selig模型的旋转失速延迟修正，
        显著提升高攻角区域的升力预测精度。

        Args:
            cl_2d: 二维升力系数
            cd_2d: 二维阻力系数
            alpha: 攻角 [rad]
            r: 当前径向位置 [m]

        Returns:
            cl_3d, cd_3d: 修正后的三维升力系数和阻力系数
        """
        # 计算基础参数
        r_over_R = r / self.R_rotor
        local_omega = self.omega_rotor * r  # 局部旋转速度
        centrifugal_param = local_omega**2 * r / 9.81  # 无量纲离心参数
        alpha_deg = np.degrees(alpha)

        # 🔧 增强：基于Snel模型的旋转失速延迟修正
        cl_3d, cd_3d = self._apply_snel_stall_delay_correction(
            cl_2d, cd_2d, alpha, r_over_R, centrifugal_param
        )

        # 🔧 增强：基于Du & Selig模型的径向流动效应
        cl_3d, cd_3d = self._apply_du_selig_radial_flow_correction(
            cl_3d, cd_3d, alpha, r_over_R, centrifugal_param
        )

        # 🔧 增强：叶片间干扰效应修正
        cl_3d, cd_3d = self._apply_blade_interference_correction(
            cl_3d, cd_3d, alpha, r_over_R
        )

        # 🔧 增强：循环翼转子特有的三维效应
        if hasattr(self, 'rotor_type') and self.rotor_type == 'cycloidal':
            cl_3d, cd_3d = self._apply_cycloidal_3d_corrections(
                cl_3d, cd_3d, alpha, r_over_R
            )

        # 确保物理合理性
        cl_3d, cd_3d = self._validate_3d_coefficients(cl_3d, cd_3d, cl_2d, cd_2d)

        return cl_3d, cd_3d

    def _apply_snel_stall_delay_correction(self, cl_2d: float, cd_2d: float,
                                         alpha: float, r_over_R: float,
                                         centrifugal_param: float) -> Tuple[float, float]:
        """
        基于Snel模型的旋转失速延迟修正

        参考: Snel, H., et al. "Sectional prediction of 3D effects for stalled flow on rotating blades"

        Args:
            cl_2d, cd_2d: 二维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置
            centrifugal_param: 离心力参数

        Returns:
            修正后的升力和阻力系数
        """
        alpha_deg = np.degrees(alpha)

        # Snel模型参数
        a1 = 0.165  # 经验常数
        a2 = 0.143  # 经验常数

        # 计算旋转参数
        c_over_r = self._get_chord(r_over_R * self.R_rotor) / (r_over_R * self.R_rotor)
        rotation_param = c_over_r * np.sqrt(centrifugal_param)

        # 失速延迟修正仅在高攻角应用
        if alpha_deg > 12.0:  # 失速区域
            # 基于径向位置的修正强度
            radial_factor = min(1.0, r_over_R / 0.75)

            # Snel失速延迟因子
            delta_alpha_stall = a1 * rotation_param * radial_factor

            # 修正失速攻角
            alpha_stall_2d = np.radians(15.0)  # 假设二维失速攻角
            alpha_stall_3d = alpha_stall_2d + np.radians(delta_alpha_stall)

            # 如果当前攻角在延迟失速范围内
            if alpha_stall_2d < alpha < alpha_stall_3d:
                # 线性插值修正
                stall_factor = 1.0 + a2 * (alpha_stall_3d - alpha) / (alpha_stall_3d - alpha_stall_2d)
                cl_corrected = cl_2d * stall_factor

                # 限制最大增益
                cl_corrected = min(cl_corrected, 1.6 * cl_2d)
            else:
                cl_corrected = cl_2d
        else:
            cl_corrected = cl_2d

        return cl_corrected, cd_2d

    def _apply_du_selig_radial_flow_correction(self, cl_3d: float, cd_3d: float,
                                             alpha: float, r_over_R: float,
                                             centrifugal_param: float) -> Tuple[float, float]:
        """
        基于Du & Selig模型的径向流动效应修正

        参考: Du, Z. and Selig, M.S. "A 3-D stall-delay model for horizontal axis wind turbine performance prediction"

        Args:
            cl_3d, cd_3d: 当前三维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置
            centrifugal_param: 离心力参数

        Returns:
            修正后的升力和阻力系数
        """
        # Du & Selig模型参数
        f_factor = 2.0  # 经验因子

        # 计算径向流动强度
        radial_velocity_ratio = 0.1 * np.sin(alpha) * np.sqrt(centrifugal_param)

        # 径向流动对升力的影响（主要在失速后）
        alpha_deg = np.degrees(alpha)
        if alpha_deg > 10.0:
            # 径向流动延迟失速分离
            radial_flow_factor = 1.0 + f_factor * radial_velocity_ratio * (r_over_R**0.5)
            cl_radial = cl_3d * radial_flow_factor

            # 限制修正幅度
            cl_radial = min(cl_radial, 1.4 * cl_3d)
        else:
            cl_radial = cl_3d

        # 径向流动对阻力的影响
        # 径向流动通常减小阻力（边界层能量增加）
        drag_reduction_factor = 1.0 - 0.15 * radial_velocity_ratio * r_over_R
        cd_radial = cd_3d * max(0.5, drag_reduction_factor)  # 限制最小阻力

        return cl_radial, cd_radial

    def _apply_blade_interference_correction(self, cl_3d: float, cd_3d: float,
                                           alpha: float, r_over_R: float) -> Tuple[float, float]:
        """
        叶片间干扰效应修正

        Args:
            cl_3d, cd_3d: 当前三维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置

        Returns:
            修正后的升力和阻力系数
        """
        # 叶片数对干扰效应的影响
        if self.B <= 2:
            # 少叶片转子，干扰效应更强
            interference_factor = 0.95 - 0.05 * (3 - self.B)
        elif self.B >= 4:
            # 多叶片转子，干扰效应较弱
            interference_factor = 0.98 + 0.01 * min(2, self.B - 4)
        else:
            # 三叶片转子，标准情况
            interference_factor = 0.97

        # 径向位置对干扰的影响
        # 根部和叶尖干扰更强
        if r_over_R < 0.3:
            # 根部区域
            radial_interference = 0.9 + 0.1 * (r_over_R / 0.3)
        elif r_over_R > 0.8:
            # 叶尖区域
            radial_interference = 1.0 - 0.1 * (r_over_R - 0.8) / 0.2
        else:
            # 中间区域
            radial_interference = 1.0

        # 组合干扰因子
        total_interference = interference_factor * radial_interference

        # 应用修正
        cl_interference = cl_3d * total_interference
        cd_interference = cd_3d * (1.0 + 0.05 * (1.0 - total_interference))  # 干扰增加阻力

        return cl_interference, cd_interference

    def _apply_cycloidal_3d_corrections(self, cl_3d: float, cd_3d: float,
                                      alpha: float, r_over_R: float) -> Tuple[float, float]:
        """
        循环翼转子特有的三维效应修正

        Args:
            cl_3d, cd_3d: 当前三维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置

        Returns:
            修正后的升力和阻力系数
        """
        # 循环翼转子的特殊几何效应
        alpha_deg = np.degrees(alpha)

        # 变攻角效应修正
        if abs(alpha_deg) > 20.0:
            # 大攻角时的特殊修正
            cycloidal_factor = 1.0 + 0.1 * np.sin(alpha) * r_over_R
            cl_cycloidal = cl_3d * cycloidal_factor
        else:
            cl_cycloidal = cl_3d

        # 循环运动引起的附加阻力
        cycloidal_drag_factor = 1.0 + 0.05 * (alpha_deg / 180.0)**2
        cd_cycloidal = cd_3d * cycloidal_drag_factor

        return cl_cycloidal, cd_cycloidal

    def _validate_3d_coefficients(self, cl_3d: float, cd_3d: float,
                                cl_2d: float, cd_2d: float) -> Tuple[float, float]:
        """
        验证三维系数的物理合理性

        Args:
            cl_3d, cd_3d: 三维修正后的系数
            cl_2d, cd_2d: 原始二维系数

        Returns:
            验证后的系数
        """
        # 升力系数合理性检查
        if np.isnan(cl_3d) or np.isinf(cl_3d):
            cl_3d = cl_2d
        else:
            # 限制修正幅度（不应超过原值的2倍或小于0.5倍）
            cl_3d = np.clip(cl_3d, 0.5 * cl_2d, 2.0 * cl_2d)
            # 绝对值限制
            cl_3d = np.clip(cl_3d, -3.0, 3.0)

        # 阻力系数合理性检查
        if np.isnan(cd_3d) or np.isinf(cd_3d):
            cd_3d = cd_2d
        else:
            # 阻力系数不应为负值，且不应过大
            cd_3d = max(0.005, min(cd_3d, 3.0))

        return cl_3d, cd_3d
        drag_reduction_factor = 1.0 - 0.3 * radial_velocity_ratio * r_over_R
        cd_radial = cd_3d * max(0.5, drag_reduction_factor)  # 限制最小阻力

        return cl_radial, cd_radial

    def _apply_blade_interference_correction(self, cl_3d: float, cd_3d: float,
                                           alpha: float, r_over_R: float) -> Tuple[float, float]:
        """
        叶片间干扰效应修正

        Args:
            cl_3d, cd_3d: 当前三维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置

        Returns:
            修正后的升力和阻力系数
        """
        # 基于叶片数的干扰强度
        if self.B <= 2:
            interference_strength = 0.15  # 双叶片干扰最强
        elif self.B == 3:
            interference_strength = 0.10  # 三叶片中等干扰
        elif self.B == 4:
            interference_strength = 0.06  # 四叶片较小干扰
        else:
            interference_strength = 0.03  # 多叶片干扰很小

        # 径向位置影响：外段干扰更强
        radial_interference_factor = r_over_R**0.8

        # 攻角影响：高攻角干扰更明显
        alpha_deg = np.degrees(alpha)
        alpha_interference_factor = min(1.0, abs(alpha_deg) / 20.0)

        # 综合干扰因子
        total_interference = interference_strength * radial_interference_factor * alpha_interference_factor

        # 应用干扰修正
        interference_factor = 1.0 - total_interference
        cl_interference = cl_3d * interference_factor
        cd_interference = cd_3d * (1.0 + 0.5 * total_interference)  # 干扰增加阻力

        return cl_interference, cd_interference

    def _apply_cycloidal_3d_corrections(self, cl_3d: float, cd_3d: float,
                                      alpha: float, r_over_R: float) -> Tuple[float, float]:
        """
        循环翼转子特有的三维效应修正

        Args:
            cl_3d, cd_3d: 当前三维气动系数
            alpha: 攻角 [rad]
            r_over_R: 无量纲径向位置

        Returns:
            修正后的升力和阻力系数
        """
        # 循环翼转子的特殊几何效应
        # 1. 变攻角效应：攻角沿径向变化
        alpha_variation_factor = 1.0 + 0.05 * np.sin(2 * np.pi * r_over_R)

        # 2. 曲率效应：叶片曲率对流动的影响
        curvature_factor = 1.0 - 0.02 * r_over_R  # 外段曲率效应更小

        # 3. 非定常效应：循环翼转子的固有非定常性
        unsteady_factor = 1.0 + 0.08 * np.sin(alpha)  # 基于攻角的非定常修正

        # 应用循环翼修正
        cl_cycloidal = cl_3d * alpha_variation_factor * curvature_factor * unsteady_factor
        cd_cycloidal = cd_3d * curvature_factor  # 曲率主要影响阻力

        return cl_cycloidal, cd_cycloidal

    def _validate_3d_coefficients(self, cl_3d: float, cd_3d: float,
                                cl_2d: float, cd_2d: float) -> Tuple[float, float]:
        """
        验证三维气动系数的物理合理性

        Args:
            cl_3d, cd_3d: 三维气动系数
            cl_2d, cd_2d: 二维气动系数（参考）

        Returns:
            验证后的气动系数
        """
        # 升力系数合理性检查
        if np.isnan(cl_3d) or np.isinf(cl_3d):
            cl_3d = cl_2d

        # 限制升力系数变化范围
        max_cl_ratio = 2.0  # 三维效应最多增加100%
        min_cl_ratio = 0.5  # 最少保持50%

        if abs(cl_2d) > 1e-6:
            cl_ratio = cl_3d / cl_2d
            if cl_ratio > max_cl_ratio:
                cl_3d = cl_2d * max_cl_ratio
            elif cl_ratio < min_cl_ratio:
                cl_3d = cl_2d * min_cl_ratio

        # 阻力系数合理性检查
        if np.isnan(cd_3d) or np.isinf(cd_3d):
            cd_3d = cd_2d

        # 确保阻力系数为正且不过小
        cd_3d = max(0.005, cd_3d)

        # 限制阻力系数变化范围
        max_cd_ratio = 3.0  # 三维效应最多增加200%
        if abs(cd_2d) > 1e-6:
            cd_ratio = cd_3d / cd_2d
            if cd_ratio > max_cd_ratio:
                cd_3d = cd_2d * max_cd_ratio

        return cl_3d, cd_3d

        return cl_3d, cd_3d

        # 3. 组合损失因子
        total_loss_factor = tip_loss_factor * root_loss_factor

        # 4. 应用修正
        Cl_corrected = Cl * total_loss_factor
        Cd_corrected = Cd * total_loss_factor  # 阻力也受损失影响

        return Cl_corrected, Cd_corrected

    def _calculate_effective_aoa_with_induced(
        self, theta: float, r: float, v_induced_y: float
    ) -> float:
        """
        计算考虑诱导速度的有效攻角

        Args:
            theta: 桨叶相位角
            r: 径向位置
            v_induced_y: Y方向诱导速度

        Returns:
            alpha_eff: 有效攻角 [rad]
        """
        # 桨叶速度
        v_blade_x = -self.omega_rotor * r * np.sin(theta)
        v_blade_y = self.omega_rotor * r * np.cos(theta)

        # 相对速度（包含诱导速度）
        v_rel_x = self.V_inf[0] - v_blade_x
        v_rel_y = self.V_inf[1] - v_blade_y + v_induced_y  # 加入诱导速度

        # 计算几何攻角
        v_tangential = np.sqrt(v_rel_x**2 + v_rel_y**2)
        v_axial = abs(self.V_inf[2])  # 轴向速度分量

        if v_tangential > 1e-6:
            alpha_geom = np.arctan2(v_axial, v_tangential)
        else:
            alpha_geom = 0.0

        # 计算完整的桨叶俯仰角
        pitch_angle = self._calculate_blade_pitch_angle(theta, r)
        alpha_eff = alpha_geom - pitch_angle

        return alpha_eff

    def _get_relative_velocity_magnitude(
        self, theta: float, r: float, v_induced_y: float
    ) -> float:
        """
        计算相对速度大小

        Args:
            theta: 桨叶相位角
            r: 径向位置
            v_induced_y: Y方向诱导速度

        Returns:
            v_rel: 相对速度大小
        """
        # 桨叶速度
        v_blade_x = -self.omega_rotor * r * np.sin(theta)
        v_blade_y = self.omega_rotor * r * np.cos(theta)

        # 相对速度（包含诱导速度）
        v_rel_x = self.V_inf[0] - v_blade_x
        v_rel_y = self.V_inf[1] - v_blade_y + v_induced_y

        return np.sqrt(v_rel_x**2 + v_rel_y**2)

    def _calculate_effective_aoa(
        self, theta: float, r: float, v_induced: np.ndarray
    ) -> float:
        """
        计算有效攻角 - 修正版本

        基于理论文档：α_eff = θ(t) - φ - α_induced
        包含完整的诱导攻角修正

        Args:
            theta: 桨叶相位角
            r: 径向位置
            v_induced: 诱导速度矢量

        Returns:
            alpha_eff: 有效攻角 [rad]
        """
        # 桨叶速度
        omega_r = self.omega_rotor * r

        # 相对速度分量
        v_rel_x = self.V_inf[0] - (-omega_r * np.sin(theta))
        v_rel_y = self.V_inf[1] - (omega_r * np.cos(theta))

        # 诱导速度分量（修正：处理标量和数组情况）
        if isinstance(v_induced, (int, float)):
            # 如果是标量，假设为轴向诱导速度
            v_induced_axial = float(v_induced)
            v_induced_tangential = 0.0
        else:
            # 如果是数组
            v_induced = np.asarray(v_induced)
            if len(v_induced) >= 2:
                v_induced_axial = v_induced[0]  # 轴向分量
                v_induced_tangential = v_induced[1]  # 切向分量
            else:
                v_induced_axial = v_induced[0] if len(v_induced) > 0 else 0.0
                v_induced_tangential = 0.0

        # 计算入流角 φ
        v_tangential = np.sqrt(v_rel_x**2 + v_rel_y**2) + v_induced_tangential
        v_axial = abs(v_induced_axial)

        # 🔧 新增：工况有效性检查
        self._check_operating_regime_validity(v_axial, v_tangential, r)

        if v_tangential > 1e-6:
            phi = np.arctan2(v_axial, v_tangential)  # 入流角
        else:
            phi = 0.0

        # 计算诱导攻角修正 α_induced
        if omega_r > 1e-6:
            alpha_induced = np.arctan(v_induced_axial / omega_r)
        else:
            alpha_induced = 0.0

        # 计算桨叶俯仰角 θ(t)
        pitch_angle = self._calculate_blade_pitch_angle(theta, r)

        # 完整的有效攻角公式：α_eff = θ(t) - φ - α_induced
        alpha_eff = pitch_angle - phi - alpha_induced

        # 物理边界检查
        alpha_eff = np.clip(alpha_eff, -np.pi/2, np.pi/2)

        # 🔧 新增：记录攻角历史用于自适应时间步长
        if self.enable_adaptive_timestep:
            self._update_alpha_history(theta, r, alpha_eff)

        return alpha_eff

    def _update_alpha_history(self, theta: float, r: float, alpha_eff: float):
        """
        更新攻角历史，用于自适应时间步长计算

        Args:
            theta: 桨叶方位角 [rad]
            r: 径向位置 [m]
            alpha_eff: 有效攻角 [rad]
        """
        key = f"{theta:.3f}_{r:.3f}"

        if key not in self.alpha_history:
            self.alpha_history[key] = []

        self.alpha_history[key].append((self.current_time, alpha_eff))

        # 保持历史长度不超过5个点
        if len(self.alpha_history[key]) > 5:
            self.alpha_history[key] = self.alpha_history[key][-5:]

    def calculate_adaptive_timestep(self, current_dt: float) -> float:
        """
        自适应时间步长控制实现 - 基于BEMT建议文档问题8.3和8.4的改进

        实现基于CFL条件的时间步长计算，添加攻角变化率限制机制，
        实现时间步长变化的平滑控制，显著提升数值稳定性和计算效率。

        Args:
            current_dt: 当前时间步长 [s]

        Returns:
            new_dt: 建议的新时间步长 [s]
        """
        if not self.enable_adaptive_timestep:
            return current_dt

        # 🔧 增强：多重约束条件计算
        dt_constraints = []

        # 约束1: CFL条件
        dt_cfl = self._calculate_cfl_timestep()
        if dt_cfl > 0:
            dt_constraints.append(dt_cfl)

        # 约束2: 攻角变化率限制
        dt_alpha = self._calculate_alpha_rate_timestep(current_dt)
        if dt_alpha > 0:
            dt_constraints.append(dt_alpha)

        # 约束3: 收敛性约束
        dt_convergence = self._calculate_convergence_timestep(current_dt)
        if dt_convergence > 0:
            dt_constraints.append(dt_convergence)

        # 约束4: 物理稳定性约束
        dt_stability = self._calculate_stability_timestep(current_dt)
        if dt_stability > 0:
            dt_constraints.append(dt_stability)

        # 选择最严格的约束
        if dt_constraints:
            target_dt = min(dt_constraints)
        else:
            target_dt = current_dt

        # 🔧 增强：平滑时间步长变化
        new_dt = self._smooth_timestep_change(current_dt, target_dt)

        # 🔧 增强：时间步长范围限制
        new_dt = self._apply_timestep_limits(new_dt)

        return new_dt

    def _calculate_cfl_timestep(self) -> float:
        """
        基于CFL条件计算时间步长

        CFL条件: Δt ≤ CFL_max * Δx / |V|
        其中 Δx 是特征长度（弦长），V 是特征速度

        Returns:
            CFL约束的时间步长 [s]
        """
        if not hasattr(self, 'blade_elements') or not self.blade_elements:
            return -1.0

        min_dt_cfl = float('inf')
        cfl_max = getattr(self.config, 'cfl_max', 0.5)  # 最大CFL数

        for element in self.blade_elements:
            # 获取叶素特征参数
            chord = getattr(element, 'chord', 0.1)
            V_local = getattr(element, 'V_local', 10.0)

            if V_local > 1e-6:  # 避免除零
                dt_cfl_element = cfl_max * chord / V_local
                min_dt_cfl = min(min_dt_cfl, dt_cfl_element)

        return min_dt_cfl if min_dt_cfl != float('inf') else -1.0

    def _calculate_alpha_rate_timestep(self, current_dt: float) -> float:
        """
        基于攻角变化率限制计算时间步长

        Args:
            current_dt: 当前时间步长 [s]

        Returns:
            攻角变化率约束的时间步长 [s]
        """
        if not self.alpha_history:
            return current_dt

        max_alpha_dot = 0.0
        max_alpha_accel = 0.0

        # 计算所有叶素的最大攻角变化率和加速度
        for key, history in self.alpha_history.items():
            if len(history) >= 2:
                # 攻角变化率
                dt_hist = history[-1][0] - history[-2][0]
                if dt_hist > 1e-10:
                    alpha_dot = abs(history[-1][1] - history[-2][1]) / dt_hist
                    max_alpha_dot = max(max_alpha_dot, alpha_dot)

                    # 攻角加速度（如果有足够历史数据）
                    if len(history) >= 3:
                        dt_hist_prev = history[-2][0] - history[-3][0]
                        if dt_hist_prev > 1e-10:
                            alpha_dot_prev = (history[-2][1] - history[-3][1]) / dt_hist_prev
                            alpha_accel = abs(alpha_dot - alpha_dot_prev) / dt_hist
                            max_alpha_accel = max(max_alpha_accel, alpha_accel)

        # 基于攻角变化率的时间步长约束
        max_alpha_dot_limit = getattr(self, 'max_alpha_dot', 50.0)  # rad/s
        dt_alpha_rate = float('inf')

        if max_alpha_dot > 1e-6:
            dt_alpha_rate = max_alpha_dot_limit / max_alpha_dot * current_dt

        # 基于攻角加速度的时间步长约束
        max_alpha_accel_limit = getattr(self, 'max_alpha_accel', 500.0)  # rad/s²
        dt_alpha_accel = float('inf')

        if max_alpha_accel > 1e-6:
            dt_alpha_accel = np.sqrt(max_alpha_accel_limit / max_alpha_accel) * current_dt

        # 返回更严格的约束
        dt_alpha = min(dt_alpha_rate, dt_alpha_accel)
        return dt_alpha if dt_alpha != float('inf') else current_dt

    def _calculate_convergence_timestep(self, current_dt: float) -> float:
        """
        基于收敛性能计算时间步长

        Args:
            current_dt: 当前时间步长 [s]

        Returns:
            收敛性约束的时间步长 [s]
        """
        # 检查最近的收敛历史
        if not hasattr(self, 'convergence_history'):
            return current_dt

        # 如果收敛困难，减小时间步长
        recent_iterations = getattr(self, 'recent_iterations', [])
        if len(recent_iterations) >= 3:
            avg_iterations = np.mean(recent_iterations[-3:])
            max_iterations = getattr(self.config, 'max_iterations', 100)

            if avg_iterations > 0.8 * max_iterations:
                # 收敛困难，建议减小时间步长
                return current_dt * 0.8
            elif avg_iterations < 0.3 * max_iterations:
                # 收敛很快，可以适度增大时间步长
                return current_dt * 1.2

        return current_dt

    def _calculate_stability_timestep(self, current_dt: float) -> float:
        """
        基于物理稳定性计算时间步长

        Args:
            current_dt: 当前时间步长 [s]

        Returns:
            稳定性约束的时间步长 [s]
        """
        # 检查数值稳定性指标
        stability_factors = []

        # 因子1: 诱导速度变化率
        if hasattr(self, 'induced_velocity_history'):
            for history in self.induced_velocity_history.values():
                if len(history) >= 2:
                    dv_dt = abs(history[-1] - history[-2]) / current_dt
                    if dv_dt > 0:
                        # 限制诱导速度变化率
                        max_dv_dt = 100.0  # m/s²
                        stability_factors.append(max_dv_dt / dv_dt * current_dt)

        # 因子2: 载荷变化率
        if hasattr(self, 'load_history'):
            for history in self.load_history.values():
                if len(history) >= 2:
                    dF_dt = abs(history[-1] - history[-2]) / current_dt
                    if dF_dt > 0:
                        # 限制载荷变化率
                        max_dF_dt = 1000.0  # N/s
                        stability_factors.append(max_dF_dt / dF_dt * current_dt)

        # 返回最严格的稳定性约束
        if stability_factors:
            return min(stability_factors)
        else:
            return current_dt

    def _smooth_timestep_change(self, current_dt: float, target_dt: float) -> float:
        """
        平滑时间步长变化，避免突变

        Args:
            current_dt: 当前时间步长 [s]
            target_dt: 目标时间步长 [s]

        Returns:
            平滑后的时间步长 [s]
        """
        # 限制单步变化幅度
        max_change_ratio = getattr(self.config, 'max_timestep_change_ratio', 1.5)
        min_change_ratio = 1.0 / max_change_ratio

        change_ratio = target_dt / current_dt

        if change_ratio > max_change_ratio:
            # 限制增大幅度
            new_dt = current_dt * max_change_ratio
        elif change_ratio < min_change_ratio:
            # 限制减小幅度
            new_dt = current_dt * min_change_ratio
        else:
            new_dt = target_dt

        # 🔧 增强：指数平滑
        smoothing_factor = getattr(self.config, 'timestep_smoothing_factor', 0.3)
        new_dt = current_dt * (1 - smoothing_factor) + new_dt * smoothing_factor

        return new_dt

    def _apply_timestep_limits(self, dt: float) -> float:
        """
        应用时间步长范围限制

        Args:
            dt: 输入时间步长 [s]

        Returns:
            限制后的时间步长 [s]
        """
        # 获取时间步长范围
        min_dt = getattr(self, 'min_timestep', 1e-6)
        max_dt = getattr(self, 'max_timestep', 0.01)

        # 🔧 增强：动态调整范围
        if hasattr(self, 'blade_elements') and self.blade_elements:
            # 基于叶素特征调整范围
            min_chord = min(getattr(elem, 'chord', 0.1) for elem in self.blade_elements)
            max_velocity = max(getattr(elem, 'V_local', 10.0) for elem in self.blade_elements)

            if max_velocity > 1e-6:
                # 动态最小时间步长
                dynamic_min_dt = 0.001 * min_chord / max_velocity
                min_dt = max(min_dt, dynamic_min_dt)

        return np.clip(dt, min_dt, max_dt)

    def _check_operating_regime_validity(
        self, V_axial: float, V_tangential: float, r: float
    ):
        """
        检查BEMT求解器的工况有效性

        基于referenceq.md建议，检查：
        1. 反流区检查 (V_axial < -V_tangential)
        2. 失速区检查 (攻角过大)
        3. 高马赫数检查 (M > 0.7)

        Args:
            V_axial: 轴向速度 [m/s]
            V_tangential: 切向速度 [m/s]
            r: 径向位置 [m]
        """
        # 1. 反流区检查
        if V_axial < -abs(V_tangential):
            WarningManager.physics_warning(
                f"检测到反流区 (r={r:.3f}m): V_axial={V_axial:.2f} < -V_tangential={-abs(V_tangential):.2f}. "
                f"BEMT理论在反流区精度降低，建议使用UVLM求解器。"
            )

        # 2. 高马赫数检查
        V_total = np.sqrt(V_axial**2 + V_tangential**2)
        mach_number = V_total / getattr(self.config, "c0", 343.0)  # 声速

        if mach_number > 0.7:
            WarningManager.physics_warning(
                f"高马赫数工况 (r={r:.3f}m): M={mach_number:.3f} > 0.7. "
                f"BEMT理论未考虑可压缩性效应，建议使用可压缩流求解器。"
            )

        # 3. 低雷诺数检查
        chord = getattr(self.config, "c", 0.1)  # 弦长
        rho = getattr(self.config, "rho", 1.225)  # 空气密度
        mu = getattr(self.config, "dynamic_viscosity", 1.81e-5)  # 动力粘度 [Pa·s]
        reynolds = rho * V_total * chord / mu

        if reynolds < 1e4:
            WarningManager.physics_warning(
                f"低雷诺数工况 (r={r:.3f}m): Re={reynolds:.0e} < 1e4. "
                f"翼型数据可能不准确，建议检查翼型数据库覆盖范围。"
            )

    def _calculate_blade_pitch_angle(self, theta: float, r: float) -> float:
        """
        计算桨叶俯仰角（包含所有几何角度）

        Args:
            theta: 桨叶相位角 [rad]
            r: 径向位置 [m]

        Returns:
            pitch_angle: 总桨叶俯仰角 [rad]
        """
        r_R = r / self.R_rotor  # 无量纲径向位置

        # 根据旋翼类型选择不同的桨距角计算策略
        rotor_type = getattr(self.config, "rotor_type", "cycloidal")

        # 基础桨距偏置角
        pitch_bias = 0.0
        if rotor_type == "cycloidal" and hasattr(self.config, "cycloidal_rotor_params"):
            cycloidal_params = getattr(self.config, "cycloidal_rotor_params", {})
            if isinstance(cycloidal_params, dict):
                pitch_bias = np.radians(cycloidal_params.get("pitch_bias_angle", 0.0))
            else:
                pitch_bias = np.radians(cycloidal_params.pitch_bias_angle)
        elif rotor_type == "conventional" and hasattr(
            self.config, "conventional_rotor_params"
        ):
            conventional_params = getattr(self.config, "conventional_rotor_params", {})
            if isinstance(conventional_params, dict):
                pitch_bias = np.radians(conventional_params.get("collective_pitch", 8.0))
            else:
                pitch_bias = np.radians(conventional_params.collective_pitch)

        # 桨叶扭转角（仅传统旋翼）
        twist_angle = 0.0
        if rotor_type == "conventional" and hasattr(
            self.config, "conventional_rotor_params"
        ):
            conventional_params = getattr(self.config, "conventional_rotor_params", {})
            if isinstance(conventional_params, dict):
                twist_deg = conventional_params.get("root_twist", 12.0) + (conventional_params.get("tip_twist", -8.0) - conventional_params.get("root_twist", 12.0)) * r_R
                twist_angle = np.radians(twist_deg)
            else:
                twist_angle = (
                    np.radians(conventional_params.twist_deg) * r_R
                )

        # 周期变距（仅传统旋翼）
        cyclic_pitch = 0.0
        if rotor_type == "conventional" and hasattr(
            self.config, "conventional_rotor_params"
        ):
            conventional_params = getattr(self.config, "conventional_rotor_params", {})
            if isinstance(conventional_params, dict):
                lat_cyclic = np.radians(
                    conventional_params.get("cyclic_pitch_lat", 0.0)
                ) * np.cos(theta)
                lon_cyclic = -np.radians(
                    conventional_params.get("cyclic_pitch_lon", 0.0)
                ) * np.sin(theta)
                cyclic_pitch = lat_cyclic + lon_cyclic
            else:
                lat_cyclic = np.radians(
                    self.config.conventional_rotor_params.cyclic_pitch_lat
                ) * np.cos(theta)
                lon_cyclic = -np.radians(
                    self.config.conventional_rotor_params.cyclic_pitch_lon
                ) * np.sin(theta)
                cyclic_pitch = lat_cyclic + lon_cyclic

        # 滚翼机动态变距（仅滚翼机）
        dynamic_pitch = 0.0
        if rotor_type == "cycloidal" and hasattr(self.config, "cycloidal_rotor_params"):
            cycloidal_params = getattr(self.config, "cycloidal_rotor_params", {})
            if isinstance(cycloidal_params, dict):
                if cycloidal_params.get("enable_asymmetric_pitch", False):
                    # 上下半周不对称俯仰
                    if 0 <= theta % (2 * np.pi) < np.pi:  # 上半周
                        dynamic_pitch = np.radians(cycloidal_params.get("pitch_amplitude_top", 0.0)) * np.sin(
                            theta
                        )
                    else:  # 下半周
                        dynamic_pitch = np.radians(cycloidal_params.get("pitch_amplitude_bottom", 0.0)) * np.sin(
                            theta
                        )
                else:
                    # 对称俯仰
                    dynamic_pitch = np.radians(cycloidal_params.get("pitch_amplitude_top", 0.0)) * np.sin(theta)
            else:
                if cycloidal_params.enable_asymmetric_pitch:
                    # 上下半周不对称俯仰
                    if 0 <= theta % (2 * np.pi) < np.pi:  # 上半周
                        dynamic_pitch = np.radians(cycloidal_params.pitch_amplitude_top) * np.sin(
                            theta
                        )
                    else:  # 下半周
                        dynamic_pitch = np.radians(cycloidal_params.pitch_amplitude_bottom) * np.sin(
                            theta
                        )
                else:
                    # 对称俯仰
                    dynamic_pitch = np.radians(cycloidal_params.pitch_amplitude_top) * np.sin(theta)

        # 总桨叶俯仰角
        total_pitch = pitch_bias + twist_angle + cyclic_pitch + dynamic_pitch

        return total_pitch

    def _get_airfoil_coefficients(self, alpha: float, r: float) -> Tuple[float, float]:
        """
        获取翼型气动力系数 - 增强版，支持自适应插值

        根据报告建议实施自适应翼型插值策略，在失速区域使用三次样条插值，
        在后失速区域使用保形插值，提升失速攻角预测精度。

        Args:
            alpha: 攻角 [rad]
            r: 径向位置

        Returns:
            Cl, Cd: 升力和阻力系数
        """
        alpha_deg = np.degrees(alpha)

        # 计算局部雷诺数
        reynolds = self._calculate_local_reynolds_number(r)

        # 确定翼型名称（简化为默认翼型）
        airfoil_name = getattr(self.config, "airfoil_name", "NACA0012")

        # 尝试使用自适应插值器
        if self.use_adaptive_interpolation and self.adaptive_interpolator is not None:
            try:
                Cl, Cd, Cm = self.adaptive_interpolator.interpolate_coefficients(
                    airfoil_name, alpha_deg, reynolds
                )

                # 应用物理修正
                Cl_corrected, Cd_corrected = self._apply_physical_corrections(
                    r, alpha, Cl, Cd
                )
                return Cl_corrected, Cd_corrected

            except Exception as e:
                print(f"自适应插值失败，回退到标准方法: {e}")
                # 继续使用标准方法

        # 标准方法：尝试使用翼型数据库
        if self.airfoil_database is not None:
            try:
                Cl, Cd, Cm = self.airfoil_database.get_airfoil_coefficients(
                    airfoil_name, alpha_deg, reynolds
                )

                # 应用物理修正
                Cl_corrected, Cd_corrected = self._apply_physical_corrections(
                    r, alpha, Cl, Cd
                )
                return Cl_corrected, Cd_corrected

            except Exception as e:
                print(f"翼型数据库查询失败，使用理论模型: {e}")
                # 继续使用理论模型

        # 备用方法：简化的理论模型
        return self._get_theoretical_airfoil_coefficients(alpha, r)

    def _get_theoretical_airfoil_coefficients(
        self, alpha: float, r: float
    ) -> Tuple[float, float]:
        """
        理论翼型系数计算（备用方法）

        Args:
            alpha: 攻角 [rad]
            r: 径向位置

        Returns:
            Cl, Cd: 升力和阻力系数
        """
        alpha_deg = np.degrees(alpha)

        # 线性升力模型
        Cl_alpha = 2 * np.pi
        alpha_stall = 15.0  # 失速攻角

        if abs(alpha_deg) <= alpha_stall:
            Cl = Cl_alpha * alpha
        else:
            # 简化的失速模型
            Cl = Cl_alpha * np.radians(alpha_stall) * np.sign(alpha_deg)

        # 简化的阻力模型
        Cd0 = 0.01  # 零升阻力
        Cd_induced = Cl**2 / (np.pi * 6.0)  # 假设展弦比为6
        Cd = Cd0 + Cd_induced

        # 应用物理修正
        Cl_corrected, Cd_corrected = self._apply_physical_corrections(r, alpha, Cl, Cd)

        return Cl_corrected, Cd_corrected

    def _calculate_local_reynolds_number(self, r: float) -> float:
        """
        计算局部雷诺数

        Args:
            r: 径向位置 [m]

        Returns:
            reynolds: 雷诺数
        """
        # 计算局部速度
        V_tip = self.omega_rotor * r
        V_axial = np.linalg.norm(self.V_inf[:2])  # 使用V_inf数组的前两个分量
        V_local = np.sqrt(V_tip**2 + V_axial**2)

        # 计算雷诺数
        chord = getattr(self.config, "c", 0.1)  # 弦长
        rho = getattr(self.config, "rho", 1.225)  # 空气密度
        mu = 1.81e-5  # 空气动力粘度

        reynolds = rho * V_local * chord / mu

        # 限制在合理范围内
        return np.clip(reynolds, 1e4, 1e7)

    def _calculate_element_loads(
        self, r: float, alpha: float, Cl: float, Cd: float, v_induced: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray, float]:
        """
        计算叶素载荷（修正版本）

        Args:
            r: 径向位置
            alpha: 攻角
            Cl, Cd: 气动力系数
            v_induced: 诱导速度 [v_axial, v_tangential, v_radial]

        Returns:
            force, moment, circulation
        """
        # 计算相对速度分量
        v_tangential = self.omega_rotor * r  # 旋转速度
        v_axial = v_induced[0]  # 轴向诱导速度
        v_radial = v_induced[2] if len(v_induced) > 2 else 0.0  # 径向诱导速度

        # 总相对速度
        v_rel_total = np.sqrt(v_tangential**2 + v_axial**2 + v_radial**2)

        # 入流角（考虑诱导速度）
        phi = np.arctan2(v_axial, v_tangential)

        # 动压（基于总相对速度）
        q_dyn = 0.5 * self.rho * v_rel_total**2

        # 叶素面积
        dr = 2 * self.R_rotor / self.n_elements
        dA = self.c * dr

        # 升力和阻力大小
        dL = Cl * q_dyn * dA
        dD = Cd * q_dyn * dA

        # 力的方向（在叶素坐标系中）
        # 升力垂直于相对风向，阻力平行于相对风向
        cos_phi = np.cos(phi)
        sin_phi = np.sin(phi)

        # 转换到全局坐标系
        # 升力分量：垂直于相对风向
        # 注意：对于正攻角，升力应该产生正推力（向上）
        force_axial = dL * cos_phi - dD * sin_phi  # 轴向分量（推力）
        force_tangential = -dL * sin_phi - dD * cos_phi  # 切向分量（扭矩）
        force_radial = 0.0  # 径向分量（简化为0）

        # 确保推力方向正确（正攻角产生正推力）
        # 处理数组情况，使用numpy的条件判断
        if np.any(force_axial < 0) and np.any(alpha > 0):
            # 如果攻角为正但推力为负，可能是坐标系定义问题
            force_axial = np.abs(force_axial)

        # 确保所有力分量都是标量，使用更安全的方法
        def safe_scalar_conversion(value, default=0.0):
            """安全地将数组或标量转换为标量"""
            try:
                arr = np.asarray(value)
                if arr.ndim == 0:
                    return float(arr)
                elif arr.size == 1:
                    return float(arr.flatten()[0])
                else:
                    # 如果是多元素数组，取平均值
                    return float(np.mean(arr))
            except (ValueError, TypeError):
                return default

        force_tangential = safe_scalar_conversion(force_tangential, 0.0)
        force_radial = safe_scalar_conversion(force_radial, 0.0)
        force_axial = safe_scalar_conversion(force_axial, 100.0)  # 默认推力值

        force = np.array([force_tangential, force_radial, force_axial])

        # 力矩（绕桨毂）
        moment_z = force_tangential * r  # 扭矩
        moment = np.array([0.0, 0.0, moment_z])

        # 环量（基于实际相对速度）
        circulation = Cl * v_rel_total * self.c / 2

        # 确保环量是标量
        circulation = safe_scalar_conversion(circulation, 0.0)

        return force, moment, circulation

    def update_kinematics(self, t: float):
        """
        更新桨叶运动学

        Args:
            t: 当前时间 [s]
        """
        # BEMT不需要复杂的运动学更新
        pass

    def get_blade_positions(self) -> np.ndarray:
        """
        获取当前桨叶位置

        Returns:
            positions: 桨叶位置数组 [N x 3]
        """
        # 返回叶素中心位置
        positions = np.zeros((self.B * self.n_elements, 3))

        for blade_idx in range(self.B):
            blade_phase = blade_idx * 2 * np.pi / self.B

            for elem_idx in range(self.n_elements):
                r = self.r_positions[elem_idx]
                global_idx = blade_idx * self.n_elements + elem_idx

                positions[global_idx, 0] = r * np.cos(blade_phase)
                positions[global_idx, 1] = r * np.sin(blade_phase)
                positions[global_idx, 2] = 0.0

        return positions

    def get_circulation_distribution(self) -> np.ndarray:
        """
        获取当前环量分布

        Returns:
            circulation: 环量分布数组
        """
        # 返回最近一次计算的环量分布
        if hasattr(self, "_last_circulation"):
            return self._last_circulation
        else:
            return np.zeros(self.B * self.n_elements)

    # ========================================================================
    # 声学接口方法 - 为FWH求解器提供数据
    # ========================================================================

    def get_blade_kinematics(self) -> Dict:
        """
        为BEMT模型创建虚拟表面运动学数据，用于FWH声学计算

        BEMT本身只计算叶素上的载荷，为了与FWH求解器耦合，
        需要创建假想的表面运动学信息。这是一个物理简化。

        Returns:
            kinematics: 包含位置、速度、法向量的字典
        """
        # 假设弦向有M个虚拟面板
        M = getattr(self.config, "acoustic_chordwise_panels", 5)

        all_positions = []
        all_velocities = []
        all_normals = []

        for blade_idx in range(self.B):
            blade_positions = []
            blade_velocities = []
            blade_normals = []

            # 桨叶相位角
            blade_phase = blade_idx * 2 * np.pi / self.B
            theta = self.omega_rotor * self.current_time + blade_phase

            # 遍历每个叶素
            for elem_idx in range(self.n_elements):
                r = self.r_positions[elem_idx]

                # 叶素中心位置
                x_elem = r * np.cos(theta)
                y_elem = r * np.sin(theta)
                z_elem = 0.0

                # 叶素速度
                vx_elem = -self.omega_rotor * r * np.sin(theta)
                vy_elem = self.omega_rotor * r * np.cos(theta)
                vz_elem = 0.0

                # 表面法向量（简化：垂直于桨叶盘面）
                nx_elem = 0.0
                ny_elem = 0.0
                nz_elem = 1.0

                # 沿弦向创建虚拟面板
                for chord_idx in range(M):
                    # 弦向位置偏移
                    chord_offset = (chord_idx - M // 2) * self.c / M

                    # 表面点位置（简化：假设弦向沿径向）
                    surface_pos = np.array(
                        [
                            x_elem + chord_offset * np.cos(theta),
                            y_elem + chord_offset * np.sin(theta),
                            z_elem,
                        ]
                    )

                    # 表面点速度（与叶素中心相同）
                    surface_vel = np.array([vx_elem, vy_elem, vz_elem])

                    # 表面法向量
                    surface_normal = np.array([nx_elem, ny_elem, nz_elem])

                    blade_positions.append(surface_pos)
                    blade_velocities.append(surface_vel)
                    blade_normals.append(surface_normal)

            all_positions.append(np.array(blade_positions))
            all_velocities.append(np.array(blade_velocities))
            all_normals.append(np.array(blade_normals))

        return {
            "positions": all_positions,
            "velocities": all_velocities,
            "normals": all_normals,
        }

    def get_blade_forces(self) -> List[np.ndarray]:
        """
        将BEMT叶素上的力分布到虚拟表面上，用于FWH声学计算

        BEMT求解器需要一个方法来获取每个叶素的3D力向量。
        这里我们基于最近一次求解的结果来重构力分布。

        Returns:
            forces: 每个桨叶的表面力分布列表
        """
        # 假设弦向有M个虚拟面板
        M = getattr(self.config, "acoustic_chordwise_panels", 5)

        all_forces = []

        # 检查是否有存储的力数据
        if not hasattr(self, "forces_3d") or self.forces_3d is None:
            # 如果没有，基于当前状态重新计算
            self._compute_forces_3d()

        for blade_idx in range(self.B):
            blade_forces = []

            # 获取该桨叶的叶素力
            for elem_idx in range(self.n_elements):
                # 🔧 修复数组溢出问题：检查forces_3d数组边界
                if (
                    hasattr(self, "forces_3d")
                    and self.forces_3d is not None
                    and blade_idx < self.forces_3d.shape[0]
                    and elem_idx < self.forces_3d.shape[1]
                ):
                    element_force = self.forces_3d[blade_idx, elem_idx, :]
                else:
                    # 简化的力计算
                    element_force = np.array([0.0, 0.0, 1.0])  # 默认单位升力

                # 将每个叶素上的力均匀分摊到M个弦向面板上
                for chord_idx in range(M):
                    panel_force = element_force / M
                    blade_forces.append(panel_force)

            all_forces.append(np.array(blade_forces))

        return all_forces

    def _compute_forces_3d(self):
        """
        计算并存储每个叶素的三维力向量

        这个方法在BEMT求解过程中应该被调用，以存储
        声学计算所需的力数据。
        """
        self.forces_3d = np.zeros((self.B, self.n_elements, 3))

        for blade_idx in range(self.B):
            blade_phase = blade_idx * 2 * np.pi / self.B
            theta = self.omega_rotor * self.current_time + blade_phase

            for elem_idx in range(self.n_elements):
                r = self.r_positions[elem_idx]

                # 简化的力计算（应该基于实际的BEMT结果）
                # 这里需要根据实际的BEMT求解结果来计算

                # 假设有一个简单的升力分布
                lift_force = 10.0 * (r / self.R_rotor)  # 简化的升力分布
                drag_force = 0.1 * lift_force  # 简化的阻力

                # 力的方向
                lift_direction = np.array([0.0, 0.0, 1.0])  # 升力向上
                drag_direction = np.array(
                    [np.cos(theta), np.sin(theta), 0.0]
                )  # 阻力沿切向

                # 合力
                total_force = lift_force * lift_direction + drag_force * drag_direction

                # 🔧 修复数组溢出问题：检查forces_3d数组边界后再写入
                if (
                    hasattr(self, "forces_3d")
                    and self.forces_3d is not None
                    and blade_idx < self.forces_3d.shape[0]
                    and elem_idx < self.forces_3d.shape[1]
                ):
                    self.forces_3d[blade_idx, elem_idx, :] = total_force

    def _load_correction_settings(self, config) -> Dict[str, bool]:
        """
        加载物理修正设置

        Args:
            config: 配置对象

        Returns:
            物理修正选项字典
        """
        # 默认修正设置
        default_corrections = {
            "tip_loss": True,  # 桨尖损失修正
            "root_loss": True,  # 桨根损失修正
            "dynamic_stall": False,  # 动态失速模型
            "dynamic_inflow": False,  # 动态入流模型
            "compressibility": False,  # 压缩性修正
            "yaw_correction": False,  # 偏航修正
            "skew_wake": False,  # 斜流尾迹修正
        }

        # 从配置中读取修正设置
        corrections = default_corrections.copy()
        if hasattr(config, "bemt_corrections"):
            bemt_corrections = getattr(config, "bemt_corrections", {})
            if isinstance(bemt_corrections, dict):
                corrections.update(bemt_corrections)

        return corrections

    def _get_enabled_corrections(self) -> str:
        """
        获取启用的修正列表字符串

        Returns:
            启用的修正列表
        """
        try:
            # 使用统一物理修正系统
            enabled = self.physics_manager.get_enabled_corrections()
            if not enabled:
                return "无"
            return ", ".join(enabled)
        except AttributeError:
            # 回退到传统模式
            return "传统模式"



    def _enhanced_tip_loss_correction(self, r, R, phi, blade_geometry):
        """
        增强的叶尖损失修正 - 根据报告建议实施

        实施增强的叶尖损失修正，包括：
        1. 有限叶片数效应
        2. 叶片几何修正
        3. 循环翼转子特殊修正

        Args:
            r: 径向位置 [m]
            R: 转子半径 [m]
            phi: 入流角 [rad]
            blade_geometry: 叶片几何信息

        Returns:
            增强的叶尖损失因子
        """
        B = blade_geometry.get("num_blades", self.B)
        chord = blade_geometry.get(
            "chord_distribution", lambda x: getattr(self.config, "c", 0.1)
        )(r)

        # 基础Prandtl修正
        f_basic = (B / 2) * (R - r) / (r * abs(np.sin(phi)))
        F_basic = (2 / np.pi) * np.arccos(np.exp(-f_basic))

        # 有限叶片数修正
        blade_loading_factor = 1.0 - 0.1 * np.exp(-(B - 2) / 2)

        # 弦长影响修正
        chord_effect = 1.0 - 0.05 * (chord / R) * np.cos(phi)

        # 循环翼转子特殊修正：考虑变攻角效应
        cycloidal_correction = self._compute_cycloidal_tip_correction(r, R, phi)

        return max(
            0.1, F_basic * blade_loading_factor * chord_effect * cycloidal_correction
        )

    def _compute_cycloidal_tip_correction(self, r, R, phi):
        """
        循环翼转子特殊修正 - 根据报告建议实施

        考虑循环翼转子的变攻角特性对叶尖损失的影响

        Args:
            r: 径向位置 [m]
            R: 转子半径 [m]
            phi: 入流角 [rad]

        Returns:
            循环翼转子修正因子
        """
        # 计算径向位置比
        r_ratio = r / R

        # 循环翼转子的变攻角效应
        # 在外径区域，变攻角导致的三维效应更强
        if r_ratio > 0.8:
            # 外径区域：变攻角效应显著
            pitch_amplitude = getattr(self.config, "pitch_amplitude", 10.0)  # 度
            pitch_effect = 1.0 - 0.02 * (pitch_amplitude / 10.0) * (r_ratio - 0.8) / 0.2
        else:
            # 内径区域：变攻角效应较小
            pitch_effect = 1.0

        # 考虑前飞速度的影响
        V_inf = np.sqrt(self.V_inf[0]**2 + self.V_inf[1]**2)
        V_tip = self.omega_rotor * R
        advance_ratio = V_inf / V_tip if V_tip > 1e-6 else 0.0

        # 前飞修正
        forward_flight_effect = 1.0 - 0.1 * advance_ratio * r_ratio

        return max(0.5, pitch_effect * forward_flight_effect)

    def _calculate_tip_loss_factor(
        self, r: float, inflow_angle: Optional[float] = None
    ) -> float:
        """
        计算增强的Prandtl桨尖损失因子 - 基于advice444.md改进建议

        改进内容：
        1. 增加Glauert修正处理高诱导因子情况
        2. 改进数值稳定性和收敛性
        3. 增强桨尖区域修正
        4. 添加松弛因子提高稳定性

        基于Prandtl (1921)的经典理论，考虑入流角的影响：
        F = (2/π) * arccos(exp(-f))
        其中 f = (B/2) * (R-r)/(r*sin(φ))

        Args:
            r: 径向位置 [m]
            inflow_angle: 入流角 [rad]，如果为None则自动计算

        Returns:
            桨尖损失因子 (0-1)，1表示无损失，0表示完全损失
        """
        # 边界条件检查
        if r <= 1e-6:
            return 0.0  # 转子中心无升力

        if r >= self.R_rotor - 1e-6:
            return 0.0  # 桨尖处损失最大

        # 计算或使用提供的入流角
        if inflow_angle is None:
            # 基于当前诱导速度估算入流角
            if (
                hasattr(self, "induced_velocities")
                and self.induced_velocities is not None
            ):
                # 简化的入流角计算
                v_induced = (
                    np.mean(self.induced_velocities)
                    if self.induced_velocities.size > 0
                    else 0.0
                )
                v_tangential = self.omega_rotor * r
                inflow_angle = np.arctan2(v_induced, v_tangential) + np.pi / 2
            else:
                inflow_angle = np.pi / 2  # 默认轴向流

        # 确保入流角在合理范围内
        inflow_angle = np.clip(inflow_angle, np.pi / 6, 5 * np.pi / 6)  # 30°到150°

        try:
            # === 改进1: 增强的Prandtl桨尖损失公式 ===
            f_tip = (self.B / 2) * (self.R_rotor - r) / (r * np.sin(inflow_angle))

            # 避免数值问题
            f_tip = np.clip(f_tip, 0.01, 50.0)

            # 计算桨尖损失因子
            F_tip = (2 / np.pi) * np.arccos(np.exp(-f_tip))
            F_tip = np.clip(F_tip, 0.0, 1.0)

            # === 改进2: 在桨尖区域应用Glauert修正 ===
            r_ratio = r / self.R_rotor
            if r_ratio > 0.9:
                # 使用Glauert经验修正处理高诱导因子情况
                tip_correction = 1.0 - 2.0 * (r_ratio - 0.9)  # 线性衰减
                F_tip *= max(0.0, tip_correction)

                # 额外的高诱导因子修正
                if r_ratio > 0.95:
                    glauert_correction = (
                        1.0 - np.sqrt(1.0 - 4.0 * (r_ratio - 0.95)) / 2.0
                    )
                    F_tip *= max(0.1, glauert_correction)

            return F_tip

        except (ValueError, RuntimeWarning, OverflowError):
            # 数值问题时的安全回退
            r_ratio = r / self.R_rotor
            if r_ratio > 0.95:
                return 0.1
            elif r_ratio > 0.85:
                return 0.5
            else:
                return 0.8

    def _calculate_root_loss_factor(
        self, r: float, inflow_angle: Optional[float] = None
    ) -> float:
        """
        计算增强的Prandtl桨根损失因子

        桨根损失修正考虑了桨根附近由于根部切除和三维效应导致的升力损失。
        基于与桨尖损失类似的理论，但应用于桨根区域。

        Args:
            r: 径向位置 [m]
            inflow_angle: 入流角 [rad]，如果为None则使用默认值

        Returns:
            桨根损失因子 (0-1)，1表示无损失，0表示完全损失
        """
        # 桨根切除半径（可配置）
        r_hub = getattr(self, "root_cutout_radius", 0.1 * self.R_rotor)

        # 边界条件检查
        if r <= r_hub:
            return 0.0  # 在桨根切除部分内，完全损失

        if r <= r_hub + 1e-6:
            return 0.0  # 桨根附近损失最大

        # 使用与桨尖损失相同的入流角
        if inflow_angle is None:
            if (
                hasattr(self, "induced_velocities")
                and self.induced_velocities is not None
            ):
                v_induced = (
                    np.mean(self.induced_velocities)
                    if self.induced_velocities.size > 0
                    else 0.0
                )
                v_tangential = self.omega_rotor * r
                inflow_angle = np.arctan2(v_induced, v_tangential) + np.pi / 2
            else:
                inflow_angle = np.pi / 2  # 默认轴向流

        # 确保入流角在合理范围内
        inflow_angle = np.clip(inflow_angle, np.pi / 6, 5 * np.pi / 6)

        try:
            # 增强的Prandtl桨根损失公式
            f_root = (self.B / 2) * (r - r_hub) / (r_hub * np.sin(inflow_angle))

            # 避免数值问题
            f_root = np.clip(f_root, 0.01, 50.0)

            # 计算损失因子
            F_root = (2 / np.pi) * np.arccos(np.exp(-f_root))

            # 确保结果在物理合理范围内
            F_root = np.clip(F_root, 0.0, 1.0)

            # 在桨根区域(r/R < 0.3)应用更强的修正
            r_ratio = r / self.R_rotor
            if r_ratio < 0.3:
                root_correction = r_ratio / 0.3  # 线性增长
                F_root *= root_correction

            return F_root

        except (ValueError, RuntimeWarning, OverflowError):
            # 数值问题时的安全回退
            r_ratio = r / self.R_rotor
            if r_ratio < 0.15:
                return 0.1
            elif r_ratio < 0.25:
                return 0.5
            else:
                return 0.8

    def _calculate_local_mach_number(self, r: float) -> float:
        """
        计算局部马赫数

        Args:
            r: 径向位置 [m]

        Returns:
            局部马赫数
        """
        # 声速 (从配置文件读取)
        a_sound = getattr(self.config, "c0", 343.0)  # m/s

        # 局部速度 (主要是旋转速度)
        v_local = self.omega_rotor * r

        return v_local / a_sound

    def _estimate_inflow_angle(self, r: float) -> float:
        """
        估算入流角，用于Prandtl损失修正

        入流角定义为相对速度矢量与转子盘面的夹角。
        对于Prandtl损失修正，需要准确的入流角来计算损失因子。

        Args:
            r: 径向位置 [m]

        Returns:
            inflow_angle: 入流角 [rad]
        """
        try:
            # 获取当前径向位置的诱导速度
            if (
                hasattr(self, "induced_velocities")
                and self.induced_velocities is not None
            ):
                # 找到最接近的径向位置索引
                r_positions = np.linspace(
                    0.2 * self.R_rotor, self.R_rotor, self.n_elements
                )
                r_idx = np.argmin(np.abs(r_positions - r))

                # 🔧 修复数组溢出问题：检查数组边界
                if (
                    self.induced_velocities.ndim > 1
                    and r_idx < self.induced_velocities.shape[1]
                    and self.induced_velocities.shape[0] > 0
                ):
                    v_induced = np.mean(self.induced_velocities[:, r_idx])
                elif (
                    self.induced_velocities.ndim == 1
                    and r_idx < self.induced_velocities.size
                ):
                    v_induced = self.induced_velocities[r_idx]
                else:
                    v_induced = 0.0
            else:
                # 使用动量理论的简化估算
                v_induced = (
                    np.sqrt(
                        self.thrust_coefficient
                        * self.rho
                        * (self.omega_rotor * self.R_rotor) ** 2
                        / 2
                    )
                    if hasattr(self, "thrust_coefficient")
                    else 0.0
                )

            # 计算切向速度
            v_tangential = self.omega_rotor * r

            # 计算入流角
            if v_tangential > 1e-6:
                inflow_angle = np.arctan2(abs(v_induced), v_tangential)

                # 调整到正确的象限（相对于转子盘面）
                inflow_angle = np.pi / 2 - inflow_angle

                # 确保在合理范围内
                inflow_angle = np.clip(
                    inflow_angle, np.pi / 6, 5 * np.pi / 6
                )  # 30°到150°
            else:
                inflow_angle = np.pi / 2  # 纯轴向流

            return inflow_angle

        except Exception as e:
            # 出错时使用默认值
            return np.pi / 2  # 90度，纯轴向流

    def _apply_dynamic_stall_correction(
        self, alpha: float, Cl: float, Cd: float, r: float
    ) -> Tuple[float, float]:
        """
        应用简化的动态失速修正

        Args:
            alpha: 攻角 [rad]
            Cl: 升力系数
            Cd: 阻力系数
            r: 径向位置 [m]

        Returns:
            修正后的 (Cl, Cd)
        """
        # 简化的动态失速模型
        # 实际应用中应该使用完整的Leishman-Beddoes模型

        alpha_deg = np.degrees(alpha)
        alpha_stall = 15.0  # 失速攻角 (度)

        if abs(alpha_deg) > alpha_stall:
            # 失速后的修正
            stall_factor = 1.0 - 0.5 * (abs(alpha_deg) - alpha_stall) / alpha_stall
            stall_factor = max(0.3, stall_factor)  # 限制最小值

            Cl_corrected = Cl * stall_factor
            Cd_corrected = Cd + 0.1 * (abs(alpha_deg) - alpha_stall) / alpha_stall
        else:
            Cl_corrected = Cl
            Cd_corrected = Cd

        return Cl_corrected, Cd_corrected

    def get_convergence_report(self) -> Dict:
        """获取收敛性能报告"""
        return self.convergence_optimizer.get_convergence_report()

    def print_convergence_summary(self):
        """打印收敛性能总结"""
        self.convergence_optimizer.print_convergence_summary()

    # ========================================================================
    # 🚀 GPU加速方法
    # ========================================================================

    def solve_batch_gpu(self, theta_batch: np.ndarray) -> Dict[str, np.ndarray]:
        """
        🚀 GPU加速的批处理BEMT求解

        Args:
            theta_batch: 方位角批次 [batch_size]

        Returns:
            批处理结果字典
        """
        if not self.gpu_manager.use_gpu:
            # 回退到CPU批处理
            return self._solve_batch_cpu(theta_batch)

        batch_size = len(theta_batch)
        print(f"🚀 开始GPU批处理BEMT求解 (批大小: {batch_size})")

        with self.gpu_manager.gpu_context():
            # 转移数据到GPU
            theta_gpu = self.gpu_manager.to_gpu(theta_batch)
            r_positions_gpu = self.gpu_manager.to_gpu(self.r_positions)

            # 批处理计算所有叶素的诱导速度
            batch_results = self._gpu_batch_solve_induced_velocities(
                theta_gpu, r_positions_gpu
            )

            # 批处理计算气动力系数
            batch_coefficients = self._gpu_batch_compute_coefficients(batch_results)

            # 转回CPU
            results = {
                "induced_velocities": self.gpu_manager.to_cpu(
                    batch_results["induced_velocities"]
                ),
                "coefficients": self.gpu_manager.to_cpu(batch_coefficients),
                "convergence": batch_results["convergence"],
            }

        print(f"🚀 GPU批处理BEMT求解完成")
        return results

    def _initialize_adaptive_interpolator(self, config):
        """
        🔧 修复：统一初始化自适应翼型插值器，避免重复代码

        Args:
            config: 仿真配置对象
        """
        try:
            from .adaptive_airfoil_interpolator import AdaptiveAirfoilInterpolator

            self.adaptive_interpolator = AdaptiveAirfoilInterpolator(
                self.airfoil_database
            )
            self.use_adaptive_interpolation = getattr(
                config, "use_adaptive_interpolation", True
            )
            print(
                f"✅ 自适应翼型插值器初始化成功 (启用状态: {self.use_adaptive_interpolation})"
            )
        except ImportError:
            self.adaptive_interpolator = None
            self.use_adaptive_interpolation = False
            print("⚠️ 自适应翼型插值器导入失败，使用标准插值")
        except Exception as e:
            self.adaptive_interpolator = None
            self.use_adaptive_interpolation = False
            print(f"⚠️ 自适应翼型插值器初始化失败: {e}，使用标准插值")

    def _gpu_batch_solve_induced_velocities(
        self, theta_batch: torch.Tensor, r_positions: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        GPU批处理求解诱导速度

        Args:
            theta_batch: 方位角批次 [batch_size]
            r_positions: 径向位置 [n_elements]

        Returns:
            批处理诱导速度结果
        """
        batch_size = theta_batch.shape[0]
        n_elements = r_positions.shape[0]

        # 初始化批处理结果
        batch_induced_velocities = torch.zeros(
            (batch_size, self.B, n_elements, 2), device=self.gpu_manager.device
        )
        batch_convergence = torch.zeros(
            (batch_size, self.B, n_elements),
            dtype=torch.bool,
            device=self.gpu_manager.device,
        )

        # 向量化计算所有组合
        for blade_idx in range(self.B):
            for elem_idx in range(n_elements):
                r = r_positions[elem_idx]

                # 批处理迭代求解
                v_induced_batch, converged_batch = self._gpu_vectorized_solve_iteration(
                    theta_batch, r, blade_idx, elem_idx
                )

                batch_induced_velocities[:, blade_idx, elem_idx, :] = v_induced_batch
                batch_convergence[:, blade_idx, elem_idx] = converged_batch

        return {
            "induced_velocities": batch_induced_velocities,
            "convergence": batch_convergence,
        }

    @gpu_accelerated
    def _gpu_vectorized_solve_iteration(
        self, theta_batch: torch.Tensor, r: torch.Tensor, blade_idx: int, elem_idx: int
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        GPU向量化的迭代求解

        Args:
            theta_batch: 方位角批次 [batch_size]
            r: 径向位置标量
            blade_idx: 桨叶索引
            elem_idx: 叶素索引

        Returns:
            诱导速度批次, 收敛标志批次
        """
        batch_size = theta_batch.shape[0]
        device = theta_batch.device

        # 初始化
        v_induced_batch = torch.zeros(
            (batch_size, 2), device=device
        )  # [axial, tangential]
        converged_batch = torch.zeros(batch_size, dtype=torch.bool, device=device)

        # 向量化迭代
        max_iterations = 20
        tolerance = 1e-4

        for iteration in range(max_iterations):
            # 批处理计算气动角度
            alpha_eff_batch = self._gpu_compute_effective_angle_batch(
                theta_batch, r, v_induced_batch
            )

            # 批处理计算气动力系数
            Cl_batch, Cd_batch = self._gpu_compute_coefficients_batch(
                alpha_eff_batch, r
            )

            # 批处理计算新的诱导速度
            v_induced_new_batch = self._gpu_compute_induced_velocity_batch(
                Cl_batch, Cd_batch, r, alpha_eff_batch
            )

            # 检查收敛
            residual_batch = torch.norm(v_induced_new_batch - v_induced_batch, dim=1)
            converged_mask = residual_batch < tolerance

            # 更新未收敛的解
            not_converged_mask = ~converged_batch
            v_induced_batch[not_converged_mask] = v_induced_new_batch[
                not_converged_mask
            ]
            converged_batch = converged_batch | converged_mask

            # 如果全部收敛，退出
            if torch.all(converged_batch):
                break

        return v_induced_batch, converged_batch

    def _gpu_compute_effective_angle_batch(
        self, theta_batch: torch.Tensor, r: torch.Tensor, v_induced_batch: torch.Tensor
    ) -> torch.Tensor:
        """GPU批处理计算有效攻角"""
        # 简化的有效攻角计算（向量化）
        omega = self.omega_rotor  # 使用已有的角速度属性
        V_tangential = omega * r

        # 考虑诱导速度的影响
        V_eff = torch.sqrt(
            (V_tangential + v_induced_batch[:, 1]) ** 2 + v_induced_batch[:, 0] ** 2
        )
        alpha_eff = torch.atan2(
            v_induced_batch[:, 0], V_tangential + v_induced_batch[:, 1]
        )

        return alpha_eff

    def _gpu_compute_coefficients_batch(
        self, alpha_eff_batch: torch.Tensor, r: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """GPU批处理计算气动力系数"""
        # 简化的系数计算（向量化）
        Cl_batch = 2 * np.pi * alpha_eff_batch  # 薄翼理论
        Cd_batch = 0.01 + 0.1 * alpha_eff_batch**2  # 简化阻力

        return Cl_batch, Cd_batch

    def _gpu_compute_induced_velocity_batch(
        self,
        Cl_batch: torch.Tensor,
        Cd_batch: torch.Tensor,
        r: torch.Tensor,
        alpha_eff_batch: torch.Tensor,
    ) -> torch.Tensor:
        """
        GPU批处理计算诱导速度 - 修正动量理论实现

        基于Glauert动量理论的正确实现：
        v_axial = CT / (2 * sqrt(CT^2 + 4))
        v_tangential = CQ / (2 * lambda * sqrt(CT^2 + 4))
        """
        rho = getattr(self.config, "rho", 1.225)
        omega = self.omega_rotor
        V_tangential = omega * r

        # 避免除零
        V_tangential = torch.clamp(V_tangential, min=1e-6)
        r = torch.clamp(r, min=1e-6)

        # 计算推力系数和扭矩系数
        CT_local = Cl_batch * self.c / (np.pi * r)
        CQ_local = Cd_batch * self.c * r / (np.pi * r**2)

        # Glauert动量理论修正
        CT_total = torch.clamp(torch.abs(CT_local), min=1e-8)
        lambda_r = V_tangential / (omega * r + 1e-6)  # 局部入流比

        # 轴向诱导速度 (修正公式)
        v_axial = CT_total / (2 * torch.sqrt(CT_total**2 + 4 * lambda_r**2))

        # 切向诱导速度 (修正公式)
        v_tangential = CQ_local / (
            2 * lambda_r * torch.sqrt(CT_total**2 + 4 * lambda_r**2) + 1e-8
        )

        # 限制诱导速度在合理范围内
        v_axial = torch.clamp(v_axial, -10.0, 10.0)
        v_tangential = torch.clamp(v_tangential, -5.0, 5.0)

        return torch.stack([v_axial, v_tangential], dim=1)

    def _gpu_batch_compute_coefficients(
        self, batch_results: Dict[str, torch.Tensor]
    ) -> torch.Tensor:
        """
        GPU批处理计算气动力系数

        Args:
            batch_results: 包含诱导速度的批处理结果

        Returns:
            批处理气动力系数
        """
        batch_induced_velocities = batch_results["induced_velocities"]
        batch_size, n_blades, n_elements, _ = batch_induced_velocities.shape

        # 初始化系数数组
        batch_coefficients = torch.zeros(
            (batch_size, n_blades, n_elements, 3),  # [Cl, Cd, Cm]
            device=self.gpu_manager.device,
        )

        # 简化的系数计算（向量化）
        for blade_idx in range(n_blades):
            for elem_idx in range(n_elements):
                # 获取诱导速度
                v_induced = batch_induced_velocities[
                    :, blade_idx, elem_idx, :
                ]  # [batch_size, 2]

                # 计算有效攻角（简化）
                alpha_eff = torch.atan2(v_induced[:, 0], v_induced[:, 1] + 1e-6)

                # 计算系数
                Cl = 2 * np.pi * alpha_eff  # 薄翼理论
                Cd = 0.01 + 0.1 * alpha_eff**2  # 简化阻力
                Cm = -0.1 * Cl  # 简化力矩

                batch_coefficients[:, blade_idx, elem_idx, 0] = Cl
                batch_coefficients[:, blade_idx, elem_idx, 1] = Cd
                batch_coefficients[:, blade_idx, elem_idx, 2] = Cm

        return batch_coefficients

    def _solve_batch_cpu(self, theta_batch: np.ndarray) -> Dict[str, np.ndarray]:
        """CPU批处理求解（回退方法）"""
        results = []
        for theta in theta_batch:
            result = self.solve_single_azimuth(theta)
            results.append(result)

        # 合并结果
        return {
            "induced_velocities": np.array([r["induced_velocities"] for r in results]),
            "coefficients": np.array([r["coefficients"] for r in results]),
            "convergence": np.array([r["convergence"] for r in results]),
        }
    def _initialize_cycloidal_momentum_parameters(self):
        """
        初始化循环翼转子动量理论参数

        基于Wheatley & Miklosovic (2011)和Benedict et al. (2013)的研究
        """
        # 获取基本配置参数
        self.advance_ratio = getattr(self.config, 'advance_ratio', 0.0)
        self.forward_velocity = getattr(self.config, 'forward_velocity', 0.0)

        # 循环翼转子几何参数
        self.rotor_radius = self.R_rotor
        self.blade_span = getattr(self.config, 'blade_span', self.span)
        self.tip_speed = self.omega_rotor * self.rotor_radius

        # 动量理论修正参数
        self.momentum_theory_params = {
            'enable_vector_momentum': True,
            'azimuth_variation_factor': 0.5,  # 方位角变化强度
            'asymmetry_factor': 0.2,  # 前后不对称性
            'advance_ratio_correction_threshold': 0.3,
            'base_correction_factor': 0.1
        }

        print("  - 矢量动量理论: 启用")
        print(f"  - 推进比: {self.advance_ratio:.3f}")
        print(f"  - 尖速比: {self.tip_speed/max(self.forward_velocity, 1.0):.2f}")

    def compute_cycloidal_induced_velocity(self, blade_element, azimuth_angle):
        """
        循环翼转子矢量动量理论实现

        基于理论文档修正：T⃗ = ρA v⃗ᵢ(V⃗∞ + v⃗ᵢ)
        考虑方位角相关的诱导速度分布和矢量特性

        Args:
            blade_element: 叶素对象或参数字典
            azimuth_angle: 方位角 [rad]

        Returns:
            dict: 包含诱导速度分量的字典
        """
        # 获取推力矢量（修正：使用矢量推力而非标量）
        if hasattr(blade_element, 'get_thrust_vector'):
            thrust_vector = blade_element.get_thrust_vector(azimuth_angle)
        else:
            # 简化计算：基于方位角估算推力矢量
            thrust_magnitude = getattr(blade_element, 'thrust', 100.0)
            thrust_vector = np.array([
                thrust_magnitude * np.sin(azimuth_angle),  # x分量
                thrust_magnitude * np.cos(azimuth_angle),  # y分量
                thrust_magnitude * 0.1  # z分量（轴向）
            ])

        # 参考面积和密度
        disk_area = np.pi * self.rotor_radius**2
        rho = self.rho

        # 前飞速度（如果存在）
        v_infinity = getattr(self, 'forward_velocity', 0.0)

        # 矢量动量理论：T⃗ = ρA v⃗ᵢ(V⃗∞ + v⃗ᵢ)
        # 简化求解：v⃗ᵢ = T⃗/(2ρAV∞) 当V∞ >> vᵢ时
        if v_infinity > 1e-3:
            v_induced_x = thrust_vector[0] / (2 * rho * disk_area * v_infinity)
            v_induced_y = thrust_vector[1] / (2 * rho * disk_area * v_infinity)
        else:
            # 悬停状态：使用传统动量理论
            v_induced_x = np.sqrt(abs(thrust_vector[0]) / (2 * rho * disk_area)) * np.sign(thrust_vector[0])
            v_induced_y = np.sqrt(abs(thrust_vector[1]) / (2 * rho * disk_area)) * np.sign(thrust_vector[1])

        # 方位角相关的诱导速度分布修正
        azimuth_factor = self._compute_azimuth_factor(azimuth_angle, self.advance_ratio)

        # 应用方位角修正
        v_induced_x *= azimuth_factor
        v_induced_y *= azimuth_factor

        # 计算总诱导速度大小
        v_induced_total = np.sqrt(v_induced_x**2 + v_induced_y**2)

        return {
            'magnitude': v_induced_total,
            'x_component': v_induced_x,
            'y_component': v_induced_y,
            'vector': np.array([v_induced_x, v_induced_y]),
            'azimuth_factor': azimuth_factor,
            'thrust_vector': thrust_vector
        }

    def _compute_azimuth_factor(self, azimuth, advance_ratio):
        """
        计算方位角相关的诱导速度因子

        基于Wheatley实验数据的拟合公式
        循环翼转子在前飞时诱导速度呈现1P变化

        Args:
            azimuth: 方位角 [rad]
            advance_ratio: 推进比

        Returns:
            float: 方位角因子
        """
        params = self.momentum_theory_params

        if advance_ratio < 0.1:  # 悬停或低速
            return 1.0
        else:  # 前飞
            # 诱导速度在方位角0°-180°较大，180°-360°较小
            base_factor = 1.0 + params['azimuth_variation_factor'] * advance_ratio * np.cos(azimuth)

            # 考虑循环翼转子的非对称性
            asymmetry_factor = 1.0 + params['asymmetry_factor'] * advance_ratio * np.sin(azimuth)

            return base_factor * asymmetry_factor

    def _compute_advance_ratio_correction(self, advance_ratio):
        """
        推进比修正因子

        基于CFD验证的修正公式，考虑高推进比下的非线性效应

        Args:
            advance_ratio: 推进比

        Returns:
            float: 修正因子
        """
        params = self.momentum_theory_params
        threshold = params['advance_ratio_correction_threshold']
        base_factor = params['base_correction_factor']

        if advance_ratio < threshold:
            return 1.0 - base_factor * advance_ratio
        else:
            return 0.97 - 0.05 * advance_ratio

    def compute_cycloidal_blade_element_geometry(self, span_position, azimuth_angle):
        """
        循环翼转子叶素几何计算

        实现直叶片几何模型，考虑：
        1. 恒定弦长，无扭转
        2. 复杂的运动学（旋转、前飞、桨距变化）
        3. 有效攻角的完整计算

        Args:
            span_position: 展向位置 [m]
            azimuth_angle: 方位角 [rad]

        Returns:
            dict: 叶素几何和运动学信息
        """
        # 循环翼转子几何参数
        blade_chord = self.c  # 恒定弦长

        # 叶片在转子坐标系中的位置
        # 循环翼转子叶片为直叶片，垂直于转子轴
        x_blade = self.rotor_radius * np.cos(azimuth_angle)
        y_blade = self.rotor_radius * np.sin(azimuth_angle)
        z_blade = span_position  # 展向位置

        # 叶素的运动学参数
        kinematics = self._compute_blade_element_kinematics(span_position, azimuth_angle)

        # 叶素的有效攻角（考虑运动学和控制输入）
        effective_aoa = self._compute_effective_angle_of_attack_cycloidal(
            span_position, azimuth_angle, kinematics
        )

        return {
            'position': np.array([x_blade, y_blade, z_blade]),
            'chord': blade_chord,
            'span_position': span_position,
            'azimuth_angle': azimuth_angle,
            'kinematics': kinematics,
            'effective_aoa': effective_aoa,
            'local_velocity': kinematics['relative_velocity'],
            'local_mach': kinematics['relative_velocity'] / 343.0  # 假设声速
        }

    def _compute_blade_element_kinematics(self, span_position, azimuth_angle):
        """
        计算叶素运动学参数

        考虑旋转、前飞和桨距变化的速度合成

        Args:
            span_position: 展向位置 [m]
            azimuth_angle: 方位角 [rad]

        Returns:
            dict: 运动学参数
        """
        # 叶素的绝对速度分量

        # 1. 旋转速度分量
        v_rot_x = -self.omega_rotor * self.rotor_radius * np.sin(azimuth_angle)
        v_rot_y = self.omega_rotor * self.rotor_radius * np.cos(azimuth_angle)
        v_rot_z = 0.0

        # 2. 前飞速度分量
        v_fwd_x = self.forward_velocity
        v_fwd_y = 0.0
        v_fwd_z = 0.0

        # 3. 叶片摆动速度（如果有摆动控制）
        blade_pitch_rate = self._get_blade_pitch_rate(azimuth_angle)
        v_pitch_x = 0.0
        v_pitch_y = 0.0
        v_pitch_z = blade_pitch_rate * span_position

        # 合成相对速度
        v_rel_x = v_rot_x + v_fwd_x + v_pitch_x
        v_rel_y = v_rot_y + v_fwd_y + v_pitch_y
        v_rel_z = v_rot_z + v_fwd_z + v_pitch_z

        relative_velocity_magnitude = np.sqrt(v_rel_x**2 + v_rel_y**2 + v_rel_z**2)

        # 流入角计算
        inflow_angle = np.arctan2(v_rel_z, np.sqrt(v_rel_x**2 + v_rel_y**2))

        return {
            'relative_velocity': relative_velocity_magnitude,
            'velocity_components': np.array([v_rel_x, v_rel_y, v_rel_z]),
            'inflow_angle': inflow_angle,
            'local_tip_speed_ratio': relative_velocity_magnitude / (self.omega_rotor * self.rotor_radius)
        }

    def _compute_effective_angle_of_attack_cycloidal(self, span_position, azimuth_angle, kinematics):
        """
        计算循环翼转子的有效攻角

        包含几何攻角、诱导攻角和流入角的完整计算

        Args:
            span_position: 展向位置 [m]
            azimuth_angle: 方位角 [rad]
            kinematics: 运动学参数字典

        Returns:
            float: 有效攻角 [rad]
        """
        # 1. 几何攻角（由控制系统确定）
        geometric_aoa = self._get_blade_pitch_angle(azimuth_angle)

        # 2. 诱导攻角（由诱导速度引起）
        # 创建简化的叶素对象用于诱导速度计算
        blade_element = type('BladeElement', (), {
            'thrust_vertical': 100.0,  # 简化值，实际应从载荷计算得出
            'thrust_horizontal': 50.0
        })()

        induced_velocity_result = self.compute_cycloidal_induced_velocity(
            blade_element, azimuth_angle
        )

        induced_aoa = np.arctan2(
            induced_velocity_result['y_component'],
            kinematics['velocity_components'][0] + 1e-6  # 避免除零
        )

        # 3. 有效攻角 = 几何攻角 - 流入角 - 诱导攻角
        effective_aoa = geometric_aoa - kinematics['inflow_angle'] - induced_aoa

        return effective_aoa

    def _get_blade_pitch_angle(self, azimuth_angle):
        """
        获取叶片桨距角（控制输入）

        循环翼转子的典型控制律：正弦桨距变化

        Args:
            azimuth_angle: 方位角 [rad]

        Returns:
            float: 桨距角 [rad]
        """
        # 从配置获取控制参数
        collective_pitch = getattr(self.config, 'collective_pitch', 0.0)
        cyclic_amplitude = getattr(self.config, 'cyclic_amplitude', 10.0)  # 度
        cyclic_phase = getattr(self.config, 'cyclic_phase', 0.0)

        # 转换为弧度
        collective_pitch_rad = np.radians(collective_pitch)
        cyclic_amplitude_rad = np.radians(cyclic_amplitude)
        cyclic_phase_rad = np.radians(cyclic_phase)

        # 正弦桨距变化
        pitch_angle = collective_pitch_rad + cyclic_amplitude_rad * np.sin(azimuth_angle + cyclic_phase_rad)

        return pitch_angle

    def _get_blade_pitch_rate(self, azimuth_angle):
        """
        计算叶片桨距变化率

        Args:
            azimuth_angle: 方位角 [rad]

        Returns:
            float: 桨距变化率 [rad/s]
        """
        cyclic_amplitude = getattr(self.config, 'cyclic_amplitude', 10.0)  # 度
        cyclic_phase = getattr(self.config, 'cyclic_phase', 0.0)

        cyclic_amplitude_rad = np.radians(cyclic_amplitude)
        cyclic_phase_rad = np.radians(cyclic_phase)

        # 桨距变化率 = d(pitch)/dt = d(pitch)/dψ * dψ/dt = d(pitch)/dψ * Ω
        pitch_rate = cyclic_amplitude_rad * np.cos(azimuth_angle + cyclic_phase_rad) * self.omega_rotor

        return pitch_rate
