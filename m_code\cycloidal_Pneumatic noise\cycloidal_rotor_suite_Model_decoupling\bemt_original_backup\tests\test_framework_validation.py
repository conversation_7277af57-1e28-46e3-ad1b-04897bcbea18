#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试框架验证
===========

验证测试框架本身的正确性和完整性。

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import time
import traceback

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_import_capabilities():
    """测试导入能力"""
    print("🧪 测试框架导入验证")
    print("-" * 40)
    
    import_results = {}
    
    # 测试数据配置
    try:
        from test_data_config import TEST_DATA
        rotor_configs = len(TEST_DATA.rotor_configs)
        flight_configs = len(TEST_DATA.flight_configs)
        airfoil_configs = len(TEST_DATA.airfoil_configs)
        test_cases = len(TEST_DATA.test_cases)
        
        import_results['test_data_config'] = {
            'success': True,
            'details': f"旋翼:{rotor_configs}, 飞行:{flight_configs}, 翼型:{airfoil_configs}, 用例:{test_cases}"
        }
        print(f"✅ 测试数据配置: {import_results['test_data_config']['details']}")
        
    except Exception as e:
        import_results['test_data_config'] = {'success': False, 'error': str(e)}
        print(f"❌ 测试数据配置: {e}")
    
    # 核心模块
    try:
        from simple_bemt import SimpleBEMT
        solver = SimpleBEMT(radius=1.0, num_blades=4)
        import_results['simple_bemt'] = {'success': True, 'details': 'SimpleBEMT求解器'}
        print(f"✅ SimpleBEMT求解器: 可用")
        
    except Exception as e:
        import_results['simple_bemt'] = {'success': False, 'error': str(e)}
        print(f"❌ SimpleBEMT求解器: {e}")
    
    # GPU加速模块
    try:
        from utils.gpu_acceleration import get_gpu_manager
        gpu_manager = get_gpu_manager()
        gpu_available = gpu_manager.use_gpu
        backend = gpu_manager.gpu_backend or 'CPU'
        
        import_results['gpu_acceleration'] = {
            'success': True, 
            'details': f"GPU: {backend}, 可用: {gpu_available}"
        }
        print(f"✅ GPU加速模块: {import_results['gpu_acceleration']['details']}")
        
    except Exception as e:
        import_results['gpu_acceleration'] = {'success': False, 'error': str(e)}
        print(f"❌ GPU加速模块: {e}")
    
    # 自适应网格模块
    try:
        from utils.adaptive_mesh import AdaptiveMeshRefinement
        amr = AdaptiveMeshRefinement()
        import_results['adaptive_mesh'] = {'success': True, 'details': 'AMR可用'}
        print(f"✅ 自适应网格模块: 可用")
        
    except Exception as e:
        import_results['adaptive_mesh'] = {'success': False, 'error': str(e)}
        print(f"❌ 自适应网格模块: {e}")
    
    # 高级收敛模块
    try:
        from utils.advanced_convergence import EnhancedConvergenceOptimizer
        optimizer = EnhancedConvergenceOptimizer()
        import_results['advanced_convergence'] = {'success': True, 'details': '收敛优化器可用'}
        print(f"✅ 高级收敛模块: 可用")
        
    except Exception as e:
        import_results['advanced_convergence'] = {'success': False, 'error': str(e)}
        print(f"❌ 高级收敛模块: {e}")
    
    # 动态失速模块
    try:
        from physics.complete_dynamic_stall import CompleteLeishmanBeddoesModel
        lb_model = CompleteLeishmanBeddoesModel()
        state_info = lb_model.get_state_info()
        n_states = len(state_info['states'])
        
        import_results['dynamic_stall'] = {
            'success': True, 
            'details': f'L-B模型, {n_states}状态'
        }
        print(f"✅ 动态失速模块: {import_results['dynamic_stall']['details']}")
        
    except Exception as e:
        import_results['dynamic_stall'] = {'success': False, 'error': str(e)}
        print(f"❌ 动态失速模块: {e}")
    
    # 物理修正模块
    try:
        from physics.corrections import ComprehensivePhysicalCorrections
        corrections = ComprehensivePhysicalCorrections()
        import_results['physics_corrections'] = {'success': True, 'details': '物理修正可用'}
        print(f"✅ 物理修正模块: 可用")
        
    except Exception as e:
        import_results['physics_corrections'] = {'success': False, 'error': str(e)}
        print(f"❌ 物理修正模块: {e}")
    
    # 统计结果
    total_modules = len(import_results)
    successful_imports = sum(1 for r in import_results.values() if r['success'])
    
    print(f"\n📊 导入测试结果: {successful_imports}/{total_modules} 成功 ({successful_imports/total_modules*100:.1f}%)")
    
    return import_results


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 基本功能验证")
    print("-" * 40)
    
    functionality_results = {}
    
    # 测试SimpleBEMT基本求解
    try:
        from simple_bemt import SimpleBEMT
        from test_data_config import TEST_DATA
        
        # 获取真实参数
        rotor_config = TEST_DATA.get_rotor_config('UH-60')
        flight_config = TEST_DATA.get_flight_config('hover')
        
        solver = SimpleBEMT(
            radius=rotor_config.radius,
            num_blades=rotor_config.num_blades,
            hub_radius=rotor_config.hub_radius
        )
        
        # 执行求解
        start_time = time.time()
        result = solver.solve(
            rpm=flight_config.rpm,
            forward_speed=flight_config.forward_speed,
            verbose=False
        )
        solve_time = time.time() - start_time
        
        # 验证结果
        converged = result.get('converged', False)
        thrust = result.get('thrust', 0)
        power = result.get('power', 0)
        iterations = result.get('iterations', 0)
        
        functionality_results['basic_solve'] = {
            'success': converged and thrust > 0 and power > 0,
            'details': f"推力:{thrust:.1f}N, 功率:{power:.1f}W, 迭代:{iterations}, 时间:{solve_time*1000:.1f}ms"
        }
        
        if functionality_results['basic_solve']['success']:
            print(f"✅ 基本求解: {functionality_results['basic_solve']['details']}")
        else:
            print(f"❌ 基本求解: 收敛={converged}, 推力={thrust:.1f}, 功率={power:.1f}")
        
    except Exception as e:
        functionality_results['basic_solve'] = {'success': False, 'error': str(e)}
        print(f"❌ 基本求解: {e}")
    
    # 测试时间步进求解
    try:
        result = solver.solve_unsteady(
            t_span=(0.0, 0.01),
            dt=0.002,
            rpm=flight_config.rpm,
            forward_speed=flight_config.forward_speed,
            integration_method='rk4'
        )
        
        total_steps = result.get('total_steps', 0)
        final_thrust = result.get('final_state', {}).get('thrust', 0)
        
        functionality_results['unsteady_solve'] = {
            'success': total_steps > 0 and final_thrust > 0,
            'details': f"步数:{total_steps}, 最终推力:{final_thrust:.1f}N"
        }
        
        if functionality_results['unsteady_solve']['success']:
            print(f"✅ 时间步进求解: {functionality_results['unsteady_solve']['details']}")
        else:
            print(f"❌ 时间步进求解: 步数={total_steps}, 推力={final_thrust:.1f}")
        
    except Exception as e:
        functionality_results['unsteady_solve'] = {'success': False, 'error': str(e)}
        print(f"❌ 时间步进求解: {e}")
    
    # 测试翼型插值
    try:
        from aerodynamics.airfoil_interpolator import EnhancedAirfoilInterpolator
        
        interpolator = EnhancedAirfoilInterpolator()
        airfoils = interpolator.get_available_airfoils()
        
        # 测试插值
        test_alpha = 5.0
        cl, cd = interpolator.interpolate('naca0012', test_alpha)
        
        functionality_results['airfoil_interpolation'] = {
            'success': len(airfoils) > 5 and not (np.isnan(cl) or np.isnan(cd)),
            'details': f"翼型数:{len(airfoils)}, α={test_alpha}°时 Cl={cl:.3f}, Cd={cd:.4f}"
        }
        
        if functionality_results['airfoil_interpolation']['success']:
            print(f"✅ 翼型插值: {functionality_results['airfoil_interpolation']['details']}")
        else:
            print(f"❌ 翼型插值: 翼型数={len(airfoils)}, Cl={cl}, Cd={cd}")
        
    except Exception as e:
        functionality_results['airfoil_interpolation'] = {'success': False, 'error': str(e)}
        print(f"❌ 翼型插值: {e}")
    
    # 统计结果
    total_functions = len(functionality_results)
    successful_functions = sum(1 for r in functionality_results.values() if r['success'])
    
    print(f"\n📊 功能测试结果: {successful_functions}/{total_functions} 成功 ({successful_functions/total_functions*100:.1f}%)")
    
    return functionality_results


def run_framework_validation():
    """运行测试框架验证"""
    
    print("🚀 BEMT测试框架验证")
    print("=" * 60)
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # 导入能力测试
    import_results = test_import_capabilities()
    
    # 基本功能测试
    functionality_results = test_basic_functionality()
    
    total_time = time.time() - start_time
    
    # 总结报告
    print(f"\n📊 测试框架验证总结")
    print("=" * 60)
    
    # 统计导入结果
    total_imports = len(import_results)
    successful_imports = sum(1 for r in import_results.values() if r['success'])
    import_rate = successful_imports / total_imports * 100
    
    # 统计功能结果
    total_functions = len(functionality_results)
    successful_functions = sum(1 for r in functionality_results.values() if r['success'])
    function_rate = successful_functions / total_functions * 100
    
    print(f"🎯 总体结果:")
    print(f"   模块导入: {successful_imports}/{total_imports} 成功 ({import_rate:.1f}%)")
    print(f"   基本功能: {successful_functions}/{total_functions} 成功 ({function_rate:.1f}%)")
    print(f"   执行时间: {total_time:.2f}s")
    print()
    
    # 详细失败信息
    failed_imports = [name for name, result in import_results.items() if not result['success']]
    failed_functions = [name for name, result in functionality_results.items() if not result['success']]
    
    if failed_imports:
        print("❌ 导入失败模块:")
        for module in failed_imports:
            error = import_results[module].get('error', '未知错误')
            print(f"   - {module}: {error}")
        print()
    
    if failed_functions:
        print("❌ 功能失败项目:")
        for function in failed_functions:
            error = functionality_results[function].get('error', '未知错误')
            print(f"   - {function}: {error}")
        print()
    
    # 最终判断
    overall_success = (import_rate >= 80 and function_rate >= 80)
    
    if overall_success:
        print("🎉 测试框架验证通过！")
        print("   可以进行完整的深度优化功能测试")
    else:
        print("⚠️  测试框架存在问题，需要修复后再进行完整测试")
        if import_rate < 80:
            print(f"   模块导入成功率不足: {import_rate:.1f}% < 80%")
        if function_rate < 80:
            print(f"   基本功能成功率不足: {function_rate:.1f}% < 80%")
    
    print()
    print("=" * 60)
    
    return {
        'overall_success': overall_success,
        'import_rate': import_rate,
        'function_rate': function_rate,
        'execution_time': total_time,
        'import_results': import_results,
        'functionality_results': functionality_results
    }


if __name__ == "__main__":
    # 需要numpy用于数值检查
    import numpy as np
    
    results = run_framework_validation()
    
    if results['overall_success']:
        print("🎯 测试框架验证成功，退出码: 0")
        sys.exit(0)
    else:
        print("⚠️  测试框架验证失败，退出码: 1")
        sys.exit(1)
