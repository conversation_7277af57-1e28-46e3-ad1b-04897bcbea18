#!/usr/bin/env python3
"""
完整工作流程测试
===============

测试BEMT求解器的完整工作流程，包括：
- 配置管理
- 求解器创建
- 时域仿真
- 性能分析
- 结果验证

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
import numpy as np

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config import ConfigManager
from core.solver_factory import SolverFactory


def test_complete_workflow():
    """测试完整工作流程"""
    print("=" * 70)
    print("🚁 BEMT中保真度模块 - 完整工作流程测试")
    print("=" * 70)
    
    try:
        # 1. 配置管理测试
        print("\n📋 1. 配置管理测试")
        print("-" * 50)
        
        # 创建标准配置
        config_dict = {
            # 基本参数
            'R_rotor': 0.4,          # 转子半径 [m]
            'B': 4,                  # 桨叶数
            'c': 0.07,               # 弦长 [m]
            'omega_rotor': 120.0,    # 角速度 [rad/s]
            'rho': 1.225,            # 空气密度 [kg/m³]
            
            # BEMT参数
            'bemt_n_elements': 15,
            'bemt_max_iterations': 40,
            'bemt_tolerance': 1e-4,
            'relaxation_factor': 0.6,
            
            # 物理模型
            'enable_tip_loss': True,
            'enable_hub_loss': True,
            'enable_dynamic_stall': False,
            
            # 转子类型
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 10.0,
            'pitch_phase_offset': 0.0,
            'pitch_bias_angle': 2.0,
            
            # 仿真参数
            'dt': 0.005,
            'application_type': 'general'
        }
        
        config = ConfigManager(config_dict)
        print(f"   ✅ 配置创建成功")
        print(f"   - 参数数量: {len(config.to_dict())}")
        print(f"   - 转子半径: {config.get('R_rotor')}m")
        print(f"   - 桨叶数: {config.get('B')}")
        print(f"   - 叶素数: {config.get('bemt_n_elements')}")
        
        # 2. 求解器创建测试
        print("\n🔧 2. 求解器创建测试")
        print("-" * 50)
        
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        print(f"   ✅ 求解器创建成功")
        print(f"   - 类型: {solver.solver_type}")
        print(f"   - 保真度: {solver.fidelity_level}")
        print(f"   - 转子参数: R={solver.R_rotor}m, B={solver.B}")
        print(f"   - 离散化: {solver.n_elements}个叶素")
        
        # 验证配置
        if not solver.validate_configuration():
            raise ValueError("求解器配置验证失败")
        print(f"   ✅ 配置验证通过")
        
        # 3. 单步求解测试
        print("\n⚡ 3. 单步求解测试")
        print("-" * 50)
        
        t = 0.0
        dt = 0.005
        
        start_time = time.time()
        result = solver.solve_step(t, dt)
        solve_time = time.time() - start_time
        
        print(f"   ✅ 单步求解成功")
        print(f"   - 求解时间: {solve_time:.4f}s")
        print(f"   - 收敛状态: {'成功' if result['convergence_info']['converged'] else '失败'}")
        print(f"   - 迭代次数: {result['convergence_info']['iterations']}")
        print(f"   - 残差: {result['convergence_info']['residual']:.2e}")
        
        # 显示性能结果
        performance = result['performance']
        print(f"   - 推力: {performance['thrust']:.3f} N")
        print(f"   - 功率: {performance['power']:.3f} W")
        print(f"   - 转矩: {performance['torque']:.4f} N·m")
        print(f"   - 品质因数: {performance['figure_of_merit']:.3f}")
        
        # 4. 时域仿真测试
        print("\n⏱️  4. 时域仿真测试")
        print("-" * 50)
        
        # 仿真参数
        t_end = 0.1  # 仿真时间 [s]
        dt = 0.005   # 时间步长 [s]
        n_steps = int(t_end / dt)
        
        print(f"   仿真设置: t_end={t_end}s, dt={dt}s, 步数={n_steps}")
        
        # 存储结果
        time_history = []
        thrust_history = []
        power_history = []
        torque_history = []
        convergence_history = []
        solve_times = []
        
        # 时间循环
        simulation_start = time.time()
        
        for i in range(n_steps):
            t = i * dt
            step_start = time.time()
            
            try:
                result = solver.solve_step(t, dt)
                step_time = time.time() - step_start
                
                # 记录结果
                time_history.append(t)
                thrust_history.append(result['performance']['thrust'])
                power_history.append(result['performance']['power'])
                torque_history.append(result['performance']['torque'])
                convergence_history.append(result['convergence_info']['converged'])
                solve_times.append(step_time)
                
                # 进度显示
                if (i + 1) % 5 == 0:
                    progress = (i + 1) / n_steps * 100
                    print(f"   进度: {progress:.0f}% (t={t:.3f}s)")
                
            except Exception as e:
                print(f"   ⚠️  步骤 {i} 失败: {e}")
                break
        
        simulation_time = time.time() - simulation_start
        
        # 仿真统计
        if len(time_history) > 0:
            convergence_rate = sum(convergence_history) / len(convergence_history) * 100
            
            print(f"   ✅ 时域仿真完成")
            print(f"   - 总仿真时间: {simulation_time:.3f}s")
            print(f"   - 成功步数: {len(time_history)}/{n_steps}")
            print(f"   - 收敛率: {convergence_rate:.1f}%")
            print(f"   - 平均求解时间: {np.mean(solve_times):.4f}s")
            print(f"   - 实时因子: {t_end/simulation_time:.1f}x")
        
        # 5. 性能分析测试
        print("\n📊 5. 性能分析测试")
        print("-" * 50)
        
        if len(thrust_history) > 5:
            # 基本统计
            thrust_mean = np.mean(thrust_history)
            thrust_std = np.std(thrust_history)
            power_mean = np.mean(power_history)
            power_std = np.std(power_history)
            torque_mean = np.mean(torque_history)
            
            print(f"   ✅ 性能统计分析")
            print(f"   - 推力: {thrust_mean:.3f} ± {thrust_std:.3f} N")
            print(f"   - 功率: {power_mean:.3f} ± {power_std:.3f} W")
            print(f"   - 转矩: {torque_mean:.4f} N·m")
            
            # 无量纲系数
            rho = config.get('rho')
            R = config.get('R_rotor')
            omega = config.get('omega_rotor')
            A = np.pi * R**2
            tip_speed = omega * R
            
            CT = thrust_mean / (rho * A * tip_speed**2)
            CP = power_mean / (rho * A * tip_speed**3)
            
            print(f"   - 推力系数 CT: {CT:.5f}")
            print(f"   - 功率系数 CP: {CP:.5f}")
            
            # 品质因数
            if power_mean > 1e-6:
                ideal_power = thrust_mean**1.5 / np.sqrt(2 * rho * A)
                FM = ideal_power / power_mean
                print(f"   - 品质因数 FM: {FM:.3f}")
            
            # 振动分析
            thrust_vibration = thrust_std / thrust_mean if thrust_mean > 0 else 0
            power_vibration = power_std / power_mean if power_mean > 0 else 0
            
            print(f"   - 推力振动水平: {thrust_vibration:.1%}")
            print(f"   - 功率振动水平: {power_vibration:.1%}")
        
        # 6. 求解器性能统计
        print("\n🔍 6. 求解器性能统计")
        print("-" * 50)
        
        stats = solver.get_performance_stats()
        print(f"   ✅ 求解器统计")
        print(f"   - 总迭代次数: {stats.get('total_iterations', 0)}")
        print(f"   - 收敛失败次数: {stats.get('convergence_failures', 0)}")
        print(f"   - 平均求解时间: {stats.get('average_solve_time', 0):.4f}s")
        print(f"   - 最后残差: {stats.get('last_residual', 0):.2e}")
        
        # 7. 结果验证
        print("\n✅ 7. 结果验证")
        print("-" * 50)
        
        validation_passed = True
        
        # 验证推力合理性
        if len(thrust_history) > 0:
            thrust_range = [0.0, 10.0]  # 合理推力范围 [N]
            if not (thrust_range[0] <= thrust_mean <= thrust_range[1]):
                print(f"   ⚠️  推力超出合理范围: {thrust_mean:.3f} N")
                validation_passed = False
            else:
                print(f"   ✅ 推力在合理范围内: {thrust_mean:.3f} N")
        
        # 验证功率合理性
        if len(power_history) > 0:
            power_range = [0.0, 100.0]  # 合理功率范围 [W]
            if not (power_range[0] <= power_mean <= power_range[1]):
                print(f"   ⚠️  功率超出合理范围: {power_mean:.3f} W")
                validation_passed = False
            else:
                print(f"   ✅ 功率在合理范围内: {power_mean:.3f} W")
        
        # 验证收敛性
        if len(convergence_history) > 0:
            if convergence_rate < 80.0:
                print(f"   ⚠️  收敛率偏低: {convergence_rate:.1f}%")
                validation_passed = False
            else:
                print(f"   ✅ 收敛率良好: {convergence_rate:.1f}%")
        
        # 验证求解效率
        if len(solve_times) > 0:
            avg_solve_time = np.mean(solve_times)
            if avg_solve_time > 0.1:  # 100ms阈值
                print(f"   ⚠️  求解时间偏长: {avg_solve_time:.4f}s")
                validation_passed = False
            else:
                print(f"   ✅ 求解效率良好: {avg_solve_time:.4f}s")
        
        # 总结
        print("\n" + "=" * 70)
        if validation_passed:
            print("🎉 完整工作流程测试通过！")
            print("   BEMT中保真度模块所有核心功能正常工作。")
        else:
            print("⚠️  完整工作流程测试部分失败！")
            print("   请检查上述警告项目。")
        print("=" * 70)
        
        return validation_passed
        
    except Exception as e:
        print(f"\n❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_configurations():
    """测试不同配置的求解器"""
    print("\n" + "=" * 70)
    print("🔧 不同配置测试")
    print("=" * 70)
    
    test_configs = [
        {
            'name': '小型转子',
            'R_rotor': 0.2, 'B': 3, 'c': 0.04,
            'omega_rotor': 200.0, 'pitch_amplitude': 6.0,
            'bemt_n_elements': 8
        },
        {
            'name': '中型转子',
            'R_rotor': 0.4, 'B': 4, 'c': 0.08,
            'omega_rotor': 120.0, 'pitch_amplitude': 10.0,
            'bemt_n_elements': 12
        },
        {
            'name': '大型转子',
            'R_rotor': 0.6, 'B': 6, 'c': 0.12,
            'omega_rotor': 80.0, 'pitch_amplitude': 15.0,
            'bemt_n_elements': 16
        }
    ]
    
    print("\n配置对比测试:")
    print("配置      推力[N]  功率[W]  FM     CT      CP     收敛")
    print("-" * 65)
    
    success_count = 0
    
    for config_data in test_configs:
        try:
            # 创建基础配置
            base_config = {
                'rho': 1.225,
                'bemt_max_iterations': 30,
                'bemt_tolerance': 1e-3,
                'relaxation_factor': 0.7,
                'enable_tip_loss': True,
                'enable_hub_loss': False,
                'enable_dynamic_stall': False,
                'rotor_type': 'cycloidal',
                'pitch_phase_offset': 0.0,
                'pitch_bias_angle': 0.0,
                'dt': 0.01,
                'application_type': 'general'
            }
            base_config.update(config_data)
            
            config = ConfigManager(base_config)
            factory = SolverFactory()
            solver = factory.create_solver('bemt_medium', config.to_dict())
            
            # 求解
            result = solver.solve_step(0.0, 0.01)
            perf = result['performance']
            conv = result['convergence_info']['converged']
            
            # 显示结果
            print(f"{config_data['name']:8s}  "
                  f"{perf['thrust']:6.2f}  "
                  f"{perf['power']:6.1f}  "
                  f"{perf['figure_of_merit']:5.3f}  "
                  f"{perf['CT']:6.4f}  "
                  f"{perf['CP']:6.4f}  "
                  f"{'✅' if conv else '❌'}")
            
            if conv:
                success_count += 1
                
        except Exception as e:
            print(f"{config_data['name']:8s}  计算失败: {str(e)[:30]}...")
    
    print(f"\n✅ 配置测试完成: {success_count}/{len(test_configs)} 成功")
    return success_count == len(test_configs)


def main():
    """主函数"""
    print("开始BEMT中保真度模块完整工作流程测试...")
    
    # 运行测试
    test_results = []
    
    # 完整工作流程测试
    test_results.append(test_complete_workflow())
    
    # 不同配置测试
    test_results.append(test_different_configurations())
    
    # 总结
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n" + "=" * 70)
    print("🏁 最终测试总结")
    print("=" * 70)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("   BEMT中保真度模块完整工作流程验证成功！")
        print("   系统已准备好用于实际应用。")
        return 0
    else:
        print("⚠️  部分测试失败！")
        print("   请检查失败的测试项目并进行修复。")
        return 1


if __name__ == "__main__":
    sys.exit(main())