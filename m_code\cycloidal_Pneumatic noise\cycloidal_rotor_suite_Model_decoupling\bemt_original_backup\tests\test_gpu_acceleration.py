#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速功能测试
==============

验证GPU加速功能的正确性和性能提升，包括CUDA/OpenCL性能对比。

测试内容:
1. GPU管理器初始化和设备检测
2. CUDA加速计算正确性验证
3. OpenCL加速计算正确性验证
4. GPU vs CPU性能对比
5. 大规模计算加速比测试
6. 内存使用效率测试

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import time
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import unittest

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_data_config import TEST_DATA
from utils.gpu_acceleration import get_gpu_manager, GPUAccelerationManager


class TestGPUAcceleration(unittest.TestCase):
    """GPU加速功能测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_data = TEST_DATA
        self.tolerance = 1e-6  # 数值精度容差
        self.performance_threshold = 2.0  # 最小加速比要求
        
        # 获取真实测试参数
        self.rotor_config = self.test_data.get_rotor_config('UH-60')
        self.flight_config = self.test_data.get_flight_config('hover')
        
        # 设置测试站位
        self.n_stations = 50
        self.r_stations = np.linspace(
            self.rotor_config.hub_radius, 
            self.rotor_config.radius, 
            self.n_stations
        )
        
        # 初始化GPU管理器
        self.gpu_manager = get_gpu_manager()
        
        print(f"\n🧪 GPU加速功能测试初始化")
        print(f"   测试旋翼: UH-60 (R={self.rotor_config.radius:.2f}m)")
        print(f"   测试条件: 悬停 (RPM={self.flight_config.rpm})")
        print(f"   站位数量: {self.n_stations}")
        print(f"   GPU后端: {self.gpu_manager.gpu_backend or 'CPU'}")
    
    def test_gpu_manager_initialization(self):
        """测试GPU管理器初始化"""
        print("\n📋 测试1: GPU管理器初始化")
        
        # 验证GPU管理器属性
        self.assertIsInstance(self.gpu_manager, GPUAccelerationManager)
        self.assertIsInstance(self.gpu_manager.use_gpu, bool)
        self.assertIn(self.gpu_manager.gpu_backend, [None, 'cuda', 'opencl'])
        
        # 获取设备信息
        device_info = self.gpu_manager.device_info
        
        if self.gpu_manager.use_gpu:
            print(f"   ✅ GPU可用: {self.gpu_manager.gpu_backend}")
            if self.gpu_manager.gpu_backend == 'cuda':
                print(f"   设备名称: {device_info.get('name', 'Unknown')}")
                print(f"   计算能力: {device_info.get('compute_capability', 'Unknown')}")
                print(f"   显存: {device_info.get('memory_free', 0):.1f}/{device_info.get('memory_total', 0):.1f} GB")
        else:
            print(f"   ⚠️  GPU不可用，使用CPU计算")
        
        # 验证上下文管理器
        with self.gpu_manager.gpu_context():
            pass  # 应该不抛出异常
        
        print("   ✅ GPU管理器初始化测试通过")
    
    def test_vectorized_computation_correctness(self):
        """测试向量化计算正确性"""
        print("\n📋 测试2: 向量化计算正确性")
        
        # 准备真实测试数据
        lambda_i = np.full(self.n_stations, 0.05)  # 典型诱导速度比
        mu = self.flight_config.forward_speed / (self.flight_config.omega * self.rotor_config.radius)
        
        # GPU计算
        start_time = time.time()
        gpu_result = self.gpu_manager.vectorized_bemt_computation(
            self.r_stations, lambda_i, mu, 
            self.flight_config.omega, self.flight_config.density
        )
        gpu_time = time.time() - start_time
        
        # 验证结果完整性
        required_keys = ['phi', 'alpha', 'cl', 'cd', 'dT', 'dQ']
        for key in required_keys:
            self.assertIn(key, gpu_result)
            self.assertEqual(len(gpu_result[key]), self.n_stations)
            self.assertTrue(np.all(np.isfinite(gpu_result[key])))
        
        # 验证物理合理性
        phi = gpu_result['phi']
        alpha = gpu_result['alpha']
        cl = gpu_result['cl']
        cd = gpu_result['cd']
        dT = gpu_result['dT']
        dQ = gpu_result['dQ']
        
        # 流入角应该为正值且合理
        self.assertTrue(np.all(phi > 0))
        self.assertTrue(np.all(phi < np.pi/4))  # 小于45度
        
        # 攻角应该在合理范围内
        self.assertTrue(np.all(np.abs(alpha) < np.pi/3))  # 小于60度
        
        # 升力系数应该在合理范围内
        self.assertTrue(np.all(cl > -2.0))
        self.assertTrue(np.all(cl < 3.0))
        
        # 阻力系数应该为正值
        self.assertTrue(np.all(cd > 0))
        self.assertTrue(np.all(cd < 1.0))
        
        # 推力和扭矩应该为正值（悬停条件）
        self.assertTrue(np.all(dT > 0))
        self.assertTrue(np.all(dQ > 0))
        
        print(f"   流入角范围: {np.rad2deg(np.min(phi)):.1f}° - {np.rad2deg(np.max(phi)):.1f}°")
        print(f"   攻角范围: {np.rad2deg(np.min(alpha)):.1f}° - {np.rad2deg(np.max(alpha)):.1f}°")
        print(f"   升力系数范围: {np.min(cl):.3f} - {np.max(cl):.3f}")
        print(f"   阻力系数范围: {np.min(cd):.4f} - {np.max(cd):.4f}")
        print(f"   计算时间: {gpu_time*1000:.2f}ms")
        print("   ✅ 向量化计算正确性测试通过")
    
    def test_gpu_cpu_consistency(self):
        """测试GPU和CPU计算一致性"""
        print("\n📋 测试3: GPU和CPU计算一致性")
        
        if not self.gpu_manager.use_gpu:
            print("   ⚠️  GPU不可用，跳过一致性测试")
            return
        
        # 准备测试数据
        lambda_i = np.linspace(0.02, 0.08, self.n_stations)
        mu = 0.1  # 前飞比
        
        # 强制使用CPU计算
        original_use_gpu = self.gpu_manager.use_gpu
        self.gpu_manager.use_gpu = False
        
        cpu_result = self.gpu_manager.vectorized_bemt_computation(
            self.r_stations, lambda_i, mu,
            self.flight_config.omega, self.flight_config.density
        )
        
        # 恢复GPU设置
        self.gpu_manager.use_gpu = original_use_gpu
        
        # GPU计算
        gpu_result = self.gpu_manager.vectorized_bemt_computation(
            self.r_stations, lambda_i, mu,
            self.flight_config.omega, self.flight_config.density
        )
        
        # 比较结果
        max_errors = {}
        for key in ['phi', 'alpha', 'cl', 'cd', 'dT', 'dQ']:
            error = np.abs(gpu_result[key] - cpu_result[key])
            max_error = np.max(error)
            rel_error = max_error / (np.max(np.abs(cpu_result[key])) + 1e-10)
            max_errors[key] = rel_error
            
            # 验证误差在容差范围内
            self.assertLess(rel_error, self.tolerance, 
                          f"{key}的GPU-CPU相对误差({rel_error:.2e})超过容差({self.tolerance:.2e})")
        
        print("   GPU-CPU相对误差:")
        for key, error in max_errors.items():
            print(f"     {key}: {error:.2e}")
        
        print("   ✅ GPU和CPU计算一致性测试通过")
    
    def test_performance_benchmark(self):
        """测试性能基准"""
        print("\n📋 测试4: 性能基准测试")
        
        # 不同规模的性能测试
        test_sizes = [20, 50, 100, 200]
        performance_results = {}
        
        for n_stations in test_sizes:
            r_stations = np.linspace(
                self.rotor_config.hub_radius,
                self.rotor_config.radius,
                n_stations
            )
            lambda_i = np.full(n_stations, 0.05)
            mu = 0.1
            
            # CPU性能测试
            if self.gpu_manager.use_gpu:
                original_use_gpu = self.gpu_manager.use_gpu
                self.gpu_manager.use_gpu = False
            
            cpu_times = []
            for _ in range(10):  # 多次测试取平均
                start_time = time.time()
                self.gpu_manager.vectorized_bemt_computation(
                    r_stations, lambda_i, mu,
                    self.flight_config.omega, self.flight_config.density
                )
                cpu_times.append(time.time() - start_time)
            
            avg_cpu_time = np.mean(cpu_times)
            
            # GPU性能测试
            if self.gpu_manager.use_gpu:
                self.gpu_manager.use_gpu = original_use_gpu
                
                gpu_times = []
                for _ in range(10):
                    start_time = time.time()
                    self.gpu_manager.vectorized_bemt_computation(
                        r_stations, lambda_i, mu,
                        self.flight_config.omega, self.flight_config.density
                    )
                    gpu_times.append(time.time() - start_time)
                
                avg_gpu_time = np.mean(gpu_times)
                speedup = avg_cpu_time / avg_gpu_time
            else:
                avg_gpu_time = avg_cpu_time
                speedup = 1.0
            
            performance_results[n_stations] = {
                'cpu_time': avg_cpu_time,
                'gpu_time': avg_gpu_time,
                'speedup': speedup
            }
            
            print(f"   站位数{n_stations:3d}: CPU={avg_cpu_time*1000:.2f}ms, "
                  f"GPU={avg_gpu_time*1000:.2f}ms, 加速比={speedup:.1f}x")
        
        # 验证性能要求
        if self.gpu_manager.use_gpu:
            max_speedup = max(result['speedup'] for result in performance_results.values())
            self.assertGreaterEqual(max_speedup, self.performance_threshold,
                                  f"GPU加速比({max_speedup:.1f}x)未达到要求({self.performance_threshold}x)")
        
        print("   ✅ 性能基准测试通过")
    
    def test_memory_efficiency(self):
        """测试内存使用效率"""
        print("\n📋 测试5: 内存使用效率")
        
        if not self.gpu_manager.use_gpu:
            print("   ⚠️  GPU不可用，跳过内存效率测试")
            return
        
        # 大规模数据测试
        large_n_stations = 1000
        r_stations = np.linspace(
            self.rotor_config.hub_radius,
            self.rotor_config.radius,
            large_n_stations
        )
        lambda_i = np.random.uniform(0.02, 0.08, large_n_stations)
        mu = 0.15
        
        try:
            # 测试大规模GPU计算
            start_time = time.time()
            result = self.gpu_manager.vectorized_bemt_computation(
                r_stations, lambda_i, mu,
                self.flight_config.omega, self.flight_config.density
            )
            compute_time = time.time() - start_time
            
            # 验证结果
            self.assertEqual(len(result['dT']), large_n_stations)
            self.assertTrue(np.all(np.isfinite(result['dT'])))
            
            # 获取性能统计
            perf_stats = self.gpu_manager.get_performance_stats()
            
            print(f"   大规模计算({large_n_stations}站位): {compute_time*1000:.2f}ms")
            print(f"   GPU时间: {perf_stats.get('gpu_time', 0)*1000:.2f}ms")
            print(f"   加速比: {perf_stats.get('acceleration_ratio', 1.0):.1f}x")
            
            # 内存效率验证
            if self.gpu_manager.gpu_backend == 'cuda':
                device_info = self.gpu_manager.device_info
                memory_used_ratio = (device_info.get('memory_total', 1) - 
                                   device_info.get('memory_free', 0)) / device_info.get('memory_total', 1)
                self.assertLess(memory_used_ratio, 0.8, "GPU内存使用率过高")
                print(f"   GPU内存使用率: {memory_used_ratio*100:.1f}%")
            
        except Exception as e:
            self.fail(f"大规模GPU计算失败: {e}")
        
        print("   ✅ 内存使用效率测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n📋 测试6: 错误处理")
        
        # 测试无效输入处理
        invalid_stations = np.array([])
        lambda_i = np.array([0.05])
        
        try:
            result = self.gpu_manager.vectorized_bemt_computation(
                invalid_stations, lambda_i, 0.1,
                self.flight_config.omega, self.flight_config.density
            )
            # 应该返回空结果或抛出异常
            if result:
                for key in result:
                    self.assertEqual(len(result[key]), 0)
        except Exception:
            pass  # 预期的异常
        
        # 测试维度不匹配
        mismatched_lambda = np.array([0.05, 0.06])  # 长度不匹配
        stations = np.array([0.5, 0.7, 0.9])
        
        try:
            result = self.gpu_manager.vectorized_bemt_computation(
                stations, mismatched_lambda, 0.1,
                self.flight_config.omega, self.flight_config.density
            )
            # 应该处理维度不匹配
        except Exception:
            pass  # 预期的异常
        
        print("   ✅ 错误处理测试通过")


def run_gpu_acceleration_tests():
    """运行GPU加速功能测试"""
    
    print("🚀 开始GPU加速功能测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestGPUAcceleration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # 统计结果
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"\n📊 GPU加速功能测试结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed}")
    print(f"   失败: {failures}")
    print(f"   错误: {errors}")
    print(f"   通过率: {(passed/total_tests)*100:.1f}%")
    
    # 详细失败信息
    if failures or errors:
        print(f"\n❌ 失败详情:")
        for test, traceback in result.failures + result.errors:
            print(f"   {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    success = (failures == 0 and errors == 0)
    print(f"\n🎯 GPU加速功能测试: {'✅ 通过' if success else '❌ 失败'}")
    
    return {
        'success': success,
        'total': total_tests,
        'passed': passed,
        'failed': failures + errors,
        'details': {
            'failures': result.failures,
            'errors': result.errors
        }
    }


if __name__ == "__main__":
    results = run_gpu_acceleration_tests()
    
    if results['success']:
        print("\n🎉 所有GPU加速功能测试通过！")
    else:
        print(f"\n⚠️  {results['failed']}/{results['total']} 测试失败")
        sys.exit(1)
