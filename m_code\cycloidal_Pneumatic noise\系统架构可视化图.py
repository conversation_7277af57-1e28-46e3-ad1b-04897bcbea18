#!/usr/bin/env python3
"""
旋翼仿真系统架构可视化图生成器
=====================================

生成系统架构的可视化图表，包括：
1. 总体系统架构图
2. 模块间依赖关系图
3. 数据流向图
4. 工作流程图

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_system_architecture_diagram():
    """创建系统总体架构图"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'user': '#E3F2FD',      # 浅蓝色 - 用户层
        'config': '#F3E5F5',    # 浅紫色 - 配置层
        'solver': '#E8F5E8',    # 浅绿色 - 求解器层
        'physics': '#FFF3E0',   # 浅橙色 - 物理层
        'analysis': '#FFEBEE',  # 浅红色 - 分析层
        'output': '#F1F8E9'     # 浅黄绿色 - 输出层
    }
    
    # 用户接口层
    user_box = FancyBboxPatch((0.5, 8.5), 9, 1, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['user'], 
                             edgecolor='black', linewidth=2)
    ax.add_patch(user_box)
    ax.text(5, 9, '用户接口层\n(main.py, config.yaml, 命令行工具)', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # 配置管理层
    config_box = FancyBboxPatch((0.5, 7), 9, 1, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['config'], 
                               edgecolor='black', linewidth=1)
    ax.add_patch(config_box)
    ax.text(5, 7.5, '配置管理层\n(ConfigManager, SimulationConfig, 参数验证)', 
            ha='center', va='center', fontsize=11)
    
    # 求解器工厂层
    solver_factory_box = FancyBboxPatch((0.5, 5.5), 9, 1, 
                                       boxstyle="round,pad=0.1", 
                                       facecolor=colors['solver'], 
                                       edgecolor='black', linewidth=1)
    ax.add_patch(solver_factory_box)
    ax.text(5, 6, '求解器工厂层\n(SolverFactory, 多保真度管理, 求解器选择)', 
            ha='center', va='center', fontsize=11)
    
    # 核心求解器层
    solver_boxes = [
        (0.5, 4, 2.8, 1, '低保真度\nLiftingLine\n求解器'),
        (3.6, 4, 2.8, 1, '中保真度\nBEMT\n求解器'),
        (6.7, 4, 2.8, 1, '高保真度\nUVLM\n求解器')
    ]
    
    for x, y, w, h, text in solver_boxes:
        box = FancyBboxPatch((x, y), w, h, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['solver'], 
                            edgecolor='blue', linewidth=1)
        ax.add_patch(box)
        ax.text(x + w/2, y + h/2, text, ha='center', va='center', fontsize=10)
    
    # 物理模型层
    physics_boxes = [
        (0.5, 2.5, 2.2, 1, '几何模型\nRotorGeometry\nBladeElement'),
        (3, 2.5, 2, 1, '翼型数据库\nAirfoilDatabase\n系数查询'),
        (5.3, 2.5, 2.2, 1, '物理修正\nTipLoss\nHubLoss'),
        (7.8, 2.5, 1.7, 1, '动态失速\nLeishman\nBeddoes')
    ]
    
    for x, y, w, h, text in physics_boxes:
        box = FancyBboxPatch((x, y), w, h, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['physics'], 
                            edgecolor='orange', linewidth=1)
        ax.add_patch(box)
        ax.text(x + w/2, y + h/2, text, ha='center', va='center', fontsize=9)
    
    # 分析处理层
    analysis_boxes = [
        (0.5, 1, 2.2, 1, '气动分析\n载荷计算\n性能评估'),
        (3, 1, 2, 1, '声学分析\nFW-H求解\nBPM噪声'),
        (5.3, 1, 2.2, 1, '时间积分\nRK4/Euler\n收敛控制'),
        (7.8, 1, 1.7, 1, '验证框架\n误差分析\n不确定性')
    ]
    
    for x, y, w, h, text in analysis_boxes:
        box = FancyBboxPatch((x, y), w, h, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['analysis'], 
                            edgecolor='red', linewidth=1)
        ax.add_patch(box)
        ax.text(x + w/2, y + h/2, text, ha='center', va='center', fontsize=9)
    
    # 输出层
    output_box = FancyBboxPatch((0.5, -0.5), 9, 1, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['output'], 
                               edgecolor='green', linewidth=2)
    ax.add_patch(output_box)
    ax.text(5, 0, '输出层\n(结果文件, 可视化图表, 验证报告, 性能统计)', 
            ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 添加连接箭头
    arrows = [
        (5, 8.5, 5, 8),      # 用户层 -> 配置层
        (5, 7, 5, 6.5),      # 配置层 -> 工厂层
        (5, 5.5, 5, 5),      # 工厂层 -> 求解器层
        (2, 4, 2, 3.5),      # 求解器 -> 物理模型
        (5, 4, 5, 3.5),      # 求解器 -> 物理模型
        (8, 4, 8, 3.5),      # 求解器 -> 物理模型
        (2, 2.5, 2, 2),      # 物理模型 -> 分析层
        (5, 2.5, 5, 2),      # 物理模型 -> 分析层
        (8, 2.5, 8, 2),      # 物理模型 -> 分析层
        (5, 1, 5, 0.5),      # 分析层 -> 输出层
    ]
    
    for x1, y1, x2, y2 in arrows:
        arrow = patches.FancyArrowPatch((x1, y1), (x2, y2),
                                       arrowstyle='->', 
                                       mutation_scale=20, 
                                       color='black', alpha=0.7)
        ax.add_patch(arrow)
    
    plt.title('旋翼空气动力学仿真系统 - 总体架构图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('系统总体架构图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_workflow_diagram():
    """创建工作流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 定义流程步骤
    steps = [
        (5, 11, '开始', '#FFE0B2'),
        (5, 10, '加载配置', '#E1F5FE'),
        (5, 9, '初始化求解器', '#E8F5E8'),
        (2.5, 8, '几何离散化', '#FFF3E0'),
        (7.5, 8, '物理模型初始化', '#FFF3E0'),
        (5, 7, '时间步循环', '#F3E5F5'),
        (5, 6, '更新运动学', '#FFEBEE'),
        (2.5, 5, '计算气动载荷', '#E8F5E8'),
        (7.5, 5, '应用物理修正', '#E8F5E8'),
        (5, 4, '收敛性检查', '#FFF9C4'),
        (5, 3, '声学分析', '#FCE4EC'),
        (5, 2, '后处理', '#F1F8E9'),
        (5, 1, '结果输出', '#E0F2F1'),
        (5, 0, '结束', '#FFE0B2')
    ]
    
    # 绘制流程框
    for x, y, text, color in steps:
        if '循环' in text or '检查' in text:
            # 菱形框
            diamond = patches.RegularPolygon((x, y), 4, radius=0.6, 
                                           orientation=np.pi/4,
                                           facecolor=color, 
                                           edgecolor='black')
            ax.add_patch(diamond)
        else:
            # 矩形框
            box = FancyBboxPatch((x-0.8, y-0.3), 1.6, 0.6, 
                               boxstyle="round,pad=0.1", 
                               facecolor=color, 
                               edgecolor='black')
            ax.add_patch(box)
        
        ax.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')
    
    # 添加流程箭头
    flow_arrows = [
        (5, 10.7, 5, 10.3),    # 开始 -> 加载配置
        (5, 9.7, 5, 9.3),      # 加载配置 -> 初始化求解器
        (5, 8.7, 2.5, 8.3),   # 初始化求解器 -> 几何离散化
        (5, 8.7, 7.5, 8.3),   # 初始化求解器 -> 物理模型初始化
        (2.5, 7.7, 5, 7.3),   # 几何离散化 -> 时间步循环
        (7.5, 7.7, 5, 7.3),   # 物理模型初始化 -> 时间步循环
        (5, 6.4, 5, 6.6),     # 时间步循环 -> 更新运动学
        (5, 5.7, 2.5, 5.3),   # 更新运动学 -> 计算气动载荷
        (5, 5.7, 7.5, 5.3),   # 更新运动学 -> 应用物理修正
        (2.5, 4.7, 5, 4.6),   # 计算气动载荷 -> 收敛性检查
        (7.5, 4.7, 5, 4.6),   # 应用物理修正 -> 收敛性检查
        (5, 3.4, 5, 3.6),     # 收敛性检查 -> 声学分析
        (5, 2.7, 5, 2.3),     # 声学分析 -> 后处理
        (5, 1.7, 5, 1.3),     # 后处理 -> 结果输出
        (5, 0.7, 5, 0.3),     # 结果输出 -> 结束
    ]
    
    for x1, y1, x2, y2 in flow_arrows:
        arrow = patches.FancyArrowPatch((x1, y1), (x2, y2),
                                       arrowstyle='->', 
                                       mutation_scale=15, 
                                       color='blue', alpha=0.8)
        ax.add_patch(arrow)
    
    # 添加循环箭头（未收敛时）
    loop_arrow = patches.FancyArrowPatch((4.4, 4), (1, 6),
                                        arrowstyle='->', 
                                        mutation_scale=15, 
                                        color='red', alpha=0.8,
                                        connectionstyle="arc3,rad=0.3")
    ax.add_patch(loop_arrow)
    ax.text(1.5, 5, '未收敛', ha='center', va='center', fontsize=9, color='red')
    
    plt.title('旋翼仿真系统 - 工作流程图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('系统工作流程图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_data_flow_diagram():
    """创建数据流向图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 数据节点
    data_nodes = [
        (1, 7, 'config.yaml', '#E3F2FD'),
        (3, 7, 'ConfigManager', '#F3E5F5'),
        (6, 7, '求解器参数', '#E8F5E8'),
        (9, 7, '几何数据', '#FFF3E0'),
        (11, 7, '翼型数据', '#FFEBEE'),
        
        (1, 5, '运动学状态', '#E1F5FE'),
        (3, 5, '相对速度', '#E8F5E8'),
        (6, 5, '气动系数', '#FFF3E0'),
        (9, 5, '叶素载荷', '#FFEBEE'),
        (11, 5, '诱导速度', '#F1F8E9'),
        
        (1, 3, '收敛残差', '#FFF9C4'),
        (3, 3, '性能参数', '#FCE4EC'),
        (6, 3, '声学源项', '#E0F2F1'),
        (9, 3, '远场噪声', '#FFCDD2'),
        (11, 3, '验证结果', '#F3E5F5'),
        
        (6, 1, '最终报告', '#C8E6C9')
    ]
    
    # 绘制数据节点
    for x, y, text, color in data_nodes:
        box = FancyBboxPatch((x-0.7, y-0.3), 1.4, 0.6, 
                           boxstyle="round,pad=0.1", 
                           facecolor=color, 
                           edgecolor='black')
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9)
    
    # 数据流箭头
    data_flows = [
        (1.7, 7, 2.3, 7),      # config.yaml -> ConfigManager
        (3.7, 7, 5.3, 7),      # ConfigManager -> 求解器参数
        (6.7, 7, 8.3, 7),      # 求解器参数 -> 几何数据
        (9.7, 7, 10.3, 7),     # 几何数据 -> 翼型数据
        
        (1, 6.7, 1, 5.3),      # 向下流动
        (3, 6.7, 3, 5.3),
        (6, 6.7, 6, 5.3),
        (9, 6.7, 9, 5.3),
        (11, 6.7, 11, 5.3),
        
        (1.7, 5, 2.3, 5),      # 水平流动
        (3.7, 5, 5.3, 5),
        (6.7, 5, 8.3, 5),
        (9.7, 5, 10.3, 5),
        
        (1, 4.7, 1, 3.3),      # 向下流动
        (3, 4.7, 3, 3.3),
        (6, 4.7, 6, 3.3),
        (9, 4.7, 9, 3.3),
        (11, 4.7, 11, 3.3),
        
        (1.7, 3, 2.3, 3),      # 水平流动
        (3.7, 3, 5.3, 3),
        (6.7, 3, 8.3, 3),
        (9.7, 3, 10.3, 3),
        
        (6, 2.7, 6, 1.3),      # 最终汇总
    ]
    
    for x1, y1, x2, y2 in data_flows:
        arrow = patches.FancyArrowPatch((x1, y1), (x2, y2),
                                       arrowstyle='->', 
                                       mutation_scale=12, 
                                       color='darkblue', alpha=0.7)
        ax.add_patch(arrow)
    
    # 添加层级标签
    ax.text(0.2, 7, '输入层', rotation=90, ha='center', va='center', 
            fontsize=12, fontweight='bold', color='blue')
    ax.text(0.2, 5, '计算层', rotation=90, ha='center', va='center', 
            fontsize=12, fontweight='bold', color='green')
    ax.text(0.2, 3, '分析层', rotation=90, ha='center', va='center', 
            fontsize=12, fontweight='bold', color='red')
    ax.text(0.2, 1, '输出层', rotation=90, ha='center', va='center', 
            fontsize=12, fontweight='bold', color='purple')
    
    plt.title('旋翼仿真系统 - 数据流向图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('系统数据流向图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_module_dependency_diagram():
    """创建模块依赖关系图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 模块节点定义
    modules = [
        # 核心模块
        (7, 9, 'main.py', '#FFE0B2', 'large'),
        (7, 8, 'CycloneSimulation', '#E3F2FD', 'large'),
        
        # 配置模块
        (3, 7, 'ConfigManager', '#F3E5F5', 'medium'),
        (11, 7, 'SimulationConfig', '#F3E5F5', 'medium'),
        
        # 求解器模块
        (2, 5.5, 'BEMTSolver', '#E8F5E8', 'medium'),
        (7, 5.5, 'UVLMSolver', '#E8F5E8', 'medium'),
        (12, 5.5, 'LiftingLineSolver', '#E8F5E8', 'medium'),
        
        # 物理模块
        (1, 4, 'RotorGeometry', '#FFF3E0', 'small'),
        (3, 4, 'AirfoilDatabase', '#FFF3E0', 'small'),
        (5, 4, 'PhysicalCorrections', '#FFF3E0', 'small'),
        (7, 4, 'LeishmanBeddoes', '#FFF3E0', 'small'),
        (9, 4, 'WakeSystem', '#FFF3E0', 'small'),
        (11, 4, 'BladeElement', '#FFF3E0', 'small'),
        (13, 4, 'TimeIntegrator', '#FFF3E0', 'small'),
        
        # 分析模块
        (2, 2.5, 'FWHSolver', '#FFEBEE', 'small'),
        (4, 2.5, 'NoiseAnalyzer', '#FFEBEE', 'small'),
        (6, 2.5, 'BPMNoiseModel', '#FFEBEE', 'small'),
        (8, 2.5, 'PerformanceCalculator', '#FFEBEE', 'small'),
        (10, 2.5, 'ValidationFramework', '#FFEBEE', 'small'),
        (12, 2.5, 'ErrorAnalyzer', '#FFEBEE', 'small'),
        
        # 工具模块
        (4, 1, 'DataManager', '#F1F8E9', 'small'),
        (7, 1, 'PlotManager', '#F1F8E9', 'small'),
        (10, 1, 'ReportGenerator', '#F1F8E9', 'small'),
    ]
    
    # 绘制模块节点
    for x, y, name, color, size in modules:
        if size == 'large':
            w, h = 2.5, 0.8
        elif size == 'medium':
            w, h = 2, 0.6
        else:
            w, h = 1.5, 0.5
        
        box = FancyBboxPatch((x-w/2, y-h/2), w, h, 
                           boxstyle="round,pad=0.1", 
                           facecolor=color, 
                           edgecolor='black')
        ax.add_patch(box)
        ax.text(x, y, name, ha='center', va='center', 
                fontsize=8 if size == 'small' else 10, fontweight='bold')
    
    # 依赖关系箭头
    dependencies = [
        # 主程序依赖
        (7, 8.6, 7, 8.4),      # main -> CycloneSimulation
        (7, 7.6, 3, 7.4),      # CycloneSimulation -> ConfigManager
        (7, 7.6, 11, 7.4),     # CycloneSimulation -> SimulationConfig
        
        # 求解器依赖
        (7, 7.6, 2, 5.9),      # CycloneSimulation -> BEMTSolver
        (7, 7.6, 7, 5.9),      # CycloneSimulation -> UVLMSolver
        (7, 7.6, 12, 5.9),     # CycloneSimulation -> LiftingLineSolver
        
        # 物理模块依赖
        (2, 5.1, 1, 4.4),      # BEMTSolver -> RotorGeometry
        (2, 5.1, 3, 4.4),      # BEMTSolver -> AirfoilDatabase
        (2, 5.1, 5, 4.4),      # BEMTSolver -> PhysicalCorrections
        (7, 5.1, 7, 4.4),      # UVLMSolver -> LeishmanBeddoes
        (7, 5.1, 9, 4.4),      # UVLMSolver -> WakeSystem
        (12, 5.1, 11, 4.4),    # LiftingLineSolver -> BladeElement
        (12, 5.1, 13, 4.4),    # LiftingLineSolver -> TimeIntegrator
        
        # 分析模块依赖
        (7, 7.6, 2, 2.9),      # CycloneSimulation -> FWHSolver
        (7, 7.6, 4, 2.9),      # CycloneSimulation -> NoiseAnalyzer
        (7, 7.6, 6, 2.9),      # CycloneSimulation -> BPMNoiseModel
        (7, 7.6, 8, 2.9),      # CycloneSimulation -> PerformanceCalculator
        (7, 7.6, 10, 2.9),     # CycloneSimulation -> ValidationFramework
        (7, 7.6, 12, 2.9),     # CycloneSimulation -> ErrorAnalyzer
        
        # 工具模块依赖
        (7, 7.6, 4, 1.4),      # CycloneSimulation -> DataManager
        (7, 7.6, 7, 1.4),      # CycloneSimulation -> PlotManager
        (7, 7.6, 10, 1.4),     # CycloneSimulation -> ReportGenerator
    ]
    
    for x1, y1, x2, y2 in dependencies:
        arrow = patches.FancyArrowPatch((x1, y1), (x2, y2),
                                       arrowstyle='->', 
                                       mutation_scale=10, 
                                       color='darkblue', alpha=0.6)
        ax.add_patch(arrow)
    
    plt.title('旋翼仿真系统 - 模块依赖关系图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('模块依赖关系图.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数 - 生成所有架构图"""
    print("🎨 开始生成旋翼仿真系统架构可视化图...")
    
    print("📊 1. 生成系统总体架构图...")
    create_system_architecture_diagram()
    
    print("🔄 2. 生成工作流程图...")
    create_workflow_diagram()
    
    print("📈 3. 生成数据流向图...")
    create_data_flow_diagram()
    
    print("🔗 4. 生成模块依赖关系图...")
    create_module_dependency_diagram()
    
    print("✅ 所有架构图生成完成！")
    print("\n生成的文件:")
    print("- 系统总体架构图.png")
    print("- 系统工作流程图.png") 
    print("- 系统数据流向图.png")
    print("- 模块依赖关系图.png")

if __name__ == "__main__":
    main()