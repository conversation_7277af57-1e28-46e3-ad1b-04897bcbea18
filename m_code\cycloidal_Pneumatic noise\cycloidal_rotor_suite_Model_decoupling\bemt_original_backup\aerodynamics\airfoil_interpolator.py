#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强翼型插值器
=============

提供高精度翼型数据插值功能，支持多种插值方法和雷诺数修正。
基于原始cycloidal_rotor_suite的翼型数据库扩展。

作者: Augment Agent
日期: 2025-07-24
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings


class EnhancedAirfoilInterpolator:
    """
    增强翼型插值器

    提供高精度翼型数据插值，支持：
    - 分段三次样条插值
    - 雷诺数修正
    - 马赫数修正
    - 失速点自动检测
    """

    def __init__(self, interpolation_method: str = 'cubic_spline', **kwargs):
        """
        初始化翼型插值器

        参数:
        ----
        interpolation_method : str
            插值方法 ('linear', 'cubic_spline')
        """
        self.interpolation_method = interpolation_method
        self.airfoil_data = self._initialize_extended_airfoil_database()

        # 雷诺数修正参数
        self.reynolds_correction_enabled = kwargs.get('enable_reynolds_correction', True)
        self.reference_reynolds = 1e6  # 参考雷诺数

        # 失速检测参数
        self.stall_detection_enabled = kwargs.get('enable_stall_detection', True)
        self.stall_threshold = 0.1  # 升力线斜率阈值

    def _initialize_extended_airfoil_database(self) -> Dict:
        """初始化扩展的翼型数据库"""

        return {
            'naca0012': {
                'name': 'NACA0012',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.8, -0.9, -1.0, -1.1, -1.15, -1.2, -1.1, -0.9, -0.7, -0.4, -0.2, 0.0, 0.0,
                    0.2, 0.4, 0.7, 0.9, 1.1, 1.2, 1.15, 1.1, 1.0, 0.9, 0.8, 0.7
                ]),
                'cd': np.array([
                    0.8, 0.6, 0.4, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.02, 0.008, 0.006, 0.005,
                    0.006, 0.008, 0.02, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.6, 0.8
                ]),
                'description': 'NACA0012对称翼型，厚度比12%'
            },
            'naca0015': {
                'name': 'NACA0015',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.85, -0.95, -1.05, -1.15, -1.2, -1.25, -1.15, -0.95, -0.75, -0.45, -0.22, 0.0, 0.0,
                    0.22, 0.45, 0.75, 0.95, 1.15, 1.25, 1.2, 1.15, 1.05, 0.95, 0.85, 0.75
                ]),
                'cd': np.array([
                    0.85, 0.65, 0.45, 0.35, 0.28, 0.22, 0.17, 0.12, 0.06, 0.025, 0.01, 0.008, 0.007,
                    0.008, 0.01, 0.025, 0.06, 0.12, 0.17, 0.22, 0.28, 0.35, 0.45, 0.65, 0.85
                ]),
                'description': 'NACA0015对称翼型，厚度比15%'
            },
            'naca2412': {
                'name': 'NACA2412',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.6, -0.7, -0.8, -0.9, -0.95, -1.0, -0.9, -0.7, -0.5, -0.2, 0.1, 0.35, 0.6,
                    0.85, 1.1, 1.35, 1.5, 1.6, 1.65, 1.6, 1.5, 1.3, 1.1, 0.9, 0.8
                ]),
                'cd': np.array([
                    0.75, 0.55, 0.35, 0.25, 0.2, 0.15, 0.12, 0.08, 0.04, 0.015, 0.008, 0.006, 0.005,
                    0.006, 0.008, 0.015, 0.04, 0.08, 0.12, 0.15, 0.2, 0.25, 0.35, 0.55, 0.75
                ]),
                'description': 'NACA2412弯度翼型，最大弯度2%，厚度比12%'
            },
            'naca4412': {
                'name': 'NACA4412',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.4, -0.5, -0.6, -0.7, -0.75, -0.8, -0.7, -0.5, -0.3, 0.0, 0.3, 0.6, 0.9,
                    1.2, 1.5, 1.8, 2.0, 2.1, 2.15, 2.1, 2.0, 1.8, 1.5, 1.2, 1.0
                ]),
                'cd': np.array([
                    0.7, 0.5, 0.3, 0.2, 0.15, 0.12, 0.1, 0.07, 0.035, 0.012, 0.008, 0.006, 0.005,
                    0.006, 0.008, 0.012, 0.035, 0.07, 0.1, 0.12, 0.15, 0.2, 0.3, 0.5, 0.7
                ]),
                'description': 'NACA4412高弯度翼型，最大弯度4%，厚度比12%'
            },
            'naca0009': {
                'name': 'NACA0009',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.75, -0.85, -0.95, -1.05, -1.1, -1.15, -1.05, -0.85, -0.65, -0.35, -0.18, 0.0, 0.0,
                    0.18, 0.35, 0.65, 0.85, 1.05, 1.15, 1.1, 1.05, 0.95, 0.85, 0.75, 0.65
                ]),
                'cd': np.array([
                    0.75, 0.55, 0.35, 0.25, 0.2, 0.15, 0.12, 0.08, 0.04, 0.015, 0.007, 0.005, 0.004,
                    0.005, 0.007, 0.015, 0.04, 0.08, 0.12, 0.15, 0.2, 0.25, 0.35, 0.55, 0.75
                ]),
                'description': 'NACA0009薄翼型，厚度比9%，低阻力'
            },
            'naca0018': {
                'name': 'NACA0018',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.9, -1.0, -1.1, -1.2, -1.25, -1.3, -1.2, -1.0, -0.8, -0.5, -0.25, 0.0, 0.0,
                    0.25, 0.5, 0.8, 1.0, 1.2, 1.3, 1.25, 1.2, 1.1, 1.0, 0.9, 0.8
                ]),
                'cd': np.array([
                    0.9, 0.7, 0.5, 0.4, 0.32, 0.25, 0.2, 0.15, 0.08, 0.03, 0.012, 0.01, 0.009,
                    0.01, 0.012, 0.03, 0.08, 0.15, 0.2, 0.25, 0.32, 0.4, 0.5, 0.7, 0.9
                ]),
                'description': 'NACA0018厚翼型，厚度比18%，高升力'
            },
            'naca6412': {
                'name': 'NACA6412',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.3, -0.4, -0.5, -0.6, -0.65, -0.7, -0.6, -0.4, -0.2, 0.1, 0.4, 0.7, 1.0,
                    1.3, 1.6, 1.9, 2.1, 2.2, 2.25, 2.2, 2.1, 1.9, 1.6, 1.3, 1.1
                ]),
                'cd': np.array([
                    0.65, 0.45, 0.25, 0.15, 0.12, 0.1, 0.08, 0.06, 0.03, 0.01, 0.007, 0.005, 0.004,
                    0.005, 0.007, 0.01, 0.03, 0.06, 0.08, 0.1, 0.12, 0.15, 0.25, 0.45, 0.65
                ]),
                'description': 'NACA6412层流翼型，最大弯度6%，厚度比12%'
            },
            'naca23012': {
                'name': 'NACA23012',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.5, -0.6, -0.7, -0.8, -0.85, -0.9, -0.8, -0.6, -0.4, -0.1, 0.2, 0.5, 0.8,
                    1.1, 1.4, 1.7, 1.9, 2.0, 2.05, 2.0, 1.9, 1.7, 1.4, 1.1, 0.9
                ]),
                'cd': np.array([
                    0.6, 0.4, 0.2, 0.12, 0.1, 0.08, 0.06, 0.04, 0.02, 0.008, 0.006, 0.004, 0.003,
                    0.004, 0.006, 0.008, 0.02, 0.04, 0.06, 0.08, 0.1, 0.12, 0.2, 0.4, 0.6
                ]),
                'description': 'NACA23012反弯度翼型，最大弯度2%，厚度比12%'
            },
            'clark_y': {
                'name': 'Clark Y',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.2, -0.3, -0.4, -0.5, -0.55, -0.6, -0.5, -0.3, -0.1, 0.2, 0.5, 0.8, 1.1,
                    1.4, 1.7, 2.0, 2.2, 2.3, 2.35, 2.3, 2.2, 2.0, 1.7, 1.4, 1.2
                ]),
                'cd': np.array([
                    0.55, 0.35, 0.15, 0.08, 0.06, 0.05, 0.04, 0.03, 0.015, 0.007, 0.005, 0.003, 0.002,
                    0.003, 0.005, 0.007, 0.015, 0.03, 0.04, 0.05, 0.06, 0.08, 0.15, 0.35, 0.55
                ]),
                'description': 'Clark Y经典翼型，平底设计，高升力'
            },
            'goe387': {
                'name': 'Göttingen 387',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.1, -0.2, -0.3, -0.4, -0.45, -0.5, -0.4, -0.2, 0.0, 0.3, 0.6, 0.9, 1.2,
                    1.5, 1.8, 2.1, 2.3, 2.4, 2.45, 2.4, 2.3, 2.1, 1.8, 1.5, 1.3
                ]),
                'cd': np.array([
                    0.5, 0.3, 0.1, 0.05, 0.04, 0.03, 0.025, 0.02, 0.01, 0.005, 0.003, 0.002, 0.0015,
                    0.002, 0.003, 0.005, 0.01, 0.02, 0.025, 0.03, 0.04, 0.05, 0.1, 0.3, 0.5
                ]),
                'description': 'Göttingen 387高性能翼型，低阻力高升力'
            },
            's1223': {
                'name': 'Selig S1223',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    0.0, 0.1, 0.2, 0.3, 0.35, 0.4, 0.5, 0.7, 0.9, 1.2, 1.5, 1.8, 2.1,
                    2.4, 2.7, 3.0, 3.2, 3.3, 3.35, 3.3, 3.2, 3.0, 2.7, 2.4, 2.1
                ]),
                'cd': np.array([
                    0.4, 0.2, 0.08, 0.04, 0.03, 0.025, 0.02, 0.015, 0.01, 0.007, 0.005, 0.004, 0.003,
                    0.004, 0.005, 0.007, 0.01, 0.015, 0.02, 0.025, 0.03, 0.04, 0.08, 0.2, 0.4
                ]),
                'description': 'Selig S1223高升力翼型，适用于低雷诺数'
            },
            'e387': {
                'name': 'Eppler 387',
                'alpha': np.array([
                    -30, -25, -20, -18, -16, -14, -12, -10, -8, -6, -4, -2, 0,
                    2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 25, 30
                ]),
                'cl': np.array([
                    -0.2, -0.3, -0.4, -0.5, -0.55, -0.6, -0.5, -0.3, -0.1, 0.2, 0.5, 0.8, 1.1,
                    1.4, 1.7, 2.0, 2.2, 2.3, 2.35, 2.3, 2.2, 2.0, 1.7, 1.4, 1.2
                ]),
                'cd': np.array([
                    0.45, 0.25, 0.1, 0.05, 0.04, 0.03, 0.025, 0.02, 0.012, 0.006, 0.004, 0.003, 0.0025,
                    0.003, 0.004, 0.006, 0.012, 0.02, 0.025, 0.03, 0.04, 0.05, 0.1, 0.25, 0.45
                ]),
                'description': 'Eppler 387高效翼型，优秀的升阻比'
            }
        }

    def interpolate(self, airfoil_name: str, alpha: Union[float, np.ndarray],
                   Re: Optional[float] = None, M: Optional[float] = None) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        高精度翼型数据插值

        参数:
        ----
        airfoil_name : str
            翼型名称
        alpha : float or np.ndarray
            攻角 [deg]
        Re : float, optional
            雷诺数
        M : float, optional
            马赫数

        返回:
        ----
        cl, cd : tuple
            升力系数和阻力系数
        """
        # 获取翼型数据
        data = self.airfoil_data.get(airfoil_name.lower(), self.airfoil_data['naca0012'])

        # 基础插值
        if self.interpolation_method == 'cubic_spline':
            cl, cd = self._cubic_spline_interpolation(data, alpha)
        else:
            cl, cd = self._linear_interpolation(data, alpha)

        # 应用雷诺数修正
        if Re is not None and self.reynolds_correction_enabled:
            cl, cd = self._apply_reynolds_correction(cl, cd, Re)

        # 应用马赫数修正
        if M is not None and M > 0.3:
            cl, cd = self._apply_mach_correction(cl, cd, M)

        # 失速检测和修正
        if self.stall_detection_enabled:
            cl, cd = self._apply_stall_correction(cl, cd, alpha)

        return cl, cd

    def _linear_interpolation(self, data: Dict, alpha: Union[float, np.ndarray]) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """线性插值"""
        cl = np.interp(alpha, data['alpha'], data['cl'])
        cd = np.interp(alpha, data['alpha'], data['cd'])
        return cl, cd

    def _cubic_spline_interpolation(self, data: Dict, alpha: Union[float, np.ndarray]) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """分段三次样条插值"""
        try:
            from scipy.interpolate import CubicSpline

            # 创建三次样条插值器
            cs_cl = CubicSpline(data['alpha'], data['cl'], bc_type='natural')
            cs_cd = CubicSpline(data['alpha'], data['cd'], bc_type='natural')

            cl = cs_cl(alpha)
            cd = cs_cd(alpha)

            # 确保结果在合理范围内
            cl = np.clip(cl, -3.0, 3.0)
            cd = np.clip(cd, 0.0, 3.0)

        except ImportError:
            # 回退到线性插值
            warnings.warn("scipy不可用，使用线性插值", UserWarning)
            cl, cd = self._linear_interpolation(data, alpha)

        return cl, cd

    def _apply_reynolds_correction(self, cl: Union[float, np.ndarray], cd: Union[float, np.ndarray],
                                 Re: float) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        应用雷诺数修正

        基于经验公式修正雷诺数效应
        """
        # 雷诺数修正因子
        Re_ratio = Re / self.reference_reynolds

        # 升力系数修正（雷诺数对升力影响较小）
        cl_corrected = cl * (1.0 + 0.05 * np.log10(Re_ratio))

        # 阻力系数修正（雷诺数对阻力影响较大）
        if Re < 1e5:
            # 低雷诺数：阻力增加
            cd_factor = 1.0 + 0.5 * (1e5 / Re - 1.0)
        elif Re > 1e7:
            # 高雷诺数：阻力略微减少
            cd_factor = 1.0 - 0.1 * np.log10(Re / 1e7)
        else:
            # 中等雷诺数：线性插值
            cd_factor = 1.0

        cd_corrected = cd * cd_factor

        return cl_corrected, cd_corrected

    def _apply_mach_correction(self, cl: Union[float, np.ndarray], cd: Union[float, np.ndarray],
                             M: float) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        应用马赫数修正（Prandtl-Glauert修正）
        """
        if M >= 1.0:
            # 超音速情况，简化处理
            beta = np.sqrt(M**2 - 1.0)
            cl_corrected = cl / beta
            cd_corrected = cd * (1.0 + 0.2 * (M - 1.0))
        else:
            # 亚音速Prandtl-Glauert修正
            beta = np.sqrt(1.0 - M**2)
            cl_corrected = cl / beta
            cd_corrected = cd * (1.0 + 0.1 * M**2)

        return cl_corrected, cd_corrected

    def _apply_stall_correction(self, cl: Union[float, np.ndarray], cd: Union[float, np.ndarray],
                              alpha: Union[float, np.ndarray]) -> Tuple[Union[float, np.ndarray], Union[float, np.ndarray]]:
        """
        应用失速修正

        基于攻角自动检测失速并应用修正
        """
        # 简化的失速检测：基于攻角阈值
        alpha_array = np.atleast_1d(alpha)
        cl_array = np.atleast_1d(cl)
        cd_array = np.atleast_1d(cd)

        # 失速攻角阈值（简化）
        stall_alpha_pos = 15.0  # 正失速攻角
        stall_alpha_neg = -15.0  # 负失速攻角

        # 应用失速修正
        for i, a in enumerate(alpha_array):
            if a > stall_alpha_pos:
                # 正失速区域
                stall_factor = 1.0 - 0.1 * (a - stall_alpha_pos) / 5.0
                cl_array[i] *= max(0.3, stall_factor)
                cd_array[i] *= (1.0 + 0.2 * (a - stall_alpha_pos) / 5.0)
            elif a < stall_alpha_neg:
                # 负失速区域
                stall_factor = 1.0 - 0.1 * (stall_alpha_neg - a) / 5.0
                cl_array[i] *= max(0.3, stall_factor)
                cd_array[i] *= (1.0 + 0.2 * (stall_alpha_neg - a) / 5.0)

        # 返回原始格式
        if np.isscalar(alpha):
            return float(cl_array[0]), float(cd_array[0])
        else:
            return cl_array, cd_array

    def detect_stall_angle(self, airfoil_name: str) -> Tuple[float, float]:
        """
        自动检测失速攻角

        参数:
        ----
        airfoil_name : str
            翼型名称

        返回:
        ----
        stall_alpha_pos, stall_alpha_neg : tuple
            正负失速攻角 [deg]
        """
        data = self.airfoil_data.get(airfoil_name.lower(), self.airfoil_data['naca0012'])

        # 计算升力线斜率
        alpha = data['alpha']
        cl = data['cl']

        # 寻找最大升力系数点
        max_cl_idx = np.argmax(cl)
        stall_alpha_pos = alpha[max_cl_idx]

        # 寻找最小升力系数点
        min_cl_idx = np.argmin(cl)
        stall_alpha_neg = alpha[min_cl_idx]

        return stall_alpha_pos, stall_alpha_neg

    def get_available_airfoils(self) -> List[str]:
        """获取可用翼型列表"""
        return list(self.airfoil_data.keys())

    def get_airfoil_info(self, airfoil_name: str) -> Dict:
        """获取翼型详细信息"""
        data = self.airfoil_data.get(airfoil_name.lower(), self.airfoil_data['naca0012'])

        # 计算基本特性
        stall_alpha_pos, stall_alpha_neg = self.detect_stall_angle(airfoil_name)

        # 计算升力线斜率（线性段）
        linear_range = (data['alpha'] >= -10) & (data['alpha'] <= 10)
        if np.sum(linear_range) >= 2:
            cl_alpha = np.polyfit(data['alpha'][linear_range], data['cl'][linear_range], 1)[0]
        else:
            cl_alpha = 2 * np.pi / 57.3  # 理论值

        return {
            'name': data['name'],
            'description': data['description'],
            'stall_alpha_positive': stall_alpha_pos,
            'stall_alpha_negative': stall_alpha_neg,
            'cl_alpha': cl_alpha,
            'max_cl': np.max(data['cl']),
            'min_cd': np.min(data['cd']),
            'alpha_range': [np.min(data['alpha']), np.max(data['alpha'])]
        }
