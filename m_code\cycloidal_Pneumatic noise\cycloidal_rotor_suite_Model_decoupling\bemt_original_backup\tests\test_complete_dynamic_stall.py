#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整动态失速测试
===============

验证12状态Leishman-Beddoes动态失速模型的准确性。

测试内容:
1. L-B模型初始化和状态管理
2. 动态失速迟滞环特性
3. 涡脱落和对流建模
4. 与实验数据对比验证
5. 非定常响应特性
6. 物理合理性检查

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import time
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import unittest

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_data_config import TEST_DATA
from physics.complete_dynamic_stall import CompleteLeishmanBeddoesModel


class TestCompleteDynamicStall(unittest.TestCase):
    """完整动态失速测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_data = TEST_DATA
        self.tolerance = 1e-3
        
        # 获取真实测试参数
        self.rotor_config = self.test_data.get_rotor_config('UH-60')
        self.flight_config = self.test_data.get_flight_config('high_speed')
        self.airfoil_config = self.test_data.get_airfoil_config('SC1095')
        
        # 初始化L-B模型
        self.lb_model = CompleteLeishmanBeddoesModel(
            airfoil_params={
                'name': 'SC1095',
                'alpha_0': 0.0,
                'cl_alpha': 2*np.pi,
                'alpha_ss': np.radians(16.0),  # SC1095失速攻角
                'cl_max': 1.6,
                'cd_0': 0.0095,
                'cm_0': 0.0
            },
            chord=0.533,  # UH-60根部弦长
            mach_number=0.6  # 叶尖马赫数
        )
        
        print(f"\n🧪 完整动态失速测试初始化")
        print(f"   翼型: SC1095")
        print(f"   弦长: {0.533:.3f}m")
        print(f"   马赫数: 0.6")
        print(f"   状态变量: 12个")
    
    def test_lb_model_initialization(self):
        """测试L-B模型初始化和状态管理"""
        print("\n📋 测试1: L-B模型初始化和状态管理")
        
        # 验证模型初始化
        self.assertIsInstance(self.lb_model, CompleteLeishmanBeddoesModel)
        
        # 验证状态变量数量
        state_info = self.lb_model.get_state_info()
        states = state_info['states']
        
        self.assertEqual(len(states), 12, f"状态变量数量不正确: {len(states)}")
        
        # 验证状态变量名称
        expected_states = ['X1', 'X2', 'Y1', 'Y2', 'X_v1', 'X_v2', 
                          'X_v3', 'X_v4', 'Z1', 'Z2', 'W1', 'W2']
        
        for state_name in expected_states:
            self.assertIn(state_name, states, f"缺少状态变量: {state_name}")
        
        # 验证初始状态为零
        for state_name, state_value in states.items():
            self.assertAlmostEqual(state_value, 0.0, places=10,
                                 msg=f"初始状态{state_name}应为零: {state_value}")
        
        # 验证参数设置
        params = state_info['parameters']
        self.assertIn('alpha_ss', params)
        self.assertIn('cl_max', params)
        self.assertIn('T_f', params)
        self.assertIn('T_v', params)
        
        print(f"   状态变量: {list(states.keys())}")
        print(f"   失速攻角: {np.rad2deg(params['alpha_ss']):.1f}°")
        print(f"   最大升力系数: {params['cl_max']:.2f}")
        print("   ✅ L-B模型初始化测试通过")
    
    def test_dynamic_stall_hysteresis(self):
        """测试动态失速迟滞环特性"""
        print("\n📋 测试2: 动态失速迟滞环特性")
        
        # 创建正弦攻角变化（典型的动态失速试验）
        # α(t) = α_mean + α_amp * sin(ωt)
        alpha_mean = np.radians(10.0)  # 平均攻角
        alpha_amp = np.radians(10.0)   # 攻角幅值
        reduced_freq = 0.1             # 无量纲频率
        
        # 时间参数
        dt = 0.01
        n_cycles = 2
        n_steps_per_cycle = int(2 * np.pi / (reduced_freq * dt))
        total_steps = n_cycles * n_steps_per_cycle
        
        # 存储结果
        time_history = []
        alpha_history = []
        cl_history = []
        cd_history = []
        cm_history = []
        
        velocity = 50.0  # m/s
        
        for i in range(total_steps):
            time = i * dt
            alpha = alpha_mean + alpha_amp * np.sin(reduced_freq * time)
            alpha_dot = alpha_amp * reduced_freq * np.cos(reduced_freq * time)
            
            # 计算动态失速
            result = self.lb_model.compute_dynamic_stall(
                alpha, alpha_dot, velocity, time, dt
            )
            
            # 存储结果
            time_history.append(time)
            alpha_history.append(alpha)
            cl_history.append(result['cl'])
            cd_history.append(result['cd'])
            cm_history.append(result['cm'])
        
        # 转换为numpy数组
        alpha_history = np.array(alpha_history)
        cl_history = np.array(cl_history)
        cd_history = np.array(cd_history)
        
        # 验证迟滞环特性
        
        # 1. 找到上升和下降阶段
        alpha_deg = np.rad2deg(alpha_history)
        
        # 找到第二个周期（稳定后）
        second_cycle_start = n_steps_per_cycle
        second_cycle_end = 2 * n_steps_per_cycle
        
        alpha_cycle = alpha_deg[second_cycle_start:second_cycle_end]
        cl_cycle = cl_history[second_cycle_start:second_cycle_end]
        
        # 找到最大和最小攻角点
        max_alpha_idx = np.argmax(alpha_cycle)
        min_alpha_idx = np.argmin(alpha_cycle)
        
        # 上升阶段和下降阶段
        if max_alpha_idx < min_alpha_idx:
            up_phase = slice(0, max_alpha_idx)
            down_phase = slice(max_alpha_idx, min_alpha_idx)
        else:
            up_phase = slice(min_alpha_idx, max_alpha_idx)
            down_phase = slice(max_alpha_idx, len(alpha_cycle))
        
        # 验证迟滞效应
        if len(alpha_cycle[up_phase]) > 5 and len(alpha_cycle[down_phase]) > 5:
            # 在相同攻角下，上升阶段的升力应该高于下降阶段
            mid_alpha = (np.max(alpha_cycle) + np.min(alpha_cycle)) / 2
            
            # 找到接近中间攻角的点
            up_mid_idx = np.argmin(np.abs(alpha_cycle[up_phase] - mid_alpha))
            down_mid_idx = np.argmin(np.abs(alpha_cycle[down_phase] - mid_alpha))
            
            if up_mid_idx < len(cl_cycle[up_phase]) and down_mid_idx < len(cl_cycle[down_phase]):
                cl_up = cl_cycle[up_phase][up_mid_idx]
                cl_down = cl_cycle[down_phase][down_mid_idx]
                
                hysteresis_width = cl_up - cl_down
                
                # 验证迟滞效应存在
                self.assertGreater(hysteresis_width, 0.05,
                                 f"迟滞效应不明显: {hysteresis_width:.3f}")
        
        # 2. 验证动态失速峰值
        max_cl_dynamic = np.max(cl_history)
        static_cl_max = self.lb_model.params['cl_max']
        
        # 动态失速峰值应该超过静态失速
        self.assertGreater(max_cl_dynamic, static_cl_max * 1.1,
                          f"动态失速峰值不足: {max_cl_dynamic:.2f} vs {static_cl_max:.2f}")
        
        # 3. 验证物理合理性
        self.assertTrue(np.all(np.isfinite(cl_history)), "升力系数包含无效值")
        self.assertTrue(np.all(np.isfinite(cd_history)), "阻力系数包含无效值")
        self.assertTrue(np.all(cd_history > 0), "阻力系数应为正值")
        
        print(f"   攻角范围: {np.rad2deg(np.min(alpha_history)):.1f}° - {np.rad2deg(np.max(alpha_history)):.1f}°")
        print(f"   升力系数范围: {np.min(cl_history):.2f} - {np.max(cl_history):.2f}")
        print(f"   动态失速峰值: {max_cl_dynamic:.2f} (静态: {static_cl_max:.2f})")
        print(f"   迟滞环宽度: {hysteresis_width:.3f}" if 'hysteresis_width' in locals() else "   迟滞环: 检测中")
        print("   ✅ 动态失速迟滞环特性测试通过")
    
    def test_vortex_shedding_modeling(self):
        """测试涡脱落和对流建模"""
        print("\n📋 测试3: 涡脱落和对流建模")
        
        # 模拟突然失速条件
        alpha_initial = np.radians(5.0)   # 初始攻角
        alpha_final = np.radians(25.0)    # 失速攻角
        alpha_dot = np.radians(100.0)     # 快速攻角变化
        
        velocity = 60.0
        dt = 0.005
        n_steps = 200
        
        # 存储涡相关状态
        vortex_states_history = []
        vortex_strength_history = []
        
        for i in range(n_steps):
            time = i * dt
            
            # 阶跃攻角变化
            if time < 0.1:
                alpha = alpha_initial
                alpha_dot_current = 0.0
            elif time < 0.2:
                alpha = alpha_initial + (alpha_final - alpha_initial) * (time - 0.1) / 0.1
                alpha_dot_current = alpha_dot
            else:
                alpha = alpha_final
                alpha_dot_current = 0.0
            
            # 计算动态失速
            result = self.lb_model.compute_dynamic_stall(
                alpha, alpha_dot_current, velocity, time, dt
            )
            
            # 获取状态信息
            state_info = self.lb_model.get_state_info()
            states = state_info['states']
            
            # 存储涡相关状态
            vortex_states = {
                'X_v1': states['X_v1'],
                'X_v2': states['X_v2'],
                'X_v3': states['X_v3'],
                'X_v4': states['X_v4']
            }
            vortex_states_history.append(vortex_states)
            vortex_strength_history.append(result.get('vortex_strength', 0))
        
        # 验证涡脱落特性
        
        # 1. 涡强度应该在失速后增长
        pre_stall_strength = np.mean(vortex_strength_history[:20])   # 失速前
        post_stall_strength = np.max(vortex_strength_history[40:80]) # 失速后
        
        self.assertGreater(post_stall_strength, pre_stall_strength * 2,
                          f"涡强度增长不足: {post_stall_strength:.3f} vs {pre_stall_strength:.3f}")
        
        # 2. 涡状态应该显示对流特性
        X_v1_history = [states['X_v1'] for states in vortex_states_history]
        X_v4_history = [states['X_v4'] for states in vortex_states_history]
        
        # X_v1应该先增长（涡生成）
        max_X_v1_idx = np.argmax(X_v1_history)
        self.assertGreater(max_X_v1_idx, 20, "涡生成时机不正确")
        
        # X_v4应该延迟增长（涡对流）
        max_X_v4_idx = np.argmax(X_v4_history)
        self.assertGreater(max_X_v4_idx, max_X_v1_idx,
                          "涡对流延迟不正确")
        
        # 3. 验证涡强度的物理合理性
        max_vortex_strength = np.max(vortex_strength_history)
        self.assertLess(max_vortex_strength, 1.0,
                       f"涡强度过大: {max_vortex_strength:.3f}")
        
        self.assertGreater(max_vortex_strength, 0.01,
                          f"涡强度过小: {max_vortex_strength:.3f}")
        
        print(f"   失速前涡强度: {pre_stall_strength:.4f}")
        print(f"   失速后最大涡强度: {post_stall_strength:.4f}")
        print(f"   涡强度增长: {post_stall_strength/pre_stall_strength:.1f}x")
        print(f"   涡生成时刻: {max_X_v1_idx*dt:.3f}s")
        print(f"   涡对流时刻: {max_X_v4_idx*dt:.3f}s")
        print("   ✅ 涡脱落和对流建模测试通过")
    
    def test_experimental_data_comparison(self):
        """测试与实验数据对比验证"""
        print("\n📋 测试4: 与实验数据对比验证")
        
        # 使用经典的NACA0012动态失速实验数据进行验证
        # 参考: McCroskey et al. (1982) 的实验数据
        
        # 实验条件
        alpha_mean = np.radians(15.0)
        alpha_amp = np.radians(10.0)
        reduced_freq = 0.15
        
        # 重新配置模型为NACA0012
        naca0012_model = CompleteLeishmanBeddoesModel(
            airfoil_params={
                'name': 'NACA0012',
                'alpha_0': 0.0,
                'cl_alpha': 2*np.pi,
                'alpha_ss': np.radians(15.0),
                'cl_max': 1.43,
                'cd_0': 0.0115,
                'cm_0': 0.0
            },
            chord=0.1,
            mach_number=0.3
        )
        
        # 模拟实验条件
        dt = 0.01
        n_steps = int(4 * np.pi / (reduced_freq * dt))  # 2个周期
        
        velocity = 50.0
        cl_results = []
        alpha_results = []
        
        for i in range(n_steps):
            time = i * dt
            alpha = alpha_mean + alpha_amp * np.sin(reduced_freq * time)
            alpha_dot = alpha_amp * reduced_freq * np.cos(reduced_freq * time)
            
            result = naca0012_model.compute_dynamic_stall(
                alpha, alpha_dot, velocity, time, dt
            )
            
            cl_results.append(result['cl'])
            alpha_results.append(alpha)
        
        cl_results = np.array(cl_results)
        alpha_results = np.array(alpha_results)
        
        # 与实验数据的关键特征对比
        
        # 1. 最大升力系数
        max_cl_computed = np.max(cl_results)
        expected_max_cl = 2.2  # 实验观测的动态失速峰值
        
        relative_error = abs(max_cl_computed - expected_max_cl) / expected_max_cl
        self.assertLess(relative_error, 0.3,
                       f"最大升力系数误差过大: {relative_error*100:.1f}%")
        
        # 2. 失速延迟角度
        static_stall_alpha = np.radians(15.0)
        stall_indices = np.where(cl_results > 1.43)[0]  # 超过静态失速值
        
        if len(stall_indices) > 0:
            dynamic_stall_alpha = alpha_results[stall_indices[0]]
            stall_delay = np.rad2deg(dynamic_stall_alpha - static_stall_alpha)
            
            # 实验显示失速延迟约3-5度
            self.assertGreater(stall_delay, 1.0,
                              f"失速延迟不足: {stall_delay:.1f}°")
            self.assertLess(stall_delay, 8.0,
                           f"失速延迟过大: {stall_delay:.1f}°")
        
        # 3. 迟滞环面积（定性验证）
        # 计算升力系数的变化范围
        cl_range = np.max(cl_results) - np.min(cl_results)
        self.assertGreater(cl_range, 1.5,
                          f"升力系数变化范围不足: {cl_range:.2f}")
        
        print(f"   最大升力系数: {max_cl_computed:.2f} (实验: ~{expected_max_cl:.1f})")
        print(f"   相对误差: {relative_error*100:.1f}%")
        if 'stall_delay' in locals():
            print(f"   失速延迟: {stall_delay:.1f}° (实验: 3-5°)")
        print(f"   升力系数范围: {cl_range:.2f}")
        print("   ✅ 实验数据对比验证通过")
    
    def test_unsteady_response_characteristics(self):
        """测试非定常响应特性"""
        print("\n📋 测试5: 非定常响应特性")
        
        # 测试不同频率下的响应
        frequencies = [0.05, 0.1, 0.2, 0.5]  # 无量纲频率
        alpha_mean = np.radians(12.0)
        alpha_amp = np.radians(8.0)
        
        frequency_responses = {}
        
        for freq in frequencies:
            # 重置模型状态
            self.lb_model.reset_states()
            
            dt = 0.005
            n_steps = int(4 * np.pi / (freq * dt))  # 2个周期
            
            velocity = 50.0
            cl_max_list = []
            
            for i in range(n_steps):
                time = i * dt
                alpha = alpha_mean + alpha_amp * np.sin(freq * time)
                alpha_dot = alpha_amp * freq * np.cos(freq * time)
                
                result = self.lb_model.compute_dynamic_stall(
                    alpha, alpha_dot, velocity, time, dt
                )
                
                cl_max_list.append(result['cl'])
            
            max_cl = np.max(cl_max_list)
            min_cl = np.min(cl_max_list)
            
            frequency_responses[freq] = {
                'max_cl': max_cl,
                'min_cl': min_cl,
                'cl_range': max_cl - min_cl
            }
        
        # 验证频率效应
        
        # 1. 高频下动态效应应该更强
        low_freq_range = frequency_responses[frequencies[0]]['cl_range']
        high_freq_range = frequency_responses[frequencies[-1]]['cl_range']
        
        # 注意：在某些情况下，极高频率可能导致响应减弱
        # 这里验证中等频率范围内的趋势
        mid_low_freq_range = frequency_responses[frequencies[1]]['cl_range']
        mid_high_freq_range = frequency_responses[frequencies[2]]['cl_range']
        
        self.assertGreater(mid_high_freq_range, mid_low_freq_range * 0.8,
                          "频率效应不明显")
        
        # 2. 验证最大升力系数的频率依赖性
        max_cl_values = [resp['max_cl'] for resp in frequency_responses.values()]
        
        # 应该存在频率相关的变化
        max_cl_range = max(max_cl_values) - min(max_cl_values)
        self.assertGreater(max_cl_range, 0.1,
                          f"频率对最大升力系数影响不足: {max_cl_range:.3f}")
        
        print("   频率响应特性:")
        for freq, response in frequency_responses.items():
            print(f"     k={freq:.2f}: Cl_max={response['max_cl']:.2f}, "
                  f"范围={response['cl_range']:.2f}")
        
        print(f"   频率效应强度: {max_cl_range:.3f}")
        print("   ✅ 非定常响应特性测试通过")
    
    def test_physical_reasonableness_check(self):
        """测试物理合理性检查"""
        print("\n📋 测试6: 物理合理性检查")
        
        # 测试各种物理约束
        test_conditions = [
            # (alpha, alpha_dot, description)
            (np.radians(5.0), 0.0, "小攻角静态"),
            (np.radians(20.0), 0.0, "大攻角静态"),
            (np.radians(10.0), np.radians(50.0), "正攻角变化率"),
            (np.radians(10.0), np.radians(-50.0), "负攻角变化率"),
            (np.radians(0.0), np.radians(100.0), "零攻角快速变化"),
        ]
        
        velocity = 50.0
        dt = 0.01
        
        for alpha, alpha_dot, description in test_conditions:
            # 重置状态
            self.lb_model.reset_states()
            
            # 计算多个时间步以建立历史
            for i in range(10):
                time = i * dt
                result = self.lb_model.compute_dynamic_stall(
                    alpha, alpha_dot, velocity, time, dt
                )
            
            # 验证物理合理性
            
            # 1. 系数值应该有限
            self.assertTrue(np.isfinite(result['cl']), f"{description}: 升力系数无效")
            self.assertTrue(np.isfinite(result['cd']), f"{description}: 阻力系数无效")
            self.assertTrue(np.isfinite(result['cm']), f"{description}: 力矩系数无效")
            
            # 2. 阻力系数应该为正
            self.assertGreater(result['cd'], 0, f"{description}: 阻力系数应为正")
            
            # 3. 升力系数应该在合理范围内
            self.assertGreater(result['cl'], -3.0, f"{description}: 升力系数过小")
            self.assertLess(result['cl'], 4.0, f"{description}: 升力系数过大")
            
            # 4. 阻力系数应该在合理范围内
            self.assertLess(result['cd'], 2.0, f"{description}: 阻力系数过大")
            
            # 5. 状态变量应该有界
            state_info = self.lb_model.get_state_info()
            states = state_info['states']
            
            for state_name, state_value in states.items():
                self.assertTrue(np.isfinite(state_value),
                               f"{description}: 状态{state_name}无效")
                self.assertLess(abs(state_value), 10.0,
                               f"{description}: 状态{state_name}过大: {state_value}")
            
            print(f"     {description}: Cl={result['cl']:.3f}, Cd={result['cd']:.4f}, "
                  f"Cm={result['cm']:.4f} ✅")
        
        print("   ✅ 物理合理性检查通过")


def run_complete_dynamic_stall_tests():
    """运行完整动态失速测试"""
    
    print("🚀 开始完整动态失速测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCompleteDynamicStall)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # 统计结果
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"\n📊 完整动态失速测试结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed}")
    print(f"   失败: {failures}")
    print(f"   错误: {errors}")
    print(f"   通过率: {(passed/total_tests)*100:.1f}%")
    
    # 详细失败信息
    if failures or errors:
        print(f"\n❌ 失败详情:")
        for test, traceback in result.failures + result.errors:
            print(f"   {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    success = (failures == 0 and errors == 0)
    print(f"\n🎯 完整动态失速测试: {'✅ 通过' if success else '❌ 失败'}")
    
    return {
        'success': success,
        'total': total_tests,
        'passed': passed,
        'failed': failures + errors,
        'details': {
            'failures': result.failures,
            'errors': result.errors
        }
    }


if __name__ == "__main__":
    results = run_complete_dynamic_stall_tests()
    
    if results['success']:
        print("\n🎉 所有完整动态失速测试通过！")
    else:
        print(f"\n⚠️  {results['failed']}/{results['total']} 测试失败")
        sys.exit(1)
