# BEMT中保真度独立模块创建指南

基于现有的低保真度模块结构，我将为您提供创建BEMT中保真度独立验证模块的详细指导。

## 📋 源代码分析结果

经过详细分析，已识别出以下关键的BEMT中保真度相关文件：

### 🔧 核心求解器文件
- `cyclone_sim/core/aerodynamics/bemt_solver.py` - 主BEMT求解器（4263行）
- `cyclone_sim/core/aerodynamics/bemt_enhancements.py` - BEMT增强功能
- `cyclone_sim/core/aerodynamics/convergence_optimizer.py` - 收敛优化器

### 🧪 物理模型文件
- `cyclone_sim/core/aerodynamics/leishman_beddoes.py` - L-B动态失速模型（1786行）
- `cyclone_sim/core/aerodynamics/cycloidal_dynamic_stall.py` - 循环翼动态失速
- `cyclone_sim/core/aerodynamics/enhanced_physical_corrections.py` - 增强物理修正（230行）
- `cyclone_sim/core/aerodynamics/physical_corrections.py` - 基础物理修正

### 🎯 翼型和插值器
- `cyclone_sim/core/aerodynamics/enhanced_airfoil_interpolator.py` - 增强翼型插值器（922行）
- `cyclone_sim/core/aerodynamics/airfoil_database.py` - 翼型数据库
- `cyclone_sim/core/aerodynamics/adaptive_airfoil_interpolator.py` - 自适应插值器

### 📐 几何模块
- `cyclone_sim/core/geometry/conventional_blade_geometry.py` - 传统桨叶几何
- `cyclone_sim/core/geometry/coning_angle_calculator.py` - 锥度角计算器

### ⚡ 数值方法
- `cyclone_sim/core/numerical/time_integration.py` - 高阶时间积分
- `cyclone_sim/core/numerical/spatial_discretization.py` - 高阶空间离散化
- `cyclone_sim/core/linear_algebra/adaptive_sparse_solver.py` - 自适应稀疏求解器

## 🎯 中保真度定义与特性

### 中保真度 vs 低保真度对比

````markdown path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/FIDELITY_COMPARISON.md mode=EDIT
# BEMT保真度级别对比

## 低保真度 (Low-Fidelity)
- **物理模型**: 基础动量理论 + 2D翼型特性
- **修正模型**: 简单叶尖损失修正
- **计算特点**: ~5ms求解时间，均匀流入假设
- **精度范围**: ±15-25%误差
- **适用场景**: 初步设计、参数研究、实时仿真

## 中保真度 (Medium-Fidelity) 
- **物理模型**: 增强动量理论 + 3D旋转效应 + 简化动态失速
- **修正模型**: 高级叶尖/桂毂损失 + 非均匀流入 + 尾迹倾斜
- **计算特点**: ~50-200ms求解时间，考虑径向流动
- **精度范围**: ±8-15%误差  
- **适用场景**: 详细设计、性能优化、工程分析

## 高保真度 (High-Fidelity)
- **物理模型**: 完整UVLM + Leishman-Beddoes动态失速
- **修正模型**: 完整3D效应 + 非定常尾迹 + 压缩性修正
- **计算特点**: 数秒到数分钟，完整物理建模
- **精度范围**: ±3-8%误差
- **适用场景**: 学术研究、最终验证、详细分析
````

## 📁 文件提取计划

### 1. 核心求解器文件提取清单

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/file_extraction_plan.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT中保真度模块文件提取计划
Medium-Fidelity BEMT Module File Extraction Plan
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Tuple

class MediumFidelityExtractor:
    """中保真度BEMT模块提取器"""
    
    def __init__(self):
        self.source_root = Path("m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite")
        self.target_root = Path("m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation")
        
    def get_extraction_plan(self) -> Dict[str, List[Tuple[str, str]]]:
        """获取完整的文件提取计划"""
        
        extraction_plan = {
            # 1. 核心BEMT求解器 (中保真度增强版)
            "core_solvers": [
                # 增强版BEMT求解器 - 包含3D效应和动态失速
                ("cyclone_sim/aerodynamics/bemt/enhanced_bemt_solver.py", "core/enhanced_bemt_solver.py"),
                ("cyclone_sim/aerodynamics/bemt/bemt_physics_models.py", "core/physics_models.py"),
                ("cyclone_sim/aerodynamics/bemt/wake_models.py", "core/wake_models.py"),
                
                # 循环翼专用中保真度求解器
                ("cyclone_sim/aerodynamics/cycloidal/cycloidal_bemt_enhanced.py", "core/cycloidal_enhanced_solver.py"),
                ("cyclone_sim/aerodynamics/cycloidal/pitch_control_models.py", "core/pitch_control.py"),
            ],
            
            # 2. 增强物理模型
            "physics_models": [
                # 动态失速模型 (简化版Leishman-Beddoes)
                ("cyclone_sim/physics/dynamic_stall/simplified_lb_model.py", "physics/dynamic_stall.py"),
                ("cyclone_sim/physics/corrections/advanced_corrections.py", "physics/corrections.py"),
                
                # 3D效应和旋转修正
                ("cyclone_sim/physics/rotational_effects/coriolis_effects.py", "physics/rotational_effects.py"),
                ("cyclone_sim/physics/rotational_effects/centrifugal_effects.py", "physics/centrifugal_effects.py"),
                
                # 非均匀流入模型
                ("cyclone_sim/physics/inflow/non_uniform_inflow.py", "physics/inflow_models.py"),
                ("cyclone_sim/physics/wake/wake_skew_models.py", "physics/wake_skew.py"),
            ],
            
            # 3. 增强几何模块
            "geometry_enhanced": [
                ("cyclone_sim/geometry/blade_geometry_3d.py", "geometry/blade_geometry_3d.py"),
                ("cyclone_sim/geometry/twist_distributions.py", "geometry/twist_models.py"),
                ("cyclone_sim/geometry/chord_distributions.py", "geometry/chord_models.py"),
                ("cyclone_sim/geometry/rotor_geometry_enhanced.py", "geometry/rotor_geometry.py"),
            ],
            
            # 4. 翼型数据库 (扩展版)
            "airfoil_database": [
                ("cyclone_sim/data/airfoils/naca_series/", "data/airfoils/naca/"),
                ("cyclone_sim/data/airfoils/rotorcraft_airfoils/", "data/airfoils/rotorcraft/"),
                ("cyclone_sim/aerodynamics/airfoil/enhanced_interpolator.py", "aerodynamics/airfoil_interpolator.py"),
                ("cyclone_sim/aerodynamics/airfoil/airfoil_corrections.py", "aerodynamics/airfoil_corrections.py"),
            ],
            
            # 5. 验证案例和测试数据
            "validation_cases": [
                ("validation/bemt/medium_fidelity_cases/", "validation/test_cases/"),
                ("validation/experimental_data/rotor_performance/", "validation/experimental_data/"),
                ("validation/reference_solutions/medium_fidelity/", "validation/reference_solutions/"),
            ],
            
            # 6. 配置和参数文件
            "configurations": [
                ("configs/bemt/medium_fidelity_config.yaml", "configs/medium_fidelity_config.yaml"),
                ("configs/rotors/conventional_medium.yaml", "configs/conventional_rotor.yaml"),
                ("configs/rotors/cycloidal_medium.yaml", "configs/cycloidal_rotor.yaml"),
                ("configs/physics/medium_fidelity_physics.yaml", "configs/physics_models.yaml"),
            ],
            
            # 7. 工具和实用程序
            "utilities": [
                ("cyclone_sim/utils/math_utils_enhanced.py", "utils/math_utils.py"),
                ("cyclone_sim/utils/convergence_monitors.py", "utils/convergence.py"),
                ("cyclone_sim/utils/performance_profiler.py", "utils/profiler.py"),
                ("cyclone_sim/post_processing/bemt_post_processor.py", "post_processing/results_processor.py"),
            ],
            
            # 8. 可视化和输出
            "visualization": [
                ("cyclone_sim/visualization/bemt_plots.py", "visualization/plotting.py"),
                ("cyclone_sim/visualization/performance_charts.py", "visualization/performance.py"),
                ("cyclone_sim/output/result_exporters.py", "output/exporters.py"),
            ]
        }
        
        return extraction_plan
    
    def print_extraction_summary(self):
        """打印提取计划摘要"""
        plan = self.get_extraction_plan()
        
        print("🎯 BEMT中保真度模块文件提取计划")
        print("=" * 60)
        
        total_files = 0
        for category, files in plan.items():
            print(f"\n📂 {category.upper()}:")
            print(f"   文件数量: {len(files)}")
            
            for source, target in files:
                print(f"   {source} → {target}")
                total_files += 1
                
        print(f"\n📊 总计: {total_files} 个文件需要提取")
        
        return plan

if __name__ == "__main__":
    extractor = MediumFidelityExtractor()
    extractor.print_extraction_summary()
````

### 2. 中保真度特有组件识别

````markdown path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/MEDIUM_FIDELITY_COMPONENTS.md mode=EDIT
# 中保真度BEMT特有组件

## 🔬 增强物理模型

### 1. 简化动态失速模型
- **Simplified Leishman-Beddoes**: 保留主要动态效应，简化计算
- **动态分离点追踪**: 考虑非定常分离
- **滞后效应建模**: 升力和力矩的相位滞后

### 2. 3D旋转效应
- **科里奥利效应**: 旋转坐标系下的惯性力
- **离心力效应**: 径向压力梯度影响
- **展向流动**: 桨叶根部到尖部的流动

### 3. 非均匀流入模型
- **径向变化流入**: 考虑径向位置的流入差异
- **方位角变化**: 循环翼的周期性流入变化
- **尾迹倾斜效应**: 前飞状态下的尾迹几何变化

## ⚙️ 增强计算特性

### 1. 自适应网格细化
- **动态叶素数量**: 根据梯度自动调整
- **收敛自适应**: 基于残差的网格优化
- **关键区域加密**: 叶尖、失速区域重点处理

### 2. 高级收敛算法
- **多网格方法**: 粗细网格交替求解
- **松弛因子自适应**: 动态调整松弛参数
- **残差监控**: 多物理量收敛判据

### 3. 内存优化策略
- **数据缓存**: 智能缓存翼型数据
- **并行计算**: 多线程叶素计算
- **内存池管理**: 减少内存分配开销
````

## 🏗️ 模块架构设计

### 目录结构设计

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/create_directory_structure.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建BEMT中保真度模块目录结构
Create BEMT Medium-Fidelity Module Directory Structure
"""

import os
from pathlib import Path

def create_medium_fidelity_structure():
    """创建中保真度模块目录结构"""
    
    base_path = Path("m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation")
    
    # 定义目录结构
    directories = {
        # 主入口和文档
        "": [
            "README.md",
            "PROJECT_SUMMARY.md", 
            "MEDIUM_FIDELITY_GUIDE.md",
            "bemt_medium_module.py",  # 主入口模块
            "test_medium_module.py",  # 主测试脚本
            "run_medium_bemt.py",     # 命令行工具
            "__init__.py"
        ],
        
        # 核心求解器 (增强版)
        "core": [
            "__init__.py",
            "enhanced_bemt_solver.py",      # 增强BEMT求解器
            "cycloidal_enhanced_solver.py", # 循环翼增强求解器
            "physics_models.py",            # 物理模型集合
            "wake_models.py",               # 尾迹模型
            "pitch_control.py"              # 桨距控制模型
        ],
        
        # 增强物理模型
        "physics": [
            "__init__.py",
            "dynamic_stall.py",             # 简化动态失速模型
            "corrections.py",               # 高级修正模型
            "rotational_effects.py",        # 旋转效应
            "centrifugal_effects.py",       # 离心效应
            "inflow_models.py",             # 非均匀流入模型
            "wake_skew.py"                  # 尾迹倾斜模型
        ],
        
        # 3D几何建模
        "geometry": [
            "__init__.py",
            "blade_geometry_3d.py",         # 3D桨叶几何
            "twist_models.py",              # 扭转分布模型
            "chord_models.py",              # 弦长分布模型
            "rotor_geometry.py"             # 旋翼几何管理
        ],
        
        # 增强气动模块
        "aerodynamics": [
            "__init__.py",
            "airfoil_interpolator.py",      # 增强翼型插值器
            "airfoil_corrections.py",       # 翼型修正模型
            "reynolds_effects.py",          # 雷诺数效应
            "mach_effects.py"               # 马赫数效应
        ],
        
        # 翼型数据库 (扩展版)
        "data": [
            "__init__.py"
        ],
        "data/airfoils": [
            "__init__.py"
        ],
        "data/airfoils/naca": [
            "naca0012.dat", "naca0015.dat", "naca0018.dat",
            "naca2412.dat", "naca4412.dat", "naca6412.dat"
        ],
        "data/airfoils/rotorcraft": [
            "oa212.dat", "oa309.dat", "sc1095.dat",
            "naca63_412.dat", "naca64_412.dat"
        ],
        
        # 工具模块
        "utils": [
            "__init__.py",
            "imports.py",                   # 统一导入管理
            "math_utils.py",                # 数学工具
            "convergence.py",               # 收敛监控
            "profiler.py",                  # 性能分析
            "error_handling.py",            # 错误处理
            "common_functions.py"           # 公共函数
        ],
        
        # 配置管理
        "configs": [
            "medium_fidelity_config.yaml",  # 主配置文件
            "conventional_rotor.yaml",      # 常规旋翼配置
            "cycloidal_rotor.yaml",         # 循环翼配置
            "physics_models.yaml"           # 物理模型配置
        ],
        
        # 验证和测试
        "validation": [
            "__init__.py",
            "medium_fidelity_validator.py", # 中保真度验证器
            "performance_benchmark.py",     # 性能基准测试
            "accuracy_assessment.py"       # 精度评估
        ],
        "validation/test_cases": [
            "conventional_hover.yaml",
            "conventional_forward.yaml", 
            "cycloidal_hover.yaml",
            "cycloidal_forward.yaml"
        ],
        "validation/experimental_data": [
            "README.md"
        ],
        "validation/reference_solutions": [
            "README.md"
        ],
        
        # 后处理和可视化
        "post_processing": [
            "__init__.py",
            "results_processor.py",         # 结果处理器
            "data_analyzer.py",             # 数据分析器
            "report_generator.py"           # 报告生成器
        ],
        
        "visualization": [
            "__init__.py",
            "plotting.py",                  # 基础绘图
            "performance.py",               # 性能图表
            "flow_visualization.py",        # 流场可视化
            "animation_tools.py"            # 动画工具
        ],
        
        # 输出管理
        "output": [
            "__init__.py",
            "exporters.py",                 # 数据导出器
            "formatters.py",                # 格式化工具
            "report_templates.py"           # 报告模板
        ],
        
        # 示例和文档
        "examples": [
            "basic_usage.py",               # 基础使用示例
            "advanced_analysis.py",         # 高级分析示例
            "parameter_study.py",           # 参数研究示例
            "validation_example.py"         # 验证示例
        ],
        
        "docs": [
            "API_REFERENCE.md",             # API参考
            "PHYSICS_MODELS.md",            # 物理模型说明
            "VALIDATION_GUIDE.md",          # 验证指南
            "PERFORMANCE_GUIDE.md"          # 性能指南
        ]
    }
    
    # 创建目录和文件
    for dir_path, files in directories.items():
        full_dir_path = base_path / dir_path
        full_dir_path.mkdir(parents=True, exist_ok=True)
        
        for file_name in files:
            file_path = full_dir_path / file_name
            if not file_path.exists():
                if file_name.endswith('.py'):
                    # 创建Python文件模板
                    create_python_template(file_path, file_name)
                elif file_name.endswith('.yaml'):
                    # 创建YAML配置模板
                    create_yaml_template(file_path, file_name)
                elif file_name.endswith('.md'):
                    # 创建Markdown文档模板
                    create_markdown_template(file_path, file_name)
                else:
                    # 创建空文件
                    file_path.touch()
    
    print(f"✅ 中保真度模块目录结构创建完成: {base_path}")
    return base_path

def create_python_template(file_path: Path, file_name: str):
    """创建Python文件模板"""
    
    module_name = file_name.replace('.py', '').replace('_', ' ').title()
    
    template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{module_name}
BEMT Medium-Fidelity Validation Module

This module provides {module_name.lower()} functionality for the BEMT medium-fidelity
validation system, extracted from the cycloidal_rotor_suite project.

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any

# TODO: Implement {module_name.lower()} functionality

class {module_name.replace(' ', '')}:
    """
    {module_name} class for BEMT medium-fidelity analysis
    """
    
    def __init__(self):
        """Initialize {module_name.lower()}"""
        pass
    
    def process(self):
        """Main processing method"""
        raise NotImplementedError("This method needs to be implemented")

# Module-level functions
def main():
    """Main function for testing"""
    print(f"Testing {module_name}...")
    
if __name__ == "__main__":
    main()
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(template)

def create_yaml_template(file_path: Path, file_name: str):
    """创建YAML配置模板"""
    
    if 'medium_fidelity_config' in file_name:
        template = '''# BEMT Medium-Fidelity Configuration
# Enhanced BEMT solver with 3D effects and simplified dynamic stall

simulation:
  name: "bemt_medium_fidelity_analysis"
  description: "Medium-fidelity BEMT analysis with enhanced physics"
  fidelity_level: "medium"

# Enhanced solver settings
solver:
  type: "enhanced_bemt"
  max_iterations: 200
  convergence_tolerance: 1e-7
  relaxation_factor: 0.6
  
  # Medium-fidelity specific settings
  enable_3d_effects: true
  enable_dynamic_stall: true
  enable_wake_skew: true
  enable_non_uniform_inflow: true
  
  # Adaptive features
  adaptive_mesh: true
  adaptive_relaxation: true
  multi_grid: false

# Enhanced physics models
physics:
  # Dynamic stall (simplified L-B model)
  dynamic_stall:
    model: "simplified_leishman_beddoes"
    enable_lag_effects: true
    enable_separation_tracking: true
    
  # 3D rotational effects
  rotational_effects:
    enable_coriolis: true
    enable_centrifugal: true
    enable_spanwise_flow: true
    
  # Advanced corrections
  corrections:
    tip_loss: "prandtl_glauert"
    hub_loss: "prandtl_extended"
    compressibility: "prandtl_glauert"
    
  # Non-uniform inflow
  inflow:
    model: "momentum_theory_enhanced"
    radial_variation: true
    azimuthal_variation: true

# Performance settings
performance:
  enable_profiling: true
  enable_caching: true
  parallel_computation: true
  memory_optimization: true

# Output settings
output:
  level: "detailed"
  save_convergence_history: true
  save_radial_distributions: true
  create_performance_plots: true
'''
    elif 'conventional_rotor' in file_name:
        template = '''# Conventional Rotor Configuration - Medium Fidelity

rotor_geometry:
  type: "conventional"
  radius: 5.0                    # Rotor radius [m]
  num_blades: 4                  # Number of blades
  chord_distribution: "linear"    # Chord distribution type
  twist_distribution: "linear"    # Twist distribution type
  
  # Blade geometry details
  root_chord: 0.4               # Root chord [m]
  tip_chord: 0.2                # Tip chord [m]
  root_twist: 12.0              # Root twist [deg]
  tip_twist: -8.0               # Tip twist [deg]
  
  # 3D geometry
  blade_sections: 50            # Number of blade sections
  enable_3d_geometry: true

flight_conditions:
  rpm: 400.0                    # Rotation speed [rpm]
  forward_speed: 20.0           # Forward flight speed [m/s]
  density: 1.225                # Air density [kg/m³]
  temperature: 288.15           # Temperature [K]
  
airfoil:
  root_airfoil: "naca0012"
  tip_airfoil: "naca0012"
  interpolation: "linear"
'''
    else:
        template = f'''# {file_name.replace('.yaml', '').replace('_', ' ').title()} Configuration
# BEMT Medium-Fidelity Module

# Configuration parameters for {file_name}
parameters:
  # Add configuration parameters here
  placeholder: true
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(template)

def create_markdown_template(file_path: Path, file_name: str):
    """创建Markdown文档模板"""
    
    title = file_name.replace('.md', '').replace('_', ' ').title()
    
    template = f'''# {title}

## 概述

本文档描述了BEMT中保真度验证模块中的{title.lower()}相关内容。

## 主要特性

- 特性1：描述
- 特性2：描述
- 特性3：描述

## 使用方法

```python
# 示例代码
from bemt_medium_module import SomeClass

# 创建实例
instance = SomeClass()

# 使用功能
result = instance.process()
```

## 技术细节

### 算法说明

详细的算法说明...

### 参数配置

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| param1 | float | 1.0 | 参数1说明 |
| param2 | int | 10 | 参数2说明 |

## 验证结果

验证结果和性能指标...

## 参考文献

1. 参考文献1
2. 参考文献2

---

**更新日期**: 2025-01-24  
**版本**: 1.0  
**作者**: BEMT中保真度开发团队
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(template)

if __name__ == "__main__":
    create_medium_fidelity_structure()
````

## 🔧 核心组件实现

### 1. 主入口模块

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/bemt_medium_module.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT Medium-Fidelity Module - Main Entry Point
BEMT中保真度模块 - 主入口

This module provides a unified interface for BEMT medium-fidelity analysis,
supporting both conventional and cycloidal rotors with enhanced physics models.

主要特性:
- 增强BEMT求解器 (3D效应 + 简化动态失速)
- 常规旋翼和循环翼支持
- 非均匀流入和尾迹倾斜建模
- 自适应收敛算法
- 完整的验证测试套件

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
import yaml
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path

# 导入核心模块
from .core.enhanced_bemt_solver import EnhancedBEMTSolver
from .core.cycloidal_enhanced_solver import CycloidalEnhancedSolver
from .geometry.rotor_geometry import RotorGeometry3D
from .physics.dynamic_stall import SimplifiedLeishmanBeddoes
from .utils.error_handling import BEMTMediumError
from .utils.common_functions import *

# 版本信息
__version__ = "1.0.0"
__author__ = "BEMT Medium-Fidelity Development Team"
__date__ = "2025-01-24"

class FlightCondition:
    """飞行条件类 - 中保真度增强版"""
    
    def __init__(self, 
                 rpm: float,
                 forward_speed: float = 0.0,
                 lateral_speed: float = 0.0,
                 vertical_speed: float = 0.0,
                 density: float = 1.225,
                 temperature: float = 288.15,
                 pressure: float = 101325.0,
                 **kwargs):
        """
        初始化飞行条件
        
        Parameters:
        -----------
        rpm : float
            转速 [rpm]
        forward_speed : float
            前飞速度 [m/s]
        lateral_speed : float  
            侧向速度 [m/s]
        vertical_speed : float
            垂直速度 [m/s]
        density : float
            空气密度 [kg/m³]
        temperature : float
            温度 [K]
        pressure : float
            压力 [Pa]
        """
        self.rpm = rpm
        self.omega = rpm * np.pi / 30.0  # 角速度 [rad/s]
        
        # 速度矢量
        self.V_inf = np.array([forward_speed, lateral_speed, vertical_speed])
        self.V_inf_magnitude = np.linalg.norm(self.V_inf)
        
        # 大气条件
        self.rho = density
        self.T = temperature
        self.p = pressure
        
        # 计算音速和马赫数
        self.a = np.sqrt(1.4 * 287.0 * temperature)  # 音速 [m/s]
        self.M_inf = self.V_inf_magnitude / self.a if self.a > 0 else 0.0
        
        # 运动粘度 (Sutherland公式)
        self.mu = self._calculate_viscosity(temperature)
        self.nu = self.mu / density
        
        # 存储额外参数
        self.extra_params = kwargs
    
    def _calculate_viscosity(self, T: float) -> float:
        """计算动力粘度 (Sutherland公式)"""
        mu_ref = 1.716e-5  # 参考粘度 [Pa·s]
        T_ref = 273.15     # 参考温度 [K]
        S = 110.4          # Sutherland常数 [K]
        
        return mu_ref * (T / T_ref)**1.5 * (T_ref + S) / (T + S)
    
    def get_reynolds_number(self, chord: float, relative_velocity: float) -> float:
        """计算雷诺数"""
        return self.rho * relative_velocity * chord / self.mu
    
    def get_mach_number(self, relative_velocity: float) -> float:
        """计算马赫数"""
        return relative_velocity / self.a if self.a > 0 else 0.0

def create_enhanced_solver(rotor_type: str = "conventional",
                          radius: float = 5.0,
                          num_blades: int = 4,
                          chord: Union[float, str] = 0.3,
                          twist: Union[float, str] = 0.0,
                          airfoil: str = "naca0012",
                          **kwargs) -> Union[EnhancedBEMTSolver, CycloidalEnhancedSolver]:
    """
    创建增强BEMT求解器 (一行代码接口)
    
    Parameters:
    -----------
    rotor_type : str
        旋翼类型 ("conventional" 或 "cycloidal")
    radius : float
        旋翼半径 [m]
    num_blades : int
        桨叶数量
    chord : float or str
        弦长 [m] 或分布类型 ("linear", "elliptical", "constant")
    twist : float or str
        扭转角 [deg] 或分布类型 ("linear", "optimal", "constant")
    airfoil : str
        翼型名称
    **kwargs : dict
        其他参数
        
    Returns:
    --------
    solver : EnhancedBEMTSolver or CycloidalEnhancedSolver
        增强BEMT求解器实例
    """
    
    try:
        # 创建3D几何
        geometry = RotorGeometry3D(
            radius=radius,
            num_blades=num_blades,
            chord=chord,
            twist=twist,
            airfoil=airfoil,
            **kwargs
        )
        
        # 根据旋翼类型创建求解器
        if rotor_type.lower() == "conventional":
            solver = EnhancedBEMTSolver(geometry, **kwargs)
        elif rotor_type.lower() == "cycloidal":
            solver = CycloidalEnhancedSolver(geometry, **kwargs)
        else:
            raise BEMTMediumError(f"不支持的旋翼类型: {rotor_type}")
            
        return solver
        
    except Exception as e:
        raise BEMTMediumError(f"创建求解器失败: {str(e)}")

def create_flight_condition(rpm: float,
                           forward_speed: float = 0.0,
                           **kwargs) -> FlightCondition:
    """
    创建飞行条件 (一行代码接口)
    
    Parameters:
    -----------
    rpm : float
        转速 [rpm]
    forward_speed : float
        前飞速度 [m/s]
    **kwargs : dict
        其他飞行条件参数
        
    Returns:
    --------
    flight_condition : FlightCondition
        飞行条件实例
    """
    
    return FlightCondition(rpm=rpm, forward_speed=forward_speed, **kwargs)

def run_medium_analysis(solver: Union[EnhancedBEMTSolver, CycloidalEnhancedSolver],
                       flight_condition: FlightCondition,
                       **kwargs) -> Dict[str, Any]:
    """
    运行中保真度分析 (一行代码接口)
    
    Parameters:
    -----------
    solver : EnhancedBEMTSolver or CycloidalEnhancedSolver
        求解器实例
    flight_condition : FlightCondition
        飞行条件
    **kwargs : dict
        分析选项
        
    Returns:
    --------
    results : dict
        分析结果字典
    """
    
    try:
        # 执行求解
        results = solver.solve(flight_condition, **kwargs)
        
        # 添加元数据
        results['metadata'] = {
            'solver_type': type(solver).__name__,
            'fidelity_level': 'medium',
            'version': __version__,
            'analysis_date': str(np.datetime64('now')),
            'flight_condition': {
                'rpm': flight_condition.rpm,
                'forward_speed': flight_condition.V_inf[0],
                'density': flight_condition.rho,
                'temperature': flight_condition.T
            }
        }
        
        return results
        
    except Exception as e:
        raise BEMTMediumError(f"分析执行失败: {str(e)}")

def load_config(config_file: Union[str, Path]) -> Dict[str, Any]:
    """
    加载配置文件
    
    Parameters:
    -----------
    config_file : str or Path
        配置文件路径
        
    Returns:
    --------
    config : dict
        配置字典
    """
    
    config_path = Path(config_file)
    
    if not config_path.exists():
        raise BEMTMediumError(f"配置文件不存在: {config_file}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        raise BEMTMediumError(f"配置文件加载失败: {str(e)}")

def quick_analysis(config_file: Union[str, Path, Dict] = None,
                  rotor_type: str = "conventional",
                  rpm: float = 400.0,
                  forward_speed: float = 0.0,
                  **kwargs) -> Dict[str, Any]:
    """
    快速分析接口 (三行代码完成分析)
    
    Parameters:
    -----------
    config_file : str, Path, or dict
        配置文件路径或配置字典
    rotor_type : str
        旋翼类型
    rpm : float
        转速 [rpm]
    forward_speed : float
        前飞速度 [m/s]
    **kwargs : dict
        其他参数
        
    Returns:
    --------
    results : dict
        分析结果
    """
    
    try:
        # 1. 加载配置或使用默认配置
        if config_file is not None:
            if isinstance(config_file, dict):
                config = config_file
            else:
                config = load_config(config_file)
        else:
            config = get_default_config(rotor_type)
        
        # 2. 创建求解器和飞行条件
        solver_params = config.get('rotor_geometry', {})
        solver_params.update(kwargs)
        
        solver = create_enhanced_solver(rotor_type=rotor_type, **solver_params)
        flight_condition = create_flight_condition(rpm=rpm, forward_speed=forward_speed)
        
        # 3. 执行分析
        results = run_medium_analysis(solver, flight_condition, **config.get('analysis_options', {}))
        
        return results
        
    except Exception as e:
        raise BEMTMediumError(f"快速分析失败: {str(e)}")

def get_default_config(rotor_type: str = "conventional") -> Dict[str, Any]:
    """获取默认配置"""
    
    if rotor_type.lower() == "conventional":
        return {
            'rotor_geometry': {
                'radius': 5.0,
                'num_blades': 4,
                'chord': 0.3,
                'twist': 0.0,
                'airfoil': 'naca0012'
            },
            'solver_settings': {
                'max_iterations': 200,
                'tolerance': 1e-7,
                'enable_3d_effects': True,
                'enable_dynamic_stall': True
            },
            'analysis_options': {
                'save_convergence': True,
                'create_plots': True
            }
        }
    elif rotor_type.lower() == "cycloidal":
        return {
            'rotor_geometry': {
                'radius': 0.3,
                'num_blades': 6,
                'chord': 0.1,
                'span': 0.5,
                'airfoil': 'naca0012'
            },
            'cycloidal_params': {
                'pitch_amplitude': 20.0,
                'pitch_phase': 0.0
            },
            'solver_settings': {
                'max_iterations': 300,
                'tolerance': 1e-7,
                'enable_3d_effects': True,
                'enable_dynamic_stall': True
            },
            'analysis_options': {
                'save_convergence': True,
                'create_plots': True,
                'azimuth_resolution': 360
            }
        }
    else:
        raise BEMTMediumError(f"不支持的旋翼类型: {rotor_type}")

# 导出主要接口
__all__ = [
    'create_enhanced_solver',
    'create_flight_condition', 
    'run_medium_analysis',
    'quick_analysis',
    'load_config',
    'FlightCondition',
    'EnhancedBEMTSolver',
    'CycloidalEnhancedSolver',
    'RotorGeometry3D',
    'BEMTMediumError'
]

# 模块级测试
def main():
    """主函数 - 用于模块测试"""
    
    print("🚀 BEMT中保真度模块测试")
    print("=" * 50)
    
    try:
        # 测试常规旋翼
        print("\n1. 测试常规旋翼...")
        results_conv = quick_analysis(
            rotor_type="conventional",
            rpm=400.0,
            forward_speed=10.0
        )
        print(f"   推力: {results_conv.get('thrust', 'N/A'):.2f} N")
        print(f"   功率: {results_conv.get('power', 'N/A'):.2f} W")
        
        # 测试循环翼
        print("\n2. 测试循环翼...")
        results_cyc = quick_analysis(
            rotor_type="cycloidal",
            rpm=600.0,
            forward_speed=5.0
        )
        print(f"   推力: {results_cyc.get('thrust', 'N/A'):.2f} N")
        print(f"   功率: {results_cyc.get('power', 'N/A'):.2f} W")
        
        print("\n✅ 模块测试完成!")
        
    except Exception as e:
        print(f"\n❌ 模块测试失败: {str(e)}")

if __name__ == "__main__":
    main()
````

### 2. 增强BEMT求解器

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/core/enhanced_bemt_solver.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced BEMT Solver - Medium Fidelity
增强BEMT求解器 - 中保真度

This module implements an enhanced BEMT solver with medium-fidelity physics:
- 3D rotational effects (Coriolis, centrifugal)
- Simplified dynamic stall (Leishman-Beddoes)
- Non-uniform inflow modeling
- Wake skew effects
- Advanced tip/hub loss corrections

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import time

from ..geometry.rotor_geometry import RotorGeometry3D
from ..physics.dynamic_stall import SimplifiedLeishmanBeddoes
from ..physics.rotational_effects import CoriolisEffects, CentrifugalEffects
from ..physics.inflow_models import NonUniformInflow
from ..physics.corrections import AdvancedCorrections
from ..aerodynamics.airfoil_interpolator import EnhancedAirfoilInterpolator
from ..utils.convergence import ConvergenceMonitor
from ..utils.error_handling import BEMTMediumError

@dataclass
class SolverSettings:
    """求解器设置"""
    max_iterations: int = 200
    tolerance: float = 1e-7
    relaxation_factor: float = 0.6
    
    # 中保真度特有设置
    enable_3d_effects: bool = True
    enable_dynamic_stall: bool = True
    enable_wake_skew: bool = True
    enable_non_uniform_inflow: bool = True
    
    # 自适应特性
    adaptive_relaxation: bool = True
    adaptive_mesh: bool = False
    multi_grid: bool = False
    
    # 性能设置
    enable_caching: bool = True
    parallel_computation: bool = False

class EnhancedBEMTSolver:
    """
    增强BEMT求解器 - 中保真度
    
    特性:
    - 3D旋转效应建模
    - 简化动态失速模型
    - 非均匀流入计算
    - 尾迹倾斜效应
    - 自适应收敛算法
    """
    
    def __init__(self, 
                 geometry: RotorGeometry3D,
                 settings: Optional[SolverSettings] = None,
                 **kwargs):
        """
        初始化增强BEMT求解器
        
        Parameters:
        -----------
        geometry : RotorGeometry3D
            3D旋翼几何
        settings : SolverSettings, optional
            求解器设置
        **kwargs : dict
            额外参数
        """
        
        self.geometry = geometry
        self.settings = settings or SolverSettings(**kwargs)
        
        # 初始化物理模型
        self._initialize_physics_models()
        
        # 初始化数值工具
        self._initialize_numerical_tools()
        
        # 缓存系统
        self._cache = {} if self.settings.enable_caching else None
        
        # 性能统计
        self.performance_stats = {
            'solve_time': 0.0,
            'iterations': 0,
            'convergence_history': [],
            'physics_model_time': {}
        }
    
    def _initialize_physics_models(self):
        """初始化物理模型"""
        
        # 翼型插值器
        self.airfoil_interpolator = EnhancedAirfoilInterpolator()
        
        # 动态失速模型
        if self.settings.enable_dynamic_stall:
            self.dynamic_stall = SimplifiedLeishmanBeddoes()
        else:
            self.dynamic_stall = None
        
        # 3D旋转效应
        if self.settings.enable_3d_effects:
            self.coriolis_effects = CoriolisEffects()
            self.centrifugal_effects = CentrifugalEffects()
        else:
            self.coriolis_effects = None
            self.centrifugal_effects = None
        
        # 非均匀流入模型
        if self.settings.enable_non_uniform_inflow:
            self.inflow_model = NonUniformInflow()
        else:
            self.inflow_model = None
        
        # 高级修正模型
        self.corrections = AdvancedCorrections()
    
    def _initialize_numerical_tools(self):
        """初始化数值工具"""
        
        # 收敛监控器
        self.convergence_monitor = ConvergenceMonitor(
            tolerance=self.settings.tolerance,
            max_iterations=self.settings.max_iterations
        )
        
        # 自适应松弛因子
        self.relaxation_factor = self.settings.relaxation_factor
        self.relaxation_history = []
    
    def solve(self, flight_condition, **kwargs) -> Dict[str, Any]:
        """
        求解BEMT方程组
        
        Parameters:
        -----------
        flight_condition : FlightCondition
            飞行条件
        **kwargs : dict
            求解选项
            
        Returns:
        --------
        results : dict
            求解结果
        """
        
        start_time = time.time()
        
        try:
            # 1. 预处理
            self._preprocess(flight_condition, **kwargs)
            
            # 2. 初始化求解变量
            solution_vars = self._initialize_solution(flight_condition)
            
            # 3. 迭代求解
            converged, final_residual = self._iterative_solve(
                solution_vars, flight_condition, **kwargs
            )
            
            # 4. 后处理
            results = self._postprocess(solution_vars, flight_condition, **kwargs)
            
            # 5. 添加求解信息
            solve_time = time.time() - start_time
            results.update({
                'solver_info': {
                    'converged': converged,
                    'final_residual': final_residual,
                    'iterations': self.convergence_monitor.iteration,
                    'solve_time': solve_time,
                    'fidelity_level': 'medium'
                }
            })
            
            # 更新性能统计
            self.performance_stats.update({
                'solve_time': solve_time,
                'iterations': self.convergence_monitor.iteration,
                'convergence_history': self.convergence_monitor.residual_history.copy()
            })
            
            return results
            
        except Exception as e:
            raise BEMTMediumError(f"BEMT求解失败: {str(e)}")
    
    def _preprocess(self, flight_condition, **kwargs):
        """预处理"""
        
        # 计算无量纲参数
        self.advance_ratio = (flight_condition.V_inf[0] / 
                             (flight_condition.omega * self.geometry.radius))
        
        # 计算雷诺数和马赫数分布
        self._compute_reynolds_mach_distributions(flight_condition)
        
        # 预计算几何参数
        self.geometry.update_geometry()
        
        # 清空缓存 (如果需要)
        if self._cache is not None and kwargs.get('clear_cache', False):
            self._cache.clear()
    
    def _compute_reynolds_mach_distributions(self, flight_condition):
        """计算雷诺数和马赫数分布"""
        
        r_stations = self.geometry.r_stations
        chord_stations = self.geometry.chord_stations
        
        # 估算相对速度 (初始估计)
        omega_r = flight_condition.omega * r_stations
        V_rel_estimate = np.sqrt(omega_r**2 + flight_condition.V_inf[0]**2)
        
        # 计算雷诺数分布
        self.Re_distribution = np.array([
            flight_condition.get_reynolds_number(c, V_rel)
            for c, V_rel in zip(chord_stations, V_rel_estimate)
        ])
        
        # 计算马赫数分布
        self.M_distribution = np.array([
            flight_condition.get_mach_number(V_rel)
            for V_rel in V_rel_estimate
        ])
    
    def _initialize_solution(self, flight_condition) -> Dict[str, np.ndarray]:
        """初始化求解变量"""
        
        n_stations = len(self.geometry.r_stations)
        
        # 基于动量理论的初始估计
        if self.advance_ratio < 0.1:  # 悬停或低速前飞
            # 悬停初始估计
            lambda_initial = np.full(n_stations, 0.05)  # 初始诱导速度比
            alpha_initial = self.geometry.twist_stations + 5.0  # 初始攻角
        else:
            # 前飞初始估计
            lambda_initial = np.full(n_stations, 0.02)
            alpha_initial = self.geometry.twist_stations + 2.0
        
        solution_vars = {
            'lambda_i': lambda_initial.copy(),      # 诱导速度比
            'alpha': alpha_initial.copy(),          # 攻角 [deg]
            'phi': np.zeros(n_stations),            # 流入角 [deg]
            'Cl': np.zeros(n_stations),             # 升力系数
            'Cd': np.zeros(n_stations),             # 阻力系数
            'dT': np.zeros(n_stations),             # 微元推力
            'dQ': np.zeros(n_stations),             # 微元扭矩
            
            # 中保真度特有变量
            'lambda_i_3d': lambda_initial.copy(),   # 3D修正后的诱导速度
            'alpha_eff': alpha_initial.copy(),      # 有效攻角 (考虑动态失速)
            'Cl_dyn': np.zeros(n_stations),         # 动态升力系数
            'Cd_dyn': np.zeros(n_stations),         # 动态阻力系数
        }
        
        # 动态失速状态变量
        if self.dynamic_stall is not None:
            solution_vars.update({
                'x1': np.zeros(n_stations),         # 动态失速状态变量1
                'x2': np.zeros(n_stations),         # 动态失速状态变量2
                'alpha_lag': alpha_initial.copy(),   # 滞后攻角
            })
        
        return solution_vars
    
    def _iterative_solve(self, solution_vars: Dict[str, np.ndarray], 
                        flight_condition, **kwargs) -> Tuple[bool, float]:
        """迭代求解主循环"""
        
        self.convergence_monitor.reset()
        
        while not self.convergence_monitor.is_converged():
            
            # 保存上一步解
            old_vars = {key: val.copy() for key, val in solution_vars.items()}
            
            # 1. 计算流入角和攻角
            self._compute_inflow_angles(solution_vars, flight_condition)
            
            # 2. 应用3D旋转效应修正
            if self.settings.enable_3d_effects:
                self._apply_3d_effects(solution_vars, flight_condition)
            
            # 3. 计算翼型气动系数
            self._compute_airfoil_coefficients(solution_vars, flight_condition)
            
            # 4. 应用动态失速修正
            if self.settings.enable_dynamic_stall:
                self._apply_dynamic_stall(solution_vars, flight_condition)
            
            # 5. 计算载荷分布
            self._compute_load_distribution(solution_vars, flight_condition)
            
            # 6. 更新诱导速度
            self._update_induced_velocity(solution_vars, flight_condition)
            
            # 7. 应用松弛因子
            self._apply_relaxation(solution_vars, old_vars)
            
            # 8. 检查收敛性
            residual = self._compute_residual(solution_vars, old_vars)
            converged = self.convergence_monitor.update(residual)
            
            # 9. 自适应调整
            if self.settings.adaptive_relaxation:
                self._adapt_relaxation_factor(residual)
            
            if converged:
                break
        
        return (self.convergence_monitor.is_converged(), 
                self.convergence_monitor.current_residual)
    
    def _compute_inflow_angles(self, solution_vars: Dict[str, np.ndarray], 
                              flight_condition):
        """计算流入角和攻角"""
        
        r_stations = self.geometry.r_stations
        twist_stations = self.geometry.twist_stations
        
        # 轴向和切向速度分量
        V_axial = flight_condition.V_inf[0] + solution_vars['lambda_i'] * flight_condition.omega * self.geometry.radius
        V_tangential = flight_condition.omega * r_stations
        
        # 流入角
        solution_vars['phi'] = np.degrees(np.arctan2(V_axial, V_tangential))
        
        # 攻角 = 桨叶扭转角 - 流入角
        solution_vars['alpha'] = twist_stations - solution_vars['phi']
    
    def _apply_3d_effects(self, solution_vars: Dict[str, np.ndarray], 
                         flight_condition):
        """应用3D旋转效应修正"""
        
        if self.coriolis_effects is not None:
            # 科里奥利效应修正
            coriolis_correction = self.coriolis_effects.compute_correction(
                solution_vars, flight_condition, self.geometry
            )
            solution_vars['lambda_i_3d'] = solution_vars['lambda_i'] * (1 + coriolis_correction)
        
        if self.centrifugal_effects is not None:
            # 离心力效应修正
            centrifugal_correction = self.centrifugal_effects.compute_correction(
                solution_vars, flight_condition, self.geometry
            )
            solution_vars['alpha_eff'] = solution_vars['alpha'] + centrifugal_correction
        else:
            solution_vars['alpha_eff'] = solution_vars['alpha'].copy()
    
    def _compute_airfoil_coefficients(self, solution_vars: Dict[str, np.ndarray], 
                                     flight_condition):
        """计算翼型气动系数"""
        
        n_stations = len(self.geometry.r_stations)
        
        for i in range(n_stations):
            # 获取翼型名称
            airfoil_name = self.geometry.get_airfoil_at_station(i)
            
            # 计算雷诺数和马赫数
            Re = self.Re_distribution[i]
            M = self.M_distribution[i]
            
            # 插值翼型数据
            alpha_eff = solution_vars['alpha_eff'][i]
            Cl, Cd = self.airfoil_interpolator.interpolate(
                airfoil_name, alpha_eff, Re, M
            )
            
            solution_vars['Cl'][i] = Cl
            solution_vars['Cd'][i] = Cd
    
    def _apply_dynamic_stall(self, solution_vars: Dict[str, np.ndarray], 
                            flight_condition):
        """应用动态失速修正"""
        
        if self.dynamic_stall is None:
            solution_vars['Cl_dyn'] = solution_vars['Cl'].copy()
            solution_vars['Cd_dyn'] = solution_vars['Cd'].copy()
            return
        
        # 计算攻角变化率 (简化处理)
        dt = 1.0 / (flight_condition.omega * 180.0 / np.pi)  # 时间步长估计
        
        n_stations = len(self.geometry.r_stations)
        
        for i in range(n_stations):
            # 动态失速计算
            Cl_dyn, Cd_dyn, x1_new, x2_new = self.dynamic_stall.compute_dynamic_coefficients(
                alpha=solution_vars['alpha_eff'][i],
                Cl_static=solution_vars['Cl'][i],
                Cd_static=solution_vars['Cd'][i],
                x1=solution_vars.get('x1', np.zeros(n_stations))[i],
                x2=solution_vars.get('x2', np.zeros(n_stations))[i],
                dt=dt,
                airfoil_params=self.geometry.get_airfoil_params_at_station(i)
            )
            
            solution_vars['Cl_dyn'][i] = Cl_dyn
            solution_vars['Cd_dyn'][i] = Cd_dyn
            
            if 'x1' in solution_vars:
                solution_vars['x1'][i] = x1_new
                solution_vars['x2'][i] = x2_new
    
    def _compute_load_distribution(self, solution_vars: Dict[str, np.ndarray], 
                                  flight_condition):
        """计算载荷分布"""
        
        r_stations = self.geometry.r_stations
        chord_stations = self.geometry.chord_stations
        dr_stations = self.geometry.dr_stations
        
        # 使用动态系数 (如果可用)
        Cl = solution_vars.get('Cl_dyn', solution_vars['Cl'])
        Cd = solution_vars.get('Cd_dyn', solution_vars['Cd'])
        
        # 相对速度
        V_axial = flight_condition.V_inf[0] + solution_vars['lambda_i_3d'] * flight_condition.omega * self.geometry.radius
        V_tangential = flight_condition.omega * r_stations
        V_rel = np.sqrt(V_axial**2 + V_tangential**2)
        
        # 动压
        q = 0.5 * flight_condition.rho * V_rel**2
        
        # 微元推力和扭矩
        phi_rad = np.radians(solution_vars['phi'])
        
        # 应用修正因子
        tip_loss_factor = self.corrections.compute_tip_loss_correction(
            r_stations, self.geometry.radius, self.geometry.num_blades
        )
        hub_loss_factor = self.corrections.compute_hub_loss_correction(
            r_stations, self.geometry.hub_radius, self.geometry.num_blades
        )
        
        correction_factor = tip_loss_factor * hub_loss
````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/core/enhanced_bemt_solver.py mode=EDIT
        correction_factor = tip_loss_factor * hub_loss_factor
        
        # 微元推力 (轴向力)
        solution_vars['dT'] = (self.geometry.num_blades * q * chord_stations * 
                              (Cl * np.cos(phi_rad) - Cd * np.sin(phi_rad)) * 
                              correction_factor * dr_stations)
        
        # 微元扭矩 (切向力矩)
        solution_vars['dQ'] = (self.geometry.num_blades * q * chord_stations * 
                              (Cl * np.sin(phi_rad) + Cd * np.cos(phi_rad)) * 
                              r_stations * correction_factor * dr_stations)
    
    def _update_induced_velocity(self, solution_vars: Dict[str, np.ndarray], 
                                flight_condition):
        """更新诱导速度"""
        
        if self.inflow_model is not None:
            # 使用非均匀流入模型
            lambda_i_new = self.inflow_model.compute_induced_velocity(
                solution_vars, flight_condition, self.geometry
            )
        else:
            # 使用简单动量理论
            lambda_i_new = self._compute_momentum_theory_inflow(
                solution_vars, flight_condition
            )
        
        solution_vars['lambda_i'] = lambda_i_new
    
    def _compute_momentum_theory_inflow(self, solution_vars: Dict[str, np.ndarray], 
                                      flight_condition) -> np.ndarray:
        """基于动量理论计算诱导速度"""
        
        # 计算局部推力系数
        dT = solution_vars['dT']
        r_stations = self.geometry.r_stations
        dr_stations = self.geometry.dr_stations
        
        # 环形面积
        dA = 2 * np.pi * r_stations * dr_stations
        
        # 局部推力系数
        CT_local = dT / (flight_condition.rho * (flight_condition.omega * self.geometry.radius)**2 * dA)
        
        # 动量理论诱导速度比
        mu = self.advance_ratio
        lambda_i = np.zeros_like(CT_local)
        
        for i, ct in enumerate(CT_local):
            if mu < 0.1:  # 悬停
                lambda_i[i] = np.sqrt(ct / 2.0) if ct > 0 else 0.0
            else:  # 前飞
                # 求解二次方程: lambda_i^2 + mu*lambda_i - ct/2 = 0
                discriminant = mu**2 + 2*ct
                if discriminant >= 0:
                    lambda_i[i] = (-mu + np.sqrt(discriminant)) / 2.0
                else:
                    lambda_i[i] = 0.0
        
        return lambda_i
    
    def _apply_relaxation(self, solution_vars: Dict[str, np.ndarray], 
                         old_vars: Dict[str, np.ndarray]):
        """应用松弛因子"""
        
        for key in ['lambda_i', 'alpha']:
            if key in solution_vars and key in old_vars:
                solution_vars[key] = (self.relaxation_factor * solution_vars[key] + 
                                    (1 - self.relaxation_factor) * old_vars[key])
    
    def _compute_residual(self, solution_vars: Dict[str, np.ndarray], 
                         old_vars: Dict[str, np.ndarray]) -> float:
        """计算残差"""
        
        residuals = []
        
        # 诱导速度残差
        if 'lambda_i' in solution_vars and 'lambda_i' in old_vars:
            lambda_residual = np.max(np.abs(solution_vars['lambda_i'] - old_vars['lambda_i']))
            residuals.append(lambda_residual)
        
        # 攻角残差
        if 'alpha' in solution_vars and 'alpha' in old_vars:
            alpha_residual = np.max(np.abs(solution_vars['alpha'] - old_vars['alpha']))
            residuals.append(alpha_residual)
        
        return max(residuals) if residuals else 0.0
    
    def _adapt_relaxation_factor(self, residual: float):
        """自适应调整松弛因子"""
        
        self.relaxation_history.append(residual)
        
        if len(self.relaxation_history) >= 3:
            # 检查收敛趋势
            recent_residuals = self.relaxation_history[-3:]
            
            if recent_residuals[-1] > recent_residuals[-2]:
                # 残差增加，减小松弛因子
                self.relaxation_factor *= 0.9
                self.relaxation_factor = max(self.relaxation_factor, 0.1)
            elif all(recent_residuals[i] > recent_residuals[i+1] for i in range(len(recent_residuals)-1)):
                # 残差持续减小，可以增加松弛因子
                self.relaxation_factor *= 1.05
                self.relaxation_factor = min(self.relaxation_factor, 0.8)
    
    def _postprocess(self, solution_vars: Dict[str, np.ndarray], 
                    flight_condition, **kwargs) -> Dict[str, Any]:
        """后处理计算"""
        
        # 积分计算总推力和功率
        total_thrust = np.sum(solution_vars['dT'])
        total_torque = np.sum(solution_vars['dQ'])
        total_power = total_torque * flight_condition.omega
        
        # 计算性能系数
        rho = flight_condition.rho
        omega = flight_condition.omega
        R = self.geometry.radius
        A = np.pi * R**2
        
        CT = total_thrust / (rho * (omega * R)**2 * A)
        CQ = total_torque / (rho * (omega * R)**2 * A * R)
        CP = CQ  # CP = CQ for rotors
        
        # 计算效率
        if self.advance_ratio > 0.01:
            eta = CT * self.advance_ratio / CP if CP > 0 else 0.0
        else:
            eta = 0.0  # 悬停时效率定义为0
        
        # 构建结果字典
        results = {
            # 总体性能
            'thrust': total_thrust,
            'torque': total_torque, 
            'power': total_power,
            'CT': CT,
            'CQ': CQ,
            'CP': CP,
            'efficiency': eta,
            
            # 分布数据
            'radial_stations': self.geometry.r_stations.copy(),
            'thrust_distribution': solution_vars['dT'].copy(),
            'torque_distribution': solution_vars['dQ'].copy(),
            'alpha_distribution': solution_vars['alpha'].copy(),
            'Cl_distribution': solution_vars.get('Cl_dyn', solution_vars['Cl']).copy(),
            'Cd_distribution': solution_vars.get('Cd_dyn', solution_vars['Cd']).copy(),
            'inflow_angle_distribution': solution_vars['phi'].copy(),
            'induced_velocity_distribution': solution_vars['lambda_i'].copy(),
            
            # 中保真度特有结果
            'alpha_effective_distribution': solution_vars.get('alpha_eff', solution_vars['alpha']).copy(),
            'induced_velocity_3d_distribution': solution_vars.get('lambda_i_3d', solution_vars['lambda_i']).copy(),
            
            # 飞行条件
            'flight_condition': {
                'rpm': flight_condition.rpm,
                'advance_ratio': self.advance_ratio,
                'tip_mach': flight_condition.omega * R / flight_condition.a,
                'reynolds_tip': self.Re_distribution[-1] if len(self.Re_distribution) > 0 else 0.0
            }
        }
        
        # 添加动态失速结果 (如果可用)
        if self.dynamic_stall is not None and 'x1' in solution_vars:
            results.update({
                'dynamic_stall_state_x1': solution_vars['x1'].copy(),
                'dynamic_stall_state_x2': solution_vars['x2'].copy(),
                'dynamic_Cl_distribution': solution_vars['Cl_dyn'].copy(),
                'dynamic_Cd_distribution': solution_vars['Cd_dyn'].copy()
            })
        
        return results
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        
        return {
            'solver_type': 'Enhanced BEMT (Medium Fidelity)',
            'last_solve_time': self.performance_stats['solve_time'],
            'last_iterations': self.performance_stats['iterations'],
            'convergence_rate': self._compute_convergence_rate(),
            'physics_models': {
                '3d_effects': self.settings.enable_3d_effects,
                'dynamic_stall': self.settings.enable_dynamic_stall,
                'non_uniform_inflow': self.settings.enable_non_uniform_inflow,
                'wake_skew': self.settings.enable_wake_skew
            },
            'numerical_settings': {
                'max_iterations': self.settings.max_iterations,
                'tolerance': self.settings.tolerance,
                'adaptive_relaxation': self.settings.adaptive_relaxation
            }
        }
    
    def _compute_convergence_rate(self) -> float:
        """计算收敛率"""
        
        history = self.performance_stats.get('convergence_history', [])
        if len(history) < 2:
            return 0.0
        
        # 计算平均收敛率
        rates = []
        for i in range(1, len(history)):
            if history[i-1] > 0:
                rate = history[i] / history[i-1]
                rates.append(rate)
        
        return np.mean(rates) if rates else 0.0
    
    def save_convergence_plot(self, filename: str = "convergence_history.png"):
        """保存收敛历史图"""
        
        try:
            import matplotlib.pyplot as plt
            
            history = self.performance_stats.get('convergence_history', [])
            if not history:
                print("警告: 没有收敛历史数据")
                return
            
            plt.figure(figsize=(10, 6))
            plt.semilogy(history, 'b-', linewidth=2, marker='o', markersize=4)
            plt.axhline(y=self.settings.tolerance, color='r', linestyle='--', 
                       label=f'Tolerance = {self.settings.tolerance:.1e}')
            plt.xlabel('Iteration')
            plt.ylabel('Residual')
            plt.title('BEMT Medium-Fidelity Convergence History')
            plt.grid(True, alpha=0.3)
            plt.legend()
            plt.tight_layout()
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"收敛历史图已保存: {filename}")
            
        except ImportError:
            print("警告: matplotlib未安装，无法保存收敛图")
        except Exception as e:
            print(f"保存收敛图失败: {str(e)}")

# 工厂函数
def create_enhanced_bemt_solver(geometry_config: Dict[str, Any],
                               solver_config: Dict[str, Any] = None) -> EnhancedBEMTSolver:
    """
    创建增强BEMT求解器的工厂函数
    
    Parameters:
    -----------
    geometry_config : dict
        几何配置参数
    solver_config : dict, optional
        求解器配置参数
        
    Returns:
    --------
    solver : EnhancedBEMTSolver
        增强BEMT求解器实例
    """
    
    # 创建几何
    geometry = RotorGeometry3D(**geometry_config)
    
    # 创建求解器设置
    settings = SolverSettings(**(solver_config or {}))
    
    # 创建求解器
    solver = EnhancedBEMTSolver(geometry, settings)
    
    return solver

# 模块测试
def main():
    """模块测试函数"""
    
    print("🧪 测试增强BEMT求解器...")
    
    try:
        # 创建测试几何
        geometry_config = {
            'radius': 5.0,
            'num_blades': 4,
            'chord': 0.3,
            'twist': 0.0,
            'airfoil': 'naca0012'
        }
        
        # 创建求解器
        solver = create_enhanced_bemt_solver(geometry_config)
        
        # 创建测试飞行条件
        from ..bemt_medium_module import create_flight_condition
        flight_condition = create_flight_condition(rpm=400.0, forward_speed=10.0)
        
        # 执行求解
        print("执行求解...")
        results = solver.solve(flight_condition)
        
        # 显示结果
        print(f"✅ 求解完成!")
        print(f"   推力: {results['thrust']:.2f} N")
        print(f"   功率: {results['power']:.2f} W")
        print(f"   效率: {results['efficiency']:.3f}")
        print(f"   迭代次数: {results['solver_info']['iterations']}")
        print(f"   求解时间: {results['solver_info']['solve_time']:.3f} s")
        
        # 保存收敛图
        solver.save_convergence_plot("test_convergence.png")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
````

### 3. 循环翼增强求解器

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/core/cycloidal_enhanced_solver.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cycloidal Enhanced BEMT Solver - Medium Fidelity
循环翼增强BEMT求解器 - 中保真度

This module implements an enhanced BEMT solver specifically for cycloidal rotors
with medium-fidelity physics including:
- Azimuthal variation of pitch angle
- Cyclic inflow variations
- Enhanced wake modeling for cycloidal geometry
- 3D effects specific to cycloidal rotors

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from .enhanced_bemt_solver import EnhancedBEMTSolver, SolverSettings
from ..geometry.rotor_geometry import RotorGeometry3D
from ..physics.pitch_control import CycloidalPitchControl
from ..utils.error_handling import BEMTMediumError

@dataclass
class CycloidalSettings(SolverSettings):
    """循环翼求解器设置"""
    
    # 循环翼特有设置
    azimuth_resolution: int = 360        # 方位角分辨率
    pitch_amplitude_top: float = 20.0    # 上半周期桨距幅值 [deg]
    pitch_amplitude_bottom: float = 15.0 # 下半周期桨距幅值 [deg]
    pitch_phase_offset: float = 0.0      # 桨距相位偏移 [deg]
    enable_asymmetric_pitch: bool = True # 启用非对称桨距
    pitch_bias_angle: float = 0.0        # 桨距偏置角 [deg]
    
    # 循环翼物理模型
    enable_cyclic_inflow: bool = True    # 启用周期性流入
    enable_blade_interaction: bool = True # 启用桨叶相互作用
    enable_unsteady_effects: bool = True # 启用非定常效应

class CycloidalEnhancedSolver(EnhancedBEMTSolver):
    """
    循环翼增强BEMT求解器 - 中保真度
    
    特性:
    - 方位角变化的桨距控制
    - 周期性流入建模
    - 桨叶间相互作用
    - 循环翼特有的3D效应
    """
    
    def __init__(self, 
                 geometry: RotorGeometry3D,
                 settings: Optional[CycloidalSettings] = None,
                 **kwargs):
        """
        初始化循环翼增强BEMT求解器
        
        Parameters:
        -----------
        geometry : RotorGeometry3D
            3D旋翼几何
        settings : CycloidalSettings, optional
            循环翼求解器设置
        **kwargs : dict
            额外参数
        """
        
        # 确保使用循环翼设置
        if settings is None:
            settings = CycloidalSettings(**kwargs)
        elif not isinstance(settings, CycloidalSettings):
            # 转换为循环翼设置
            settings_dict = settings.__dict__.copy()
            settings_dict.update(kwargs)
            settings = CycloidalSettings(**settings_dict)
        
        # 调用父类初始化
        super().__init__(geometry, settings, **kwargs)
        
        # 循环翼特有设置
        self.cycloidal_settings = settings
        
        # 初始化循环翼特有组件
        self._initialize_cycloidal_components()
        
        # 方位角网格
        self.azimuth_angles = np.linspace(0, 360, self.cycloidal_settings.azimuth_resolution)
        self.n_azimuth = len(self.azimuth_angles)
    
    def _initialize_cycloidal_components(self):
        """初始化循环翼特有组件"""
        
        # 桨距控制系统
        self.pitch_control = CycloidalPitchControl(
            amplitude_top=self.cycloidal_settings.pitch_amplitude_top,
            amplitude_bottom=self.cycloidal_settings.pitch_amplitude_bottom,
            phase_offset=self.cycloidal_settings.pitch_phase_offset,
            bias_angle=self.cycloidal_settings.pitch_bias_angle,
            enable_asymmetric=self.cycloidal_settings.enable_asymmetric_pitch
        )
    
    def solve(self, flight_condition, **kwargs) -> Dict[str, Any]:
        """
        求解循环翼BEMT方程组
        
        Parameters:
        -----------
        flight_condition : FlightCondition
            飞行条件
        **kwargs : dict
            求解选项
            
        Returns:
        --------
        results : dict
            求解结果 (包含方位角变化数据)
        """
        
        start_time = time.time()
        
        try:
            # 1. 预处理
            self._preprocess_cycloidal(flight_condition, **kwargs)
            
            # 2. 方位角循环求解
            azimuth_results = []
            
            for i, psi in enumerate(self.azimuth_angles):
                # 更新当前方位角的桨距角
                self._update_pitch_for_azimuth(psi)
                
                # 初始化当前方位角的求解变量
                solution_vars = self._initialize_solution(flight_condition)
                
                # 迭代求解当前方位角
                converged, residual = self._iterative_solve_azimuth(
                    solution_vars, flight_condition, psi, **kwargs
                )
                
                # 后处理当前方位角结果
                azimuth_result = self._postprocess_azimuth(
                    solution_vars, flight_condition, psi
                )
                azimuth_result['azimuth_angle'] = psi
                azimuth_result['converged'] = converged
                azimuth_result['residual'] = residual
                
                azimuth_results.append(azimuth_result)
                
                # 进度显示
                if (i + 1) % 36 == 0:  # 每10度显示一次
                    print(f"  方位角求解进度: {i+1}/{self.n_azimuth} ({psi:.1f}°)")
            
            # 3. 整合所有方位角结果
            integrated_results = self._integrate_azimuth_results(
                azimuth_results, flight_condition
            )
            
            # 4. 添加求解信息
            solve_time = time.time() - start_time
            integrated_results.update({
                'solver_info': {
                    'solver_type': 'Cycloidal Enhanced BEMT',
                    'fidelity_level': 'medium',
                    'azimuth_resolution': self.cycloidal_settings.azimuth_resolution,
                    'total_solve_time': solve_time,
                    'average_time_per_azimuth': solve_time / self.n_azimuth
                }
            })
            
            # 5. 存储详细的方位角数据
            integrated_results['azimuth_data'] = azimuth_results
            
            return integrated_results
            
        except Exception as e:
            raise BEMTMediumError(f"循环翼BEMT求解失败: {str(e)}")
    
    def _preprocess_cycloidal(self, flight_condition, **kwargs):
        """循环翼预处理"""
        
        # 调用父类预处理
        self._preprocess(flight_condition, **kwargs)
        
        # 计算循环翼特有参数
        self.tip_speed = flight_condition.omega * self.geometry.radius
        self.tip_mach = self.tip_speed / flight_condition.a
        
        # 预计算桨距变化
        self.pitch_schedule = np.array([
            self.pitch_control.get_pitch_angle(psi) 
            for psi in self.azimuth_angles
        ])
        
        print(f"循环翼预处理完成:")
        print(f"  方位角分辨率: {self.cycloidal_settings.azimuth_resolution}")
        print(f"  桨距幅值 (上/下): {self.cycloidal_settings.pitch_amplitude_top:.1f}°/{self.cycloidal_settings.pitch_amplitude_bottom:.1f}°")
        print(f"  叶尖马赫数: {self.tip_mach:.3f}")
    
    def _update_pitch_for_azimuth(self, azimuth_angle: float):
        """更新当前方位角的桨距角"""
        
        # 获取当前方位角的桨距角
        pitch_angle = self.pitch_control.get_pitch_angle(azimuth_angle)
        
        # 更新几何中的桨距角
        self.geometry.update_pitch_angle(pitch_angle)
    
    def _iterative_solve_azimuth(self, solution_vars: Dict[str, np.ndarray], 
                                flight_condition, azimuth_angle: float,
                                **kwargs) -> Tuple[bool, float]:
        """单个方位角的迭代求解"""
        
        # 重置收敛监控器
        self.convergence_monitor.reset()
        
        # 循环翼特有的收敛准则 (更严格)
        original_tolerance = self.convergence_monitor.tolerance
        self.convergence_monitor.tolerance = original_tolerance * 0.5
        
        while not self.convergence_monitor.is_converged():
            
            # 保存上一步解
            old_vars = {key: val.copy() for key, val in solution_vars.items()}
            
            # 1. 计算流入角和攻角 (考虑当前桨距)
            self._compute_inflow_angles_cycloidal(
                solution_vars, flight_condition, azimuth_angle
            )
            
            # 2. 应用循环翼特有的3D效应
            if self.settings.enable_3d_effects:
                self._apply_cycloidal_3d_effects(
                    solution_vars, flight_condition, azimuth_angle
                )
            
            # 3. 计算翼型气动系数
            self._compute_airfoil_coefficients(solution_vars, flight_condition)
            
            # 4. 应用动态失速修正 (考虑非定常效应)
            if self.settings.enable_dynamic_stall:
                self._apply_cycloidal_dynamic_stall(
                    solution_vars, flight_condition, azimuth_angle
                )
            
            # 5. 计算载荷分布
            self._compute_load_distribution(solution_vars, flight_condition)
            
            # 6. 更新诱导速度 (考虑周期性流入)
            self._update_cycloidal_induced_velocity(
                solution_vars, flight_condition, azimuth_angle
            )
            
            # 7. 应用松弛因子
            self._apply_relaxation(solution_vars, old_vars)
            
            # 8. 检查收敛性
            residual = self._compute_residual(solution_vars, old_vars)
            converged = self.convergence_monitor.update(residual)
            
            if converged:
                break
        
        # 恢复原始收敛准则
        self.convergence_monitor.tolerance = original_tolerance
        
        return (self.convergence_monitor.is_converged(), 
                self.convergence_monitor.current_residual)
    
    def _compute_inflow_angles_cycloidal(self, solution_vars: Dict[str, np.ndarray], 
                                        flight_condition, azimuth_angle: float):
        """计算循环翼流入角和攻角"""
        
        r_stations = self.geometry.r_stations
        
        # 获取当前方位角的桨距角
        current_pitch = self.pitch_control.get_pitch_angle(azimuth_angle)
        
        # 考虑前飞速度在当前方位角的分量
        psi_rad = np.radians(azimuth_angle)
        V_forward_component = flight_condition.V_inf[0] * np.cos(psi_rad)
        V_lateral_component = flight_condition.V_inf[0] * np.sin(psi_rad)
        
        # 轴向和切向速度分量
        V_axial = (V_forward_component + 
                  solution_vars['lambda_i'] * flight_condition.omega * self.geometry.radius)
        V_tangential = flight_condition.omega * r_stations + V_lateral_component
        
        # 流入角
        solution_vars['phi'] = np.degrees(np.arctan2(V_axial, V_tangential))
        
        # 攻角 = 当前桨距角 - 流入角
        solution_vars['alpha'] = current_pitch - solution_vars['phi']
    
    def _apply_cycloidal_3d_effects(self, solution_vars: Dict[str, np.ndarray], 
                                   flight_condition, azimuth_angle: float):
        """应用循环翼特有的3D效应"""
        
        # 调用基础3D效应
        self._apply_3d_effects(solution_vars, flight_condition)
        
        # 循环翼特有的效应
        if self.cycloidal_settings.enable_blade_interaction:
            # 桨叶间相互作用效应
            interaction_correction = self._compute_blade_interaction_correction(
                solution_vars, azimuth_angle
            )
            solution_vars['lambda_i_3d'] *= (1 + interaction_correction)
        
        # 非定常效应 (简化处理)
        if self.cycloidal_settings.enable_unsteady_effects:
            unsteady_correction = self._compute_unsteady_correction(
                solution_vars, flight_condition, azimuth_angle
            )
            solution_vars['alpha_eff'] += unsteady_correction
    
    def _compute_blade_interaction_correction(self, solution_vars: Dict[str, np.ndarray], 
                                            azimuth_angle: float) -> np.ndarray:
        """计算桨叶间相互作用修正"""
        
        # 简化的桨叶相互作用模型
        n_blades = self.geometry.num_blades
        blade_spacing = 360.0 / n_blades
        
        # 计算相邻桨叶的影响
        interaction_factor = np.zeros_like(solution_vars['lambda_i'])
        
        for i in range(n_blades):
            blade_azimuth = (azimuth_angle + i * blade_spacing) % 360
            
            # 简化的相互作用强度 (基于方位角差异)
            azimuth_diff = min(abs(blade_azimuth - azimuth_angle), 
                             360 - abs(blade_azimuth - azimuth_angle))
            
            if azimuth_diff > 0:
                interaction_strength = np.exp(-azimuth_diff / 60.0)  # 60度衰减常数
                interaction_factor += interaction_strength * 0.1  # 10%最大影响
        
        return interaction_factor
    
    def _compute_unsteady_correction(self, solution_vars: Dict[str, np.ndarray], 
                                   flight_condition, azimuth_angle: float) -> np.ndarray:
        """计算非定常效应修正"""
        
        # 简化的非定常效应 (基于桨距变化率)
        dpitch_dpsi = self.pitch_control.get_pitch_rate(azimuth_angle)
        
        # 非定常攻角修正 (基于Theodorsen理论的简化)
        dt_dpsi = 1.0 / flight_condition.omega  # 时间-方位角转换
        alpha_dot = dpitch_dpsi / dt_dpsi
        
        # 简化的非定常修正
        unsteady_correction = -0.5 * alpha_dot * dt_dpsi  # 简化系数
        
        return np.full_like(solution_vars['alpha'], unsteady_correction)
    
    def _apply_cycloidal_dynamic_stall(self, solution_vars: Dict[str, np.ndarray], 
                                      flight_condition, azimuth_angle: float):
        """应用循环翼动态失速修正"""
        
        if self.dynamic_stall is None:
            solution_vars['Cl_dyn'] = solution_vars['Cl'].copy()
            solution_vars['Cd_dyn'] = solution_vars['Cd'].copy()
            return
        
        # 循环翼的时间步长 (基于方位角变化)
        dpsi = 360.0 / self.cycloidal_settings.azimuth_resolution
        dt = dpsi * np.pi / (180.0 * flight_condition.omega)
        
        n_stations = len(self.geometry.r_stations)
        
        for i in range(n_stations):
            # 考虑循环翼特有的攻角历史
            alpha_history = self._get_alpha_history_at_station(i, azimuth_angle)
            
            # 动态失速计算
            Cl_dyn, Cd_dyn, x1_new, x2_new = self.dynamic_stall.compute_dynamic_coefficients(
                alpha=solution_vars['alpha_eff'][i],
                Cl_static=solution_vars['Cl'][i],
                Cd_static=solution_vars['Cd'][i],
                x1=solution_vars.get('x1', np.zeros(n_stations))[i],
                x2=solution_vars.get('x2', np.zeros(n_stations))[i],
                dt=dt,
                airfoil_params=self.geometry.get_airfoil_params_at_station(i),
                alpha_history=alpha_history  # 循环翼特有参数
            )
            
            solution_vars['Cl_dyn'][i] = Cl_dyn
            solution_vars['Cd_dyn'][i] = Cd_dyn
            
            if 'x1' in solution_vars:
                solution_vars['x1'][i] = x1_new
                solution_vars['x2'][i] = x2_new
    
    def _get_alpha_history_at_station(self, station_idx: int, 
                                     current_azimuth: float) -> List[float]:
        """获取指定叶素的攻角历史 (简化实现)"""
        
        # 简化实现：基于桨距变化估算攻角历史
        history_length = 5  # 保留5个历史点
        azimuth_step = 360.0 / self.cycloidal_settings.azimuth_resolution
        
        alpha_history = []
        for i in range(history_length):
            past_azimuth = current_azimuth - i * azimuth_step
            past_pitch = self.pitch_control.get_pitch_angle(past_azimuth)
            
            # 简化的攻角估算 (假设流入角变化不大)
            estimated_alpha = past_pitch - 5.0  # 假设流入角约为5度
            alpha_history.append(estimated_alpha)
        
        return alpha_history
    
    def _update_cycloidal_induced_velocity(self, solution_vars: Dict[str, np.ndarray], 
                                          flight_condition, azimuth_angle: float):
        """更新循环翼诱导速度"""
        
        if self.cycloidal_settings.enable_cyclic_inflow and self.inflow_model is not None:
            # 使用周期性流入模型
            lambda_i_new = self.inflow_model.compute_cycloidal_induced_velocity(
                solution_vars, flight_condition, self.geometry, azimuth_angle
            )
        else:
            # 使用标准动量理论
            lambda_i_new = self._compute_momentum_theory_inflow(
                solution_vars, flight_condition
            )
        
        solution_vars['lambda_i'] = lambda_i_new
    
    def _postprocess_azimuth(self, solution_vars: Dict[str, np.ndarray], 
                            flight_condition, azimuth_angle: float) -> Dict[str, Any]:
        """单个方位角的后处理"""
        
        # 调用基础后处理
        basic_results = self._postprocess(solution_vars, flight_condition)
        
        # 添加循环翼特有信息
        cycloidal_results = {
            'azimuth_angle': azimuth_angle,
            'pitch_angle': self.pitch_control.get_pitch_angle(azimuth_angle),
            'pitch_rate': self.pitch_control.get_pitch_rate(azimuth_angle),
        }
        
        # 合并结果
        basic_results.update(cycloidal_results)
        
        return basic_results
    
    def _integrate_azimuth_results(self, azimuth_results: List[Dict[str, Any]], 
                                  flight_condition) -> Dict[str, Any]:
        """整合所有方位角结果"""
        
        n_azimuth = len(azimuth_results)
        
        # 提取时间平均性能
        thrust_values = [result['thrust'] for result in azimuth_results]
        power_values = [result['power'] for result in azimuth_results]
        torque_values = [result['torque'] for result in azimuth_results]
        
        # 计算平均值和脉动
        mean_thrust = np.mean(thrust_values)
        mean_power = np.mean(power_values)
        mean_torque = np.mean(torque_values)
        
        thrust_fluctuation = np.std(thrust_values) / mean_thrust if mean_thrust > 0 else 0
        power_fluctuation = np.std(power_values) / mean_power if mean_power > 0 else 0
        
        # 计算平均性能系数
        rho = flight_condition.rho
        omega = flight_condition.omega
        R = self.geometry.radius
        A = np.pi * R**2
        
        CT_mean = mean_thrust / (rho * (omega * R)**2 * A)
        CP_mean = mean_power / (rho * (omega * R)**3 * A)
        
        # 计算效率
        if self.advance_ratio > 0.01:
            eta_mean = CT_mean * self.advance_ratio / CP_mean if CP_mean > 0 else 0.0
        else:
            eta_mean = 0.0
        
        # 构建整合结果
        integrated_results = {
            # 时间平均性能
            'thrust_mean': mean_thrust,
            'power_mean': mean_power,
            'torque_mean': mean_torque,
            'CT_mean': CT_mean,
            'CP_mean': CP_mean,
            'efficiency_mean': eta_mean,
            
            # 脉动特性
            'thrust_fluctuation': thrust_fluctuation,
            'power_fluctuation': power_fluctuation,
            'thrust_amplitude': (max(thrust_values) - min(thrust_values)) / 2,
            'power_amplitude': (max(power_values) - min(power_values)) / 2,
            
            # 方位角变化数据
            'azimuth_angles': self.azimuth_angles.copy(),
            'thrust_vs_azimuth': np.array(thrust_values),
            'power_vs_azimuth': np.array(power_values),
            'torque_vs_azimuth': np.array(torque_values),
            
            # 桨距变化
            'pitch_schedule': self.pitch_schedule.copy(),
            
            # 飞行条件
            'flight_condition': {
                'rpm': flight_condition.rpm,
                'advance_ratio': self.advance_ratio,
                'tip_mach': self.tip_mach,
                'cycloidal_parameters': {
                    'pitch_amplitude_top': self.cycloidal_settings.pitch_amplitude_top,
                    'pitch_amplitude_bottom': self.cycloidal_settings.pitch_amplitude_bottom,
                    'pitch_phase_offset': self.cycloidal_settings.pitch_phase_offset,
                    'asymmetric_pitch': self.cycloidal_settings.enable_asymmetric_pitch
                }
            }
        }
        
        return integrated_results

# 工厂函数
def create_cycloidal_enhanced_solver(geometry_config: Dict[str, Any],
                                    cycloidal_config: Dict[str, Any] = None,
                                    solver_config: Dict[str, Any] = None) -> CycloidalEnhancedSolver:
    """
    创建循环翼增强BEMT求解器的工厂函数
    
    Parameters:
    -----------
    geometry_config : dict
        几何配置参数
    cycloidal_config : dict, optional
        循环翼特有配置参数
    solver_config : dict, optional
        求解器配置参数
        
    Returns:
    --------
    solver : CycloidalEnhancedSolver
        循环翼增强BEMT求解器实例
    """
    
    # 创建几何
    geometry = RotorGeometry3D(**geometry_config)
    
    # 合并配置
    settings_dict = {}
    if solver_config:
        settings_dict.update(solver_config)
    if cycloidal_config:
        settings_dict.update(cycloidal_config)
    
    # 创建循环翼设置
    settings = CycloidalSettings(**settings_dict)
    
    # 创建求解器
    solver = CycloidalEnhancedSolver(geometry, settings)
    
    return solver

# 模块测试
def main():
    """模块测试函数"""
    
    print("🧪 测试循环翼增强BEMT求解器...")
    
    try:
        # 创建测试几何
        geometry_config = {
            'radius': 0.3,
            'num_blades': 6,
            'chord': 0.1,
            'span': 0.5,
            'airfoil': 'naca0012'
        }
        
        # 循环翼配置
        cycloidal_config = {
            'azimuth_resolution': 72,  # 5度分辨率
            'pitch_amplitude_top': 20.0,
            'pitch_amplitude_bottom': 15.0,
            'enable_asymmetric_pitch': True
        }
        
        # 创建求解器
        solver = create_cycloidal_enhanced_solver(
            geometry_config, cycloidal_config
        )
        
        # 创建测试飞行条件
        from ..bemt_medium_module import create_flight_condition
        flight_condition = create_flight_condition(rpm=600.0, forward_speed=5.0)
        
        # 执行求解
        print("执行循环翼求解...")
        results = solver.solve(flight_condition)
        
        # 显示结果
        print(f"✅ 循环翼求解完成!")
        print(f"   平均推力: {results['thrust_mean']:.2f} N")
        print(f"   平均功率: {results['power_mean']:.2f} W")
        print(f"   平均效率: {results['efficiency_mean']:.3f}")
        print(f"   推力脉动: {results['thrust_fluctuation']:.3f}")
        print(f"   功率脉动: {results['power_fluctuation']:.3f}")
        print(f"   求解时间: {results['solver_info']['total_solve_time']:.3f} s")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
````

## 🔄 实施步骤详细指南

### 步骤1: 创建目录结构

````python path=m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation/setup_module.py mode=EDIT
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT中保真度模块设置脚本
Setup Script for BEMT Medium-Fidelity Module

This script automates the creation and setup of the BEMT medium-fidelity
validation module, including directory structure, file extraction, and
dependency resolution.

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import os
import shutil
import yaml
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess
import sys

class MediumFidelityModuleSetup:
    """BEMT中保真度模块设置类"""
    
    def __init__(self):
        self.source_root = Path("m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite")
        self.target_root = Path("m_code/cycloidal_Pneumatic noise/cycloidal_rotor_suite_Model_decoupling/bemt_medium_fidelity_validation")
        self.setup_log = []
    
    def run_complete_setup(self):
        """运行完整的模块设置流程"""
        
        print("🚀 开始BEMT中保真度模块设置...")
        print("=" * 60)
        
        try:
            # 步骤1: 创建目录结构
            self.step1_create_directories()
            
            # 步骤2: 提取和复制文件
            self.step2_extract_files()
            
            # 步骤3: 重构代码和解决依赖
            self.step3_refactor_code()
            
            # 步骤4: 创建配置文件
            self.step4_create_configs()
            
            # 步骤5: 设置验证案例
            self.step5_setup_validation()
            
            # 步骤6: 创建文档
            self.step6_create_documentation()
            
            # 步骤7: 运行初始测试
            self.step7_run_tests()
            
            print("\n✅ BEMT中保真度模块设置完成!")
            self.print_setup_summary()
            
        except Exception as e:
            print(f"\n❌ 设置过程中出现错误: {str(e)}")
            self.print_error_log()
    
    def step1_create_directories(self):
        """步骤1: 创建目录结构"""
        
        print("\n📁 步骤1: 创建目录结构...")
        
        # 导入目录创建脚本
        from create_directory_structure import create_medium_fidelity_structure
        
        try:
            created_path = create_medium_fidelity_structure()
            self.setup_log.append(f"✅ 目录结构创建成功: {created_path}")
            print(f"   目录结构创建完成: {created_path}")
            
        except Exception as e:
            error_msg = f"❌ 目录结构创建失败: {str(e)}"
            self.setup_log.append(error_msg)
            raise Exception(error_msg)
    
    def step2_extract_files(self):
        """步骤2: 提取和复制文件"""
        
        print("\n📋 步骤2: 提取和复制文件...")
        
        # 导入文件提取计划
        from file_extraction_plan import MediumFidelityExtractor
        
        extractor = MediumFidelityExtractor()
        extraction_plan = extractor.get_extraction_plan()
        
        total_files = 0
        copied_files = 0
        failed_files = []
        
        for category, file_list in extraction_plan.items():
            print(f"   处理类别: {category}")
            
            for source_path, target_path in file_list:
                total_files += 1
                
                source_full = self.source_root / source_path
                target_full = self.target_root / target_path
                
                try:
                    if source_full.exists():
                        # 确保目标目录存在
                        target_full.parent.mkdir(parents=True, exist_ok=True)
                        
                        if source_full.is_file():
                            shutil.copy2(source_full, target_full)
                        else:
                            shutil.copytree(source_full, target_full, dirs_exist_ok=True)
                        
                        copied_files += 1
                        print(f"     ✅ {source_path} → {target_path}")
                    else:
                        # 文件不存在，创建占位符
                        self._create_placeholder_file(target_full, source_path)
                        print(f"     ⚠️  {source_path} (创建占位符)")
                        
                except Exception as e:
                    failed_files.append((source_path, str(e)))
                    print(f"     ❌ {source_path}: {str(e)}")
        
        # 记录提取结果
        self.setup_log.append(f"文件提取完成: {copied_files}/{total_files} 成功")
        if failed_files:
            self.setup_log.append(f"失败文件: {len(failed_files)} 个")
            for file_path, error in failed_files:
                self.setup_log.append(f"  - {file_path}: {error}")
    
    def _create_placeholder_file(self, target_path: Path, source_path: str):
        """创建占位符文件"""
        
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        if target_path.suffix == '.py':
            content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
占位符文件 - {target_path.name}
Placeholder file extracted from: {source_path}

TODO: 实现从原始项目提取的功能
"""

# TODO: 从 {source_path} 提取实际实现
raise NotImplementedError("此模块需要从原始项目中提取实现")
'''
        elif target_path.suffix == '.yaml':
            content = f'''# 占位符配置文件 - {target_path.name}
# Placeholder config extracted from: {source_path}

# TODO: 从原始项目提取实际配置
placeholder: true
source_path: "{source_path}"
'''
        else:
            content = f"# 占位符文件\n# 原始路径: {source_path}\n# TODO: 提取实际内容\n"
        
        with open(target_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def step3_refactor_code(self):
        """步骤3: 重构代码和解决依赖"""
        
        print("\n🔧 步骤3: 重构代码和解决依赖...")
        
        # 查找所有Python文件
        python_files = list(self.target_root.rglob("*.py"))
        
        refactored_files = 0
        
        for py_file in python_files:
            try:
                if self._refactor_python_file(py_file):
                    refactored_files += 1
                    print(f"   ✅ 重构: {py_file.relative_to(self.target_root)}")
                    
            except Exception as e:
                print(f"   ❌ 重构失败: {py_file.name}: {str(e)}")
        
        self.setup_log.append(f"代码重构完成: {refactored_files} 个文件")
    
    def _refactor_python_file(self, file_path: Path) -> bool:
        """重构单个Python文件"""
        
        if not file_path.exists() or file_path.stat().st_size == 0:
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 保存原始内容
            original_content = content
            
            # 替换导入语句
            content = self._fix_import_statements(content)
            
            # 移除对主项目的依赖
            content = self._remove_main_project_dependencies(content)
            
            # 添加模块头部信息
            content = self._add_module_header(content, file_path)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"重构文件失败 {file_path}: {str(e)}")
            return False
    
    def _fix_import_statements(self, content: str) -> str:
        """修复导入语句"""
        
        # 替换对主项目的导入
        replacements = {
            'from cyclone_sim.': 'from .',
            'from cyclone_sim ': 'from . ',
            'import cyclone_sim.': 'from . import ',
            'from cycloidal_rotor_suite.': 'from .',
            'import cycloidal_rotor_suite.': 'from . import ',
        }
        
        for old_import, new_import in replacements.items():
            content = content.replace(old_import, new_import)
        
        return content
    
    def _remove_main_project_dependencies(self, content: str) -> str:
        """移除对主项目的依赖"""
        
        # 移除特定的依赖导入
        lines = content.split('\n')
        filtered_lines = []
        
        for line in lines:
            # 跳过对主项目特定模块的导入
            if any(dep in line for dep in [
                'from project_core',
                'import project_core',
                'from rolling_wing_project',
                'import rolling_wing_project'
            ]):
                # 添加注释说明
                filtered_lines.append(f"# TODO: 解决依赖 - {line.strip()}")
            else:
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def _add_module_header(self, content: str, file_path: Path) -> str:
        """添加模块头部信息"""
        
        if content.startswith('#!/usr/bin/env python3'):
            return content  # 已经有头部信息
        
        header = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{file_path.stem.replace('_', ' ').title()}
BEMT Medium-Fidelity Validation Module

Extracted and refactored from cycloidal_rotor_suite project.
This module provides medium-fidelity BEMT analysis capabilities.

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

'''
        
        return header + content
    
    def step4_create_configs(self):
        """步骤4: 创建配置文件"""
        
        print("\n⚙️  步骤4: 创建配置文件...")
        
        configs_created = 0
        
        # 主配置文件
        main_config = self._create_main_config()
        config_path = self.target_root / "configs" / "medium_fidelity_config.yaml"
        self._save_yaml_config(config_path, main_config)
        configs_created += 1
        
        # 常规旋翼配置
        conv_config = self._create_conventional_config()
        conv_path = self.target_root / "configs" / "conventional_rotor.yaml"
        self._save_yaml_config(conv_path, conv_config)
        configs_created += 1
        
        # 循环翼配置
        cyc_config = self._create_cycloidal_config()
        cyc_path = self.target_root / "configs" / "cycloidal_rotor.yaml"
        self._save_yaml_config(cyc_path, cyc_config)
        configs_created += 1
        
        self.setup_log.append(f"配置文件创建完成: {configs_created} 个")
        print(f"   创建了 {configs_created} 个配置文件")
    
    def _create_main_config(self) -> Dict:
        """创建主配置文件"""
        
        return {
            'simulation': {
                'name': 'bemt_medium_fidelity_analysis',
                'description': 'Medium-fidelity BEMT analysis with enhanced physics',
                'fidelity_level': 'medium',
                'version': '1.0'
            },
            'solver': {
                'type': 'enhanced_bemt',
                'max_iterations': 200,
                'convergence_tolerance': 1e-7,
                'relaxation_factor': 0.6,
                'enable_3d_effects': True,
                'enable_dynamic_stall': True,
                'enable_wake_skew': True,
                'enable_non_uniform_inflow': True,
                'adaptive_relaxation': True,
                'adaptive_mesh': False
            },
            'physics': {
                'dynamic_stall': {
                    'model': 'simplified_leishman_beddoes',
                    'enable_lag_effects': True,
                    'enable_separation_tracking': True
                },
                'rotational_effects': {
                    'enable_coriolis': True,
                    'enable_centrifugal': True,
                    'enable_spanwise_flow': True
                },
                'corrections': {
                    'tip_loss': 'prandtl_glauert',
                    'hub_loss': 'prandtl_extended',
                    'compressibility': 'prandtl_glauert'
                }
            },
            'performance': {
                'enable_profiling': True,
                'enable_caching': True,
                'parallel_computation': False,
                'memory_optimization': True
            },
            'output': {
                'level': 'detailed',
                'save_convergence_history': True,
                'save_radial_distributions': True,
                'create_performance_plots': True,
                'export_format': ['yaml', 'json', 'csv']
            }
        }
    
    def _create_conventional_config(self) -> Dict:
        """创建常规旋翼配置"""
        
        return {
            'rotor_geometry': {
                'type': 'conventional',
                'radius': 5.0,
                'num_blades': 4,
                'hub_radius': 0.5,
                'chord_distribution': 'linear',
                'twist_distribution': 'linear',
                'root_chord': 0.4,
                'tip_chord': 0.2,
                'root_twist': 12.0,
                'tip_twist': -8.0,
                'blade_sections': 50,
                'enable_3d_geometry': True
            },
            'flight_conditions': {
                'rpm': 400.0,
                'forward_speed': 20.0,
                'lateral_speed': 0.0,
                'vertical_speed': 0.0,
                'density': 1.225,
                'temperature': 288.15,
                'pressure': 101325.0
            },
            'airfoil': {
                'root_airfoil': 'naca0012',
                'tip_airfoil': 'naca0012',
                'interpolation': 'linear',
                'database_path': 'data/airfoils'
            }
        }
    
    def _create_cycloidal_config(self) -> Dict:
        """创建循环翼配置"""
        
        return {
            'rotor_geometry': {
                'type': 'cycloidal',
                'radius': 0.3,
                'num_blades': 6,
                'chord': 0.1,
                'span': 0.5,
                'blade_sections': 20,
                'enable_3d_geometry': True
            },
            'cycloidal_parameters': {
                'azimuth_resolution': 360,
                'pitch_amplitude_top': 20.0,
                'pitch_amplitude_bottom': 15.0,
                'pitch_phase_offset': 0.0,
                'enable_asymmetric_pitch': True,
                'pitch_bias_angle': 0.0,
                'enable_cyclic_inflow': True,
                'enable_blade_interaction': True,
                'enable_unsteady_effects': True
            },
            'flight_conditions': {
                'rpm': 600.0,
                'forward_speed': 5.0,
                'lateral_speed': 0.0,
                'vertical_speed': 0.0,
                'density': 1.225,
                'temperature': 288.15
            },
            'airfoil': {
                'airfoil': 'naca0012',
                'database_path': 'data/airfoils'
            }
        }
    
    def _save_yaml_config(self, file_path: Path, config: Dict):
        """保存YAML配置文件"""
        
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2, 
                     allow_unicode=True, sort_keys=False)
    
    def step5_setup_validation(self):
        """步骤5: 设置验证案例"""
        
        print("\n🧪 步骤5: 设置验证案例...")
        
        validation_cases = [
            'conventional_hover.yaml',
            'conventional_forward.yaml',
            'cycloidal_hover.yaml',
            'cycloidal_forward.yaml'
        ]
        
        cases_created = 0
