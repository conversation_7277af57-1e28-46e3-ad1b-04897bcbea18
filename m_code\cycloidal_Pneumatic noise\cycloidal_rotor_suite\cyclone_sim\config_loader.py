"""
配置管理模块
============

负责加载和管理仿真配置参数，支持YAML格式配置文件。
将原来硬编码在Config类中的参数提取到配置文件中。

作者: zhen
日期: 2025.06.09
"""

import json
import os
import shutil
from dataclasses import asdict, dataclass, field, fields
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

import numpy as np
import yaml


@dataclass
class LBModelParameters:
    """
    Leishman-Beddoes动态失速模型参数

    这些参数控制L-B模型的行为，可以根据不同翼型进行调整。
    参数来源于经典文献和实验数据拟合。

    参考文献:
    - <PERSON><PERSON><PERSON>, J.G. and Beddoes, T.S. (1989). A Semi-Empirical Model for Dynamic Stall
    - 唐继伟博士论文附录A: L-B模型参数表
    """

    # 环量力参数 (Circulatory Force Parameters)
    A1: float = 0.3  # 环量力时间常数参数1
    b1: float = 0.14  # 环量力时间常数参数2
    A2: float = 0.7  # 环量力时间常数参数3
    b2: float = 0.53  # 环量力时间常数参数4

    # 冲量力参数 (Impulsive Force Parameters)
    A3: float = 1.5  # 冲量力时间常数参数1
    b3: float = 0.25  # 冲量力时间常数参数2

    # 分离点参数 (Separation Point Parameters)
    A4: float = 0.04  # 分离点时间常数参数1
    b4: float = 0.1  # 分离点时间常数参数2

    # 前缘涡参数 (Leading Edge Vortex Parameters)
    T_vl: float = 11.0  # 前缘涡对流时间常数
    C_v: float = 1.8  # 前缘涡强度系数

    # 翼型特性参数 (Airfoil Characteristics)
    alpha_0: float = 0.0  # 零升攻角 [度]
    Cn_alpha: float = 2 * np.pi  # 升力线斜率 [1/rad]
    Cm_alpha: float = -0.25  # 力矩线斜率 [1/rad]
    alpha_ss: float = 15.0  # 静态失速攻角 [度]
    Cn_1: float = 1.4  # 失速时的法向力系数

    # 高级参数 (Advanced Parameters)
    eta: float = 0.95  # 恢复因子
    kappa: float = 0.0455  # 分离点参数
    lambda_1: float = 0.04  # 压力滞后参数1
    lambda_2: float = 0.09  # 压力滞后参数2


@dataclass
class ViscousEffectsParameters:
    """
    粘性效应参数

    控制剖面阻力修正和边界层效应的参数
    """

    # 基础阻力参数
    Cd0_base: float = 0.008  # 基础剖面阻力系数
    drag_polar_k: float = 0.5  # 阻力极化曲线参数

    # 雷诺数效应
    Re_transition: float = 5e5  # 转捩雷诺数
    Re_scaling_factor: float = 0.2  # 雷诺数缩放因子

    # 失速后阻力增加
    stall_drag_multiplier: float = 2.0  # 失速后阻力倍增因子

    # 翼型选择
    default_airfoil: str = "NACA 0012"  # 默认翼型


@dataclass
class BEMTEnhancementParameters:
    """
    BEMT求解器增强功能参数

    控制集成的BEMT优化功能
    """

    # 总开关
    enable_bemt_enhancements: bool = True

    # 翼型插值增强
    use_enhanced_interpolation: bool = True
    stall_detection_method: str = "gradient_based"
    interpolation_method: str = "pchip_cubic"

    # 收敛优化
    adaptive_relaxation: bool = True
    enable_aitken_acceleration: bool = True
    convergence_mode: str = "engineering"  # strict/engineering/fast
    max_iterations: int = 100
    convergence_tolerance: float = 1e-6

    # 物理修正
    enable_enhanced_tip_loss: bool = True
    enable_hub_loss: bool = True
    enable_3d_corrections: bool = True

    # 动态失速增强
    enable_enhanced_lb_model: bool = True
    enable_vortex_shedding: bool = True
    cycloidal_correction_factor: float = 1.5

    # 性能监控
    enable_performance_monitoring: bool = True
    detailed_profiling: bool = False


@dataclass
class ValidationParameters:
    """
    验证框架参数

    控制学术验证和不确定性量化功能
    """

    # 验证框架开关
    enable_validation: bool = True

    # 误差容忍度
    default_tolerance: float = 0.05
    confidence_level: float = 0.95

    # 输出设置
    output_directory: str = "validation_results"
    generate_reports: bool = True
    report_format: str = "markdown"  # markdown/html/json

    # 不确定性量化
    enable_uncertainty_analysis: bool = True
    grid_convergence_study: bool = True
    parameter_sensitivity: bool = True


@dataclass
class PerformanceMonitoringParameters:
    """
    性能监控参数

    控制性能监控和分析功能
    """

    # 监控开关
    enable_monitoring: bool = True

    # 采样设置
    memory_sampling_interval: float = 0.1  # 秒
    detailed_profiling: bool = False

    # 报告设置
    auto_generate_reports: bool = True
    report_threshold_seconds: float = 1.0  # 超过此时间的操作会被报告


@dataclass
class WakeSystemParameters:
    """
    尾迹系统参数

    控制尾迹生成、演化和修剪的所有参数
    """

    # 尾迹年龄和距离控制
    max_wake_age: float = 15.0  # 最大尾迹年龄 [转数]
    max_wake_distance: float = 25.0  # 最大尾迹距离 [R的倍数]

    # 修剪和合并参数
    prune_interval: int = 25  # 修剪间隔 [时间步]
    enable_wake_merging: bool = True  # 启用尾迹合并

    # 涡核模型参数
    vortex_core_radius: float = 0.03  # 涡核半径 [c的倍数]
    enable_vatistas_core: bool = True  # 启用Vatistas涡核模型

    # 分层合并阈值
    near_field_merge_threshold: float = 0.5  # 近场合并阈值 [c的倍数]
    mid_field_merge_threshold: float = 2.0  # 中场合并阈值 [c的倍数]
    far_field_merge_threshold: float = 5.0  # 远场合并阈值 [c的倍数]

    # 涡丝拉伸效应
    enable_vortex_stretching: bool = True  # 启用涡丝拉伸
    stretching_factor: float = 1.0  # 拉伸强度因子

    # 🔧 新增：自适应网格细化参数
    enable_adaptive_wake_refinement: bool = True  # 启用自适应尾迹细化
    wake_error_threshold: float = 0.1  # 误差阈值
    max_wake_refinement_level: int = 3  # 最大细化级别
    wake_refinement_interval: int = 10  # 细化间隔 [时间步]


@dataclass
class BPMNoiseParameters:
    """
    BPM宽带噪声模型参数

    控制Brooks-Pope-Marcolini噪声模型的所有参数
    """

    # 基础物理参数
    air_kinematic_viscosity: float = 1.81e-5  # 空气运动粘度 [Pa·s]
    reference_pressure: float = 20e-6  # 参考声压 [Pa] (20 μPa)

    # 湍流参数
    turbulence_intensity: float = 0.05  # 湍流度
    enable_dynamic_boundary_layer: bool = True  # 动态边界层参数

    # 频率范围
    frequency_min: float = 10.0  # 最小频率 [Hz]
    frequency_max: float = 10000.0  # 最大频率 [Hz]
    n_frequencies: int = 100  # 频率点数

    # 噪声源开关
    enable_tbl_te_noise: bool = True  # 湍流边界层-后缘噪声
    enable_separation_noise: bool = True  # 分离流噪声
    enable_tip_vortex_noise: bool = True  # 桨尖涡噪声
    enable_blunt_te_noise: bool = True  # 钝后缘噪声


@dataclass
class AcousticModelParameters:
    """
    声学模型参数

    控制FW-H方程和声学计算的参数
    """

    # 基础声学参数
    reference_pressure: float = 20e-6  # 参考声压 [Pa]
    acoustic_model_level: int = 3  # 声学模型精度级别 (1-3)

    # 噪声分量开关
    enable_thickness_noise: bool = True  # 厚度噪声
    enable_loading_noise: bool = True  # 载荷噪声
    enable_quadrupole_noise: bool = True  # 四极子噪声

    # 频率分析参数
    plot_harmonics_count: int = 4  # 绘制谐波数量
    plot_freq_max: float = 1000.0  # 最大绘制频率 [Hz]

    # 噪声分解参数
    enable_noise_decomposition: bool = True  # 启用噪声源分离
    frequency_bands: int = 50  # 频带数量
    time_window: float = 0.1  # 时间窗口长度 [s]


@dataclass
class RotorConfiguration:
    """
    单个滚翼配置

    定义单个滚翼的位置、方向和相位信息
    """

    id: str = "MainRotor"  # 滚翼标识符
    position: list = field(
        default_factory=lambda: [0.0, 0.0, 0.0]
    )  # [x, y, z] 位置 [m]
    orientation: list = field(
        default_factory=lambda: [0, 0, 0]
    )  # [roll, pitch, yaw] 方向 [度]
    rotation_direction: str = "CCW"  # 旋转方向: CCW/CW
    phase_offset_deg: float = 0.0  # 相位偏移 [度]

    # 滚翼特定参数（如果与主配置不同）
    n_rpm: Optional[float] = None  # 转速 [RPM]，None表示使用主配置
    B: Optional[int] = None  # 桨叶数，None表示使用主配置
    c: Optional[float] = None  # 弦长 [m]，None表示使用主配置
    R_rotor: Optional[float] = None  # 转子半径 [m]，None表示使用主配置


@dataclass
class VehicleLayout:
    """
    多滚翼飞行器布局配置

    定义整个飞行器的滚翼布局和相位同步策略
    """

    rotors: list = field(default_factory=lambda: [RotorConfiguration()])  # 滚翼列表

    # 相位同步参数
    enable_phase_synchronization: bool = False  # 启用相位同步
    sync_strategy: str = "uniform"  # 同步策略: uniform/optimized/custom
    noise_reduction_target: float = 3.0  # 目标降噪量 [dB]

    # 气动干扰参数
    enable_rotor_interaction: bool = True  # 启用滚翼间气动干扰
    interaction_model: str = "full"  # 干扰模型: simple/full/none


@dataclass
class ConventionalRotorParameters:
    """
    传统旋翼参数

    控制传统旋翼模式下的桨叶俯仰角计算参数和几何建模参数
    """

    # ==================== 桨距控制参数 ====================
    # 总距和周期变距参数
    collective_pitch: float = 8.0  # [度] 总距角，所有桨叶的基础俯仰角
    cyclic_pitch_lat: float = 0.0  # [度] 横向周期变距 (A1s)
    cyclic_pitch_lon: float = 0.0  # [度] 纵向周期变距 (B1s)

    # 高级参数
    enable_higher_harmonics: bool = False  # 启用高次谐波变距
    A2s: float = 0.0  # [度] 2次谐波横向分量
    B2s: float = 0.0  # [度] 2次谐波纵向分量
    A3s: float = 0.0  # [度] 3次谐波横向分量
    B3s: float = 0.0  # [度] 3次谐波纵向分量

    # ==================== 几何建模参数 ====================
    # 桨叶几何参数
    hub_radius: float = 0.05  # [m] 桨毂半径
    blade_mass: float = 5.0  # [kg] 单桨叶质量
    blade_cg_position: float = 0.7  # 重心位置 (r/R)

    # 弦长分布参数
    chord_distribution_type: str = "linear"  # 弦长分布类型: linear/elliptical/optimized/custom
    root_chord: float = 0.15  # [m] 根部弦长
    tip_chord: float = 0.08  # [m] 叶尖弦长
    chord_taper_ratio: float = 0.53  # 锥度比 (tip_chord/root_chord)

    # 扭转角分布参数
    twist_distribution_type: str = "linear"  # 扭转角分布类型: linear/optimized/bell/custom
    root_twist: float = 8.0  # [度] 根部扭转角
    tip_twist: float = -8.0  # [度] 叶尖扭转角
    twist_optimization_type: str = "ideal"  # 优化类型: ideal/minimum_power/constant_inflow

    # 设计参数
    design_cl: float = 0.6  # 设计升力系数
    advance_ratio: float = 0.3  # 前进比
    design_inflow_angle: float = 5.0  # [度] 设计流入角（用于恒定流入角扭转）

    # 自定义分布数据（当使用custom类型时）
    custom_chord_stations: Optional[list] = None  # 径向位置 [r/R]
    custom_chord_values: Optional[list] = None    # 弦长值 [m]
    custom_twist_stations: Optional[list] = None  # 径向位置 [r/R]
    custom_twist_values: Optional[list] = None    # 扭转角值 [度]

    # ==================== 锥度角计算参数 ====================
    # 锥度角计算选项
    enable_coning_angle: bool = True  # 启用锥度角计算
    enable_dynamic_coning: bool = False  # 启用动态锥度角求解
    include_structural_coupling: bool = False  # 包含结构耦合

    # 物理参数
    thrust_coefficient: float = 0.008  # 推力系数
    solidity: float = 0.08  # 实度比
    lift_curve_slope: float = 5.73  # 升力线斜率 [1/rad]
    drag_coefficient: float = 0.01  # 阻力系数

    # 结构参数
    flapping_stiffness: float = 1e6  # [N·m/rad] 挥舞刚度
    hinge_offset: float = 0.05  # 铰链偏置 (r/R)

    # 计算参数
    convergence_tolerance: float = 1e-6  # 收敛容差
    max_iterations: int = 100  # 最大迭代次数

    # ==================== 标准配置选择 ====================
    # 预定义配置类型
    standard_config_type: str = "custom"  # 标准配置类型: light/medium/heavy/custom
    # light: 轻型直升机主旋翼
    # medium: 中型直升机主旋翼
    # heavy: 重型直升机主旋翼
    # custom: 自定义配置


@dataclass
class CycloidalRotorParameters:
    """
    滚翼参数

    控制滚翼模式下的桨叶俯仰角计算参数
    """

    # 基础俯仰角参数
    pitch_amplitude_top: float = 15.0  # [度] 上半周俯仰角幅值
    pitch_amplitude_bottom: float = 15.0  # [度] 下半周俯仰角幅值

    # 相位控制参数
    pitch_phase_offset: float = 0.0  # [度] 俯仰角相位偏移

    # 高级控制参数
    enable_asymmetric_pitch: bool = True  # 启用上下半周不对称俯仰
    pitch_bias_angle: float = 0.0  # [度] 俯仰角偏置

    # 非线性俯仰控制
    enable_nonlinear_pitch: bool = False  # 启用非线性俯仰控制
    pitch_nonlinearity_factor: float = 1.0  # 非线性因子


@dataclass
class NumericalParameters:
    """
    数值计算参数

    控制数值方法和稳定性的参数
    """

    # 时间积分参数
    lb_integration_method: str = "rk4"  # L-B模型时间积分方法: euler/rk2/rk4
    enable_adaptive_timestep: bool = False  # 自适应时间步长
    cfl_safety_factor: float = 0.8  # CFL安全因子

    # 迭代求解参数
    enable_preconditioner: bool = True  # 启用预条件器
    enable_subiterations: bool = True  # 气动子迭代
    subiteration_max: int = 5  # 最大子迭代次数

    # 导数计算参数
    derivative_mode: str = "smooth"  # 导数计算模式: simple/smooth/spectral
    alpha_dot_max_limit: float = 50.0  # 攻角变化率限制 [rad/s]
    smoothing_weights: list = field(
        default_factory=lambda: [0.1, 0.2, 0.3, 0.4]
    )  # 平滑权重

    # 数值稳定性参数
    circulation_damping_factor: float = 0.95  # 环量阻尼因子
    wake_regularization_factor: float = 1e-6  # 尾迹正则化因子


@dataclass
class SimulationConfig:
    """
    仿真配置数据类

    包含所有仿真参数，替代原来的Config类
    """

    # ==================== 旋翼类型控制 ====================
    rotor_type: str = "cycloidal"  # 旋翼类型: "cycloidal" 或 "conventional"
    # "cycloidal": 滚翼模式（默认）
    # "conventional": 传统旋翼模式

    # ==================== 基本几何参数 ====================
    n_rpm: float = 600.0  # 转速 [RPM]
    B: int = 6  # 桨叶数
    c: float = 0.1  # 弦长 [m]
    R_rotor: float = 0.3  # 转子半径 [m]
    L_span: float = 0.5  # 桨叶展长 [m]

    # ==================== 流体参数 ====================
    rho: float = 1.225  # 空气密度 [kg/m³]
    c0: float = 343.0  # 声速 [m/s]
    V_inf_x: float = 10.0  # x方向来流速度 [m/s]
    V_inf_y: float = 0.0  # y方向来流速度 [m/s]

    # ==================== 时间参数 ====================
    T_buildup: float = 1.0  # 气动预热时间 [s]
    T_acoustic_record: float = 1.5  # 声学记录时间 [s]
    dt_sim: float = 0.001  # 仿真时间步长 [s]

    # ==================== 观察者配置 ====================
    observer_pos: list = field(
        default_factory=lambda: [2.0, 0.0]
    )  # 观察者位置 [x, y] [m]

    # ==================== 数值参数 ====================
    N_points: int = 20  # 桨叶分段数
    N_wake_max: int = 1000  # 最大尾迹点数
    tolerance: float = 1e-6  # 收敛容差
    max_iterations: int = 100  # 最大迭代次数

    # ==================== 求解器选择 ====================
    solver_fidelity: str = "high"  # 求解器保真度级别
    # "high": UVLM高保真求解器（精确但慢）
    # "medium": 升力线理论求解器（平衡精度和速度）
    # "low": BEMT低保真求解器（快速但简化）

    # ==================== 物理模型开关 ====================
    run_acoustics: bool = True  # 运行声学计算
    enable_lb_model: bool = True  # 启用L-B动态失速模型
    enable_broadband_noise: bool = True  # 启用BPM宽带噪声
    enable_3d_uvlm: bool = True  # 启用三维UVLM
    enable_vatistas_core: bool = True  # 启用Vatistas涡核模型
    enable_wake_pruning: bool = True  # 启用尾迹修剪

    # ==================== 高级功能开关 ====================
    use_advanced_lb_features: bool = True  # 使用增强L-B模型
    enable_ground_effect: bool = False  # 地面效应
    enable_compressibility: bool = False  # 压缩性效应
    enable_viscous_effects: bool = True  # 粘性效应
    enable_turbulence_model: bool = True  # 湍流模型

    # ==================== 输出控制 ====================
    save_checkpoints: bool = True  # 保存检查点
    output_level: str = "comprehensive"  # 输出级别: basic/standard/comprehensive
    create_animations: bool = False  # 创建动画

    # ==================== GPU/CUDA 设置 ====================
    use_gpu: bool = False  # 启用GPU计算
    use_cuda: bool = False  # 启用CUDA加速
    enable_cuda: bool = False  # 启用CUDA（别名）
    device_preference: str = "auto"  # 设备偏好: "auto", "cuda", "cpu"
    gpu_memory_fraction: float = 0.8  # GPU内存使用比例
    enable_mixed_precision: bool = False  # 混合精度计算
    enable_tensor_cores: bool = True  # Tensor Cores (RTX GPU)

    # ==================== 物理模型参数 ====================
    lb_model_params: LBModelParameters = field(default_factory=LBModelParameters)
    viscous_effects_params: ViscousEffectsParameters = field(
        default_factory=ViscousEffectsParameters
    )
    wake_system_params: WakeSystemParameters = field(
        default_factory=WakeSystemParameters
    )
    bpm_noise_params: BPMNoiseParameters = field(default_factory=BPMNoiseParameters)
    acoustic_model_params: AcousticModelParameters = field(
        default_factory=AcousticModelParameters
    )
    numerical_params: NumericalParameters = field(default_factory=NumericalParameters)

    # 🔧 新增：ONERA动态失速模型参数
    enable_onera_dynamic_stall: bool = False  # 启用ONERA模型（替代L-B模型）
    onera_tau_p: float = 1.7  # ONERA压力时间常数
    onera_tau_v: float = 6.0  # ONERA涡时间常数
    onera_K_alpha: float = 0.75  # ONERA攻角修正因子
    onera_K_q: float = 0.2  # ONERA俯仰角速度修正因子

    # ==================== 旋翼模式参数 ====================
    conventional_rotor_params: ConventionalRotorParameters = field(
        default_factory=ConventionalRotorParameters
    )
    cycloidal_rotor_params: CycloidalRotorParameters = field(
        default_factory=CycloidalRotorParameters
    )

    # ==================== 多滚翼系统参数 ====================
    vehicle_layout: VehicleLayout = field(default_factory=VehicleLayout)

    def __post_init__(self):
        """初始化后处理，计算派生参数"""
        # 计算角速度
        self.omega_rotor = self.n_rpm * 2 * np.pi / 60.0

        # 计算BPF频率
        self.bpf = self.B * self.n_rpm / 60.0

        # 计算桨尖马赫数
        self.M_tip = (self.omega_rotor * self.R_rotor) / self.c0

        # 计算雷诺数
        V_tip = self.omega_rotor * self.R_rotor
        self.Re = self.rho * V_tip * self.c / 1.81e-5  # 假设动力粘度

        # 验证参数合理性
        self._validate_parameters()

    def _validate_parameters(self):
        """验证参数合理性"""
        if self.n_rpm <= 0:
            raise ValueError("转速必须为正数")
        if self.B < 2:
            raise ValueError("桨叶数必须至少为2")
        if self.c <= 0 or self.R_rotor <= 0:
            raise ValueError("几何参数必须为正数")
        if self.dt_sim <= 0:
            raise ValueError("时间步长必须为正数")

        # 自动检测转子类型（如果未明确指定）
        self.rotor_type = self._auto_detect_rotor_type()

        # 验证旋翼类型
        if self.rotor_type not in ["cycloidal", "conventional"]:
            raise ValueError(
                f"不支持的旋翼类型: {self.rotor_type}。支持的类型: 'cycloidal', 'conventional'"
            )

        # 验证转子类型特定参数
        self._validate_rotor_type_parameters()

        # 检查CFL条件
        V_max = max(
            abs(self.V_inf_x), abs(self.V_inf_y), self.omega_rotor * self.R_rotor
        )
        dx_min = 2 * np.pi * self.R_rotor / self.N_points  # 最小网格尺寸
        cfl = V_max * self.dt_sim / dx_min

        if cfl > 1.0:
            print(f"警告: CFL数 = {cfl:.2f} > 1.0，可能导致数值不稳定")
            print(f"建议减小时间步长至 dt < {dx_min / V_max:.4f} s")

        # 根据旋翼类型进行特定验证
        if self.rotor_type == "conventional":
            params = self.conventional_rotor_params
            if isinstance(params, dict):
                collective_pitch = params.get('collective_pitch', 8.0)
                root_twist = params.get('root_twist', 8.0)
                tip_twist = params.get('tip_twist', -8.0)
            else:
                collective_pitch = params.collective_pitch
                root_twist = params.root_twist
                tip_twist = params.tip_twist

            print(
                f"使用传统旋翼模式: 总距={collective_pitch:.1f}°, "
                f"根部扭转={root_twist:.1f}°, "
                f"叶尖扭转={tip_twist:.1f}°"
            )
        elif self.rotor_type == "cycloidal":
            print(
                f"使用滚翼模式: 上半周幅值={self.cycloidal_rotor_params.pitch_amplitude_top:.1f}°, "
                f"下半周幅值={self.cycloidal_rotor_params.pitch_amplitude_bottom:.1f}°"
            )

    def _auto_detect_rotor_type(self) -> str:
        """
        自动检测转子类型
        基于配置参数的特征来推断转子类型
        """
        # 如果已经明确指定了转子类型，直接返回
        if hasattr(self, 'rotor_type') and self.rotor_type in ["cycloidal", "conventional"]:
            return self.rotor_type

        # 基于参数特征进行检测
        conventional_indicators = 0
        cycloidal_indicators = 0

        # 检查传统旋翼特征
        if hasattr(self, 'conventional_rotor_params'):
            # 检查是否有周期变距参数
            if (abs(self.conventional_rotor_params.cyclic_pitch_lat) > 0.1 or
                abs(self.conventional_rotor_params.cyclic_pitch_lon) > 0.1):
                conventional_indicators += 2

            # 检查是否有扭转角分布
            if (abs(self.conventional_rotor_params.root_twist - self.conventional_rotor_params.tip_twist) > 1.0):
                conventional_indicators += 1

            # 检查是否启用了锥度角计算
            if self.conventional_rotor_params.enable_coning_angle:
                conventional_indicators += 1

            # 检查标准配置类型
            if self.conventional_rotor_params.standard_config_type in ["light", "medium", "heavy"]:
                conventional_indicators += 2

        # 检查循环翼转子特征
        if hasattr(self, 'cycloidal_rotor_params'):
            # 检查是否有不对称俯仰
            if self.cycloidal_rotor_params.enable_asymmetric_pitch:
                cycloidal_indicators += 1

            # 检查俯仰角幅值
            if (self.cycloidal_rotor_params.pitch_amplitude_top > 5.0 or
                self.cycloidal_rotor_params.pitch_amplitude_bottom > 5.0):
                cycloidal_indicators += 1

        # 基于几何参数进行推断
        # 传统旋翼通常有较大的半径和较少的桨叶
        if self.R_rotor > 1.0 and self.B <= 6:
            conventional_indicators += 1

        # 循环翼转子通常有较小的半径和较多的桨叶
        if self.R_rotor <= 1.0 and self.B >= 4:
            cycloidal_indicators += 1

        # 基于转速进行推断
        # 传统旋翼通常转速较低
        if self.n_rpm < 500:
            conventional_indicators += 1
        # 循环翼转子通常转速较高
        elif self.n_rpm > 800:
            cycloidal_indicators += 1

        # 基于弦长进行推断
        # 传统旋翼通常弦长较大
        if self.c > 0.2:
            conventional_indicators += 1
        # 循环翼转子通常弦长较小
        elif self.c <= 0.15:
            cycloidal_indicators += 1

        # 做出决策
        if conventional_indicators > cycloidal_indicators:
            detected_type = "conventional"
            print(f"🔍 自动检测到传统旋翼配置 (指标得分: {conventional_indicators} vs {cycloidal_indicators})")
        elif cycloidal_indicators > conventional_indicators:
            detected_type = "cycloidal"
            print(f"🔍 自动检测到循环翼转子配置 (指标得分: {cycloidal_indicators} vs {conventional_indicators})")
        else:
            # 默认使用循环翼转子
            detected_type = "cycloidal"
            print(f"🔍 无法明确检测转子类型，使用默认的循环翼转子配置")

        return detected_type

    def _validate_rotor_type_parameters(self):
        """验证转子类型特定参数"""
        if self.rotor_type == "conventional":
            self._validate_conventional_rotor_parameters()
        elif self.rotor_type == "cycloidal":
            self._validate_cycloidal_rotor_parameters()

    def _validate_conventional_rotor_parameters(self):
        """验证传统旋翼参数"""
        params = self.conventional_rotor_params

        # 如果params是字典，转换为对象访问方式
        if isinstance(params, dict):
            # 使用字典访问方式进行验证
            hub_radius = params.get('hub_radius', 0.5)
            root_chord = params.get('root_chord', 0.3)
            tip_chord = params.get('tip_chord', 0.15)
            chord_taper_ratio = params.get('chord_taper_ratio', 0.5)
            root_twist = params.get('root_twist', 8.0)
            tip_twist = params.get('tip_twist', -8.0)
            collective_pitch = params.get('collective_pitch', 8.0)
            cyclic_pitch_lat = params.get('cyclic_pitch_lat', 0.0)
            cyclic_pitch_lon = params.get('cyclic_pitch_lon', 0.0)
            thrust_coefficient = params.get('thrust_coefficient', 0.008)
        else:
            # 使用对象属性访问方式
            hub_radius = params.hub_radius
            root_chord = params.root_chord
            tip_chord = params.tip_chord
            chord_taper_ratio = params.chord_taper_ratio
            root_twist = params.root_twist
            tip_twist = params.tip_twist
            collective_pitch = params.collective_pitch
            cyclic_pitch_lat = params.cyclic_pitch_lat
            cyclic_pitch_lon = params.cyclic_pitch_lon
            thrust_coefficient = params.thrust_coefficient

        # 验证几何参数
        if hub_radius >= self.R_rotor:
            raise ValueError("桨毂半径必须小于转子半径")

        if root_chord <= 0 or tip_chord <= 0:
            raise ValueError("弦长必须为正值")

        if chord_taper_ratio <= 0 or chord_taper_ratio > 1:
            raise ValueError("锥度比必须在(0, 1]范围内")

        # 验证扭转角参数
        twist_range = abs(root_twist - tip_twist)
        if twist_range > 30.0:
            print(f"警告: 扭转角范围 {twist_range:.1f}° 较大，可能影响性能")

        # 验证桨距角参数
        if abs(collective_pitch) > 30.0:
            print(f"警告: 总距角 {collective_pitch:.1f}° 较大")

        if abs(cyclic_pitch_lat) > 15.0 or abs(cyclic_pitch_lon) > 15.0:
            print(f"警告: 周期变距幅值较大，可能导致不稳定")

        # 验证物理参数
        if thrust_coefficient <= 0 or thrust_coefficient > 0.02:
            print(f"警告: 推力系数 {thrust_coefficient:.4f} 可能不合理")

        # 获取其他参数进行验证
        if isinstance(params, dict):
            solidity = params.get('solidity', 0.08)
            standard_config_type = params.get('standard_config_type', 'custom')
        else:
            solidity = params.solidity
            standard_config_type = params.standard_config_type

        if solidity <= 0 or solidity > 0.3:
            print(f"警告: 实度比 {solidity:.3f} 可能不合理")

        # 验证标准配置类型
        if standard_config_type not in ["light", "medium", "heavy", "custom"]:
            print(f"警告: 未知的标准配置类型 {standard_config_type}，使用自定义配置")
            if isinstance(params, dict):
                params['standard_config_type'] = "custom"
            else:
                params.standard_config_type = "custom"

    def _validate_cycloidal_rotor_parameters(self):
        """验证循环翼转子参数"""
        params = self.cycloidal_rotor_params

        # 验证俯仰角幅值 - 修复属性访问问题
        pitch_top = getattr(params, 'pitch_amplitude_top', params.get('pitch_amplitude_top', 15.0) if isinstance(params, dict) else 15.0)
        pitch_bottom = getattr(params, 'pitch_amplitude_bottom', params.get('pitch_amplitude_bottom', 15.0) if isinstance(params, dict) else 15.0)
        
        if pitch_top <= 0 or pitch_bottom <= 0:
            raise ValueError("俯仰角幅值必须为正值")

        if pitch_top > 45.0 or pitch_bottom > 45.0:
            print(f"警告: 俯仰角幅值过大，可能导致失速")

        # 验证相位参数
        if abs(params.pitch_phase_offset) > 360.0:
            print(f"警告: 相位偏移 {params.pitch_phase_offset:.1f}° 超出正常范围")

        # 验证偏置角
        if abs(params.pitch_bias_angle) > 15.0:
            print(f"警告: 俯仰角偏置 {params.pitch_bias_angle:.1f}° 较大")

    def __getitem__(self, key: str):
        """支持字典式访问 config['param']"""
        if hasattr(self, key):
            return getattr(self, key)
        else:
            # 尝试嵌套访问，如 'simulation.time_step'
            if "." in key:
                parts = key.split(".")
                current = self
                for part in parts:
                    if hasattr(current, part):
                        current = getattr(current, part)
                    else:
                        raise KeyError(f"配置参数不存在: {key}")
                return current
            else:
                raise KeyError(f"配置参数不存在: {key}")

    def get(self, key: str, default=None):
        """支持字典式访问 config.get('param', default)"""
        try:
            return self.__getitem__(key)
        except KeyError:
            return default

    def __contains__(self, key: str) -> bool:
        """支持 'param' in config 语法"""
        try:
            self.__getitem__(key)
            return True
        except KeyError:
            return False

    def keys(self):
        """返回所有配置参数的键"""
        return [field.name for field in fields(self)]

    def items(self):
        """返回所有配置参数的键值对"""
        return [(field.name, getattr(self, field.name)) for field in fields(self)]

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for field in fields(self):
            value = getattr(self, field.name)
            if hasattr(value, "__dict__"):  # 嵌套对象
                if hasattr(value, "to_dict"):
                    result[field.name] = value.to_dict()
                else:
                    result[field.name] = asdict(value)
            else:
                result[field.name] = value
        return result


class ConfigLoader:
    """
    配置加载器 (Configuration Loader)

    统一的配置管理系统，支持多种格式的配置文件加载、验证和修复。
    提供智能的参数验证、物理合理性检查和自动错误修复功能。

    主要功能:
        - 多格式支持: YAML、JSON、Python字典
        - 智能验证: 参数类型、范围、物理合理性检查
        - 自动修复: 常见配置错误的自动修复
        - 向后兼容: 支持旧版本配置文件格式
        - 详细日志: 完整的加载和验证过程记录

    支持的配置格式:
        - YAML文件 (.yaml, .yml)
        - JSON文件 (.json)
        - Python字典对象
        - 环境变量覆盖

    验证功能:
        - 必需参数检查
        - 参数类型验证
        - 数值范围验证
        - 物理合理性检查
        - 依赖关系验证
        - 单位一致性检查

    自动修复功能:
        - 缺失参数补充默认值
        - 超出范围参数自动截断
        - 不兼容参数组合修正
        - 单位转换和标准化
        - 过时参数名称更新

    物理参数范围:
        n_rpm: 50-3000 RPM (转速)
        B: 2-12 (桨叶数)
        R_rotor: 0.05-10.0 m (转子半径)
        c: 0.01-2.0 m (弦长)
        rho: 0.5-2.0 kg/m³ (空气密度)
        dt_sim: 1e-6-0.1 s (时间步长)
        V_inf_x/y: -100-100 m/s (来流速度)

    使用示例:
        >>> # 从YAML文件加载
        >>> config = ConfigLoader.from_yaml('simulation.yaml')
        >>>
        >>> # 从字典加载并验证
        >>> config_dict = {'n_rpm': 1200, 'B': 4}
        >>> config = ConfigLoader.from_dict(config_dict, validate=True)
        >>>
        >>> # 启用自动修复
        >>> config = ConfigLoader.from_yaml('config.yaml', auto_fix=True)

    错误处理:
        - ConfigurationError: 配置文件格式错误
        - ValidationError: 参数验证失败
        - FileNotFoundError: 配置文件不存在
        - PermissionError: 文件访问权限问题

    参考文献:
        [1] YAML规范: https://yaml.org/spec/1.2/spec.html
        [2] JSON规范: https://www.json.org/json-en.html
        [3] Pydantic验证: https://pydantic-docs.helpmanual.io/
    """

    # 物理参数范围定义
    PHYSICAL_RANGES = {
        "n_rpm": (50, 3000),  # 转速范围 [RPM]
        "B": (2, 12),  # 桨叶数范围
        "R_rotor": (0.05, 10.0),  # 转子半径范围 [m]
        "c": (0.01, 2.0),  # 弦长范围 [m]
        "L_span": (0.1, 20.0),  # 展长范围 [m]
        "rho": (0.5, 2.0),  # 空气密度范围 [kg/m³]
        "c0": (300, 400),  # 声速范围 [m/s]
        "dt_sim": (1e-6, 0.1),  # 时间步长范围 [s]
        "T_buildup": (0.01, 10.0),  # 建立时间范围 [s]
        "T_acoustic_record": (0.1, 60.0),  # 记录时间范围 [s]
        "V_inf_x": (-100, 100),  # 来流速度X范围 [m/s]
        "V_inf_y": (-100, 100),  # 来流速度Y范围 [m/s]
    }

    @staticmethod
    def from_yaml(file_path: str, auto_fix: bool = True) -> SimulationConfig:
        """
        从YAML文件加载配置

        Args:
            file_path: 配置文件路径
            auto_fix: 是否自动修复配置问题

        Returns:
            仿真配置对象
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        with open(file_path, "r", encoding="utf-8") as f:
            config_dict = yaml.safe_load(f)

        # 自动修复配置（如果启用）
        if auto_fix:
            config_dict, fixes_applied = ConfigLoader._auto_fix_config(
                config_dict, file_path
            )
            if fixes_applied:
                print(f"✅ 配置文件已自动修复: {file_path}")

        return ConfigLoader.from_dict(config_dict)

    @staticmethod
    def from_json(file_path: str) -> SimulationConfig:
        """从JSON文件加载配置"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")

        with open(file_path, "r", encoding="utf-8") as f:
            config_dict = json.load(f)

        return ConfigLoader.from_dict(config_dict)

    @staticmethod
    def from_dict(config_dict: Dict[str, Any]) -> SimulationConfig:
        """从字典加载配置"""
        # 处理嵌套的配置参数
        processed_dict = {}

        # 获取SimulationConfig的所有字段
        config_fields = {
            f.name: f for f in SimulationConfig.__dataclass_fields__.values()
        }

        # 处理特殊的嵌套配置
        ConfigLoader._process_nested_configs(config_dict, processed_dict)

        for key, value in config_dict.items():
            if key in config_fields:
                field = config_fields[key]

                # 处理嵌套的数据类
                if hasattr(field.type, "__dataclass_fields__"):
                    # 这是一个数据类，需要特殊处理
                    if isinstance(value, dict):
                        processed_dict[key] = field.type(**value)
                    else:
                        processed_dict[key] = value
                else:
                    processed_dict[key] = value

        return SimulationConfig(**processed_dict)

    @staticmethod
    def _process_nested_configs(
        config_dict: Dict[str, Any], processed_dict: Dict[str, Any]
    ):
        """处理嵌套配置，如GPU设置"""

        # 处理GPU/CUDA设置
        if "computing" in config_dict:
            computing_config = config_dict["computing"]

            if "gpu" in computing_config:
                gpu_config = computing_config["gpu"]

                # 映射GPU配置到SimulationConfig字段
                gpu_mappings = {
                    "use_gpu": "use_gpu",
                    "use_cuda": "use_cuda",
                    "enable_cuda": "enable_cuda",
                    "device_preference": "device_preference",
                    "memory_fraction": "gpu_memory_fraction",
                    "enable_mixed_precision": "enable_mixed_precision",
                    "enable_tensor_cores": "enable_tensor_cores",
                }

                for yaml_key, config_key in gpu_mappings.items():
                    if yaml_key in gpu_config:
                        processed_dict[config_key] = gpu_config[yaml_key]

                # 处理嵌套的GPU内存管理设置
                if "memory_management" in gpu_config:
                    mem_config = gpu_config["memory_management"]
                    if "memory_fraction" in mem_config:
                        processed_dict["gpu_memory_fraction"] = mem_config[
                            "memory_fraction"
                        ]

                # 处理嵌套的GPU计算设置
                if "computation" in gpu_config:
                    comp_config = gpu_config["computation"]
                    if "enable_mixed_precision" in comp_config:
                        processed_dict["enable_mixed_precision"] = comp_config[
                            "enable_mixed_precision"
                        ]
                    if "enable_tensor_cores" in comp_config:
                        processed_dict["enable_tensor_cores"] = comp_config[
                            "enable_tensor_cores"
                        ]

    @staticmethod
    def create_default_config() -> SimulationConfig:
        """创建默认配置"""
        return SimulationConfig()

    @staticmethod
    def save_config(config: SimulationConfig, file_path: str, format: str = "yaml"):
        """保存配置到文件"""
        # 转换为字典
        config_dict = {
            field.name: getattr(config, field.name)
            for field in config.__dataclass_fields__.values()
        }

        # 添加注释信息
        config_dict["_metadata"] = {
            "created_by": "Cyclone Sim v3.0",
            "description": "滚翼机气动声学仿真配置文件",
            "derived_parameters": {
                "omega_rotor": config.omega_rotor,
                "bpf": config.bpf,
                "M_tip": config.M_tip,
                "Re": config.Re,
            },
        }

        if format.lower() == "yaml":
            with open(file_path, "w", encoding="utf-8") as f:
                yaml.dump(
                    config_dict,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2,
                )
        elif format.lower() == "json":
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的格式: {format}")

        print(f"配置已保存到: {file_path}")

    @staticmethod
    def _fix_rotor_type_detection(config_dict: Dict[str, Any]) -> bool:
        """
        修复转子类型检测和参数

        Args:
            config_dict: 配置字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False

        # 如果没有明确指定转子类型，尝试自动检测
        if 'rotor_type' not in config_dict:
            # 基于参数特征进行检测
            conventional_indicators = 0
            cycloidal_indicators = 0

            # 检查传统旋翼特征
            if 'conventional_rotor_params' in config_dict:
                conventional_indicators += 2

            # 检查循环翼转子特征
            if 'cycloidal_rotor_params' in config_dict:
                cycloidal_indicators += 1

            # 基于几何参数推断
            if config_dict.get('R_rotor', 0.3) > 1.0 and config_dict.get('B', 6) <= 6:
                conventional_indicators += 1
            elif config_dict.get('R_rotor', 0.3) <= 1.0 and config_dict.get('B', 6) >= 4:
                cycloidal_indicators += 1

            # 基于转速推断
            if config_dict.get('n_rpm', 600) < 500:
                conventional_indicators += 1
            elif config_dict.get('n_rpm', 600) > 800:
                cycloidal_indicators += 1

            # 设置检测到的转子类型
            if conventional_indicators > cycloidal_indicators:
                config_dict['rotor_type'] = 'conventional'
                print("🔧 自动检测并设置转子类型为: conventional")
            else:
                config_dict['rotor_type'] = 'cycloidal'
                print("🔧 自动检测并设置转子类型为: cycloidal")

            fixes_applied = True

        return fixes_applied

    @staticmethod
    def _auto_fix_config(config_dict: Dict[str, Any], file_path: str) -> tuple:
        """
        自动修复配置问题

        Args:
            config_dict: 配置字典
            file_path: 配置文件路径

        Returns:
            (修复后的配置字典, 是否应用了修复)
        """
        fixes_applied = False
        original_config = config_dict.copy()

        # 1. 修复转子类型检测和参数
        if ConfigLoader._fix_rotor_type_detection(config_dict):
            fixes_applied = True
            print("✅ 自动检测并设置转子类型")

        # 2. 向后兼容性检查和自动迁移
        if ConfigLoader._migrate_legacy_config(config_dict):
            fixes_applied = True
            print("✅ 迁移旧版本配置格式")

        # 3. 修复传统旋翼参数
        if ConfigLoader._fix_conventional_rotor_params(config_dict):
            fixes_applied = True
            print("✅ 修复传统旋翼参数")

        # 4. 修复时间参数逻辑错误
        time_fixed = ConfigLoader._fix_time_parameters(config_dict)
        if time_fixed:
            fixes_applied = True
            print("  🕐 修复时间参数逻辑错误")

        # 2. 修复UVLM数值稳定性参数
        uvlm_fixed = ConfigLoader._fix_uvlm_stability(config_dict)
        if uvlm_fixed:
            fixes_applied = True
            print("  🔧 修复UVLM数值稳定性参数")

        # 3. 启用GPU加速（如果适用）
        gpu_fixed = ConfigLoader._enable_gpu_acceleration(config_dict)
        if gpu_fixed:
            fixes_applied = True
            print("  🚀 启用GPU加速优化")

        # 4. 验证参数范围
        range_fixed = ConfigLoader._fix_parameter_ranges(config_dict)
        if range_fixed:
            fixes_applied = True
            print("  📊 修复参数范围问题")

        # 如果有修复，备份原文件并保存修复后的配置
        if fixes_applied:
            ConfigLoader._backup_and_save_config(
                original_config, config_dict, file_path
            )

        return config_dict, fixes_applied

    @staticmethod
    def _fix_time_parameters(config_dict: Dict[str, Any]) -> bool:
        """修复时间参数逻辑错误"""
        fixed = False

        # 查找时间参数
        t_buildup = config_dict.get("T_buildup")
        t_acoustic_record = config_dict.get("T_acoustic_record")

        # 修复逻辑关系
        if t_buildup is not None and t_acoustic_record is not None:
            if isinstance(t_buildup, (int, float)) and isinstance(
                t_acoustic_record, (int, float)
            ):
                if t_buildup >= t_acoustic_record:
                    config_dict["T_buildup"] = 0.3 * t_acoustic_record
                    fixed = True
                elif t_buildup > 0.5 * t_acoustic_record:
                    config_dict["T_buildup"] = 0.3 * t_acoustic_record
                    fixed = True

        # 修复不合理的单个参数
        if t_buildup is not None and isinstance(t_buildup, (int, float)):
            if t_buildup <= 0:
                config_dict["T_buildup"] = 0.5
                fixed = True
            elif t_buildup > 10.0:
                config_dict["T_buildup"] = 2.0
                fixed = True

        if t_acoustic_record is not None and isinstance(
            t_acoustic_record, (int, float)
        ):
            if t_acoustic_record <= 0:
                config_dict["T_acoustic_record"] = 2.0
                fixed = True
            elif t_acoustic_record > 60.0:
                config_dict["T_acoustic_record"] = 10.0
                fixed = True

        return fixed

    @staticmethod
    def _fix_uvlm_stability(config_dict: Dict[str, Any]) -> bool:
        """修复UVLM数值稳定性参数"""
        fixed = False

        # 查找UVLM配置
        uvlm_paths = [
            ["uvlm_settings"],
            ["solver_options", "uvlm"],
            ["physics", "aerodynamics", "uvlm_settings"],
            ["solvers", "uvlm"],
        ]

        for path in uvlm_paths:
            current = config_dict
            for key in path[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]

            final_key = path[-1]
            if final_key not in current:
                current[final_key] = {}

            uvlm_config = current[final_key]

            # 修复涡核半径
            if (
                "vortex_core_radius" not in uvlm_config
                or uvlm_config.get("vortex_core_radius", 0) < 0.02
            ):
                # 自适应计算基于弦长
                chord_length = config_dict.get("c", 0.1)
                uvlm_config["vortex_core_radius"] = max(
                    0.02, min(0.05, 0.03 * chord_length)
                )
                fixed = True

            # 启用预条件器
            if not uvlm_config.get("enable_preconditioning", False):
                uvlm_config["enable_preconditioning"] = True
                uvlm_config["preconditioner_type"] = "diagonal"
                fixed = True

            # 添加数值阻尼
            if "numerical_damping" not in uvlm_config:
                uvlm_config["numerical_damping"] = 1e-6
                fixed = True

            # 设置条件数阈值
            if "condition_number_threshold" not in uvlm_config:
                uvlm_config["condition_number_threshold"] = 1e12
                fixed = True

        return fixed

    @staticmethod
    def _enable_gpu_acceleration(config_dict: Dict[str, Any]) -> bool:
        """启用GPU加速（如果适用）"""
        fixed = False

        # 检查是否为3D UVLM配置
        uvlm_indicators = ["enable_3d", "enable_3d_uvlm", "uvlm_3d", "span_elements"]
        is_3d_uvlm = any(
            config_dict.get(indicator, False) for indicator in uvlm_indicators
        )

        if is_3d_uvlm:
            # 查找UVLM配置并启用GPU
            uvlm_paths = [
                ["uvlm_settings"],
                ["solver_options", "uvlm"],
                ["physics", "aerodynamics", "uvlm_settings"],
            ]

            for path in uvlm_paths:
                current = config_dict
                for key in path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                final_key = path[-1]
                if final_key not in current:
                    current[final_key] = {}

                uvlm_config = current[final_key]

                # 启用GPU设置
                if not uvlm_config.get("use_gpu", False):
                    uvlm_config["use_gpu"] = True
                    uvlm_config["gpu_device_id"] = 0
                    uvlm_config["gpu_memory_fraction"] = 0.8
                    uvlm_config["enable_mixed_precision"] = True
                    uvlm_config["batch_processing"] = True
                    uvlm_config["gpu_batch_size"] = 1000
                    uvlm_config["cuda_optimization_level"] = "O2"
                    fixed = True

        return fixed

    @staticmethod
    def _fix_parameter_ranges(config_dict: Dict[str, Any]) -> bool:
        """修复参数范围问题"""
        fixed = False

        for param, (min_val, max_val) in ConfigLoader.PHYSICAL_RANGES.items():
            if param in config_dict:
                value = config_dict[param]
                if isinstance(value, (int, float)):
                    if value < min_val:
                        config_dict[param] = min_val
                        fixed = True
                    elif value > max_val:
                        config_dict[param] = max_val
                        fixed = True

        return fixed

    @staticmethod
    def _backup_and_save_config(
        original_config: Dict[str, Any], fixed_config: Dict[str, Any], file_path: str
    ):
        """备份原文件并保存修复后的配置"""
        file_path = Path(file_path)

        # 创建备份目录
        backup_dir = (
            file_path.parent
            / "backups"
            / f"auto_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 备份原文件
        backup_path = backup_dir / file_path.name
        shutil.copy2(file_path, backup_path)

        # 添加修复标记
        fixed_config["_auto_fix_applied"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        fixed_config["_auto_fix_version"] = "v3.0"

        # 保存修复后的配置
        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(
                fixed_config, f, default_flow_style=False, allow_unicode=True, indent=2
            )

        print(f"  💾 原文件已备份到: {backup_path}")
        print(f"  💾 修复后配置已保存: {file_path}")

    @staticmethod
    def _migrate_legacy_config(config_dict: Dict[str, Any]) -> bool:
        """
        迁移旧版本配置格式

        Args:
            config_dict: 配置字典

        Returns:
            bool: 是否应用了迁移
        """
        fixes_applied = False

        # 1. 迁移旧的转子类型参数名称
        legacy_rotor_type_mappings = {
            'rotor_mode': 'rotor_type',
            'cycloidal_mode': 'rotor_type',
            'conventional_mode': 'rotor_type'
        }

        for old_key, new_key in legacy_rotor_type_mappings.items():
            if old_key in config_dict and new_key not in config_dict:
                if old_key == 'cycloidal_mode' and config_dict[old_key]:
                    config_dict[new_key] = 'cycloidal'
                elif old_key == 'conventional_mode' and config_dict[old_key]:
                    config_dict[new_key] = 'conventional'
                else:
                    config_dict[new_key] = config_dict[old_key]

                del config_dict[old_key]
                fixes_applied = True
                print(f"🔄 迁移参数: {old_key} -> {new_key}")

        return fixes_applied

    @staticmethod
    def _fix_conventional_rotor_params(config_dict: Dict[str, Any]) -> bool:
        """
        修复传统旋翼参数

        Args:
            config_dict: 配置字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False

        # 如果是传统旋翼但缺少参数，添加默认参数
        if config_dict.get('rotor_type') == 'conventional':
            if 'conventional_rotor_params' not in config_dict:
                config_dict['conventional_rotor_params'] = {}
                fixes_applied = True

            params = config_dict['conventional_rotor_params']

            # 检测直升机类型并应用相应的标准参数
            helicopter_type = ConfigLoader._detect_helicopter_type(config_dict)
            if ConfigLoader._apply_helicopter_standard_params(config_dict, helicopter_type):
                fixes_applied = True

            # 修复弦长分布参数
            if ConfigLoader._fix_chord_distribution_params(params):
                fixes_applied = True

            # 修复扭转角分布参数
            if ConfigLoader._fix_twist_distribution_params(params):
                fixes_applied = True

            # 修复时间参数逻辑
            if ConfigLoader._fix_time_parameter_logic(config_dict):
                fixes_applied = True

        return fixes_applied

    @staticmethod
    def _detect_helicopter_type(config_dict: Dict[str, Any]) -> str:
        """检测直升机类型"""
        R_rotor = config_dict.get('R_rotor', 1.0)
        n_rpm = config_dict.get('n_rpm', 400)

        if R_rotor < 3.0 and n_rpm > 500:
            return 'light'
        elif R_rotor > 7.0 and n_rpm < 300:
            return 'heavy'
        else:
            return 'medium'

    @staticmethod
    def _apply_helicopter_standard_params(config_dict: Dict[str, Any], config_type: str) -> bool:
        """应用直升机标准参数"""
        fixes_applied = False

        if 'conventional_rotor_params' not in config_dict:
            config_dict['conventional_rotor_params'] = {}

        params = config_dict['conventional_rotor_params']

        if config_type == 'light':
            standard_params = {
                'collective_pitch': 8.0,
                'root_twist': 12.0,
                'tip_twist': -8.0,
                'chord_distribution_type': 'linear',
                'twist_distribution_type': 'linear',
                'hub_radius': 0.15,
                'thrust_coefficient': 0.008,
                'solidity': 0.08,
                'root_chord': 0.15,
                'tip_chord': 0.08,
                'enable_coning_angle': False
            }
            config_updates = {
                'R_rotor': 3.5,
                'n_rpm': 600,
                'B': 2,
                'c': 0.12
            }
        elif config_type == 'medium':
            standard_params = {
                'collective_pitch': 10.0,
                'root_twist': 14.0,
                'tip_twist': -10.0,
                'chord_distribution_type': 'tapered',
                'twist_distribution_type': 'optimized',
                'hub_radius': 0.8,
                'thrust_coefficient': 0.010,
                'solidity': 0.10,
                'root_chord': 0.3,
                'tip_chord': 0.15,
                'enable_coning_angle': True
            }
            config_updates = {
                'R_rotor': 6.5,
                'n_rpm': 280,
                'B': 4,
                'c': 0.275
            }
        elif config_type == 'heavy':
            standard_params = {
                'collective_pitch': 12.0,
                'root_twist': 15.0,
                'tip_twist': -12.0,
                'chord_distribution_type': 'optimized',
                'twist_distribution_type': 'optimized',
                'hub_radius': 1.2,
                'thrust_coefficient': 0.012,
                'solidity': 0.12,
                'root_chord': 0.45,
                'tip_chord': 0.25,
                'enable_coning_angle': True,
                'enable_dynamic_coning': True
            }
            config_updates = {
                'R_rotor': 9.0,
                'n_rpm': 220,
                'B': 5,
                'c': 0.35
            }
        else:
            return fixes_applied

        # 应用标准参数（只有当参数不存在时才设置）
        for key, value in standard_params.items():
            if key not in params:
                params[key] = value
                fixes_applied = True

        # 更新主配置参数（只有当参数不存在或使用默认值时才更新）
        for key, value in config_updates.items():
            if key not in config_dict or ConfigLoader._is_default_value(config_dict[key], key):
                config_dict[key] = value
                fixes_applied = True

        if fixes_applied:
            print(f"🔧 应用{config_type}直升机标准配置参数")

        return fixes_applied

    @staticmethod
    def _is_default_value(value: Any, param_name: str) -> bool:
        """检查是否为默认值"""
        default_values = {
            'R_rotor': 1.0,
            'n_rpm': 400,
            'B': 4,
            'c': 0.1
        }
        return value == default_values.get(param_name, None)

    @staticmethod
    def _fix_chord_distribution_params(params: Dict[str, Any]) -> bool:
        """修复弦长分布参数"""
        fixes_applied = False

        chord_type = params.get('chord_distribution_type', 'linear')

        if chord_type == 'linear':
            if 'root_chord' not in params:
                params['root_chord'] = 0.2
                fixes_applied = True
            if 'tip_chord' not in params:
                params['tip_chord'] = 0.1
                fixes_applied = True
        elif chord_type == 'tapered':
            if 'taper_ratio' not in params:
                params['taper_ratio'] = 0.5
                fixes_applied = True
        elif chord_type == 'optimized':
            if 'chord_optimization_target' not in params:
                params['chord_optimization_target'] = 'efficiency'
                fixes_applied = True

        if fixes_applied:
            print("🔧 修复弦长分布参数")

        return fixes_applied

    @staticmethod
    def _fix_twist_distribution_params(params: Dict[str, Any]) -> bool:
        """修复扭转角分布参数"""
        fixes_applied = False

        twist_type = params.get('twist_distribution_type', 'linear')

        if twist_type == 'linear':
            if 'root_twist' not in params:
                params['root_twist'] = 10.0
                fixes_applied = True
            if 'tip_twist' not in params:
                params['tip_twist'] = -8.0
                fixes_applied = True
        elif twist_type == 'optimized':
            if 'twist_optimization_target' not in params:
                params['twist_optimization_target'] = 'performance'
                fixes_applied = True

        if fixes_applied:
            print("🔧 修复扭转角分布参数")

        return fixes_applied

    @staticmethod
    def _fix_time_parameter_logic(config_dict: Dict[str, Any]) -> bool:
        """修复时间参数逻辑"""
        fixes_applied = False

        T_buildup = config_dict.get('T_buildup')
        T_acoustic_record = config_dict.get('T_acoustic_record')

        if T_buildup and T_acoustic_record:
            if T_buildup >= T_acoustic_record:
                config_dict['T_buildup'] = 0.3 * T_acoustic_record
                fixes_applied = True
                print(f"🔧 修复建立时间: {T_buildup} -> {config_dict['T_buildup']}")

        if T_acoustic_record and T_acoustic_record > 60.0:
            config_dict['T_acoustic_record'] = 10.0
            fixes_applied = True
            print(f"🔧 修复声学记录时间: {T_acoustic_record} -> {config_dict['T_acoustic_record']}")

        return fixes_applied


def create_example_configs():
    """创建示例配置文件"""

    # 基础配置
    basic_config = SimulationConfig(
        n_rpm=600,
        B=6,
        c=0.1,
        R_rotor=0.3,
        enable_3d_uvlm=False,
        enable_broadband_noise=False,
        output_level="basic",
    )

    # 高级配置
    advanced_config = SimulationConfig(
        n_rpm=800,
        B=8,
        c=0.12,
        R_rotor=0.35,
        enable_3d_uvlm=True,
        enable_broadband_noise=True,
        use_advanced_lb_features=True,
        enable_turbulence_model=True,
        output_level="comprehensive",
        create_animations=True,
    )

    return basic_config, advanced_config


if __name__ == "__main__":
    # 测试配置系统
    print("测试配置系统...")

    # 创建默认配置
    config = ConfigLoader.create_default_config()
    print(f"默认配置创建成功，BPF = {config.bpf:.1f} Hz")

    # 创建示例配置
    basic, advanced = create_example_configs()
    print(f"基础配置: 转速={basic.n_rpm} RPM, 桨叶数={basic.B}")
    print(f"高级配置: 转速={advanced.n_rpm} RPM, 桨叶数={advanced.B}")

def load_config(config_path: Union[str, Path] = None) -> Dict[str, Any]:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径，如果为None则使用默认配置

    Returns:
        配置字典
    """
    if config_path is None:
        # 返回默认配置
        return {
            'lb_model': asdict(LBModelParameters()),
            'simulation': {
                'dt': 0.001,
                'max_iterations': 1000,
                'convergence_tolerance': 1e-6
            }
        }

    config_path = Path(config_path)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    with open(config_path, 'r', encoding='utf-8') as f:
        if config_path.suffix.lower() in ['.yml', '.yaml']:
            config = yaml.safe_load(f)
        elif config_path.suffix.lower() == '.json':
            config = json.load(f)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")

        return config

    @staticmethod
    def _migrate_legacy_config(config_dict: Dict[str, Any]) -> bool:
        """
        迁移旧版本配置格式

        Args:
            config_dict: 配置字典

        Returns:
            bool: 是否应用了迁移
        """
        fixes_applied = False

        # 1. 迁移旧的转子类型参数名称
        legacy_rotor_type_mappings = {
            'rotor_mode': 'rotor_type',
            'cycloidal_mode': 'rotor_type',
            'conventional_mode': 'rotor_type'
        }

        for old_key, new_key in legacy_rotor_type_mappings.items():
            if old_key in config_dict and new_key not in config_dict:
                if old_key == 'cycloidal_mode' and config_dict[old_key]:
                    config_dict[new_key] = 'cycloidal'
                elif old_key == 'conventional_mode' and config_dict[old_key]:
                    config_dict[new_key] = 'conventional'
                else:
                    config_dict[new_key] = config_dict[old_key]

                del config_dict[old_key]
                fixes_applied = True
                print(f"🔄 迁移参数: {old_key} -> {new_key}")

        # 2. 迁移旧的传统旋翼参数结构
        if ConfigLoader._migrate_conventional_rotor_structure(config_dict):
            fixes_applied = True

        # 3. 迁移旧的GPU配置结构
        if ConfigLoader._migrate_gpu_config_structure(config_dict):
            fixes_applied = True

        # 4. 迁移旧的物理模型开关
        if ConfigLoader._migrate_physics_model_switches(config_dict):
            fixes_applied = True

        # 5. 迁移旧的数值参数
        if ConfigLoader._migrate_numerical_parameters(config_dict):
            fixes_applied = True

        return fixes_applied

    @staticmethod
    def _migrate_conventional_rotor_structure(config_dict: Dict[str, Any]) -> bool:
        """迁移传统旋翼参数结构"""
        fixes_applied = False

        # 旧版本可能将传统旋翼参数直接放在顶层
        legacy_conventional_params = [
            'collective_pitch', 'cyclic_pitch_lat', 'cyclic_pitch_lon',
            'root_twist', 'tip_twist', 'root_chord', 'tip_chord',
            'hub_radius', 'enable_coning_angle', 'thrust_coefficient',
            'solidity', 'chord_distribution_type', 'twist_distribution_type'
        ]

        # 检查是否有旧版本的传统旋翼参数
        found_legacy_params = {}
        for param in legacy_conventional_params:
            if param in config_dict:
                found_legacy_params[param] = config_dict[param]

        if found_legacy_params:
            # 确保有conventional_rotor_params结构
            if 'conventional_rotor_params' not in config_dict:
                config_dict['conventional_rotor_params'] = {}

            # 迁移参数
            for param, value in found_legacy_params.items():
                if param not in config_dict['conventional_rotor_params']:
                    config_dict['conventional_rotor_params'][param] = value
                    del config_dict[param]
                    fixes_applied = True

            if fixes_applied:
                print("🔄 迁移传统旋翼参数到新的结构")

        return fixes_applied

    @staticmethod
    def _migrate_gpu_config_structure(config_dict: Dict[str, Any]) -> bool:
        """迁移GPU配置结构"""
        fixes_applied = False

        # 旧版本的GPU参数映射
        legacy_gpu_mappings = {
            'enable_gpu': 'use_gpu',
            'gpu_enabled': 'use_gpu',
            'cuda_enabled': 'use_cuda',
            'gpu_device': 'device_preference',
            'gpu_memory': 'gpu_memory_fraction'
        }

        for old_key, new_key in legacy_gpu_mappings.items():
            if old_key in config_dict and new_key not in config_dict:
                config_dict[new_key] = config_dict[old_key]
                del config_dict[old_key]
                fixes_applied = True
                print(f"🔄 迁移GPU参数: {old_key} -> {new_key}")

        return fixes_applied

    @staticmethod
    def _migrate_physics_model_switches(config_dict: Dict[str, Any]) -> bool:
        """迁移物理模型开关"""
        fixes_applied = False

        # 旧版本的物理模型开关映射
        legacy_physics_mappings = {
            'dynamic_stall': 'enable_lb_model',
            'lb_dynamic_stall': 'enable_lb_model',
            'broadband_noise_model': 'enable_broadband_noise',
            'bpm_noise': 'enable_broadband_noise',
            'uvlm_3d': 'enable_3d_uvlm',
            'vatistas_core_model': 'enable_vatistas_core',
            'wake_pruning_enabled': 'enable_wake_pruning',
            'ground_effect_model': 'enable_ground_effect',
            'compressibility_effects': 'enable_compressibility',
            'viscous_effects_model': 'enable_viscous_effects'
        }

        for old_key, new_key in legacy_physics_mappings.items():
            if old_key in config_dict and new_key not in config_dict:
                config_dict[new_key] = config_dict[old_key]
                del config_dict[old_key]
                fixes_applied = True
                print(f"🔄 迁移物理模型开关: {old_key} -> {new_key}")

        return fixes_applied

    @staticmethod
    def _migrate_numerical_parameters(config_dict: Dict[str, Any]) -> bool:
        """迁移数值参数"""
        fixes_applied = False

        # 旧版本的数值参数映射
        legacy_numerical_mappings = {
            'max_iter': 'max_iterations',
            'convergence_tol': 'tolerance',
            'time_step': 'dt_sim',
            'n_blade_points': 'N_points',
            'max_wake_points': 'N_wake_max'
        }

        for old_key, new_key in legacy_numerical_mappings.items():
            if old_key in config_dict and new_key not in config_dict:
                config_dict[new_key] = config_dict[old_key]
                del config_dict[old_key]
                fixes_applied = True
                print(f"🔄 迁移数值参数: {old_key} -> {new_key}")

        return fixes_applied

    @staticmethod
    def _fix_conventional_rotor_params(config_dict: Dict[str, Any]) -> bool:
        """
        修复传统旋翼参数

        Args:
            config_dict: 配置字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False

        # 如果是传统旋翼但缺少参数，添加默认参数
        if config_dict.get('rotor_type') == 'conventional':
            if 'conventional_rotor_params' not in config_dict:
                config_dict['conventional_rotor_params'] = {}
                fixes_applied = True
                print("🔧 添加缺失的传统旋翼参数")

            params = config_dict['conventional_rotor_params']

            # 修复标准配置类型
            if 'standard_config_type' not in params:
                # 基于几何参数推断标准配置类型
                R_rotor = config_dict.get('R_rotor', 0.3)
                n_rpm = config_dict.get('n_rpm', 600)

                if R_rotor < 3.0 and n_rpm > 400:
                    params['standard_config_type'] = 'light'
                elif R_rotor < 8.0 and n_rpm > 250:
                    params['standard_config_type'] = 'medium'
                elif R_rotor >= 8.0 or n_rpm <= 250:
                    params['standard_config_type'] = 'heavy'
                else:
                    params['standard_config_type'] = 'custom'

                fixes_applied = True
                print(f"🔧 设置标准配置类型为: {params['standard_config_type']}")

            # 应用标准配置参数
            if ConfigLoader._apply_standard_config(params, config_dict):
                fixes_applied = True

            # 修复弦长分布参数
            if ConfigLoader._fix_chord_distribution_params(params):
                fixes_applied = True

            # 修复扭转角分布参数
            if ConfigLoader._fix_twist_distribution_params(params):
                fixes_applied = True

        return fixes_applied

    @staticmethod
    def _apply_standard_config(params: Dict[str, Any], config_dict: Dict[str, Any]) -> bool:
        """
        应用标准配置参数

        Args:
            params: 传统旋翼参数字典
            config_dict: 完整配置字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False
        config_type = params.get('standard_config_type', 'custom')

        if config_type == 'light':
            # 轻型直升机标准参数
            standard_params = {
                'collective_pitch': 6.0,
                'root_twist': 6.0,
                'tip_twist': -6.0,
                'chord_distribution_type': 'linear',
                'twist_distribution_type': 'linear',
                'hub_radius': 0.3,
                'thrust_coefficient': 0.006,
                'solidity': 0.06,
                'root_chord': 0.25,
                'tip_chord': 0.15,
                'enable_coning_angle': True
            }
            # 更新主配置参数
            config_updates = {
                'R_rotor': 4.5,
                'n_rpm': 350,
                'B': 4,
                'c': 0.2
            }
        elif config_type == 'medium':
            # 中型直升机标准参数
            standard_params = {
                'collective_pitch': 10.0,
                'root_twist': 12.0,
                'tip_twist': -10.0,
                'chord_distribution_type': 'optimized',
                'twist_distribution_type': 'optimized',
                'hub_radius': 0.8,
                'thrust_coefficient': 0.010,
                'solidity': 0.10,
                'root_chord': 0.35,
                'tip_chord': 0.20,
                'enable_coning_angle': True
            }
            # 更新主配置参数
            config_updates = {
                'R_rotor': 6.5,
                'n_rpm': 280,
                'B': 4,
                'c': 0.275
            }
        elif config_type == 'heavy':
            # 重型直升机标准参数
            standard_params = {
                'collective_pitch': 12.0,
                'root_twist': 15.0,
                'tip_twist': -12.0,
                'chord_distribution_type': 'optimized',
                'twist_distribution_type': 'optimized',
                'hub_radius': 1.2,
                'thrust_coefficient': 0.012,
                'solidity': 0.12,
                'root_chord': 0.45,
                'tip_chord': 0.25,
                'enable_coning_angle': True,
                'enable_dynamic_coning': True
            }
            # 更新主配置参数
            config_updates = {
                'R_rotor': 9.0,
                'n_rpm': 220,
                'B': 5,
                'c': 0.35
            }
        else:
            return fixes_applied

        # 应用标准参数（只有当参数不存在时才设置）
        for key, value in standard_params.items():
            if key not in params:
                params[key] = value
                fixes_applied = True

        # 更新主配置参数（只有当参数不存在或使用默认值时才更新）
        for key, value in config_updates.items():
            if key not in config_dict or ConfigLoader._is_default_value(config_dict[key], key):
                config_dict[key] = value
                fixes_applied = True

        if fixes_applied:
            print(f"🔧 应用{config_type}直升机标准配置参数")

        return fixes_applied

    @staticmethod
    def _is_default_value(value: Any, param_name: str) -> bool:
        """检查是否为默认值"""
        default_values = {
            'R_rotor': 0.3,
            'n_rpm': 600.0,
            'B': 6,
            'c': 0.1
        }
        return value == default_values.get(param_name, None)

    @staticmethod
    def _fix_chord_distribution_params(params: Dict[str, Any]) -> bool:
        """修复弦长分布参数"""
        fixes_applied = False

        # 确保弦长分布类型有效
        valid_chord_types = ['linear', 'elliptic', 'optimized', 'custom']
        if 'chord_distribution_type' not in params:
            params['chord_distribution_type'] = 'linear'
            fixes_applied = True
        elif params['chord_distribution_type'] not in valid_chord_types:
            params['chord_distribution_type'] = 'linear'
            fixes_applied = True
            print(f"🔧 修复无效的弦长分布类型，设置为linear")

        # 确保弦长参数存在
        if 'root_chord' not in params:
            params['root_chord'] = 0.3
            fixes_applied = True
        if 'tip_chord' not in params:
            params['tip_chord'] = 0.15
            fixes_applied = True

        # 计算锥度比
        if 'chord_taper_ratio' not in params:
            params['chord_taper_ratio'] = params['tip_chord'] / params['root_chord']
            fixes_applied = True

        return fixes_applied

    @staticmethod
    def _fix_twist_distribution_params(params: Dict[str, Any]) -> bool:
        """修复扭转角分布参数"""
        fixes_applied = False

        # 确保扭转角分布类型有效
        valid_twist_types = ['linear', 'optimized', 'bell', 'custom']
        if 'twist_distribution_type' not in params:
            params['twist_distribution_type'] = 'linear'
            fixes_applied = True
        elif params['twist_distribution_type'] not in valid_twist_types:
            params['twist_distribution_type'] = 'linear'
            fixes_applied = True
            print(f"🔧 修复无效的扭转角分布类型，设置为linear")

        # 确保扭转角参数存在
        if 'root_twist' not in params:
            params['root_twist'] = 8.0
            fixes_applied = True
        if 'tip_twist' not in params:
            params['tip_twist'] = -8.0
            fixes_applied = True

        return fixes_applied

    @staticmethod
    def _fix_chord_distribution_params(params: Dict[str, Any]) -> bool:
        """
        修复弦长分布参数

        Args:
            params: 传统旋翼参数字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False

        # 确保弦长分布参数完整
        if 'chord_distribution_type' not in params:
            params['chord_distribution_type'] = 'linear'
            fixes_applied = True

        if 'root_chord' not in params:
            params['root_chord'] = 0.4
            fixes_applied = True

        if 'tip_chord' not in params:
            params['tip_chord'] = 0.2
            fixes_applied = True

        # 计算锥度比
        if 'chord_taper_ratio' not in params:
            root_chord = params.get('root_chord', 0.4)
            tip_chord = params.get('tip_chord', 0.2)
            if root_chord > 0:
                params['chord_taper_ratio'] = tip_chord / root_chord
                fixes_applied = True

        if fixes_applied:
            print("🔧 修复弦长分布参数")

        return fixes_applied

    @staticmethod
    def _fix_twist_distribution_params(params: Dict[str, Any]) -> bool:
        """
        修复扭转角分布参数

        Args:
            params: 传统旋翼参数字典

        Returns:
            bool: 是否应用了修复
        """
        fixes_applied = False

        # 确保扭转角分布参数完整
        if 'twist_distribution_type' not in params:
            params['twist_distribution_type'] = 'linear'
            fixes_applied = True

        if 'root_twist' not in params:
            params['root_twist'] = 8.0
            fixes_applied = True

        if 'tip_twist' not in params:
            params['tip_twist'] = -8.0
            fixes_applied = True

        if 'twist_optimization_type' not in params:
            params['twist_optimization_type'] = 'ideal'
            fixes_applied = True

        if fixes_applied:
            print("🔧 修复扭转角分布参数")

        return fixes_applied

        # 3. 修复时间参数逻辑
        if ConfigLoader._fix_time_parameters(config_dict):
            fixes_applied = True
            print("✅ 修复时间参数逻辑")

        # 4. 修复物理参数范围
        if ConfigLoader._fix_physical_ranges(config_dict):
            fixes_applied = True
            print("✅ 修复物理参数范围")

        # 5. 修复求解器兼容性
        if ConfigLoader._fix_solver_compatibility(config_dict):
            fixes_applied = True
            print("✅ 修复求解器兼容性")

        # 6. 创建备份（如果应用了修复）
        if fixes_applied:
            ConfigLoader._create_backup(file_path, original_config)

        return config_dict, fixes_applied

    @staticmethod
    def _fix_time_parameters(config_dict: Dict[str, Any]) -> bool:
        """修复时间参数逻辑"""
        fixes_applied = False

        # 确保 T_acoustic_record >= T_buildup
        T_buildup = config_dict.get("T_buildup", 1.0)
        T_acoustic_record = config_dict.get("T_acoustic_record", 1.5)

        if T_acoustic_record < T_buildup:
            config_dict["T_acoustic_record"] = T_buildup + 0.5
            fixes_applied = True
            print(f"🔧 修复声学记录时间: {T_acoustic_record} -> {config_dict['T_acoustic_record']}")

        return fixes_applied

    @staticmethod
    def _fix_physical_ranges(config_dict: Dict[str, Any]) -> bool:
        """修复物理参数范围"""
        fixes_applied = False

        for param, (min_val, max_val) in ConfigLoader.PHYSICAL_RANGES.items():
            if param in config_dict:
                value = config_dict[param]
                if value < min_val:
                    config_dict[param] = min_val
                    fixes_applied = True
                    print(f"🔧 修复参数 {param}: {value} -> {min_val} (低于最小值)")
                elif value > max_val:
                    config_dict[param] = max_val
                    fixes_applied = True
                    print(f"🔧 修复参数 {param}: {value} -> {max_val} (超出最大值)")

        return fixes_applied

    @staticmethod
    def _fix_solver_compatibility(config_dict: Dict[str, Any]) -> bool:
        """修复求解器兼容性"""
        fixes_applied = False

        # 检查求解器保真度与转子类型的兼容性
        rotor_type = config_dict.get('rotor_type', 'cycloidal')
        solver_fidelity = config_dict.get('solver_fidelity', 'high')

        # 传统旋翼推荐使用中高保真度求解器
        if rotor_type == 'conventional' and solver_fidelity == 'low':
            config_dict['solver_fidelity'] = 'medium'
            fixes_applied = True
            print("🔧 传统旋翼推荐使用中等保真度求解器")

        # 检查GPU设置与求解器的兼容性
        use_gpu = config_dict.get('use_gpu', False)
        if use_gpu and solver_fidelity == 'low':
            print("⚠️ 警告: 低保真度求解器可能无法充分利用GPU加速")

        return fixes_applied

    @staticmethod
    def _create_backup(file_path: str, original_config: Dict[str, Any]):
        """创建配置文件备份"""
        try:
            backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(original_config, f, default_flow_style=False, allow_unicode=True, indent=2)
            print(f"📁 原始配置已备份到: {backup_path}")
        except Exception as e:
            print(f"⚠️ 备份创建失败: {e}")

    @staticmethod
    def validate_config(config: SimulationConfig) -> bool:
        """
        验证配置的完整性和合理性

        Args:
            config: 仿真配置对象

        Returns:
            bool: 配置是否有效
        """
        try:
            # 基本参数验证
            if config.n_rpm <= 0:
                print("❌ 转速必须为正数")
                return False

            if config.B < 2:
                print("❌ 桨叶数必须至少为2")
                return False

            # 转子类型特定验证
            if config.rotor_type == 'conventional':
                return ConfigLoader._validate_conventional_config(config)
            elif config.rotor_type == 'cycloidal':
                return ConfigLoader._validate_cycloidal_config(config)
            else:
                print(f"❌ 不支持的转子类型: {config.rotor_type}")
                return False

        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False

    @staticmethod
    def _validate_conventional_config(config: SimulationConfig) -> bool:
        """验证传统旋翼配置"""
        params = config.conventional_rotor_params

        # 验证几何参数
        if params.hub_radius >= config.R_rotor:
            print("❌ 桨毂半径必须小于转子半径")
            return False

        # 验证扭转角范围
        twist_range = abs(params.root_twist - params.tip_twist)
        if twist_range > 40.0:
            print(f"⚠️ 扭转角范围 {twist_range:.1f}° 过大")

        print("✅ 传统旋翼配置验证通过")
        return True

    @staticmethod
    def _validate_cycloidal_config(config: SimulationConfig) -> bool:
        """验证循环翼转子配置"""
        params = config.cycloidal_rotor_params

        # 验证俯仰角幅值
        if params.pitch_amplitude_top <= 0 or params.pitch_amplitude_bottom <= 0:
            print("❌ 俯仰角幅值必须为正数")
            return False

        print("✅ 循环翼转子配置验证通过")
        return True

    @staticmethod
    def create_conventional_rotor_config(
        rotor_size: str = "medium",
        flight_condition: str = "hover"
    ) -> SimulationConfig:
        """
        创建传统旋翼配置模板

        Args:
            rotor_size: 旋翼尺寸 ("light", "medium", "heavy")
            flight_condition: 飞行状态 ("hover", "forward", "autorotation")

        Returns:
            SimulationConfig: 传统旋翼配置
        """
        # 基础配置
        config_dict = {
            'rotor_type': 'conventional',
            'conventional_rotor_params': {
                'standard_config_type': rotor_size
            }
        }

        # 根据尺寸设置基本参数
        if rotor_size == "light":
            config_dict.update({
                'n_rpm': 450.0,
                'B': 2,
                'c': 0.25,
                'R_rotor': 4.0,
                'L_span': 3.6
            })
        elif rotor_size == "medium":
            config_dict.update({
                'n_rpm': 320.0,
                'B': 4,
                'c': 0.4,
                'R_rotor': 8.0,
                'L_span': 7.2
            })
        elif rotor_size == "heavy":
            config_dict.update({
                'n_rpm': 220.0,
                'B': 6,
                'c': 0.65,
                'R_rotor': 12.0,
                'L_span': 10.8
            })

        # 根据飞行状态设置参数
        if flight_condition == "hover":
            config_dict.update({
                'V_inf_x': 0.0,
                'V_inf_y': 0.0
            })
            config_dict['conventional_rotor_params'].update({
                'collective_pitch': 8.0,
                'cyclic_pitch_lat': 0.0,
                'cyclic_pitch_lon': 0.0
            })
        elif flight_condition == "forward":
            config_dict.update({
                'V_inf_x': 20.0,
                'V_inf_y': 0.0
            })
            config_dict['conventional_rotor_params'].update({
                'collective_pitch': 6.0,
                'cyclic_pitch_lat': 3.0,
                'cyclic_pitch_lon': 2.0
            })

        return ConfigLoader.from_dict(config_dict)

    @staticmethod
    def get_config_template_list() -> Dict[str, str]:
        """
        获取可用的配置模板列表

        Returns:
            Dict[str, str]: 模板名称和描述的字典
        """
        return {
            'light_helicopter_hover': '轻型直升机悬停配置',
            'light_helicopter_forward': '轻型直升机前飞配置',
            'medium_helicopter_hover': '中型直升机悬停配置',
            'medium_helicopter_forward': '中型直升机前飞配置',
            'heavy_helicopter_hover': '重型直升机悬停配置',
            'heavy_helicopter_cruise': '重型直升机巡航配置',
            'cycloidal_rotor_basic': '基础循环翼转子配置',
            'cycloidal_rotor_advanced': '高级循环翼转子配置'
        }

    @staticmethod
    def create_config_from_template(template_name: str) -> SimulationConfig:
        """
        从模板创建配置

        Args:
            template_name: 模板名称

        Returns:
            SimulationConfig: 配置对象
        """
        templates = {
            'light_helicopter_hover': lambda: ConfigLoader.create_conventional_rotor_config('light', 'hover'),
            'light_helicopter_forward': lambda: ConfigLoader.create_conventional_rotor_config('light', 'forward'),
            'medium_helicopter_hover': lambda: ConfigLoader.create_conventional_rotor_config('medium', 'hover'),
            'medium_helicopter_forward': lambda: ConfigLoader.create_conventional_rotor_config('medium', 'forward'),
            'heavy_helicopter_hover': lambda: ConfigLoader.create_conventional_rotor_config('heavy', 'hover'),
            'heavy_helicopter_cruise': lambda: ConfigLoader.create_conventional_rotor_config('heavy', 'forward'),
            'cycloidal_rotor_basic': lambda: SimulationConfig(rotor_type='cycloidal'),
            'cycloidal_rotor_advanced': lambda: SimulationConfig(
                rotor_type='cycloidal',
                solver_fidelity='high',
                enable_3d_uvlm=True,
                use_advanced_lb_features=True
            )
        }

        if template_name not in templates:
            raise ValueError(f"未知的模板名称: {template_name}")

        return templates[template_name]()
