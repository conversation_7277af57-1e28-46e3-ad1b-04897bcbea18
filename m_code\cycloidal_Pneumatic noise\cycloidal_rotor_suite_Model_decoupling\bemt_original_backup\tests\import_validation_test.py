#!/usr/bin/env python3
"""
BEMT中保真度模块 - 导入验证测试
=============================

验证清理和整合后的导入问题是否已修复。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import traceback

def test_imports():
    """测试各模块的导入"""
    print("=" * 70)
    print("🔍 BEMT中保真度模块 - 导入验证测试")
    print("=" * 70)
    
    # 添加当前目录到路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    test_results = {}
    
    # 测试1: 工具模块导入
    print("\n1. 测试工具模块导入...")
    try:
        from utils.config import ConfigManager
        from utils.error_handling import validate_input, safe_divide
        from utils.math_utils import rotation_matrix, interpolate_1d
        print("   ✅ 工具模块导入成功")
        test_results['utils'] = True
    except Exception as e:
        print(f"   ❌ 工具模块导入失败: {e}")
        test_results['utils'] = False
    
    # 测试2: 几何模块导入
    print("\n2. 测试几何模块导入...")
    try:
        from geometry.rotor import RotorGeometry
        print("   ✅ 几何模块导入成功")
        test_results['geometry'] = True
    except Exception as e:
        print(f"   ❌ 几何模块导入失败: {e}")
        test_results['geometry'] = False
    
    # 测试3: 物理模块导入
    print("\n3. 测试物理模块导入...")
    try:
        from physics.corrections import PhysicalCorrectionBase
        from physics.tip_loss import TipLossCorrection
        print("   ✅ 物理模块导入成功")
        test_results['physics'] = True
    except Exception as e:
        print(f"   ❌ 物理模块导入失败: {e}")
        traceback.print_exc()
        test_results['physics'] = False
    
    # 测试4: 气动模块导入
    print("\n4. 测试气动模块导入...")
    try:
        from aerodynamics.blade_element import BladeElement
        from aerodynamics.airfoil_database import AirfoilDatabase
        print("   ✅ 气动模块导入成功")
        test_results['aerodynamics'] = True
    except Exception as e:
        print(f"   ❌ 气动模块导入失败: {e}")
        traceback.print_exc()
        test_results['aerodynamics'] = False
    
    # 测试5: 核心模块导入
    print("\n5. 测试核心模块导入...")
    try:
        from core.bemt_solver import BEMTSolver
        from core.convergence import ConvergenceController
        from core.performance_calculator import PerformanceCalculator
        print("   ✅ 核心模块导入成功")
        test_results['core'] = True
    except Exception as e:
        print(f"   ❌ 核心模块导入失败: {e}")
        traceback.print_exc()
        test_results['core'] = False
    
    # 测试6: 示例模块导入
    print("\n6. 测试示例模块导入...")
    try:
        from examples.basic_usage import run_basic_example
        print("   ✅ 示例模块导入成功")
        test_results['examples'] = True
    except Exception as e:
        print(f"   ❌ 示例模块导入失败: {e}")
        test_results['examples'] = False
    
    # 生成测试报告
    print("\n" + "=" * 70)
    print("📋 导入验证测试报告")
    print("=" * 70)
    
    module_names = ["工具模块", "几何模块", "物理模块", "气动模块", "核心模块", "示例模块"]
    
    passed_tests = 0
    total_tests = len(test_results)
    
    print("模块类型                    导入状态")
    print("-" * 50)
    
    for i, (module_key, result) in enumerate(test_results.items()):
        module_name = module_names[i] if i < len(module_names) else f"模块{i+1}"
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{module_name:25s} {status}")
        if result:
            passed_tests += 1
    
    print("-" * 50)
    success_rate = passed_tests / total_tests * 100
    print(f"总体结果: {passed_tests}/{total_tests} 成功 ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 导入验证结果：完美！所有模块导入正常")
        print("\n✅ 修复结论：")
        print("   - 相对导入问题已完全解决")
        print("   - 模块依赖关系正确")
        print("   - 代码结构整合成功")
    elif success_rate >= 80:
        print("\n✅ 导入验证结果：良好，大部分模块正常")
        print("\n⚠️  需要关注的问题：")
        for module_key, result in test_results.items():
            if not result:
                print(f"   - {module_key} 模块导入失败")
    else:
        print("\n⚠️  导入验证结果：需要进一步修复")
        print("\n❌ 主要问题：")
        for module_key, result in test_results.items():
            if not result:
                print(f"   - {module_key} 模块导入失败")
    
    return success_rate >= 80

def main():
    """主函数"""
    try:
        success = test_imports()
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())