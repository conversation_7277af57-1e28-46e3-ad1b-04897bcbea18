# 🚁 旋翼空气动力学仿真系统 - 完整工作流程

## 📋 系统总体架构流程

```mermaid
graph TB
    A[用户输入] --> B[配置管理器]
    B --> C{保真度选择}
    
    C -->|低保真度| D[升力线理论求解器]
    C -->|中保真度| E[BEMT求解器]
    C -->|高保真度| F[UVLM求解器]
    
    D --> G[气动分析模块]
    E --> G
    F --> G
    
    G --> H[物理修正系统]
    H --> I[时间积分器]
    I --> J[收敛控制]
    
    J -->|未收敛| K[调整参数]
    K --> I
    J -->|收敛| L[性能计算]
    
    L --> M[声学分析模块]
    M --> N[后处理模块]
    N --> O[结果输出]
    
    O --> P[验证框架]
    P --> Q[报告生成]
```

## 🔧 详细工作流程分析

### 1️⃣ 初始化阶段

```mermaid
graph LR
    A[启动程序] --> B[加载配置文件]
    B --> C[ConfigManager初始化]
    C --> D[参数验证]
    D --> E[默认值填充]
    E --> F[求解器工厂创建]
    F --> G[几何模型初始化]
    G --> H[物理模型初始化]
```

**关键代码路径：**
- `main.py` → `cyclone_sim/simulation.py`
- `config_loader.py` → `ConfigManager`
- `solver_factory.py` → 求解器选择

### 2️⃣ 配置参数调用流程

```mermaid
graph TD
    A[配置文件 config.yaml] --> B[ConfigManager.load_from_file]
    B --> C[参数验证 _validate_config]
    C --> D[默认值合并 _get_default_config]
    D --> E[类型转换和范围检查]
    E --> F[求解器特定配置优化]
    
    F --> G{求解器类型}
    G -->|BEMT| H[BEMT配置优化]
    G -->|UVLM| I[UVLM配置优化]
    G -->|升力线| J[升力线配置优化]
    
    H --> K[配置对象创建完成]
    I --> K
    J --> K
```

**核心配置参数：**
```yaml
# 基本参数
R_rotor: 0.5          # 转子半径 [m]
B: 4                  # 桨叶数
c: 0.08               # 弦长 [m]
omega_rotor: 150.0    # 角速度 [rad/s]
rho: 1.225            # 空气密度 [kg/m³]

# 保真度选择
fidelity: "medium"    # low/medium/high
solver_type: "bemt"   # bemt/uvlm/lifting_line

# 物理模型开关
enable_tip_loss: true
enable_dynamic_stall: true
enable_acoustics: true
```

### 3️⃣ 气动分析计算流程

```mermaid
graph TD
    A[开始气动计算] --> B[求解器初始化]
    B --> C[桨叶几何离散化]
    C --> D[初始化诱导速度场]
    
    D --> E[时间步循环开始]
    E --> F[更新桨叶运动学]
    F --> G[计算相对速度]
    G --> H[查询翼型数据库]
    
    H --> I{启用动态失速?}
    I -->|是| J[L-B动态失速模型]
    I -->|否| K[静态翼型系数]
    
    J --> L[计算叶素载荷]
    K --> L
    
    L --> M[应用物理修正]
    M --> N[更新诱导速度]
    N --> O[收敛性检查]
    
    O -->|未收敛| P[应用松弛因子]
    P --> G
    O -->|收敛| Q[计算性能参数]
    
    Q --> R[下一时间步]
    R --> E
    
    E -->|时间结束| S[气动分析完成]
```

**BEMT求解器核心算法：**
```python
# 主要计算步骤
def solve_step(self, t, dt):
    # 1. 更新桨叶运动学
    self._update_blade_kinematics(t)
    
    # 2. BEMT迭代求解
    for iteration in range(max_iterations):
        # 计算叶素气动力
        forces, moments = self._solve_blade_elements()
        
        # 更新诱导速度
        self._update_induced_velocities(forces)
        
        # 检查收敛
        if self._check_convergence():
            break
    
    # 3. 应用物理修正
    corrected_results = self._apply_corrections()
    
    # 4. 计算性能参数
    performance = self._calculate_performance()
    
    return results
```

### 4️⃣ 物理修正系统流程

```mermaid
graph LR
    A[原始气动系数] --> B[叶尖损失修正]
    B --> C[叶根损失修正]
    C --> D[粘性效应修正]
    D --> E[压缩性修正]
    E --> F[三维旋转效应]
    F --> G[修正后系数]
    
    G --> H[修正统计记录]
```

**物理修正应用：**
```python
# 统一物理修正接口
def apply_all_corrections(self, input_data):
    corrections = {}
    
    # 叶尖损失修正
    if self.enable_tip_loss:
        corrections['tip_loss'] = self.tip_loss.apply(input_data)
    
    # 叶根损失修正  
    if self.enable_hub_loss:
        corrections['hub_loss'] = self.hub_loss.apply(input_data)
    
    # 应用所有修正
    return self._combine_corrections(corrections)
```

### 5️⃣ 声学分析流程

```mermaid
graph TD
    A[气动载荷结果] --> B[FW-H声学求解器]
    B --> C[声源项计算]
    C --> D[厚度噪声计算]
    D --> E[载荷噪声计算]
    E --> F[四极子噪声计算]
    
    F --> G[远场声压计算]
    G --> H[频域分析]
    H --> I[BPM宽带噪声]
    I --> J[噪声合成]
    J --> K[声学结果输出]
```

**声学计算核心：**
```python
# FW-H方程求解
def solve_fwh_equation(self, surface_data, observer_positions):
    # 1. 计算声源项
    thickness_sources = self._compute_thickness_sources()
    loading_sources = self._compute_loading_sources()
    
    # 2. 远场声压积分
    acoustic_pressure = self._integrate_acoustic_sources()
    
    # 3. 频域分析
    frequency_spectrum = self._compute_spectrum()
    
    return acoustic_results
```

### 6️⃣ 时间积分和收敛控制

```mermaid
graph TD
    A[时间步开始] --> B[选择积分方法]
    B --> C{积分方法}
    
    C -->|Euler| D[一阶Euler积分]
    C -->|RK4| E[四阶Runge-Kutta]
    C -->|自适应| F[自适应RK45]
    
    D --> G[状态更新]
    E --> G
    F --> G
    
    G --> H[收敛性检查]
    H --> I{是否收敛}
    
    I -->|否| J[调整松弛因子]
    J --> K[振荡检测]
    K --> L[Aitken加速]
    L --> A
    
    I -->|是| M[时间步完成]
```

**收敛控制算法：**
```python
class ConvergenceController:
    def check_convergence(self, residual):
        # 1. 残差检查
        if residual < self.tolerance:
            return True
            
        # 2. 振荡检测
        if self._detect_oscillation():
            self._apply_damping()
            
        # 3. Aitken加速
        if self.enable_aitken:
            self._apply_aitken_acceleration()
            
        return False
```

### 7️⃣ 验证框架工作流程

```mermaid
graph TD
    A[验证案例配置] --> B[实验数据加载]
    B --> C[仿真执行]
    C --> D[结果对比分析]
    
    D --> E[误差指标计算]
    E --> F[统计分析]
    F --> G[不确定性量化]
    
    G --> H[学术等级评估]
    H --> I[报告生成]
    I --> J[可视化输出]
```

**验证案例执行：**
```python
# Caradonna-Tung验证案例
def run_caradonna_tung_validation():
    # 1. 加载实验数据
    exp_data = load_experimental_data()
    
    # 2. 配置仿真参数
    config = create_validation_config()
    
    # 3. 执行仿真
    results = run_simulation(config)
    
    # 4. 误差分析
    errors = calculate_error_metrics(results, exp_data)
    
    # 5. 生成报告
    generate_validation_report(errors)
```

### 8️⃣ 后处理和结果输出

```mermaid
graph LR
    A[仿真原始结果] --> B[数据处理模块]
    B --> C[性能参数计算]
    C --> D[可视化生成]
    D --> E[报告生成器]
    E --> F[多格式输出]
    
    F --> G[JSON数据]
    F --> H[图表文件]
    F --> I[HTML报告]
    F --> J[学术报告]
```

## 🎯 关键技术特点

### 多保真度求解器切换
```python
# 求解器工厂模式
def create_solver(fidelity_level):
    if fidelity_level == "low":
        return LiftingLineSolver()
    elif fidelity_level == "medium":
        return BEMTSolver()
    elif fidelity_level == "high":
        return UVLMSolver()
```

### GPU加速支持
```python
# GPU加速配置
if torch.cuda.is_available():
    device = torch.device("cuda")
    # 矩阵运算GPU加速
    matrices = matrices.to(device)
    results = torch.solve(matrices)
```

### 自适应算法
```python
# 自适应时间步长
def adaptive_timestep(self, error_estimate):
    if error_estimate < tolerance:
        dt_new = min(dt * 1.2, max_dt)
    else:
        dt_new = max(dt * 0.8, min_dt)
    return dt_new
```

## 📊 性能监控和优化

### 实时性能监控
- 求解时间统计
- 内存使用监控  
- 收敛率跟踪
- GPU利用率监控

### 自动优化策略
- 智能初值估计
- 自适应松弛因子
- 动态网格调整
- 负载均衡优化

这个工作流程展示了一个完整的、工业级的旋翼仿真系统，从配置管理到最终结果输出的全过程，具有高度的模块化、可扩展性和学术验证能力。