"""
求解器工厂模块
==============

提供统一的求解器创建接口，支持策略模式和工厂模式。
整合了原有的求解器创建逻辑，并添加了新的统一物理修正系统支持。

主要功能:
- 统一的求解器创建接口
- 自动配置物理修正系统
- 求解器能力查询和比较
- 智能求解器选择

作者: Kiro AI Assistant
日期: 2025-01-22
"""

from typing import Dict, Any, Optional, Type, List
import warnings
from .base_solver import AerodynamicSolverBase, SOLVER_CAPABILITIES
from .bemt_solver import BEMTSolver
from .uvlm_solver import UVLMSolver
from .lifting_line_solver import LiftingLineSolver


class SolverFactory:
    """
    求解器工厂类

    提供统一的求解器创建和管理接口，支持不同保真度的求解器。
    """

    # 注册的求解器类型
    _solver_registry = {
        'BEMT': BEMTSolver,
        'UVLM': UVLMSolver,
        'LiftingLine': LiftingLineSolver,
        'bemt': BEMTSolver,  # 小写别名
        'uvlm': UVLMSolver,  # 小写别名
        'lifting_line': LiftingLineSolver,  # 小写别名
        'low_fidelity': BEMTSolver,
        'medium_fidelity': LiftingLineSolver,
        'high_fidelity': UVLMSolver,
    }

    @classmethod
    def create_solver(cls, solver_type: str, config: Dict[str, Any],
                     wake_system=None, airfoil_database=None) -> AerodynamicSolverBase:
        """
        创建求解器实例

        Args:
            solver_type: 求解器类型 ('BEMT', 'UVLM', 'low_fidelity', 'high_fidelity')
            config: 配置参数字典
            wake_system: 尾迹系统（可选）
            airfoil_database: 翼型数据库（可选）

        Returns:
            求解器实例

        Raises:
            ValueError: 如果求解器类型不存在
        """
        if solver_type not in cls._solver_registry:
            available_types = list(cls._solver_registry.keys())
            raise ValueError(f"未知的求解器类型: {solver_type}. 可用类型: {available_types}")

        solver_class = cls._solver_registry[solver_type]

        # 自动配置物理修正系统
        config = cls._configure_physics_corrections(config, solver_type)

        # 创建求解器实例
        solver = solver_class(config, wake_system, airfoil_database)

        print(f"✅ 创建 {solver_type} 求解器成功")
        print(f"   - 保真度: {solver.fidelity_level}")
        print(f"   - 计算成本: {solver.computational_cost}")

        # 检查是否有physics_manager属性
        if hasattr(solver, 'physics_manager') and hasattr(solver.physics_manager, 'get_enabled_corrections'):
            corrections_count = len(solver.physics_manager.get_enabled_corrections())
            print(f"   - 物理修正: {corrections_count} 个启用")
        else:
            print(f"   - 物理修正: 传统模式")

        return solver

    @classmethod
    def _configure_physics_corrections(cls, config: Dict[str, Any], solver_type: str) -> Dict[str, Any]:
        """
        根据求解器类型自动配置物理修正系统

        Args:
            config: 原始配置
            solver_type: 求解器类型

        Returns:
            配置了物理修正的配置字典
        """
        # 保存原始配置对象的引用
        original_config = config

        # 如果config是SimulationConfig对象，转换为字典进行处理
        if hasattr(config, '__dataclass_fields__'):
            from dataclasses import asdict
            config = asdict(config)
        else:
            config = config.copy()

        # 根据求解器类型设置默认物理修正
        if solver_type.upper() == 'BEMT' or solver_type == 'low_fidelity':
            # BEMT求解器的默认物理修正配置
            physics_defaults = {
                'tip_loss_model': 'prandtl',
                'enhanced_mode': True,
                'hub_loss_model': 'standard',
                'vortex_core_model': 'vatistas',
                'enable_3d_effects': False,  # BEMT通常不需要3D效应
                'rotor_type': config.get('rotor_type', 'conventional'),
                'correction_order': ['tip_loss', 'hub_loss', 'viscous_core']
            }
        elif solver_type.upper() == 'UVLM' or solver_type == 'high_fidelity':
            # UVLM求解器的默认物理修正配置
            physics_defaults = {
                'tip_loss_model': 'prandtl',
                'enhanced_mode': True,
                'hub_loss_model': 'enhanced',
                'vortex_core_model': 'vatistas',
                'enable_3d_effects': True,  # UVLM支持3D效应
                'enable_coriolis': True,
                'enable_centrifugal': True,
                'rotor_type': config.get('rotor_type', 'cycloidal'),
                'correction_order': ['viscous_core', 'tip_loss', 'hub_loss', 'rotational_effects', 'cycloidal']
            }
        else:
            physics_defaults = {}

        # 合并配置（用户配置优先）
        for key, default_value in physics_defaults.items():
            if key not in config:
                config[key] = default_value

        # 如果原始配置是SimulationConfig对象，转换回对象
        if hasattr(original_config, '__dataclass_fields__'):
            try:
                from cyclone_sim.config_loader import SimulationConfig
                # 只更新存在于SimulationConfig中的字段
                valid_fields = {f.name for f in SimulationConfig.__dataclass_fields__.values()}
                filtered_config = {k: v for k, v in config.items() if k in valid_fields}
                return SimulationConfig(**filtered_config)
            except Exception as e:
                # 如果配置创建失败，返回字典形式
                print(f"配置创建失败，使用字典形式: {e}")
                return config
        else:
            return config

    @classmethod
    def get_solver_capabilities(cls, solver_type: str) -> Dict[str, Any]:
        """
        获取求解器能力信息

        Args:
            solver_type: 求解器类型

        Returns:
            求解器能力字典
        """
        if solver_type not in cls._solver_registry:
            raise ValueError(f"未知的求解器类型: {solver_type}")

        solver_class = cls._solver_registry[solver_type]
        solver_name = solver_class.__name__

        if solver_name in SOLVER_CAPABILITIES:
            capabilities = SOLVER_CAPABILITIES[solver_name]
            return {
                'fidelity_level': capabilities.fidelity_level,
                'computational_cost': capabilities.computational_cost,
                'accuracy': capabilities.accuracy,
                'supports_3d': capabilities.supports_3d,
                'supports_unsteady': capabilities.supports_unsteady,
                'supports_stall': capabilities.supports_stall,
                'typical_speedup': capabilities.typical_speedup,
                'description': str(capabilities)
            }
        else:
            return {'error': f'未找到 {solver_name} 的能力信息'}

    @classmethod
    def recommend_solver(cls, requirements: Dict[str, Any]) -> str:
        """
        根据需求推荐合适的求解器

        Args:
            requirements: 需求字典，包含以下可选键：
                - accuracy_level: 精度要求 ('high', 'medium', 'low')
                - computational_budget: 计算预算 ('high', 'medium', 'low')
                - supports_3d: 是否需要3D效应支持
                - supports_unsteady: 是否需要非定常效应支持
                - typical_speedup_min: 最小加速比要求

        Returns:
            推荐的求解器类型
        """
        accuracy_req = requirements.get('accuracy_level', 'medium')
        budget_req = requirements.get('computational_budget', 'medium')
        needs_3d = requirements.get('supports_3d', False)
        needs_unsteady = requirements.get('supports_unsteady', True)
        min_speedup = requirements.get('typical_speedup_min', 1.0)

        # 评分系统
        scores = {}

        for solver_name, capabilities in SOLVER_CAPABILITIES.items():
            score = 0

            # 精度匹配
            if accuracy_req == capabilities.accuracy:
                score += 3
            elif (accuracy_req == 'high' and capabilities.accuracy == 'medium') or \
                 (accuracy_req == 'medium' and capabilities.accuracy in ['high', 'low']):
                score += 1

            # 计算预算匹配
            if budget_req == capabilities.computational_cost:
                score += 2
            elif (budget_req == 'low' and capabilities.computational_cost == 'medium') or \
                 (budget_req == 'high' and capabilities.computational_cost == 'medium'):
                score += 1

            # 功能需求
            if needs_3d and capabilities.supports_3d:
                score += 2
            elif not needs_3d:
                score += 1

            if needs_unsteady and capabilities.supports_unsteady:
                score += 1
            elif not needs_unsteady:
                score += 1

            # 加速比要求
            if capabilities.typical_speedup >= min_speedup:
                score += 1

            scores[solver_name] = score

        # 选择得分最高的求解器
        best_solver = max(scores, key=scores.get)

        # 转换为工厂支持的类型名
        solver_type_map = {
            'BEMTSolver': 'BEMT',
            'UVLMSolver': 'UVLM'
        }

        recommended_type = solver_type_map.get(best_solver, best_solver)

        print(f"🎯 根据需求推荐求解器: {recommended_type}")
        print(f"   - 需求: 精度={accuracy_req}, 预算={budget_req}, 3D={needs_3d}")
        print(f"   - 推荐理由: {SOLVER_CAPABILITIES[best_solver]}")

        return recommended_type

    @classmethod
    def compare_solvers(cls, solver_types: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        比较多个求解器的能力

        Args:
            solver_types: 要比较的求解器类型列表

        Returns:
            比较结果字典
        """
        comparison = {}

        for solver_type in solver_types:
            try:
                capabilities = cls.get_solver_capabilities(solver_type)
                comparison[solver_type] = capabilities
            except ValueError as e:
                comparison[solver_type] = {'error': str(e)}

        return comparison

    @classmethod
    def register_solver(cls, name: str, solver_class: Type[AerodynamicSolverBase]):
        """
        注册新的求解器类型

        Args:
            name: 求解器名称
            solver_class: 求解器类
        """
        if not issubclass(solver_class, AerodynamicSolverBase):
            raise ValueError("求解器类必须继承自AerodynamicSolverBase")

        cls._solver_registry[name] = solver_class
        print(f"✅ 注册新求解器: {name}")

    @classmethod
    def get_available_solvers(cls) -> List[str]:
        """
        获取所有可用的求解器类型

        Returns:
            可用求解器类型列表
        """
        return list(cls._solver_registry.keys())

    @classmethod
    def create_solver_with_auto_config(cls, requirements: Dict[str, Any],
                                     base_config: Dict[str, Any]) -> AerodynamicSolverBase:
        """
        根据需求自动选择和配置求解器

        Args:
            requirements: 需求字典
            base_config: 基础配置

        Returns:
            配置好的求解器实例
        """
        # 推荐求解器类型
        solver_type = cls.recommend_solver(requirements)

        # 创建求解器
        solver = cls.create_solver(solver_type, base_config)

        return solver


# 便利函数

def create_bemt_solver(config: Dict[str, Any], **kwargs) -> BEMTSolver:
    """
    创建BEMT求解器的便利函数

    Args:
        config: 配置字典
        **kwargs: 其他参数

    Returns:
        BEMT求解器实例
    """
    return SolverFactory.create_solver('BEMT', config, **kwargs)


def create_uvlm_solver(config: Dict[str, Any], **kwargs) -> UVLMSolver:
    """
    创建UVLM求解器的便利函数

    Args:
        config: 配置字典
        **kwargs: 其他参数

    Returns:
        UVLM求解器实例
    """
    return SolverFactory.create_solver('UVLM', config, **kwargs)


def create_solver_auto(requirements: Dict[str, Any],
                      config: Dict[str, Any], **kwargs) -> AerodynamicSolverBase:
    """
    自动选择和创建求解器的便利函数

    Args:
        requirements: 需求字典
        config: 配置字典
        **kwargs: 其他参数

    Returns:
        自动选择的求解器实例
    """
    return SolverFactory.create_solver_with_auto_config(requirements, config)


# 向后兼容的工厂函数

def get_solver(solver_type: str, config: Dict[str, Any], **kwargs) -> AerodynamicSolverBase:
    """
    向后兼容的求解器获取函数

    ⚠️ 已弃用: 请使用 SolverFactory.create_solver() 替代
    """
    warnings.warn(
        "get_solver函数已弃用，请使用SolverFactory.create_solver()替代",
        DeprecationWarning,
        stacklevel=2
    )
    return SolverFactory.create_solver(solver_type, config, **kwargs)


if __name__ == "__main__":
    # 示例用法
    print("求解器工厂示例")
    print("=" * 50)

    # 基础配置
    config = {
        'B': 3,
        'R_rotor': 1.0,
        'c': 0.1,
        'n_rpm': 300,
        'rho': 1.225,
        'V_inf_x': 0.0,
        'V_inf_y': 0.0
    }

    # 比较求解器
    comparison = SolverFactory.compare_solvers(['BEMT', 'UVLM'])
    print("求解器比较:")
    for solver_type, capabilities in comparison.items():
        print(f"  {solver_type}: {capabilities}")

    print()

    # 推荐求解器
    requirements = {
        'accuracy_level': 'high',
        'computational_budget': 'medium',
        'supports_3d': True
    }

    recommended = SolverFactory.recommend_solver(requirements)
    print(f"推荐求解器: {recommended}")

    print()

    # 创建求解器
    try:
        solver = SolverFactory.create_solver('BEMT', config)
        print(f"创建的求解器信息: {solver.get_solver_info()}")
    except Exception as e:
        print(f"创建求解器失败: {e}")
