"""
BEMT中保真度验证模块
==================

完整复刻原始cycloidal_rotor_suite中BEMT中保真度模块的所有核心功能。
实现叶素动量理论(BEMT)求解器，包含完整的物理模型和修正。

核心功能：
- 完整的BEMT求解器实现
- 动态失速模型（Leishman-Beddoes）
- 物理修正（叶尖/叶根损失、压缩性修正、粘性修正等）
- 翼型数据库管理
- 真实旋翼验证算例（UH-60黑鹰等）
- 性能基准测试

模块结构：
- core/: 核心求解器和算法
- aerodynamics/: 气动力学模型
- physics/: 物理修正模块
- geometry/: 几何建模
- validation/: 验证算例
- utils/: 工具函数

作者: Kiro AI Assistant
日期: 2025-01-28
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Kiro AI Assistant"

# 导入核心模块
from .core.bemt_solver import BEMTSolver
from .core.solver_factory import SolverFactory
from .aerodynamics.blade_element import BladeElement, Blade
from .aerodynamics.dynamic_stall import LeishmanBeddoesModel
from .physics.corrections import UnifiedPhysicalCorrections
from .geometry.rotor import RotorGeometry
from .utils.config import ConfigManager
from .validation.test_cases import ValidationTestSuite

# 导出主要接口
__all__ = [
    'BEMTSolver',
    'SolverFactory', 
    'BladeElement',
    'Blade',
    'LeishmanBeddoesModel',
    'UnifiedPhysicalCorrections',
    'RotorGeometry',
    'ConfigManager',
    'ValidationTestSuite'
]

# 版本信息
def get_version_info():
    """获取版本信息"""
    return {
        'version': __version__,
        'author': __author__,
        'description': 'BEMT中保真度验证模块 - 完整复刻原始功能',
        'features': [
            '完整BEMT求解器',
            'Leishman-Beddoes动态失速模型',
            '统一物理修正系统',
            '真实旋翼验证算例',
            '性能基准测试'
        ]
    }

# 模块初始化检查
def check_dependencies():
    """检查依赖项"""
    required_packages = ['numpy', 'scipy', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"警告: 缺少依赖包: {missing_packages}")
        return False
    return True

# 自动检查依赖
if not check_dependencies():
    print("请安装缺少的依赖包: pip install numpy scipy matplotlib")