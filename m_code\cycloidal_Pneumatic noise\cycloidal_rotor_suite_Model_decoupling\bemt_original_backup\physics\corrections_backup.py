"""
统一物理修正系统
===============

完整复刻原始模块的物理修正功能，提供统一的修正接口。

核心功能：
- 叶尖/叶根损失修正
- 粘性效应修正
- 压缩性修正
- 旋转效应修正
- 三维效应修正
- 统一修正管理

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import warnings
from abc import ABC, abstractmethod

class PhysicalCorrectionBase(ABC):
    """物理修正基类"""
    
    def __init__(self, config):
        self.config = config
        self.enabled = True
        self.correction_type = "base"
    
    @abstractmethod
    def apply(self, input_data):
        """应用修正 - 子类需要实现"""
        pass
    
    def validate_inputs(self, input_data):
        """验证输入参数"""
        required_keys = ['Cl', 'Cd', 'alpha', 'r_R']
        for key in required_keys:
            if key not in input_data:
                raise ValueError(f"缺少必需的输入参数: {key}")
    
    def get_correction_info(self):
        """获取修正信息"""
        return {
            'type': self.correction_type,
            'enabled': self.enabled,
            'description': f'{self.correction_type} 物理修正'
        }

# 简化的修正类实现，避免循环导入
class TipLossCorrection(PhysicalCorrectionBase):
    """叶尖损失修正 - 中保真度实现"""
    
    def __init__(self, config): 
        super().__init__(config)
        self.correction_type = 'tip_loss'
        self.B = config.get('B', 4)  # 桨叶数
        self.method = config.get('tip_loss_method', 'prandtl')
        self.enhanced_model = config.get('enhanced_tip_loss', True)
    
    def apply(self, input_data): 
        """应用叶尖损失修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        r_R = input_data['r_R']
        alpha = input_data.get('alpha', 0.0)
        
        if self.method == 'prandtl':
            # Prandtl叶尖损失修正
            if r_R > 0.99:
                F = 0.1  # 叶尖处强修正
            else:
                f = (self.B / 2) * (1 - r_R) / r_R
                F = (2 / np.pi) * np.arccos(np.exp(-np.clip(f, 0, 10)))
                F = np.clip(F, 0.1, 1.0)
            
            # 增强模型：考虑攻角影响
            if self.enhanced_model:
                alpha_effect = 1.0 - 0.1 * abs(np.degrees(alpha)) / 15.0
                alpha_effect = np.clip(alpha_effect, 0.8, 1.0)
                F *= alpha_effect
                
        elif self.method == 'goldstein':
            # Goldstein修正（更精确）
            if r_R > 0.95:
                F = 0.5 * (1 - r_R) / 0.05
            else:
                F = 1.0 - 0.1 * (1 - r_R)**2
            F = np.clip(F, 0.2, 1.0)
        else:
            F = 0.95  # 简化修正
        
        result['Cl'] = input_data['Cl'] * F
        result['Cd'] = input_data['Cd'] * F
        result['tip_loss_factor'] = F
        
        return result


class HubLossCorrection(PhysicalCorrectionBase):
    """叶根损失修正"""
    
    def __init__(self, config): 
        super().__init__(config)
        self.correction_type = 'hub_loss'
        self.hub_radius_ratio = config.get('hub_radius_ratio', 0.1)
    
    def apply(self, input_data):
        """应用叶根损失修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        r_R = input_data['r_R']
        
        # 简化的叶根损失修正
        if r_R < self.hub_radius_ratio * 2:
            F = r_R / (self.hub_radius_ratio * 2)
            F = np.clip(F, 0.1, 1.0)
        else:
            F = 1.0
        
        result['Cl'] = input_data['Cl'] * F
        result['Cd'] = input_data['Cd'] * F
        result['hub_loss_factor'] = F
        
        return result


class ViscousEffectsCorrection(PhysicalCorrectionBase):
    """粘性效应修正"""
    
    def __init__(self, config):
        super().__init__(config)
        self.correction_type = 'viscous_effects'
        self.Re_ref = config.get('Re_ref', 1e6)
    
    def apply(self, input_data):
        """应用粘性效应修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        Re = input_data.get('Re', self.Re_ref)
        
        # 简化的雷诺数修正
        if Re < 1e5:
            Re_factor = 0.8 + 0.2 * (Re / 1e5)
        else:
            Re_factor = 1.0
        
        result['Cl'] = input_data['Cl'] * Re_factor
        result['Cd'] = input_data['Cd'] / Re_factor
        result['viscous_factor'] = Re_factor
        
        return result


class UnifiedPhysicalCorrections:
    """统一物理修正系统"""
    
    def __init__(self, config):
        self.config = config
        self.tip_loss = TipLossCorrection(config)
        self.hub_loss = HubLossCorrection(config)
        self.viscous_effects = ViscousEffectsCorrection(config)
        
        # 统计信息
        self.correction_stats = {
            'total_applications': 0
        }
    
    def get_enabled_corrections(self) -> List[str]:
        """获取启用的修正列表"""
        corrections = []
        if self.config.get('enable_tip_loss', True):
            corrections.append('tip_loss')
        if self.config.get('enable_hub_loss', True):
            corrections.append('hub_loss')
        if self.config.get('enable_viscous_effects', False):
            corrections.append('viscous_effects')
        return corrections
    
    def apply_all(self, input_data):
        """应用所有修正"""
        result = input_data.copy()
        
        # 应用叶尖损失修正
        if self.config.get('enable_tip_loss', True):
            result = self.tip_loss.apply(result)
        
        # 应用叶根损失修正
        if self.config.get('enable_hub_loss', True):
            result = self.hub_loss.apply(result)
        
        # 应用粘性效应修正
        if self.config.get('enable_viscous_effects', False):
            result = self.viscous_effects.apply(result)
        
        self.correction_stats['total_applications'] += 1
        return result
    
    def apply_all_corrections(self, input_data):
        """兼容性接口"""
        return self.apply_all(input_data)
    
    def get_correction_statistics(self):
        """获取修正统计"""
        return self.correction_stats.copy()

class HubLossCorrection(PhysicalCorrectionBase):
    """叶根损失修正"""
    
    def __init__(self, config): 
        super().__init__(config)
        self.correction_type = 'hub_loss'
        self.hub_radius_ratio = config.get('hub_radius_ratio', 0.1)
    
    def apply(self, input_data):
        """应用叶根损失修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        r_R = input_data['r_R']
        
        # 简化的叶根损失修正
        if r_R < self.hub_radius_ratio * 2:
            F = r_R / (self.hub_radius_ratio * 2)
            F = np.clip(F, 0.1, 1.0)
        else:
            F = 1.0
        
        result['Cl'] = input_data['Cl'] * F
        result['Cd'] = input_data['Cd'] * F
        result['hub_loss_factor'] = F
        
        return result

class ViscousEffectsCorrection(PhysicalCorrectionBase):
    """粘性效应修正 - 中保真度实现"""
    
    def __init__(self, config):
        super().__init__(config)
        self.correction_type = 'viscous_effects'
        self.Re_ref = config.get('Re_ref', 1e6)
        self.enhanced_model = config.get('enhanced_viscous', True)
    
    def apply(self, input_data):
        """应用粘性效应修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        Re = input_data.get('Re', self.Re_ref)
        alpha = input_data.get('alpha', 0.0)
        
        # 雷诺数修正
        if Re < 1e5:
            # 低雷诺数修正
            Re_factor_Cl = 0.8 + 0.2 * (Re / 1e5)
            Re_factor_Cd = 2.0 - 1.0 * (Re / 1e5)
        elif Re > 1e7:
            # 高雷诺数修正
            Re_factor_Cl = 1.1
            Re_factor_Cd = 0.9
        else:
            # 中等雷诺数范围
            Re_factor_Cl = (Re / self.Re_ref) ** 0.1
            Re_factor_Cd = (self.Re_ref / Re) ** 0.05
        
        Re_factor_Cl = np.clip(Re_factor_Cl, 0.6, 1.3)
        Re_factor_Cd = np.clip(Re_factor_Cd, 0.7, 2.5)
        
        # 增强模型：考虑攻角和雷诺数的耦合效应
        if self.enhanced_model:
            alpha_deg = abs(np.degrees(alpha))
            if alpha_deg > 10:  # 大攻角时粘性效应更明显
                viscous_enhancement = 1.0 + 0.1 * (alpha_deg - 10) / 10
                Re_factor_Cd *= viscous_enhancement
        
        result['Cl'] = input_data['Cl'] * Re_factor_Cl
        result['Cd'] = input_data['Cd'] * Re_factor_Cd
        result['viscous_factor_Cl'] = Re_factor_Cl
        result['viscous_factor_Cd'] = Re_factor_Cd
        
        return result

class CompressibilityCorrection(PhysicalCorrectionBase):
    """压缩性修正"""
    
    def __init__(self, config):
        super().__init__(config)
        self.correction_type = 'compressibility'
        self.M_crit = config.get('M_crit', 0.7)
    
    def apply(self, input_data):
        """应用压缩性修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        M = input_data.get('M', 0.1)  # 马赫数
        
        if M < self.M_crit:
            # Prandtl-Glauert修正
            beta = np.sqrt(1 - M**2)
            Cl_factor = 1 / beta
            Cd_factor = 1.0
        else:
            # 超临界修正
            Cl_factor = 0.9
            Cd_factor = 1.5
        
        result['Cl'] = input_data['Cl'] * Cl_factor
        result['Cd'] = input_data['Cd'] * Cd_factor
        result['compressibility_factor'] = Cl_factor
        
        return result

class RotationalEffectsCorrection(PhysicalCorrectionBase):
    """旋转效应修正"""
    
    def __init__(self, config):
        super().__init__(config)
        self.correction_type = 'rotational_effects'
        self.c_R = config.get('c_R', 0.1)  # 弦长与半径比
    
    def apply(self, input_data):
        """应用旋转效应修正"""
        self.validate_inputs(input_data)
        
        result = input_data.copy()
        r_R = input_data['r_R']
        alpha = input_data['alpha']
        
        # Du & Selig旋转效应修正
        rotation_factor = 1 + (self.c_R / r_R) * np.sin(alpha) * 0.1
        rotation_factor = np.clip(rotation_factor, 0.9, 1.2)
        
        result['Cl'] = input_data['Cl'] * rotation_factor
        result['rotation_factor'] = rotation_factor
        
        return result
        def get_correction_info(self): 
            return {'type': 'hub_loss', 'description': '简化叶根损失修正'}
    
    class ViscousEffectsCorrection(PhysicalCorrectionBase):
        def __init__(self, config): 
            self.config = config
            self.correction_type = 'viscous_effects'
        def apply(self, input_data): 
            return input_data
        def get_correction_info(self): 
            return {'type': 'viscous_effects', 'description': '简化粘性修正'}
    
    class CompressibilityCorrection(PhysicalCorrectionBase):
        def __init__(self, config): 
            self.config = config
            self.correction_type = 'compressibility'
        def apply(self, input_data): 
            return input_data
        def get_correction_info(self): 
            return {'type': 'compressibility', 'description': '简化压缩性修正'}
    
    class RotationalEffectsCorrection(PhysicalCorrectionBase):
        def __init__(self, config): 
            self.config = config
            self.correction_type = 'rotational_effects'
        def apply(self, input_data): 
            return input_data
        def get_correction_info(self): 
            return {'type': 'rotational_effects', 'description': '简化旋转效应修正'}


class PhysicalCorrectionBase(ABC):
    """物理修正基类"""
    
    @abstractmethod
    def apply(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用物理修正
        
        Args:
            input_data: 输入数据
            
        Returns:
            修正后的数据
        """
        pass
    
    @abstractmethod
    def get_correction_info(self) -> Dict[str, Any]:
        """获取修正信息"""
        pass
    
    @property
    @abstractmethod
    def correction_type(self) -> str:
        """修正类型"""
        pass


class UnifiedPhysicalCorrections:
    """
    统一物理修正系统
    
    管理和应用所有物理修正，提供统一的接口。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化统一物理修正系统
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 修正器注册表
        self.corrections: Dict[str, PhysicalCorrectionBase] = {}
        
        # 修正顺序
        self.correction_order = [
            'tip_loss',
            'hub_loss', 
            'viscous_effects',
            'compressibility',
            'rotational_effects'
        ]
        
        # 初始化各种修正
        self._initialize_corrections()
        
        # 统计信息
        self.correction_stats = {
            'total_applications': 0,
            'correction_counts': {},
            'average_correction_factors': {}
        }
        
        print("统一物理修正系统初始化完成")
        print(f"  启用的修正: {list(self.corrections.keys())}")
    
    def _initialize_corrections(self):
        """初始化各种物理修正"""
        # 叶尖损失修正
        if self.config.get('enable_tip_loss', True):
            self.corrections['tip_loss'] = TipLossCorrection(self.config)
        
        # 叶根损失修正
        if self.config.get('enable_hub_loss', True):
            self.corrections['hub_loss'] = HubLossCorrection(self.config)
        
        # 粘性效应修正
        if self.config.get('enable_viscous_effects', False):
            self.corrections['viscous_effects'] = ViscousEffectsCorrection(self.config)
        
        # 压缩性修正
        if self.config.get('enable_compressibility', False):
            self.corrections['compressibility'] = CompressibilityCorrection(self.config)
        
        # 旋转效应修正
        if self.config.get('enable_rotational_effects', False):
            self.corrections['rotational_effects'] = RotationalEffectsCorrection(self.config)
        
        # 初始化统计计数器
        for correction_type in self.corrections.keys():
            self.correction_stats['correction_counts'][correction_type] = 0
            self.correction_stats['average_correction_factors'][correction_type] = 1.0
    
    def apply_all(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用所有启用的物理修正
        
        Args:
            input_data: 输入数据，包含：
                - r: 径向位置 [m]
                - R: 转子半径 [m]
                - B: 桨叶数
                - phi: 入流角 [rad]
                - omega: 角速度 [rad/s]
                - azimuth: 方位角 [rad]
                - Cl: 升力系数
                - Cd: 阻力系数
                - alpha: 攻角 [rad]
                - 其他参数...
                
        Returns:
            修正后的数据
        """
        # 验证输入数据
        self._validate_input_data(input_data)
        
        # 复制输入数据
        corrected_data = input_data.copy()
        
        # 按顺序应用修正
        for correction_type in self.correction_order:
            if correction_type in self.corrections:
                try:
                    # 应用修正
                    correction_result = self.corrections[correction_type].apply(corrected_data)
                    
                    # 更新数据
                    corrected_data.update(correction_result)
                    
                    # 更新统计
                    self._update_correction_stats(correction_type, correction_result)
                    
                except Exception as e:
                    warnings.warn(f"物理修正 {correction_type} 应用失败: {e}")
                    continue
        
        # 更新总应用次数
        self.correction_stats['total_applications'] += 1
        
        return corrected_data
    
    def apply_correction(self, correction_type: str, 
                        input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用单个物理修正
        
        Args:
            correction_type: 修正类型
            input_data: 输入数据
            
        Returns:
            修正后的数据
        """
        if correction_type not in self.corrections:
            available_types = list(self.corrections.keys())
            raise ValueError(f"修正类型 {correction_type} 不可用. "
                           f"可用类型: {available_types}")
        
        return self.corrections[correction_type].apply(input_data)
    
    def _validate_input_data(self, input_data: Dict[str, Any]):
        """验证输入数据"""
        required_fields = ['r', 'R', 'B', 'Cl', 'Cd']
        
        for field in required_fields:
            if field not in input_data:
                raise ValueError(f"缺少必要字段: {field}")
        
        # 检查数值合理性
        if input_data['r'] <= 0 or input_data['R'] <= 0:
            raise ValueError("径向位置和转子半径必须大于0")
        
        if input_data['B'] < 2:
            raise ValueError("桨叶数必须至少为2")
    
    def _update_correction_stats(self, correction_type: str, 
                               correction_result: Dict[str, Any]):
        """更新修正统计信息"""
        self.correction_stats['correction_counts'][correction_type] += 1
        
        # 计算修正因子
        if 'Cl' in correction_result and 'Cl_original' in correction_result:
            if correction_result['Cl_original'] != 0:
                correction_factor = correction_result['Cl'] / correction_result['Cl_original']
                
                # 更新平均修正因子
                current_avg = self.correction_stats['average_correction_factors'][correction_type]
                count = self.correction_stats['correction_counts'][correction_type]
                
                new_avg = (current_avg * (count - 1) + correction_factor) / count
                self.correction_stats['average_correction_factors'][correction_type] = new_avg
    
    def get_enabled_corrections(self) -> List[str]:
        """获取启用的修正列表"""
        return list(self.corrections.keys())
    
    def set_correction_enabled(self, correction_type: str, enabled: bool):
        """设置修正启用状态"""
        if enabled:
            # 启用修正
            if correction_type == 'tip_loss':
                self.corrections['tip_loss'] = TipLossCorrection(self.config)
            elif correction_type == 'hub_loss':
                self.corrections['hub_loss'] = HubLossCorrection(self.config)
            elif correction_type == 'viscous_effects':
                self.corrections['viscous_effects'] = ViscousEffectsCorrection(self.config)
            elif correction_type == 'compressibility':
                self.corrections['compressibility'] = CompressibilityCorrection(self.config)
            elif correction_type == 'rotational_effects':
                self.corrections['rotational_effects'] = RotationalEffectsCorrection(self.config)
            else:
                raise ValueError(f"未知的修正类型: {correction_type}")
        else:
            # 禁用修正
            if correction_type in self.corrections:
                del self.corrections[correction_type]
    
    def get_correction_info(self, correction_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取修正信息
        
        Args:
            correction_type: 修正类型，None表示获取所有修正信息
            
        Returns:
            修正信息
        """
        if correction_type is not None:
            if correction_type not in self.corrections:
                raise ValueError(f"修正类型 {correction_type} 不存在")
            return self.corrections[correction_type].get_correction_info()
        else:
            # 返回所有修正信息
            info = {}
            for corr_type, correction in self.corrections.items():
                info[corr_type] = correction.get_correction_info()
            return info
    
    def get_correction_statistics(self) -> Dict[str, Any]:
        """获取修正统计信息"""
        return self.correction_stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.correction_stats = {
            'total_applications': 0,
            'correction_counts': {},
            'average_correction_factors': {}
        }
        
        # 重新初始化计数器
        for correction_type in self.corrections.keys():
            self.correction_stats['correction_counts'][correction_type] = 0
            self.correction_stats['average_correction_factors'][correction_type] = 1.0
    
    def validate_corrections(self) -> Dict[str, bool]:
        """验证所有修正的有效性"""
        validation_results = {}
        
        # 测试数据
        test_data = {
            'r': 0.5,
            'R': 1.0,
            'B': 4,
            'phi': np.radians(10),
            'omega': 100.0,
            'azimuth': 0.0,
            'Cl': 0.8,
            'Cd': 0.02,
            'alpha': np.radians(8)
        }
        
        for correction_type, correction in self.corrections.items():
            try:
                result = correction.apply(test_data)
                
                # 检查结果合理性
                if 'Cl' in result and 'Cd' in result:
                    Cl_corrected = result['Cl']
                    Cd_corrected = result['Cd']
                    
                    # 基本合理性检查
                    is_valid = (
                        np.isfinite(Cl_corrected) and 
                        np.isfinite(Cd_corrected) and
                        Cd_corrected >= 0 and
                        abs(Cl_corrected) < 10.0  # 合理的升力系数范围
                    )
                    
                    validation_results[correction_type] = is_valid
                else:
                    validation_results[correction_type] = False
                    
            except Exception as e:
                warnings.warn(f"修正 {correction_type} 验证失败: {e}")
                validation_results[correction_type] = False
        
        return validation_results
    
    def generate_correction_report(self) -> str:
        """生成修正报告"""
        stats = self.get_correction_statistics()
        validation = self.validate_corrections()
        
        report = f"""
物理修正系统报告
===============

启用的修正: {len(self.corrections)} 个
总应用次数: {stats['total_applications']}

各修正详情:
"""
        
        for correction_type in self.corrections.keys():
            count = stats['correction_counts'].get(correction_type, 0)
            avg_factor = stats['average_correction_factors'].get(correction_type, 1.0)
            is_valid = validation.get(correction_type, False)
            
            report += f"""
{correction_type}:
  - 应用次数: {count}
  - 平均修正因子: {avg_factor:.4f}
  - 验证状态: {'通过' if is_valid else '失败'}
"""
        
        return report
    
    def optimize_correction_order(self, test_cases: List[Dict[str, Any]]) -> List[str]:
        """
        优化修正应用顺序
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            优化后的修正顺序
        """
        if len(self.corrections) <= 1:
            return list(self.corrections.keys())
        
        # 测试不同的修正顺序
        import itertools
        
        correction_types = list(self.corrections.keys())
        best_order = correction_types.copy()
        best_score = float('inf')
        
        # 尝试所有可能的排列（如果修正数量不多）
        if len(correction_types) <= 6:
            for order in itertools.permutations(correction_types):
                score = self._evaluate_correction_order(list(order), test_cases)
                if score < best_score:
                    best_score = score
                    best_order = list(order)
        
        return best_order
    
    def _evaluate_correction_order(self, order: List[str], 
                                 test_cases: List[Dict[str, Any]]) -> float:
        """评估修正顺序的性能"""
        total_error = 0.0
        
        for test_case in test_cases:
            try:
                # 临时设置修正顺序
                original_order = self.correction_order.copy()
                self.correction_order = order
                
                # 应用修正
                result = self.apply_all(test_case)
                
                # 计算某种误差指标（这里使用简化的指标）
                error = abs(result.get('Cl', 0) - test_case.get('Cl_target', 0))
                total_error += error
                
                # 恢复原始顺序
                self.correction_order = original_order
                
            except Exception:
                total_error += 1000.0  # 惩罚失败的情况
        
        return total_error / len(test_cases) if test_cases else float('inf')
    
    def export_correction_data(self) -> Dict[str, Any]:
        """导出修正数据"""
        export_data = {
            'enabled_corrections': list(self.corrections.keys()),
            'correction_order': self.correction_order,
            'statistics': self.correction_stats,
            'configuration': {}
        }
        
        # 导出各修正的配置
        for correction_type, correction in self.corrections.items():
            export_data['configuration'][correction_type] = correction.get_correction_info()
        
        return export_data


class UnifiedPhysicalCorrections:
    """统一物理修正系统"""
    
    def __init__(self, config):
        self.config = config
        self.corrections = {}
        self.correction_stats = {}
        
        # 初始化各种修正
        if config.get('enable_tip_loss', True):
            self.corrections['tip_loss'] = TipLossCorrection(config)
        
        if config.get('enable_hub_loss', True):
            self.corrections['hub_loss'] = HubLossCorrection(config)
        
        if config.get('enable_viscous_effects', True):
            self.corrections['viscous_effects'] = ViscousEffectsCorrection(config)
        
        if config.get('enable_compressibility', False):
            self.corrections['compressibility'] = CompressibilityCorrection(config)
        
        if config.get('enable_rotational_effects', True):
            self.corrections['rotational_effects'] = RotationalEffectsCorrection(config)
    
    def apply_all_corrections(self, input_data):
        """应用所有启用的修正"""
        corrected_data = input_data.copy()
        
        for correction_name, correction in self.corrections.items():
            try:
                corrected_data = correction.apply(corrected_data)
                
                # 更新统计信息
                if correction_name not in self.correction_stats:
                    self.correction_stats[correction_name] = 0
                self.correction_stats[correction_name] += 1
                
            except Exception as e:
                print(f"修正 {correction_name} 应用失败: {e}")
        
        return corrected_data
    
    def get_correction_info(self):
        """获取所有修正的信息"""
        info = {}
        for name, correction in self.corrections.items():
            info[name] = correction.get_correction_info()
        return info