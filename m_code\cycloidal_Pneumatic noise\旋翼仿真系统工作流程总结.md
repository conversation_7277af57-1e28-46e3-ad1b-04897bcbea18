# 🚁 旋翼空气动力学仿真系统 - 工作流程总结

## 📋 系统概述

这是一个**学术研究级别**的旋翼空气动力学仿真框架，支持从概念设计到详细分析的全谱系仿真能力。

## 🔧 核心工作流程

### 1️⃣ 系统启动和初始化

```
用户输入 → 配置加载 → 参数验证 → 求解器选择 → 模块初始化
```

**关键步骤：**
- `main.py` 启动程序
- `config_loader.py` 加载YAML配置文件
- `ConfigManager` 验证和优化参数
- `SolverFactory` 根据保真度选择求解器
- 初始化几何、物理、声学模块

### 2️⃣ 配置参数调用机制

```yaml
# 典型配置文件结构
run_mode:
  fidelity: "medium"          # 保真度选择
  enable_acoustics: true      # 声学开关
  gpu_acceleration: false     # GPU加速

rotor_config:
  rotor_type: "cycloidal"     # 转子类型
  B: 4                        # 桨叶数
  R_rotor: 1.0               # 转子半径
  n_rpm: 1200                # 转速

physics_models:
  aerodynamics_solver: "bemt" # 气动求解器
  wake_model: "prescribed"    # 尾迹模型
  dynamic_stall: true         # 动态失速
```

**配置处理流程：**
1. YAML文件解析
2. 默认值填充
3. 参数类型验证
4. 范围检查
5. 求解器特定优化

### 3️⃣ 气动分析计算流程

#### BEMT求解器核心算法

```python
def solve_step(self, t, dt):
    # 1. 更新桨叶运动学
    self._update_blade_kinematics(t)
    
    # 2. BEMT迭代求解
    for iteration in range(max_iterations):
        # 计算每个桨叶的叶素载荷
        for blade in self.blades:
            for element in blade.elements:
                # 计算相对速度
                V_rel = element.calculate_relative_velocity()
                
                # 计算有效攻角
                alpha_eff = self._calculate_pitch_angle() - inflow_angle
                
                # 查询翼型数据库
                Cl, Cd = self.airfoil_database.get_coefficients(alpha_eff)
                
                # 动态失速修正（如果启用）
                if self.enable_dynamic_stall:
                    Cl, Cd = self.lb_model.calculate_coefficients(alpha_eff, alpha_dot, V_rel, dt)
                
                # 计算叶素载荷
                dL, dD = self._calculate_element_loads(Cl, Cd, V_rel)
        
        # 通过动量理论更新诱导速度
        self._update_induced_velocities(total_loads)
        
        # 检查收敛性
        residual = self._calculate_residual()
        if residual < tolerance:
            converged = True
            break
        
        # 应用松弛因子
        self._apply_relaxation()
    
    # 3. 应用物理修正
    corrected_results = self._apply_physical_corrections()
    
    # 4. 计算性能参数
    performance = self._calculate_performance()
    
    return results
```

#### 关键计算步骤

1. **桨叶运动学更新**
   - 计算桨叶方位角
   - 更新俯仰角（循环翼）或总距/周期变距（传统旋翼）
   - 计算桨叶速度和加速度

2. **叶素载荷计算**
   - 计算相对速度向量
   - 确定有效攻角
   - 查询翼型气动系数
   - 应用动态失速修正
   - 计算升力和阻力

3. **诱导速度更新**
   - 基于动量理论计算轴向诱导速度
   - 考虑径向分布效应
   - 应用尾迹收缩修正

4. **收敛性控制**
   - 计算残差
   - 振荡检测
   - 自适应松弛因子
   - Aitken加速（可选）

### 4️⃣ 物理修正系统

```python
class UnifiedPhysicalCorrections:
    def apply_all(self, input_data):
        corrections = {}
        
        # 叶尖损失修正（Prandtl/Goldstein）
        if self.enable_tip_loss:
            F_tip = self._calculate_tip_loss_factor(r_R, B, phi)
            corrections['tip_loss'] = F_tip
        
        # 叶根损失修正
        if self.enable_hub_loss:
            F_hub = self._calculate_hub_loss_factor(r_R, hub_ratio)
            corrections['hub_loss'] = F_hub
        
        # 粘性效应修正
        if self.enable_viscous:
            Re_correction = self._calculate_reynolds_correction(Re)
            corrections['viscous'] = Re_correction
        
        # 压缩性修正
        if self.enable_compressibility:
            M_correction = self._calculate_mach_correction(M)
            corrections['compressibility'] = M_correction
        
        # 应用所有修正
        return self._combine_corrections(input_data, corrections)
```

### 5️⃣ 声学分析流程

#### FW-H方程求解

```python
def solve_fwh_equation(self, surface_data, observer_positions):
    # 1. 计算声源项
    # 厚度噪声源项
    thickness_sources = self._compute_thickness_sources(surface_data)
    
    # 载荷噪声源项  
    loading_sources = self._compute_loading_sources(surface_data)
    
    # 四极子噪声源项（高速情况）
    if self.include_quadrupole:
        quadrupole_sources = self._compute_quadrupole_sources(surface_data)
    
    # 2. 远场声压积分
    for observer in observer_positions:
        # 计算延迟时间
        retarded_time = self._calculate_retarded_time(source_pos, observer_pos)
        
        # 应用多普勒修正
        doppler_factor = self._calculate_doppler_factor(source_velocity, observer_pos)
        
        # 积分计算声压
        acoustic_pressure = self._integrate_acoustic_sources(
            thickness_sources, loading_sources, retarded_time, doppler_factor
        )
    
    # 3. 频域分析
    frequency_spectrum = self._compute_frequency_spectrum(acoustic_pressure)
    
    # 4. 声学指标计算
    spl = self._calculate_sound_pressure_level(acoustic_pressure)
    oaspl = self._calculate_overall_sound_pressure_level(frequency_spectrum)
    
    return {
        'acoustic_pressure': acoustic_pressure,
        'frequency_spectrum': frequency_spectrum,
        'spl': spl,
        'oaspl': oaspl
    }
```

### 6️⃣ 时间积分和收敛控制

#### 时间积分方法

```python
class TimeIntegrator:
    def integrate(self, y, t, dt, dydt_func):
        if self.method == 'euler':
            return y + dt * dydt_func(y, t)
        
        elif self.method == 'rk4':
            k1 = dt * dydt_func(y, t)
            k2 = dt * dydt_func(y + 0.5*k1, t + 0.5*dt)
            k3 = dt * dydt_func(y + 0.5*k2, t + 0.5*dt)
            k4 = dt * dydt_func(y + k3, t + dt)
            return y + (k1 + 2*k2 + 2*k3 + k4) / 6
        
        elif self.method == 'adaptive':
            return self._adaptive_rk45(y, t, dt, dydt_func)
```

#### 收敛控制策略

```python
class ConvergenceController:
    def check_convergence(self, residual):
        # 1. 基本残差检查
        if residual < self.tolerance:
            return True
        
        # 2. 振荡检测
        if self._detect_oscillation(residual):
            self._apply_damping()
        
        # 3. Aitken加速
        if self.enable_aitken and len(self.residual_history) >= 3:
            self._apply_aitken_acceleration()
        
        # 4. 自适应松弛因子
        if self.adaptive_relaxation:
            self._update_relaxation_factor(residual)
        
        return False
```

### 7️⃣ 验证框架工作流程

#### 学术验证案例执行

```python
def run_caradonna_tung_validation():
    # 1. 加载实验数据
    experimental_data = {
        'test_conditions': {
            'collective_pitch_deg': 8.0,
            'tip_mach_number': 0.439,
            'reynolds_number': 1.5e6,
            'rotor_radius_m': 1.143,
            'blade_count': 2
        },
        'measurement_stations': [0.50, 0.68, 0.80, 0.89, 0.96],
        'pressure_coefficients': {
            'cp_upper': [-0.8, -1.2, -1.5, -1.8, -2.1],
            'cp_lower': [0.3, 0.4, 0.5, 0.6, 0.7]
        }
    }
    
    # 2. 配置仿真参数
    config = create_validation_config(experimental_data['test_conditions'])
    
    # 3. 执行仿真
    simulation = CycloneSimulation(config)
    results = simulation.run()
    
    # 4. 误差分析
    error_metrics = calculate_error_metrics(results, experimental_data)
    
    # 5. 不确定性量化
    uncertainty_analysis = quantify_uncertainties(results, config)
    
    # 6. 学术等级评估
    academic_grade = assess_academic_grade(error_metrics)
    
    # 7. 生成验证报告
    generate_validation_report(error_metrics, uncertainty_analysis, academic_grade)
```

#### 误差分析指标

```python
def calculate_error_metrics(computed, experimental):
    # 平均绝对误差
    mae = np.mean(np.abs(computed - experimental))
    
    # 均方根误差
    rmse = np.sqrt(np.mean((computed - experimental)**2))
    
    # 平均绝对百分比误差
    mape = np.mean(np.abs((computed - experimental) / experimental)) * 100
    
    # 相关系数
    correlation = np.corrcoef(computed, experimental)[0, 1]
    
    # 决定系数
    r_squared = correlation**2
    
    return {
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'correlation': correlation,
        'r_squared': r_squared
    }
```

### 8️⃣ 后处理和结果输出

#### 性能参数计算

```python
def calculate_performance(forces, moments, geometry, operating_conditions):
    # 积分载荷
    total_thrust = np.sum(forces[:, 2])  # Z方向力
    total_power = np.sum(moments[:, 2] * omega)  # 转矩功率
    total_torque = np.sum(moments[:, 2])
    
    # 无量纲系数
    rho = operating_conditions['density']
    V_tip = omega * geometry['radius']
    disk_area = np.pi * geometry['radius']**2
    
    CT = total_thrust / (rho * disk_area * V_tip**2)
    CP = total_power / (rho * disk_area * V_tip**3)
    
    # 品质因数（悬停）
    if operating_conditions['forward_speed'] == 0:
        ideal_power = total_thrust**1.5 / np.sqrt(2 * rho * disk_area)
        figure_of_merit = ideal_power / total_power if total_power > 0 else 0
    else:
        figure_of_merit = None
    
    return {
        'thrust': total_thrust,
        'power': total_power,
        'torque': total_torque,
        'CT': CT,
        'CP': CP,
        'figure_of_merit': figure_of_merit
    }
```

#### 可视化和报告生成

```python
def generate_comprehensive_report(results, config, validation_results=None):
    report = {
        'simulation_info': {
            'timestamp': datetime.now().isoformat(),
            'solver_type': config['solver_type'],
            'fidelity_level': config['fidelity'],
            'configuration': config
        },
        'performance_results': results['performance'],
        'convergence_info': results['convergence_info'],
        'computational_metrics': {
            'total_solve_time': results['total_time'],
            'average_step_time': results['average_step_time'],
            'memory_usage': results['memory_usage']
        }
    }
    
    if validation_results:
        report['validation_results'] = validation_results
    
    # 生成多格式报告
    generate_html_report(report)
    generate_markdown_report(report)
    generate_json_report(report)
    
    # 生成可视化图表
    create_performance_plots(results)
    create_convergence_plots(results)
    if 'acoustic_results' in results:
        create_acoustic_plots(results['acoustic_results'])
```

## 🎯 关键技术特点

### 多保真度无缝切换
- **低保真度**: 升力线理论，适用于概念设计
- **中保真度**: BEMT理论，适用于工程分析  
- **高保真度**: UVLM方法，适用于精确研究

### 智能算法优化
- 自适应收敛控制
- 智能初值估计
- 振荡检测和抑制
- GPU并行加速

### 学术验证体系
- 标准验证案例库
- 完整误差分析
- 不确定性量化
- 学术等级评估

### 工程实用性
- 模块化架构设计
- 灵活的配置系统
- 专业级可视化
- 多格式报告输出

## 📊 性能指标

| 指标 | 低保真度 | 中保真度 | 高保真度 |
|------|----------|----------|----------|
| 计算时间 | 秒级 | 分钟级 | 小时级 |
| 精度 | ±20% | ±10% | ±5% |
| 适用场景 | 概念设计 | 工程分析 | 精确研究 |
| 收敛率 | >98% | >95% | >90% |

## 🚀 使用示例

### 基本使用流程

```bash
# 1. 配置仿真参数
vim config.yaml

# 2. 运行仿真
python main.py --config config.yaml

# 3. 查看结果
ls simulation_outputs/

# 4. 运行验证
python academic_validation_pro/scripts/run_caradonna_tung_validation.py
```

### 高级功能

```bash
# GPU加速仿真
python main.py --config config.yaml --gpu

# 参数扫描
python scripts/parameter_sweep.py --config sweep_config.yaml

# 学术验证
python academic_validation_pro/scripts/run_all_validations.py
```

这个工作流程展示了一个完整的、工业级的旋翼仿真系统，具有高度的模块化、可扩展性和学术验证能力，能够满足从概念设计到详细研究的各种需求。