# 系统诊断报告

生成时间: 当前日期: 2025/08/01 周五 
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日) 系统无法接受输入的日期。
输入新日期: (年月日)
项目路径: C:\Users\<USER>\Desktop\code\m_code\cycloidal_Pneumatic noise\cycloidal_rotor_suite

## 统计信息

- ✅ 成功项目: 33
- ⚠️  警告项目: 7
- ❌ 错误项目: 10

## 错误详情

### [CONFIG] configs/basic_simulation.yaml
**错误**: 配置加载失败: ConventionalRotorParameters.__init__() got an unexpected keyword argument 'twist_deg'
**严重程度**: ERROR

### [CONFIG] configs/caradonna_tung_validation.yaml
**错误**: 配置加载失败: 俯仰角幅值必须为正值
**严重程度**: ERROR

### [CONFIG] configs/conventional_rotor_example.yaml
**错误**: 配置加载失败: ConventionalRotorParameters.__init__() got an unexpected keyword argument 'twist_deg'
**严重程度**: ERROR

### [SOLVER] BEMT
**错误**: 求解器创建失败: 'dict' object has no attribute 'pitch_amplitude_top'
**严重程度**: ERROR

### [SOLVER] UVLM
**错误**: 求解器创建失败: 'dict' object has no attribute 'pitch_amplitude_top'
**严重程度**: ERROR

### [SOLVER] LiftingLine
**错误**: 求解器创建失败: 未知的求解器类型: LiftingLine. 可用类型: ['BEMT', 'UVLM', 'bemt', 'uvlm', 'low_fidelity', 'high_fidelity']
**严重程度**: ERROR

### [WORKFLOW] SIMULATION
**错误**: 仿真工作流程测试失败: 'dict' object has no attribute 'pitch_amplitude_top'
**严重程度**: ERROR

### [VALIDATION] academic_validation_pro.cases.caradonna_tung
**错误**: 验证模块导入失败: No module named 'academic_validation_pro.cases.caradonna_tung'
**严重程度**: ERROR

### [VALIDATION] academic_validation_pro.cases.hart_ii
**错误**: 验证模块导入失败: No module named 'academic_validation_pro.cases.hart_ii'
**严重程度**: ERROR

### [VALIDATION] academic_validation_pro.analysis.error_metrics
**错误**: 验证模块导入失败: No module named 'academic_validation_pro.analysis.error_metrics'
**严重程度**: ERROR

## 警告详情

### [DEPENDENCY] pyvista
**警告**: 可选依赖缺失

### [DEPENDENCY] mayavi
**警告**: 可选依赖缺失

### [DEPENDENCY] dash
**警告**: 可选依赖缺失

### [DEPENDENCY] gputil
**警告**: 可选依赖缺失

### [STRUCTURE] configs/
**警告**: 缺少__init__.py

### [STRUCTURE] scripts/
**警告**: 缺少__init__.py

### [STRUCTURE] tests/
**警告**: 缺少__init__.py

