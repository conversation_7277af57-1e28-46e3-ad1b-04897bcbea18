#!/usr/bin/env python3
"""
剩余问题修复脚本
解决测试中发现的剩余问题
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_corrections_file():
    """检查corrections.py文件的问题"""
    print("🔍 检查物理修正文件...")
    
    corrections_file = project_root / "physics" / "corrections.py"
    
    if corrections_file.exists():
        with open(corrections_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计UnifiedPhysicalCorrections类的定义数量
        class_count = content.count('class UnifiedPhysicalCorrections')
        print(f"   发现 {class_count} 个UnifiedPhysicalCorrections类定义")
        
        # 检查get_enabled_corrections方法
        method_count = content.count('def get_enabled_corrections')
        print(f"   发现 {method_count} 个get_enabled_corrections方法定义")
        
        if class_count > 1:
            print("   ⚠️  发现重复的类定义，这可能导致方法缺失")
        
        if method_count == 0:
            print("   ❌ 缺少get_enabled_corrections方法")
        else:
            print("   ✅ 找到get_enabled_corrections方法")
    
    else:
        print("   ❌ corrections.py文件不存在")

def check_blade_element_methods():
    """检查BladeElement类的方法"""
    print("\n🔍 检查BladeElement类...")
    
    blade_file = project_root / "aerodynamics" / "blade_element.py"
    
    if blade_file.exists():
        with open(blade_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查update_kinematics方法
        if 'def update_kinematics' in content:
            print("   ✅ 找到update_kinematics方法")
        else:
            print("   ❌ 缺少update_kinematics方法")
    else:
        print("   ❌ blade_element.py文件不存在")

def suggest_fixes():
    """提供修复建议"""
    print("\n💡 修复建议:")
    print("=" * 50)
    
    print("1. 物理修正接口修复:")
    print("   - 检查corrections.py文件中是否有重复的类定义")
    print("   - 确保UnifiedPhysicalCorrections类包含get_enabled_corrections方法")
    print("   - 清理重复的代码段")
    
    print("\n2. BladeElement运动学更新:")
    print("   - 在BladeElement类中添加update_kinematics方法")
    print("   - 实现基本的运动学计算功能")
    
    print("\n3. 测试验证:")
    print("   - 修复后重新运行test_code_fixes.py")
    print("   - 确保所有测试通过")

def create_quick_fix():
    """创建快速修复代码片段"""
    print("\n🔧 生成快速修复代码...")
    
    # 为UnifiedPhysicalCorrections添加缺失方法的代码片段
    corrections_fix = '''
    def get_enabled_corrections(self) -> List[str]:
        """获取启用的修正列表"""
        corrections = []
        if self.config.get('enable_tip_loss', True):
            corrections.append('tip_loss')
        if self.config.get('enable_hub_loss', True):
            corrections.append('hub_loss')
        if self.config.get('enable_viscous_effects', False):
            corrections.append('viscous_effects')
        return corrections
    '''
    
    # 为BladeElement添加update_kinematics方法的代码片段
    blade_element_fix = '''
    def update_kinematics(self, azimuth: float, collective: float, 
                         cyclic_lateral: float = 0.0, cyclic_longitudinal: float = 0.0):
        """
        更新叶素运动学状态
        
        Args:
            azimuth: 方位角 [rad]
            collective: 总距 [rad]
            cyclic_lateral: 横向周期变距 [rad]
            cyclic_longitudinal: 纵向周期变距 [rad]
        """
        # 计算当前桨叶角
        blade_angle = collective + cyclic_lateral * np.cos(azimuth) + cyclic_longitudinal * np.sin(azimuth)
        
        # 更新叶素扭转角
        self.twist = blade_angle
        
        # 更新位置（简化实现）
        self.position[0] = self.radius * np.cos(azimuth)
        self.position[1] = self.radius * np.sin(azimuth)
        self.position[2] = 0.0
        
        # 更新速度（简化实现）
        omega = self.config.get('omega_rotor', 100.0)
        self.velocity[0] = -self.radius * omega * np.sin(azimuth)
        self.velocity[1] = self.radius * omega * np.cos(azimuth)
        self.velocity[2] = 0.0
        
        self.angular_velocity = omega
    '''
    
    print("   生成的修复代码片段已准备就绪")
    print("   可以将这些代码添加到相应的类中")

def main():
    """主函数"""
    print("🧪 剩余问题诊断和修复建议")
    print("=" * 60)
    
    check_corrections_file()
    check_blade_element_methods()
    suggest_fixes()
    create_quick_fix()
    
    print("\n" + "=" * 60)
    print("📋 诊断完成")
    print("请根据上述建议修复剩余问题，然后重新运行测试验证")

if __name__ == "__main__":
    main()