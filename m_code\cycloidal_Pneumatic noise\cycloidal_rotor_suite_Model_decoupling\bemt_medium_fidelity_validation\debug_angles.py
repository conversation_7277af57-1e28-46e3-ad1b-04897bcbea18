#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试攻角计算
===========

检查攻角和升力系数计算是否正确。

作者: Augment Agent
日期: 2025-07-28
"""

import numpy as np
from simple_bemt import SimpleBEMT

def debug_angles():
    """调试攻角计算"""
    
    print('🔍 调试攻角计算')
    print('=' * 50)
    
    # 创建求解器
    solver = SimpleBEMT(radius=8.18, num_blades=4, hub_radius=0.61, num_stations=10)
    
    # 设置基本参数
    rpm = 258
    omega = rpm * 2 * np.pi / 60
    mu = 0.0  # 悬停
    lambda_i = np.full(solver.num_stations, 0.05)  # 初始诱导速度
    collective_pitch = 14.0  # 总距角
    
    print(f'基本参数:')
    print(f'   转速: {rpm} RPM ({omega:.2f} rad/s)')
    print(f'   前飞比: {mu:.3f}')
    print(f'   总距角: {collective_pitch:.1f}°')
    print(f'   初始诱导速度: {lambda_i[0]:.3f}')
    
    # 计算角度
    phi, alpha = solver._compute_angles(lambda_i, mu, omega, collective_pitch)
    
    print(f'\n径向分布:')
    print(f'{"r/R":>6s} {"r[m]":>6s} {"扭转[°]":>8s} {"流入角[°]":>9s} {"攻角[°]":>8s}')
    print('-' * 50)
    
    for i in range(solver.num_stations):
        r_R = solver.r_stations[i] / solver.R
        r = solver.r_stations[i]
        twist = solver.twist[i]
        phi_deg = np.degrees(phi[i])
        alpha_deg = alpha[i]
        
        print(f'{r_R:6.3f} {r:6.2f} {twist:8.1f} {phi_deg:9.2f} {alpha_deg:8.1f}')
    
    # 计算升力系数
    cl, cd = solver._compute_airfoil_coefficients(alpha)
    
    print(f'\n翼型系数:')
    print(f'{"r/R":>6s} {"攻角[°]":>8s} {"Cl":>6s} {"Cd":>6s}')
    print('-' * 30)
    
    for i in range(solver.num_stations):
        r_R = solver.r_stations[i] / solver.R
        print(f'{r_R:6.3f} {alpha[i]:8.1f} {cl[i]:6.3f} {cd[i]:6.4f}')
    
    # 检查攻角范围
    alpha_min, alpha_max = np.min(alpha), np.max(alpha)
    print(f'\n攻角范围: {alpha_min:.1f}° 到 {alpha_max:.1f}°')
    
    if alpha_min < -20 or alpha_max > 20:
        print('⚠️  攻角超出合理范围！')
    else:
        print('✅ 攻角在合理范围内')
    
    # 检查升力系数
    cl_min, cl_max = np.min(cl), np.max(cl)
    print(f'升力系数范围: {cl_min:.3f} 到 {cl_max:.3f}')
    
    if np.any(cl < 0):
        print('⚠️  存在负升力系数！')
        negative_indices = np.where(cl < 0)[0]
        print(f'   负升力位置: r/R = {solver.r_stations[negative_indices]/solver.R}')
    else:
        print('✅ 升力系数均为正值')

if __name__ == "__main__":
    debug_angles()
