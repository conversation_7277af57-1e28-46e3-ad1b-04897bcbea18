#!/usr/bin/env python3
"""
直接验证测试脚本
===============

直接测试核心功能，不依赖复杂的工作流程。
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本导入"""
    print("=" * 60)
    print("1. 基本导入测试")
    print("=" * 60)
    
    try:
        # 核心模块
        from cyclone_sim.config_loader import ConfigLoader
        print("✅ ConfigLoader 导入成功")
        
        from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
        print("✅ SolverFactory 导入成功")
        
        from cyclone_sim.core.aerodynamics.bemt_solver import BEMTSolver
        print("✅ BEMTSolver 导入成功")
        
        from cyclone_sim.core.aerodynamics.uvlm_solver import UVLMSolver
        print("✅ UVLMSolver 导入成功")
        
        # 声学模块
        from cyclone_sim.core.acoustics.fwh_solver import FWHSolver
        print("✅ FWHSolver 导入成功")
        
        from cyclone_sim.core.acoustics.bpm_noise import BPMNoiseModel
        print("✅ BPMNoiseModel 导入成功")
        
        # 仿真模块
        from cyclone_sim.simulation import CycloneSimulation
        print("✅ CycloneSimulation 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config_creation():
    """测试配置创建"""
    print("\n" + "=" * 60)
    print("2. 配置创建测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        
        # 基本配置
        config_dict = {
            'n_rpm': 600.0,
            'B': 4,
            'c': 0.08,
            'R_rotor': 0.4,
            'rho': 1.225,
            'c0': 343.0,
            'dt_sim': 0.002,
            'T_buildup': 0.1,
            'T_acoustic_record': 0.1,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 12.0,
            'pitch_amplitude_top': 12.0,
            'pitch_amplitude_bottom': 12.0,
            'observer_pos': [4.0, 0.0, 0.0],
        }
        
        config = ConfigLoader.from_dict(config_dict)
        print("✅ 基本配置创建成功")
        print(f"   RPM: {config.n_rpm}, 桨叶数: {config.B}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bemt_solver_direct():
    """直接测试BEMT求解器"""
    print("\n" + "=" * 60)
    print("3. BEMT求解器直接测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
        
        # BEMT配置
        config_dict = {
            'n_rpm': 600.0,
            'B': 4,
            'c': 0.08,
            'R_rotor': 0.4,
            'rho': 1.225,
            'solver_fidelity': 'low',
            'enable_3d_uvlm': False,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 12.0,
            'pitch_amplitude_top': 12.0,
            'pitch_amplitude_bottom': 12.0,
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 创建BEMT求解器
        solver = SolverFactory.create_solver('BEMT', config)
        print("✅ BEMT求解器创建成功")
        
        # 测试单步求解
        start_time = time.time()
        result = solver.solve_step(0.0, 0.002)
        solve_time = time.time() - start_time
        
        print(f"✅ 单步求解成功，耗时: {solve_time:.4f}s")
        
        # 检查结果结构
        if 'performance' in result:
            perf = result['performance']
            thrust = perf.get('thrust', 0)
            power = perf.get('power', 0)
            print(f"   推力: {thrust:.3f} N")
            print(f"   功率: {power:.3f} W")
            
            if thrust > 0:
                print("✅ 推力计算正常")
            else:
                print("⚠️  推力为零或负值")
        
        # 测试多步求解
        print("   测试多步求解...")
        for i in range(3):
            result = solver.solve_step(i * 0.002, 0.002)
            if 'performance' not in result:
                print(f"❌ 第{i+1}步求解失败")
                return False
        
        print("✅ 多步求解成功")
        return True
        
    except Exception as e:
        print(f"❌ BEMT求解器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fwh_solver_direct():
    """直接测试FWH求解器"""
    print("\n" + "=" * 60)
    print("4. FWH求解器直接测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.core.acoustics.fwh_solver import FWHSolver
        
        # FWH配置
        config_dict = {
            'c0': 343.0,
            'rho': 1.225,
            'observer_pos': [4.0, 0.0, 0.0],
            'acoustic_model_level': 2,
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 创建FWH求解器
        fwh_solver = FWHSolver(config)
        print("✅ FWH求解器创建成功")
        
        # 添加测试数据
        positions = np.array([[0.1, 0.0, 0.0], [0.2, 0.0, 0.0]])
        forces = np.array([[1.0, 0.0, 0.0], [1.5, 0.0, 0.0]])
        
        # 添加几个时间步的数据
        for i in range(5):
            t = i * 0.001
            fwh_solver.add_source_data(t, positions, forces)
        
        print("✅ 源数据添加成功")
        
        # 测试声压计算（如果方法存在）
        if hasattr(fwh_solver, 'calculate_acoustic_pressure'):
            try:
                t_acoustic, p_acoustic = fwh_solver.calculate_acoustic_pressure()
                if len(t_acoustic) > 0 and len(p_acoustic) > 0:
                    print(f"✅ 声压计算成功，数据点: {len(t_acoustic)}")
                    p_rms = np.sqrt(np.mean(p_acoustic**2))
                    print(f"   声压RMS: {p_rms:.2e} Pa")
                else:
                    print("⚠️  声压计算返回空数据")
            except Exception as e:
                print(f"⚠️  声压计算失败: {e}")
        else:
            print("⚠️  FWH求解器缺少声压计算方法")
        
        return True
        
    except Exception as e:
        print(f"❌ FWH求解器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bpm_model_direct():
    """直接测试BPM模型"""
    print("\n" + "=" * 60)
    print("5. BPM模型直接测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.core.acoustics.bpm_noise import BPMNoiseModel
        
        # BPM配置
        config_dict = {
            'c0': 343.0,
            'rho': 1.225,
            'observer_pos': [4.0, 0.0, 0.0],
            'c': 0.08,
            'R_rotor': 0.4,
            'B': 4,
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 创建BPM模型
        bpm_model = BPMNoiseModel(config)
        print("✅ BPM模型创建成功")
        
        # 测试宽带噪声计算
        V_rel = 30.0  # 相对速度 [m/s]
        alpha = np.radians(5.0)  # 攻角 [rad]
        radius = 0.3  # 径向位置 [m]
        
        frequencies, psd = bpm_model.calculate_broadband_noise(V_rel, alpha, radius)
        
        if len(frequencies) > 0 and len(psd) > 0:
            print(f"✅ 宽带噪声计算成功")
            print(f"   频率范围: {frequencies[0]:.0f} - {frequencies[-1]:.0f} Hz")
            print(f"   频点数: {len(frequencies)}")
            print(f"   最大PSD: {np.max(psd):.2e} Pa²/Hz")
        else:
            print("❌ 宽带噪声计算返回空数据")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ BPM模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_simulation():
    """测试简单仿真"""
    print("\n" + "=" * 60)
    print("6. 简单仿真测试")
    print("=" * 60)
    
    try:
        from cyclone_sim.config_loader import ConfigLoader
        from cyclone_sim.simulation import CycloneSimulation
        
        # 简单仿真配置
        config_dict = {
            'n_rpm': 300.0,  # 低转速
            'B': 3,
            'c': 0.06,
            'R_rotor': 0.3,
            'rho': 1.225,
            'c0': 343.0,
            'dt_sim': 0.01,  # 大时间步长
            'T_buildup': 0.02,  # 短时间
            'T_acoustic_record': 0.01,
            'solver_fidelity': 'low',
            'enable_3d_uvlm': False,
            'run_acoustics': False,  # 先测试纯气动
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 8.0,
            'pitch_amplitude_top': 8.0,
            'pitch_amplitude_bottom': 8.0,
        }
        
        config = ConfigLoader.from_dict(config_dict)
        
        # 创建仿真
        simulation = CycloneSimulation(config, output_dir="test_simple_output")
        print("✅ 仿真实例创建成功")
        
        # 检查关键组件
        if hasattr(simulation, 'aero_solver') and simulation.aero_solver:
            print("✅ 气动求解器存在")
        else:
            print("❌ 气动求解器缺失")
            return False
        
        if hasattr(simulation, 'wake_system') and simulation.wake_system:
            print("✅ 尾迹系统存在")
        else:
            print("⚠️  尾迹系统缺失")
        
        # 测试仿真信息
        if hasattr(simulation, 'get_simulation_info'):
            info = simulation.get_simulation_info()
            print("✅ 仿真信息获取成功")
        
        print("✅ 简单仿真测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 简单仿真测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始直接验证测试...")
    print(f"项目路径: {project_root}")
    print()
    
    # 运行所有测试
    tests = [
        ("基本导入", test_basic_imports),
        ("配置创建", test_config_creation),
        ("BEMT求解器", test_bemt_solver_direct),
        ("FWH求解器", test_fwh_solver_direct),
        ("BPM模型", test_bmp_model_direct),
        ("简单仿真", test_simple_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 生成报告
    print("\n" + "=" * 80)
    print("🎯 直接验证测试报告")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15s} {status}")
    
    print("-" * 80)
    print(f"总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有直接测试通过！核心功能正常。")
        return 0
    else:
        print(f"\n⚠️  {total-passed} 个测试失败，需要进一步修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())