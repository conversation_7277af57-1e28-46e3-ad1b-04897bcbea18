#!/usr/bin/env python3
"""
BEMT中保真度模块演示脚本
======================

演示BEMT中保真度验证模块的主要功能和使用方法。

运行方式:
    python run_demo.py

功能演示:
- 基本求解器使用
- 配置管理
- 性能分析
- 结果可视化

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
import numpy as np

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 尝试导入matplotlib，如果失败则跳过绘图
try:
    import matplotlib.pyplot as plt
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("警告: matplotlib未安装，将跳过绘图功能")

def print_header():
    """打印标题"""
    print("=" * 70)
    print("🚁 BEMT中保真度验证模块 - 功能演示")
    print("=" * 70)
    print("完整复刻原始cycloidal_rotor_suite中BEMT中保真度模块的所有核心功能")
    print()

def demo_basic_usage():
    """演示基本使用"""
    print("📋 1. 基本使用演示")
    print("-" * 50)
    
    try:
        # 导入模块
        from utils.config import ConfigManager
        from core.solver_factory import SolverFactory
        
        # 创建配置
        print("   创建配置...")
        config = ConfigManager({
            'R_rotor': 0.4,          # 转子半径 [m]
            'B': 4,                  # 桨叶数
            'c': 0.07,               # 弦长 [m]
            'omega_rotor': 120.0,    # 角速度 [rad/s]
            'rho': 1.225,            # 空气密度 [kg/m³]
            'bemt_n_elements': 15,   # 叶素数量
            'bemt_tolerance': 1e-4,  # 收敛容差
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 12.0,  # 俯仰幅值 [度]
            'enable_tip_loss': True,
            'enable_hub_loss': True
        })
        
        # 创建求解器
        print("   创建BEMT求解器...")
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        # 执行求解
        print("   执行求解...")
        t = 0.0
        dt = 0.005
        result = solver.solve_step(t, dt)
        
        # 显示结果
        performance = result['performance']
        print(f"   ✅ 推力: {performance['thrust']:.3f} N")
        print(f"   ✅ 功率: {performance['power']:.3f} W")
        print(f"   ✅ 转矩: {performance['torque']:.4f} N·m")
        print(f"   ✅ 品质因数: {performance['figure_of_merit']:.3f}")
        print(f"   ✅ 推力系数: {performance['CT']:.5f}")
        print(f"   ✅ 功率系数: {performance['CP']:.5f}")
        
        convergence = result['convergence_info']
        print(f"   ✅ 收敛状态: {'成功' if convergence['converged'] else '失败'}")
        print(f"   ✅ 迭代次数: {convergence['iterations']}")
        print(f"   ✅ 残差: {convergence['residual']:.2e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本使用演示失败: {e}")
        return False

def demo_time_simulation():
    """演示时域仿真"""
    print("\n⏱️  2. 时域仿真演示")
    print("-" * 50)
    
    try:
        from utils.config import ConfigManager
        from core.solver_factory import SolverFactory
        
        # 创建配置
        config = ConfigManager({
            'R_rotor': 0.3,
            'B': 3,
            'c': 0.06,
            'omega_rotor': 100.0,
            'rho': 1.225,
            'bemt_n_elements': 12,
            'bemt_tolerance': 5e-4,
            'rotor_type': 'cycloidal',
            'pitch_amplitude': 10.0,
            'pitch_phase_offset': 0.0,
            'enable_tip_loss': True
        })
        
        # 创建求解器
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        # 时域仿真参数
        t_end = 0.2  # 仿真时间 [s]
        dt = 0.005   # 时间步长 [s]
        
        print(f"   仿真时间: {t_end}s, 步长: {dt}s")
        
        # 存储结果
        time_history = []
        thrust_history = []
        power_history = []
        torque_history = []
        solve_times = []
        
        # 时间循环
        t = 0.0
        step_count = 0
        
        while t < t_end:
            step_start = time.time()
            
            # 求解
            result = solver.solve_step(t, dt)
            
            # 记录结果
            time_history.append(t)
            thrust_history.append(result['performance']['thrust'])
            power_history.append(result['performance']['power'])
            torque_history.append(result['performance']['torque'])
            solve_times.append(time.time() - step_start)
            
            t += dt
            step_count += 1
            
            # 进度显示
            if step_count % 10 == 0:
                progress = t / t_end * 100
                print(f"   进度: {progress:.0f}%")
        
        # 统计结果
        thrust_mean = np.mean(thrust_history)
        thrust_std = np.std(thrust_history)
        power_mean = np.mean(power_history)
        power_std = np.std(power_history)
        solve_time_mean = np.mean(solve_times) * 1000  # ms
        
        print(f"   ✅ 完成 {step_count} 个时间步")
        print(f"   ✅ 推力: {thrust_mean:.3f} ± {thrust_std:.3f} N")
        print(f"   ✅ 功率: {power_mean:.3f} ± {power_std:.3f} W")
        print(f"   ✅ 平均求解时间: {solve_time_mean:.2f} ms")
        
        # 简单可视化
        if len(time_history) > 5:
            try:
                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6))
                
                # 推力时间历程
                ax1.plot(time_history, thrust_history, 'b-', linewidth=1.5)
                ax1.set_ylabel('推力 [N]')
                ax1.set_title('BEMT时域仿真结果')
                ax1.grid(True, alpha=0.3)
                
                # 功率时间历程
                ax2.plot(time_history, power_history, 'r-', linewidth=1.5)
                ax2.set_xlabel('时间 [s]')
                ax2.set_ylabel('功率 [W]')
                ax2.grid(True, alpha=0.3)
                
                plt.tight_layout()
                plt.savefig('bemt_demo_results.png', dpi=150, bbox_inches='tight')
                print("   ✅ 结果图表已保存: bemt_demo_results.png")
                
            except Exception as e:
                print(f"   ⚠️  绘图失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 时域仿真演示失败: {e}")
        return False

def demo_physical_corrections():
    """演示物理修正"""
    print("\n🔧 3. 物理修正演示")
    print("-" * 50)
    
    try:
        from physics.corrections import UnifiedPhysicalCorrections
        
        # 创建物理修正系统
        config = {
            'enable_tip_loss': True,
            'enable_hub_loss': True,
            'enable_viscous_effects': False,
            'enable_compressibility': False
        }
        
        corrections = UnifiedPhysicalCorrections(config)
        print(f"   启用的修正: {corrections.get_enabled_corrections()}")
        
        # 测试修正应用
        input_data = {
            'r': 0.35,           # 径向位置 [m]
            'R': 0.4,            # 转子半径 [m]
            'r_R': 0.35 / 0.4,   # 径向位置比
            'B': 4,              # 桨叶数
            'phi': np.radians(8), # 入流角 [rad]
            'omega': 120.0,      # 角速度 [rad/s]
            'azimuth': 0.0,      # 方位角 [rad]
            'Cl': 0.85,          # 升力系数
            'Cd': 0.025,         # 阻力系数
            'alpha': np.radians(6) # 攻角 [rad]
        }
        
        print("   原始系数:")
        print(f"     Cl = {input_data['Cl']:.3f}")
        print(f"     Cd = {input_data['Cd']:.4f}")
        
        # 应用修正
        corrected_data = corrections.apply_all(input_data)
        
        print("   修正后系数:")
        print(f"     Cl = {corrected_data['Cl']:.3f}")
        print(f"     Cd = {corrected_data['Cd']:.4f}")
        
        # 计算修正因子
        Cl_factor = corrected_data['Cl'] / input_data['Cl']
        Cd_factor = corrected_data['Cd'] / input_data['Cd']
        
        print("   修正因子:")
        print(f"     Cl修正: {Cl_factor:.3f}")
        print(f"     Cd修正: {Cd_factor:.3f}")
        
        # 获取修正统计
        stats = corrections.get_correction_statistics()
        print(f"   ✅ 总应用次数: {stats['total_applications']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 物理修正演示失败: {e}")
        return False

def demo_airfoil_database():
    """演示翼型数据库"""
    print("\n📊 4. 翼型数据库演示")
    print("-" * 50)
    
    try:
        from aerodynamics.airfoil_database import AirfoilDatabase
        
        # 创建翼型数据库
        database = AirfoilDatabase()
        
        # 获取可用翼型
        airfoils = database.get_airfoil_list()
        print(f"   可用翼型: {len(airfoils)} 个")
        print(f"   翼型列表: {airfoils[:3]}...")  # 显示前3个
        
        # 测试系数查询
        airfoil_name = 'NACA0012'
        test_cases = [
            (0.0, 100000),    # 零攻角
            (8.0, 100000),    # 中等攻角
            (15.0, 100000),   # 高攻角
            (8.0, 200000),    # 不同雷诺数
        ]
        
        print(f"   {airfoil_name} 翼型系数查询:")
        print("   攻角[°]  雷诺数    Cl      Cd      Cm")
        print("   " + "-" * 40)
        
        for alpha_deg, Re in test_cases:
            Cl, Cd, Cm = database.get_coefficients(airfoil_name, alpha_deg, Re)
            print(f"   {alpha_deg:6.1f}  {Re:8.0f}  {Cl:6.3f}  {Cd:6.4f}  {Cm:6.3f}")
        
        # 获取数据库统计
        stats = database.get_database_statistics()
        print(f"   ✅ 数据库统计:")
        print(f"     总翼型数: {stats['total_airfoils']}")
        print(f"     总数据点: {stats['total_data_points']}")
        print(f"     雷诺数范围: {stats['reynolds_range'][0]:.0f} - {stats['reynolds_range'][1]:.0f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 翼型数据库演示失败: {e}")
        return False

def demo_dynamic_stall():
    """演示动态失速模型"""
    print("\n🌪️  5. 动态失速模型演示")
    print("-" * 50)
    
    try:
        from aerodynamics.dynamic_stall import LeishmanBeddoesModel
        
        # 创建L-B动态失速模型
        config = {
            'lb_enhanced_mode': True,
            'lb_3d_correction': False,
            'lb_integration_method': 'rk4'
        }
        
        lb_model = LeishmanBeddoesModel(chord=0.08, config=config)
        print("   ✅ Leishman-Beddoes模型创建成功")
        
        # 模拟攻角阶跃响应
        alpha_static = np.radians(5.0)   # 静态攻角
        alpha_step = np.radians(15.0)    # 阶跃攻角
        V_rel = 30.0                     # 相对速度 [m/s]
        dt = 0.001                       # 时间步长 [s]
        
        # 时间历程
        t_total = 0.1  # 总时间 [s]
        n_steps = int(t_total / dt)
        
        time_history = []
        alpha_history = []
        Cl_history = []
        Cd_history = []
        
        print("   模拟攻角阶跃响应...")
        
        for i in range(n_steps):
            t = i * dt
            
            # 攻角阶跃 (t=0.02s时发生)
            if t < 0.02:
                alpha = alpha_static
            else:
                alpha = alpha_step
            
            # 计算动态失速系数
            alpha_dot = 0.0  # 简化
            Cl, Cd, Cm = lb_model.calculate_coefficients(alpha, alpha_dot, V_rel, dt, t)
            
            # 记录结果
            if i % 10 == 0:  # 每10步记录一次
                time_history.append(t)
                alpha_history.append(np.degrees(alpha))
                Cl_history.append(Cl)
                Cd_history.append(Cd)
        
        # 显示结果
        print(f"   ✅ 仿真完成: {len(time_history)} 个数据点")
        
        if len(Cl_history) > 10:
            # 静态值 (前10个点的平均)
            Cl_static = np.mean(Cl_history[:10])
            Cd_static = np.mean(Cd_history[:10])
            
            # 最终值 (后10个点的平均)
            Cl_final = np.mean(Cl_history[-10:])
            Cd_final = np.mean(Cd_history[-10:])
            
            print(f"   静态状态: Cl={Cl_static:.3f}, Cd={Cd_static:.4f}")
            print(f"   最终状态: Cl={Cl_final:.3f}, Cd={Cd_final:.4f}")
            print(f"   升力增量: ΔCl={Cl_final-Cl_static:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 动态失速演示失败: {e}")
        return False

def demo_performance_analysis():
    """演示性能分析"""
    print("\n📈 6. 性能分析演示")
    print("-" * 50)
    
    try:
        from utils.config import ConfigManager
        from core.solver_factory import SolverFactory
        
        # 测试不同配置的性能
        test_configs = [
            {
                'name': '小型转子',
                'R_rotor': 0.2, 'B': 3, 'c': 0.04,
                'omega_rotor': 200.0, 'pitch_amplitude': 8.0
            },
            {
                'name': '中型转子',
                'R_rotor': 0.4, 'B': 4, 'c': 0.08,
                'omega_rotor': 120.0, 'pitch_amplitude': 12.0
            },
            {
                'name': '大型转子',
                'R_rotor': 0.6, 'B': 6, 'c': 0.12,
                'omega_rotor': 80.0, 'pitch_amplitude': 15.0
            }
        ]
        
        print("   配置对比分析:")
        print("   配置      推力[N]  功率[W]  FM     CT      CP")
        print("   " + "-" * 50)
        
        for config_data in test_configs:
            try:
                # 创建配置
                base_config = {
                    'rho': 1.225,
                    'bemt_n_elements': 12,
                    'bemt_tolerance': 1e-3,
                    'rotor_type': 'cycloidal',
                    'enable_tip_loss': True
                }
                base_config.update(config_data)
                
                config = ConfigManager(base_config)
                
                # 创建求解器
                factory = SolverFactory()
                solver = factory.create_solver('bemt_medium', config.to_dict())
                
                # 求解
                result = solver.solve_step(0.0, 0.005)
                perf = result['performance']
                
                # 显示结果
                print(f"   {config_data['name']:8s}  "
                      f"{perf['thrust']:6.2f}  "
                      f"{perf['power']:6.1f}  "
                      f"{perf['figure_of_merit']:5.3f}  "
                      f"{perf['CT']:6.4f}  "
                      f"{perf['CP']:6.4f}")
                
            except Exception as e:
                print(f"   {config_data['name']:8s}  计算失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 性能分析演示失败: {e}")
        return False

def demo_system_validation():
    """演示系统验证"""
    print("\n✅ 7. 系统验证演示")
    print("-" * 50)
    
    try:
        # 运行系统测试
        from test_system import run_all_tests
        
        print("   运行系统测试...")
        success = run_all_tests()
        
        if success:
            print("   ✅ 系统验证通过！")
        else:
            print("   ⚠️  系统验证部分失败")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 系统验证失败: {e}")
        return False

def print_summary(results):
    """打印总结"""
    print("\n" + "=" * 70)
    print("📋 演示总结")
    print("=" * 70)
    
    demo_names = [
        "基本使用演示",
        "时域仿真演示", 
        "物理修正演示",
        "翼型数据库演示",
        "动态失速模型演示",
        "性能分析演示",
        "系统验证演示"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(demo_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name:20s} {status}")
    
    print("-" * 70)
    print(f"总体结果: {passed}/{total} 个演示通过")
    
    if passed == total:
        print("\n🎉 所有演示成功！BEMT中保真度模块运行正常。")
        print("\n📚 更多信息请参考:")
        print("   - README.md: 详细使用说明")
        print("   - VALIDATION_REPORT.md: 验证报告")
        print("   - examples/: 更多使用示例")
    else:
        print(f"\n⚠️  {total-passed} 个演示失败，请检查相关模块。")

def main():
    """主函数"""
    print_header()
    
    # 运行所有演示
    demos = [
        demo_basic_usage,
        demo_time_simulation,
        demo_physical_corrections,
        demo_airfoil_database,
        demo_dynamic_stall,
        demo_performance_analysis,
        demo_system_validation
    ]
    
    results = []
    
    for demo_func in demos:
        try:
            result = demo_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ 演示异常: {e}")
            results.append(False)
    
    # 打印总结
    print_summary(results)
    
    return 0 if all(results) else 1

if __name__ == "__main__":
    sys.exit(main())