"""
配置管理器
=========

管理BEMT求解器的配置参数，提供统一的配置接口。

核心功能：
- 配置参数验证
- 默认值管理
- 配置文件读写
- 参数类型转换
- 配置继承和覆盖

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union, List
import warnings
from copy import deepcopy


class ConfigManager:
    """
    配置管理器
    
    提供统一的配置参数管理接口。
    """
    
    def __init__(self, config: Optional[Union[Dict[str, Any], str]] = None):
        """
        初始化配置管理器
        
        Args:
            config: 配置字典或配置文件路径
        """
        # 默认配置
        self._default_config = self._get_default_config()
        
        # 当前配置
        self._config = deepcopy(self._default_config)
        
        # 加载用户配置
        if config is not None:
            if isinstance(config, str):
                self.load_from_file(config)
            elif isinstance(config, dict):
                self.update(config)
            else:
                raise ValueError("配置必须是字典或文件路径")
        
        # 验证配置
        self._validate_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 基本参数
            'R_rotor': 0.3,          # 转子半径 [m]
            'B': 4,                  # 桨叶数
            'c': 0.1,                # 弦长 [m]
            'omega_rotor': 100.0,    # 角速度 [rad/s]
            'rho': 1.225,            # 空气密度 [kg/m³]
            
            # BEMT参数
            'bemt_n_elements': 20,           # 叶素数量
            'bemt_max_iterations': 100,      # 最大迭代次数
            'bemt_tolerance': 1e-4,          # 收敛容差
            'relaxation_factor': 0.5,        # 松弛因子
            
            # 时间积分参数
            'dt': 0.001,                     # 时间步长 [s]
            'time_integration_method': 'rk4', # 时间积分方法
            'adaptive_timestep': False,       # 自适应时间步长
            'min_timestep': 1e-8,            # 最小时间步长
            'max_timestep': 0.01,            # 最大时间步长
            
            # 物理模型开关
            'enable_tip_loss': True,         # 叶尖损失修正
            'enable_hub_loss': True,         # 叶根损失修正
            'enable_viscous_effects': False, # 粘性效应
            'enable_compressibility': False, # 压缩性修正
            'enable_rotational_effects': False, # 旋转效应
            'enable_dynamic_stall': False,   # 动态失速
            
            # 动态失速参数
            'dynamic_stall_model': 'leishman_beddoes',
            'lb_enhanced_mode': True,
            'lb_3d_correction': True,
            'lb_integration_method': 'rk4',
            
            # 翼型参数
            'airfoil_name': 'NACA0012',
            'airfoil_data_dir': None,
            'enable_airfoil_database': True,
            
            # 转子类型和控制参数
            'rotor_type': 'cycloidal',       # 'cycloidal' 或 'conventional'
            
            # 循环翼转子参数
            'pitch_amplitude': 15.0,         # 俯仰幅值 [度]
            'pitch_phase_offset': 0.0,       # 相位偏移 [度]
            'pitch_bias_angle': 0.0,         # 偏置角 [度]
            
            # 传统旋翼参数
            'collective_pitch': 8.0,         # 总距 [度]
            'cyclic_pitch_lat': 0.0,         # 横向周期变距 [度]
            'cyclic_pitch_lon': 0.0,         # 纵向周期变距 [度]
            'twist_deg': -8.0,               # 扭转角 [度]
            
            # 桨叶几何参数
            'blade_taper': 1.0,              # 锥度比
            'blade_twist_deg': 0.0,          # 总扭转角 [度]
            'precone_angle': 0.0,            # 锥角 [度]
            
            # 飞行条件
            'forward_velocity': 0.0,         # 前飞速度 [m/s]
            'vehicle_weight': 10.0,          # 飞行器重量 [N]
            
            # 入流模型
            'inflow_model': 'uniform',       # 入流模型类型
            'radial_inflow_variation': 0.2,  # 径向入流变化
            'azimuthal_inflow_variation': 0.1, # 方位角入流变化
            
            # 尾迹模型
            'wake_model': 'prescribed',      # 尾迹模型类型
            'wake_age_max': 2.0,             # 最大尾迹年龄 [转]
            'wake_pitch': 0.1,               # 尾迹螺距
            'wake_contraction': 0.95,        # 尾迹收缩率
            
            # 性能监控
            'max_history_length': 1000,      # 最大历史长度
            'max_performance_history': 1000, # 最大性能历史长度
            
            # 输出控制
            'output_directory': './output',   # 输出目录
            'save_detailed_results': False,  # 保存详细结果
            'export_format': 'json',         # 导出格式
            
            # 调试和验证
            'debug_mode': False,             # 调试模式
            'validation_mode': False,        # 验证模式
            'verbose': True,                 # 详细输出
            
            # 应用类型
            'application_type': 'general',   # 'research', 'design', 'general'
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        self._config[key] = value
        
        # 重新验证配置
        try:
            self._validate_single_parameter(key, value)
        except ValueError as e:
            warnings.warn(f"配置参数 {key} 验证失败: {e}")
    
    def update(self, config_dict: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_dict: 配置字典
        """
        self._config.update(config_dict)
        self._validate_config()
    
    def has(self, key: str) -> bool:
        """
        检查是否有指定的配置键
        
        Args:
            key: 配置键
            
        Returns:
            是否存在
        """
        return key in self._config
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return deepcopy(self._config)
    
    def load_from_file(self, file_path: str):
        """
        从文件加载配置
        
        Args:
            file_path: 配置文件路径
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_ext == '.json':
                    config_data = json.load(f)
                elif file_ext in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {file_ext}")
            
            self.update(config_data)
            print(f"配置已从文件加载: {file_path}")
            
        except Exception as e:
            raise ValueError(f"加载配置文件失败: {e}")
    
    def save_to_file(self, file_path: str, format: str = 'auto'):
        """
        保存配置到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 ('json', 'yaml', 'auto')
        """
        # 确定文件格式
        if format == 'auto':
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext == '.json':
                format = 'json'
            elif file_ext in ['.yaml', '.yml']:
                format = 'yaml'
            else:
                format = 'json'  # 默认格式
                file_path += '.json'
        
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if format == 'json':
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                elif format == 'yaml':
                    yaml.dump(self._config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            
            print(f"配置已保存到文件: {file_path}")
            
        except Exception as e:
            raise ValueError(f"保存配置文件失败: {e}")
    
    def _validate_config(self):
        """验证整个配置"""
        for key, value in self._config.items():
            try:
                self._validate_single_parameter(key, value)
            except ValueError as e:
                warnings.warn(f"配置参数 {key} 验证失败: {e}")
    
    def _validate_single_parameter(self, key: str, value: Any):
        """验证单个参数"""
        # 基本参数验证
        if key in ['R_rotor', 'c', 'omega_rotor', 'rho']:
            if not isinstance(value, (int, float)) or value <= 0:
                raise ValueError(f"{key} 必须是正数")
        
        elif key == 'B':
            if not isinstance(value, int) or value < 2:
                raise ValueError("桨叶数必须是大于等于2的整数")
        
        elif key in ['bemt_n_elements', 'bemt_max_iterations']:
            if not isinstance(value, int) or value <= 0:
                raise ValueError(f"{key} 必须是正整数")
        
        elif key in ['bemt_tolerance', 'dt']:
            if not isinstance(value, (int, float)) or value <= 0:
                raise ValueError(f"{key} 必须是正数")
        
        elif key == 'relaxation_factor':
            if not isinstance(value, (int, float)) or not (0 < value <= 1):
                raise ValueError("松弛因子必须在(0, 1]范围内")
        
        elif key == 'rotor_type':
            if value not in ['cycloidal', 'conventional']:
                raise ValueError("转子类型必须是 'cycloidal' 或 'conventional'")
        
        elif key == 'time_integration_method':
            valid_methods = ['euler', 'rk4', 'ab2', 'adaptive_rk']
            if value not in valid_methods:
                raise ValueError(f"时间积分方法必须是 {valid_methods} 之一")
        
        elif key == 'dynamic_stall_model':
            valid_models = ['leishman_beddoes', 'onera']
            if value not in valid_models:
                raise ValueError(f"动态失速模型必须是 {valid_models} 之一")
        
        elif key in ['inflow_model']:
            valid_models = ['uniform', 'non_uniform', 'dynamic', 'peters_he']
            if value not in valid_models:
                raise ValueError(f"入流模型必须是 {valid_models} 之一")
        
        elif key == 'wake_model':
            valid_models = ['prescribed', 'free', 'simplified']
            if value not in valid_models:
                raise ValueError(f"尾迹模型必须是 {valid_models} 之一")
        
        elif key == 'application_type':
            valid_types = ['research', 'design', 'general']
            if value not in valid_types:
                raise ValueError(f"应用类型必须是 {valid_types} 之一")
    
    def get_parameter_info(self, key: str) -> Dict[str, Any]:
        """
        获取参数信息
        
        Args:
            key: 参数键
            
        Returns:
            参数信息
        """
        if key not in self._config:
            raise KeyError(f"参数 {key} 不存在")
        
        # 参数描述字典
        descriptions = {
            'R_rotor': {'description': '转子半径', 'unit': 'm', 'type': 'float'},
            'B': {'description': '桨叶数', 'unit': '-', 'type': 'int'},
            'c': {'description': '弦长', 'unit': 'm', 'type': 'float'},
            'omega_rotor': {'description': '角速度', 'unit': 'rad/s', 'type': 'float'},
            'rho': {'description': '空气密度', 'unit': 'kg/m³', 'type': 'float'},
            'bemt_n_elements': {'description': '叶素数量', 'unit': '-', 'type': 'int'},
            'bemt_tolerance': {'description': '收敛容差', 'unit': '-', 'type': 'float'},
            'dt': {'description': '时间步长', 'unit': 's', 'type': 'float'},
            'pitch_amplitude': {'description': '俯仰幅值', 'unit': '度', 'type': 'float'},
        }
        
        info = descriptions.get(key, {'description': '未知参数', 'unit': '-', 'type': 'unknown'})
        info['current_value'] = self._config[key]
        info['default_value'] = self._default_config.get(key, None)
        
        return info
    
    def get_all_parameters_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有参数信息"""
        return {key: self.get_parameter_info(key) for key in self._config.keys()}
    
    def reset_to_default(self, key: Optional[str] = None):
        """
        重置为默认值
        
        Args:
            key: 参数键，None表示重置所有参数
        """
        if key is None:
            self._config = deepcopy(self._default_config)
            print("所有配置已重置为默认值")
        else:
            if key in self._default_config:
                self._config[key] = self._default_config[key]
                print(f"参数 {key} 已重置为默认值")
            else:
                warnings.warn(f"参数 {key} 没有默认值")
    
    def create_config_template(self, file_path: str, include_comments: bool = True):
        """
        创建配置模板文件
        
        Args:
            file_path: 模板文件路径
            include_comments: 是否包含注释
        """
        template_config = deepcopy(self._default_config)
        
        # 保存模板（简化实现，不添加复杂注释）
        self._save_template(file_path, template_config)
        print(f"配置模板已创建: {file_path}")
    
    def _save_template(self, file_path: str, template_config: Dict[str, Any]):
        """保存配置模板"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if file_ext == '.json':
                json.dump(template_config, f, indent=2, ensure_ascii=False)
            else:  # YAML
                yaml.dump(template_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
    
    def validate_for_application(self, application_type: str) -> List[str]:
        """
        验证配置是否适合特定应用
        
        Args:
            application_type: 应用类型
            
        Returns:
            警告信息列表
        """
        warnings_list = []
        
        if application_type == 'research':
            # 研究应用需要高精度
            if self.get('bemt_tolerance') > 1e-5:
                warnings_list.append("研究应用建议使用更严格的收敛容差 (<1e-5)")
            
            if not self.get('enable_dynamic_stall'):
                warnings_list.append("研究应用建议启用动态失速模型")
        
        elif application_type == 'design':
            # 设计应用需要平衡精度和效率
            if self.get('bemt_n_elements') < 15:
                warnings_list.append("设计应用建议使用至少15个叶素")
        
        elif application_type == 'general':
            # 一般应用检查基本合理性
            if self.get('bemt_max_iterations') < 20:
                warnings_list.append("建议最大迭代次数至少为20")
        
        return warnings_list
    
    def optimize_for_application(self, application_type: str):
        """
        为特定应用优化配置
        
        Args:
            application_type: 应用类型
        """
        if application_type == 'research':
            # 研究级配置
            self.set('bemt_tolerance', 1e-5)
            self.set('bemt_n_elements', 25)
            self.set('enable_dynamic_stall', True)
            self.set('enable_tip_loss', True)
            self.set('enable_hub_loss', True)
            print("配置已优化为研究级精度")
        
        elif application_type == 'design':
            # 设计级配置
            self.set('bemt_tolerance', 1e-4)
            self.set('bemt_n_elements', 20)
            self.set('enable_tip_loss', True)
            print("配置已优化为设计级精度")
        
        elif application_type == 'fast':
            # 快速计算配置
            self.set('bemt_tolerance', 1e-3)
            self.set('bemt_n_elements', 15)
            self.set('bemt_max_iterations', 30)
            print("配置已优化为快速计算")
        
        # 更新应用类型
        self.set('application_type', application_type)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager({len(self._config)} parameters)"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"ConfigManager(config={self._config})"

def test_config_manager():
    """测试配置管理器功能"""
    print("🔧 测试配置管理器...")
    
    try:
        # 测试基本创建
        config = ConfigManager()
        print(f"   ✅ 默认配置创建成功: {len(config.to_dict())} 个参数")
        
        # 测试参数获取和设置
        R_rotor = config.get('R_rotor')
        config.set('R_rotor', 0.5)
        new_R_rotor = config.get('R_rotor')
        print(f"   ✅ 参数设置成功: R_rotor {R_rotor} -> {new_R_rotor}")
        
        # 测试配置验证
        try:
            config.set('R_rotor', -1.0)  # 应该触发警告
            print("   ⚠️  负值设置未被阻止（仅警告）")
        except Exception as e:
            print(f"   ✅ 负值设置被正确阻止: {e}")
        
        # 测试字典转换
        config_dict = config.to_dict()
        print(f"   ✅ 字典转换成功: {type(config_dict)}")
        
        # 测试从字典创建
        new_config = ConfigManager({'R_rotor': 0.4, 'B': 6})
        print(f"   ✅ 从字典创建成功: R_rotor={new_config.get('R_rotor')}, B={new_config.get('B')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_config_manager()