#!/usr/bin/env python3
"""
验证框架主运行脚本
================

执行完整的验证流程，生成详细的验证报告。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import argparse
from pathlib import Path
import importlib.util

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from validation_framework.core.test_runner import TestRunner, ValidationPhase

def load_test_modules(runner):
    """动态加载所有测试模块"""
    test_dir = Path(__file__).parent / "tests"
    
    # 设置全局运行器
    import validation_framework.core.test_runner as test_runner_module
    test_runner_module._global_runner = runner
    
    # 加载基础功能测试
    foundation_dir = test_dir / "foundation"
    if foundation_dir.exists():
        for test_file in foundation_dir.glob("test_*.py"):
            module_name = f"validation_framework.tests.foundation.{test_file.stem}"
            try:
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"✅ 加载基础测试模块: {test_file.name}")
            except Exception as e:
                print(f"❌ 加载基础测试模块失败 {test_file.name}: {e}")
    
    # 加载集成测试
    integration_dir = test_dir / "integration"
    if integration_dir.exists():
        for test_file in integration_dir.glob("test_*.py"):
            module_name = f"validation_framework.tests.integration.{test_file.stem}"
            try:
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"✅ 加载集成测试模块: {test_file.name}")
            except Exception as e:
                print(f"❌ 加载集成测试模块失败 {test_file.name}: {e}")
    
    # 加载精度测试
    accuracy_dir = test_dir / "accuracy"
    if accuracy_dir.exists():
        for test_file in accuracy_dir.glob("test_*.py"):
            module_name = f"validation_framework.tests.accuracy.{test_file.stem}"
            try:
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"✅ 加载精度测试模块: {test_file.name}")
            except Exception as e:
                print(f"❌ 加载精度测试模块失败 {test_file.name}: {e}")
    
    # 加载性能测试
    performance_dir = test_dir / "performance"
    if performance_dir.exists():
        for test_file in performance_dir.glob("test_*.py"):
            module_name = f"validation_framework.tests.performance.{test_file.stem}"
            try:
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"✅ 加载性能测试模块: {test_file.name}")
            except Exception as e:
                print(f"❌ 加载性能测试模块失败 {test_file.name}: {e}")
    
    # 加载学术验证测试
    academic_dir = test_dir / "academic"
    if academic_dir.exists():
        for test_file in academic_dir.glob("test_*.py"):
            module_name = f"validation_framework.tests.academic.{test_file.stem}"
            try:
                spec = importlib.util.spec_from_file_location(module_name, test_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"✅ 加载学术验证测试模块: {test_file.name}")
            except Exception as e:
                print(f"❌ 加载学术验证测试模块失败 {test_file.name}: {e}")

def run_specific_phase(phase_name: str, runner: TestRunner):
    """运行特定验证阶段"""
    try:
        phase = ValidationPhase(phase_name.lower())
        print(f"\n🚀 运行验证阶段: {phase.value}")
        results = runner.run_phase(phase)
        
        # 显示阶段结果
        passed = sum(1 for r in results if r.status.value == 'passed')
        total = len(results)
        print(f"📊 阶段 {phase.value} 结果: {passed}/{total} 通过")
        
        return results
        
    except ValueError:
        print(f"❌ 无效的验证阶段: {phase_name}")
        print("可用阶段: foundation, integration, accuracy, performance, academic")
        return []

def run_all_phases(runner: TestRunner, stop_on_failure: bool = False):
    """运行所有验证阶段"""
    print("\n🚀 开始完整验证流程")
    print("="*60)
    
    all_results = runner.run_all_phases(stop_on_failure)
    
    # 生成详细报告
    generate_detailed_report(all_results, runner)
    
    return all_results

def generate_detailed_report(all_results, runner: TestRunner):
    """生成详细的验证报告"""
    report_dir = Path("validation_reports")
    report_dir.mkdir(exist_ok=True)
    
    # 生成Markdown报告
    report_file = report_dir / "validation_report.md"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 🚁 Cycloidal Rotor Suite 验证报告\n\n")
        f.write(f"**生成时间**: {runner.stats.get('timestamp', 'N/A')}\n\n")
        
        # 执行摘要
        f.write("## 📋 执行摘要\n\n")
        f.write(f"- **总测试数**: {runner.stats['total_tests']}\n")
        f.write(f"- **通过**: {runner.stats['passed']} ✅\n")
        f.write(f"- **失败**: {runner.stats['failed']} ❌\n")
        f.write(f"- **错误**: {runner.stats['errors']} 💥\n")
        f.write(f"- **跳过**: {runner.stats['skipped']} ⏭️\n")
        f.write(f"- **总耗时**: {runner.stats['total_time']:.2f}s\n")
        
        success_rate = runner.stats['passed'] / max(runner.stats['total_tests'], 1) * 100
        f.write(f"- **通过率**: {success_rate:.1f}%\n\n")
        
        # 按阶段详细结果
        f.write("## 📊 分阶段结果\n\n")
        
        for phase in ValidationPhase:
            phase_results = all_results.get(phase, [])
            if not phase_results:
                continue
                
            f.write(f"### {phase.value.title()} 阶段\n\n")
            
            passed = sum(1 for r in phase_results if r.status.value == 'passed')
            failed = sum(1 for r in phase_results if r.status.value == 'failed')
            errors = sum(1 for r in phase_results if r.status.value == 'error')
            
            f.write(f"- **通过**: {passed}/{len(phase_results)}\n")
            f.write(f"- **失败**: {failed}\n")
            f.write(f"- **错误**: {errors}\n\n")
            
            # 详细测试结果
            f.write("#### 测试详情\n\n")
            f.write("| 测试名称 | 状态 | 执行时间 | 错误信息 |\n")
            f.write("|----------|------|----------|----------|\n")
            
            for result in phase_results:
                status_icon = {
                    'passed': '✅',
                    'failed': '❌',
                    'error': '💥',
                    'skipped': '⏭️'
                }.get(result.status.value, '❓')
                
                error_msg = result.error_message or '-'
                if len(error_msg) > 50:
                    error_msg = error_msg[:47] + "..."
                
                f.write(f"| {result.test_name} | {status_icon} {result.status.value} | {result.execution_time:.3f}s | {error_msg} |\n")
            
            f.write("\n")
        
        # 问题总结
        failed_tests = [r for r in runner.results if r.status.value in ['failed', 'error']]
        if failed_tests:
            f.write("## ❌ 问题总结\n\n")
            
            for result in failed_tests:
                f.write(f"### {result.test_name} ({result.phase.value})\n\n")
                f.write(f"**状态**: {result.status.value}\n\n")
                f.write(f"**错误信息**: {result.error_message}\n\n")
                
                if result.details:
                    f.write("**详细信息**:\n")
                    f.write(f"```\n{result.details}\n```\n\n")
        
        # 改进建议
        f.write("## 💡 改进建议\n\n")
        
        if success_rate >= 95:
            f.write("🎉 验证结果优秀！系统运行良好，建议：\n")
            f.write("- 继续保持代码质量\n")
            f.write("- 考虑添加更多边界条件测试\n")
            f.write("- 优化性能和内存使用\n")
        elif success_rate >= 80:
            f.write("✅ 验证结果良好，建议：\n")
            f.write("- 修复失败的测试用例\n")
            f.write("- 加强错误处理机制\n")
            f.write("- 完善文档和注释\n")
        elif success_rate >= 60:
            f.write("⚠️ 验证结果一般，需要改进：\n")
            f.write("- 优先修复关键功能问题\n")
            f.write("- 重构不稳定的代码模块\n")
            f.write("- 加强单元测试覆盖\n")
        else:
            f.write("❌ 验证结果不佳，需要重点修复：\n")
            f.write("- 立即修复阻塞性问题\n")
            f.write("- 重新审视系统架构\n")
            f.write("- 加强代码审查流程\n")
        
        f.write("\n---\n")
        f.write("*报告由 Cycloidal Rotor Suite 验证框架自动生成*\n")
    
    print(f"\n📄 详细验证报告已生成: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Cycloidal Rotor Suite 验证框架")
    parser.add_argument("--phase", type=str, help="运行特定验证阶段 (foundation/integration/accuracy/performance/academic)")
    parser.add_argument("--stop-on-failure", action="store_true", help="在失败时停止执行")
    parser.add_argument("--output-dir", type=str, default="validation_results", help="结果输出目录")
    
    args = parser.parse_args()
    
    print("🚁 Cycloidal Rotor Suite 验证框架")
    print("="*60)
    
    # 创建测试运行器
    runner = TestRunner(args.output_dir)
    
    # 加载测试模块
    print("\n📦 加载测试模块...")
    load_test_modules(runner)
    
    # 运行验证
    if args.phase:
        # 运行特定阶段
        run_specific_phase(args.phase, runner)
    else:
        # 运行所有阶段
        run_all_phases(runner, args.stop_on_failure)
    
    print(f"\n📁 验证结果保存在: {args.output_dir}")
    print("🎯 验证完成！")

if __name__ == "__main__":
    main()