#!/usr/bin/env python3
"""
全面气动声学验证脚本
==================

对整个cycloidal_rotor_suite项目进行完整的气动声学验证，包括：
1. 气动模块深度验证 (BEMT, UVLM, LiftingLine)
2. 声学模块完整验证 (FWH, BPM)
3. 耦合计算验证
4. 完整算例运行验证

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Any
import numpy as np

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ComprehensiveValidator:
    """全面验证器"""
    
    def __init__(self):
        self.test_results = []
        self.detailed_results = {}
        self.performance_metrics = {}
        
    def log_test_result(self, category: str, test_name: str, passed: bool, 
                       details: str = "", metrics: Dict = None):
        """记录测试结果"""
        result = {
            'category': category,
            'test_name': test_name,
            'passed': passed,
            'details': details,
            'metrics': metrics or {}
        }
        self.test_results.append(result)
        
        if category not in self.detailed_results:
            self.detailed_results[category] = []
        self.detailed_results[category].append(result)
        
        status = "✅" if passed else "❌"
        print(f"{status} [{category}] {test_name}")
        if details:
            print(f"   {details}")

    def test_aerodynamic_solvers_deep(self):
        """深度测试气动求解器"""
        print("=" * 80)
        print("1. 气动求解器深度验证")
        print("=" * 80)
        
        try:
            from cyclone_sim.config_loader import ConfigLoader
            from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
            
            # 创建测试配置
            base_config = {
                'n_rpm': 600.0,
                'B': 4,
                'c': 0.08,
                'R_rotor': 0.4,
                'rho': 1.225,
                'dt_sim': 0.002,
                'T_buildup': 0.1,
                'T_acoustic_record': 0.1,
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 12.0,
                'pitch_amplitude_top': 12.0,
                'pitch_amplitude_bottom': 12.0,
            }
            
            # 测试不同保真度求解器
            solver_configs = [
                ('BEMT', {'solver_fidelity': 'low', 'enable_3d_uvlm': False}),
                ('LiftingLine', {'solver_fidelity': 'medium', 'enable_3d_uvlm': False}),
                ('UVLM', {'solver_fidelity': 'high', 'enable_3d_uvlm': True}),
            ]
            
            for solver_name, solver_config in solver_configs:
                print(f"\n--- 测试 {solver_name} 求解器 ---")
                
                # 合并配置
                test_config = {**base_config, **solver_config}
                config = ConfigLoader.from_dict(test_config)
                
                try:
                    # 创建求解器
                    solver = SolverFactory.create_solver(solver_name, config)
                    self.log_test_result("AERODYNAMICS", f"{solver_name}_creation", True,
                                        f"求解器创建成功")
                    
                    # 测试求解器信息
                    if hasattr(solver, 'get_solver_info'):
                        info = solver.get_solver_info()
                        self.log_test_result("AERODYNAMICS", f"{solver_name}_info", True,
                                           f"类型: {info.get('solver_type', 'unknown')}, "
                                           f"保真度: {info.get('fidelity_level', 'unknown')}")
                    
                    # 测试单步求解
                    start_time = time.time()
                    result = solver.solve_step(0.0, 0.002)
                    solve_time = time.time() - start_time
                    
                    # 验证结果结构
                    required_keys = ['forces', 'performance', 'convergence_info']
                    missing_keys = [key for key in required_keys if key not in result]
                    
                    if not missing_keys:
                        self.log_test_result("AERODYNAMICS", f"{solver_name}_solve_step", True,
                                           f"求解时间: {solve_time:.4f}s",
                                           {'solve_time': solve_time})
                        
                        # 验证性能参数
                        perf = result['performance']
                        thrust = perf.get('thrust', 0)
                        power = perf.get('power', 0)
                        
                        if thrust > 0 and thrust < 100:  # 合理范围
                            self.log_test_result("AERODYNAMICS", f"{solver_name}_thrust", True,
                                               f"推力: {thrust:.3f} N")
                        else:
                            self.log_test_result("AERODYNAMICS", f"{solver_name}_thrust", False,
                                               f"推力异常: {thrust:.3f} N")
                        
                        # 验证收敛性
                        conv_info = result['convergence_info']
                        converged = conv_info.get('converged', False)
                        iterations = conv_info.get('iterations', 0)
                        
                        self.log_test_result("AERODYNAMICS", f"{solver_name}_convergence", converged,
                                           f"迭代次数: {iterations}, 收敛: {converged}")
                        
                        # 多步求解测试
                        print(f"   进行多步求解测试...")
                        multi_step_success = True
                        solve_times = []
                        
                        for i in range(5):
                            try:
                                step_start = time.time()
                                step_result = solver.solve_step(i * 0.002, 0.002)
                                step_time = time.time() - step_start
                                solve_times.append(step_time)
                            except Exception as e:
                                multi_step_success = False
                                break
                        
                        if multi_step_success:
                            avg_time = np.mean(solve_times)
                            self.log_test_result("AERODYNAMICS", f"{solver_name}_multi_step", True,
                                               f"5步平均时间: {avg_time:.4f}s",
                                               {'avg_solve_time': avg_time})
                        else:
                            self.log_test_result("AERODYNAMICS", f"{solver_name}_multi_step", False,
                                               "多步求解失败")
                    else:
                        self.log_test_result("AERODYNAMICS", f"{solver_name}_solve_step", False,
                                           f"缺少结果键: {missing_keys}")
                    
                except Exception as e:
                    self.log_test_result("AERODYNAMICS", f"{solver_name}_creation", False,
                                       f"求解器创建失败: {str(e)}")
                    
        except Exception as e:
            self.log_test_result("AERODYNAMICS", "general_test", False,
                               f"气动测试失败: {str(e)}")

    def test_acoustic_modules_complete(self):
        """完整测试声学模块"""
        print("\n" + "=" * 80)
        print("2. 声学模块完整验证")
        print("=" * 80)
        
        # 测试FWH求解器
        print("\n--- 测试FWH求解器 ---")
        try:
            from cyclone_sim.core.acoustics.fwh_solver import FWHSolver
            from cyclone_sim.config_loader import ConfigLoader
            
            # 创建声学配置
            acoustic_config = {
                'c0': 343.0,  # 声速
                'rho': 1.225,  # 密度
                'observer_pos': [4.0, 0.0, 0.0],  # 观察者位置
                'acoustic_model_level': 3,
                'acoustics': {
                    'include_broadband_noise': True,
                    'enable_thickness_noise': True,
                    'enable_loading_noise': True,
                }
            }
            
            config = ConfigLoader.from_dict(acoustic_config)
            
            # 创建FWH求解器
            fwh_solver = FWHSolver(config)
            self.log_test_result("ACOUSTICS", "fwh_creation", True,
                               "FWH求解器创建成功")
            
            # 测试添加源数据
            test_positions = np.array([[0.1, 0.0, 0.0], [0.2, 0.0, 0.0]])
            test_forces = np.array([[1.0, 0.0, 0.0], [1.5, 0.0, 0.0]])
            
            try:
                fwh_solver.add_source_data(0.0, test_positions, test_forces)
                fwh_solver.add_source_data(0.001, test_positions, test_forces)
                self.log_test_result("ACOUSTICS", "fwh_add_data", True,
                                   "源数据添加成功")
            except Exception as e:
                self.log_test_result("ACOUSTICS", "fwh_add_data", False,
                                   f"源数据添加失败: {str(e)}")
            
            # 测试声压计算
            try:
                if hasattr(fwh_solver, 'calculate_acoustic_pressure'):
                    t_acoustic, p_acoustic = fwh_solver.calculate_acoustic_pressure()
                    
                    if len(t_acoustic) > 0 and len(p_acoustic) > 0:
                        self.log_test_result("ACOUSTICS", "fwh_pressure_calc", True,
                                           f"声压计算成功，数据点: {len(t_acoustic)}")
                        
                        # 验证声压合理性
                        p_rms = np.sqrt(np.mean(p_acoustic**2))
                        if 1e-6 < p_rms < 1.0:  # 合理的声压范围
                            self.log_test_result("ACOUSTICS", "fwh_pressure_range", True,
                                               f"声压RMS: {p_rms:.2e} Pa")
                        else:
                            self.log_test_result("ACOUSTICS", "fwh_pressure_range", False,
                                               f"声压异常: {p_rms:.2e} Pa")
                    else:
                        self.log_test_result("ACOUSTICS", "fwh_pressure_calc", False,
                                           "声压计算返回空数据")
                else:
                    self.log_test_result("ACOUSTICS", "fwh_pressure_calc", False,
                                       "缺少声压计算方法")
                    
            except Exception as e:
                self.log_test_result("ACOUSTICS", "fwh_pressure_calc", False,
                                   f"声压计算失败: {str(e)}")
                
        except Exception as e:
            self.log_test_result("ACOUSTICS", "fwh_creation", False,
                               f"FWH求解器测试失败: {str(e)}")
        
        # 测试BPM宽带噪声模型
        print("\n--- 测试BPM宽带噪声模型 ---")
        try:
            from cyclone_sim.core.acoustics.bpm_noise import BPMNoiseModel
            
            # 创建BPM配置
            bmp_config = {
                'c0': 343.0,
                'rho': 1.225,
                'observer_pos': [4.0, 0.0, 0.0],
                'c': 0.08,  # 弦长
                'R_rotor': 0.4,  # 转子半径
                'B': 4,  # 桨叶数
                'turbulence_intensity': 0.05,
            }
            
            config = ConfigLoader.from_dict(bmp_config)
            
            # 创建BPM模型
            bpm_model = BPMNoiseModel(config)
            self.log_test_result("ACOUSTICS", "bmp_creation", True,
                               "BPM模型创建成功")
            
            # 测试宽带噪声计算
            try:
                V_rel = 30.0  # 相对速度
                alpha = np.radians(5.0)  # 攻角
                radius = 0.3  # 径向位置
                
                frequencies, psd = bmp_model.calculate_broadband_noise(V_rel, alpha, radius)
                
                if len(frequencies) > 0 and len(psd) > 0:
                    self.log_test_result("ACOUSTICS", "bmp_calculation", True,
                                       f"宽带噪声计算成功，频点: {len(frequencies)}")
                    
                    # 验证频率范围
                    f_min, f_max = frequencies[0], frequencies[-1]
                    if 10 <= f_min < f_max <= 20000:
                        self.log_test_result("ACOUSTICS", "bmp_frequency_range", True,
                                           f"频率范围: {f_min:.0f}-{f_max:.0f} Hz")
                    else:
                        self.log_test_result("ACOUSTICS", "bmp_frequency_range", False,
                                           f"频率范围异常: {f_min:.0f}-{f_max:.0f} Hz")
                    
                    # 验证PSD合理性
                    psd_max = np.max(psd)
                    if 1e-12 < psd_max < 1e-3:  # 合理的PSD范围
                        self.log_test_result("ACOUSTICS", "bmp_psd_range", True,
                                           f"最大PSD: {psd_max:.2e} Pa²/Hz")
                    else:
                        self.log_test_result("ACOUSTICS", "bmp_psd_range", False,
                                           f"PSD异常: {psd_max:.2e} Pa²/Hz")
                else:
                    self.log_test_result("ACOUSTICS", "bmp_calculation", False,
                                       "宽带噪声计算返回空数据")
                    
            except Exception as e:
                self.log_test_result("ACOUSTICS", "bmp_calculation", False,
                                   f"宽带噪声计算失败: {str(e)}")
                
        except Exception as e:
            self.log_test_result("ACOUSTICS", "bmp_creation", False,
                               f"BPM模型测试失败: {str(e)}")

    def test_aeroacoustic_coupling(self):
        """测试气动声学耦合"""
        print("\n" + "=" * 80)
        print("3. 气动声学耦合验证")
        print("=" * 80)
        
        try:
            from cyclone_sim.simulation import CycloneSimulation
            from cyclone_sim.config_loader import ConfigLoader
            
            # 创建耦合仿真配置
            coupling_config = {
                'n_rpm': 600.0,
                'B': 4,
                'c': 0.08,
                'R_rotor': 0.4,
                'rho': 1.225,
                'c0': 343.0,
                'dt_sim': 0.002,
                'T_buildup': 0.02,  # 短时间测试
                'T_acoustic_record': 0.02,
                'solver_fidelity': 'low',  # 使用快速求解器
                'enable_3d_uvlm': False,
                'run_acoustics': True,  # 启用声学计算
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 10.0,
                'pitch_amplitude_top': 10.0,
                'pitch_amplitude_bottom': 10.0,
                'observer_pos': [4.0, 0.0, 0.0],
                'enable_broadband_noise': False,  # 简化测试
            }
            
            config = ConfigLoader.from_dict(coupling_config)
            
            # 创建仿真实例
            simulation = CycloneSimulation(config, output_dir="test_coupling_output")
            self.log_test_result("COUPLING", "simulation_creation", True,
                               "耦合仿真实例创建成功")
            
            # 检查关键组件
            components = ['aero_solver', 'fwh_solver', 'wake_system']
            for component in components:
                if hasattr(simulation, component) and getattr(simulation, component) is not None:
                    self.log_test_result("COUPLING", f"{component}_exists", True,
                                       f"{component}组件存在")
                else:
                    self.log_test_result("COUPLING", f"{component}_exists", False,
                                       f"{component}组件缺失")
            
            # 测试仿真信息获取
            if hasattr(simulation, 'get_simulation_info'):
                info = simulation.get_simulation_info()
                self.log_test_result("COUPLING", "simulation_info", True,
                                   f"仿真信息获取成功")
            
        except Exception as e:
            self.log_test_result("COUPLING", "coupling_test", False,
                               f"耦合测试失败: {str(e)}")

    def test_complete_workflow_execution(self):
        """测试完整工作流程执行"""
        print("\n" + "=" * 80)
        print("4. 完整工作流程执行验证")
        print("=" * 80)
        
        # 测试纯气动工作流程
        print("\n--- 纯气动工作流程 ---")
        try:
            from cyclone_sim.simulation import CycloneSimulation
            from cyclone_sim.config_loader import ConfigLoader
            
            # 纯气动配置
            aero_config = {
                'n_rpm': 300.0,  # 降低转速加快计算
                'B': 3,
                'c': 0.06,
                'R_rotor': 0.3,
                'rho': 1.225,
                'dt_sim': 0.01,  # 增大时间步长
                'T_buildup': 0.05,  # 短时间测试
                'T_acoustic_record': 0.05,
                'solver_fidelity': 'low',
                'enable_3d_uvlm': False,
                'run_acoustics': False,  # 禁用声学
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 8.0,
                'pitch_amplitude_top': 8.0,
                'pitch_amplitude_bottom': 8.0,
            }
            
            config = ConfigLoader.from_dict(aero_config)
            simulation = CycloneSimulation(config, output_dir="test_aero_workflow")
            
            # 运行纯气动仿真
            start_time = time.time()
            try:
                # 检查是否有纯气动方法
                if hasattr(simulation, 'run_aerodynamics_only'):
                    results = simulation.run_aerodynamics_only()
                else:
                    # 使用标准方法
                    results = simulation.run_simulation()
                
                workflow_time = time.time() - start_time
                
                # 验证结果
                if isinstance(results, dict) and len(results) > 0:
                    self.log_test_result("WORKFLOW", "aero_execution", True,
                                       f"纯气动工作流程成功，耗时: {workflow_time:.2f}s",
                                       {'execution_time': workflow_time})
                    
                    # 检查关键结果
                    if 'time_history' in results:
                        time_points = len(results['time_history'])
                        self.log_test_result("WORKFLOW", "aero_time_history", True,
                                           f"时间历程数据: {time_points}个点")
                    
                    if 'force_history' in results:
                        force_points = len(results['force_history'])
                        self.log_test_result("WORKFLOW", "aero_force_history", True,
                                           f"力历程数据: {force_points}个点")
                else:
                    self.log_test_result("WORKFLOW", "aero_execution", False,
                                       "纯气动工作流程返回无效结果")
                    
            except Exception as e:
                self.log_test_result("WORKFLOW", "aero_execution", False,
                                   f"纯气动工作流程执行失败: {str(e)}")
                
        except Exception as e:
            self.log_test_result("WORKFLOW", "aero_workflow", False,
                               f"纯气动工作流程测试失败: {str(e)}")
        
        # 测试气动声学耦合工作流程
        print("\n--- 气动声学耦合工作流程 ---")
        try:
            # 耦合配置
            coupling_config = {
                'n_rpm': 300.0,
                'B': 3,
                'c': 0.06,
                'R_rotor': 0.3,
                'rho': 1.225,
                'c0': 343.0,
                'dt_sim': 0.01,
                'T_buildup': 0.03,  # 很短的测试时间
                'T_acoustic_record': 0.02,
                'solver_fidelity': 'low',
                'enable_3d_uvlm': False,
                'run_acoustics': True,  # 启用声学
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 8.0,
                'pitch_amplitude_top': 8.0,
                'pitch_amplitude_bottom': 8.0,
                'observer_pos': [3.0, 0.0, 0.0],
                'enable_broadband_noise': False,  # 简化测试
            }
            
            config = ConfigLoader.from_dict(coupling_config)
            simulation = CycloneSimulation(config, output_dir="test_coupling_workflow")
            
            # 运行耦合仿真
            start_time = time.time()
            try:
                results = simulation.run_simulation()
                workflow_time = time.time() - start_time
                
                # 验证结果
                if isinstance(results, dict) and len(results) > 0:
                    self.log_test_result("WORKFLOW", "coupling_execution", True,
                                       f"耦合工作流程成功，耗时: {workflow_time:.2f}s",
                                       {'execution_time': workflow_time})
                    
                    # 检查声学结果
                    acoustic_keys = ['time_acoustic', 'pressure_acoustic', 'frequencies', 'spectrum_db']
                    acoustic_results = [key for key in acoustic_keys if key in results]
                    
                    if acoustic_results:
                        self.log_test_result("WORKFLOW", "acoustic_results", True,
                                           f"声学结果包含: {acoustic_results}")
                        
                        # 验证OASPL
                        if 'oaspl' in results:
                            oaspl = results['oaspl']
                            if 40 < oaspl < 120:  # 合理的OASPL范围
                                self.log_test_result("WORKFLOW", "oaspl_range", True,
                                                   f"OASPL: {oaspl:.1f} dB")
                            else:
                                self.log_test_result("WORKFLOW", "oaspl_range", False,
                                                   f"OASPL异常: {oaspl:.1f} dB")
                    else:
                        self.log_test_result("WORKFLOW", "acoustic_results", False,
                                           "缺少声学结果")
                else:
                    self.log_test_result("WORKFLOW", "coupling_execution", False,
                                       "耦合工作流程返回无效结果")
                    
            except Exception as e:
                self.log_test_result("WORKFLOW", "coupling_execution", False,
                                   f"耦合工作流程执行失败: {str(e)}")
                
        except Exception as e:
            self.log_test_result("WORKFLOW", "coupling_workflow", False,
                               f"耦合工作流程测试失败: {str(e)}")

    def test_performance_benchmarks(self):
        """测试性能基准"""
        print("\n" + "=" * 80)
        print("5. 性能基准测试")
        print("=" * 80)
        
        try:
            from cyclone_sim.config_loader import ConfigLoader
            from cyclone_sim.core.aerodynamics.solver_factory import SolverFactory
            
            # 性能测试配置
            perf_config = {
                'n_rpm': 600.0,
                'B': 4,
                'c': 0.08,
                'R_rotor': 0.4,
                'rho': 1.225,
                'dt_sim': 0.002,
                'rotor_type': 'cycloidal',
                'pitch_amplitude': 12.0,
                'pitch_amplitude_top': 12.0,
                'pitch_amplitude_bottom': 12.0,
            }
            
            # 测试不同求解器的性能
            solver_types = ['BEMT', 'LiftingLine', 'UVLM']
            performance_results = {}
            
            for solver_type in solver_types:
                print(f"\n--- {solver_type} 性能测试 ---")
                
                try:
                    config = ConfigLoader.from_dict(perf_config)
                    solver = SolverFactory.create_solver(solver_type, config)
                    
                    # 单步性能测试
                    times = []
                    for i in range(10):
                        start_time = time.time()
                        result = solver.solve_step(i * 0.002, 0.002)
                        solve_time = time.time() - start_time
                        times.append(solve_time)
                    
                    avg_time = np.mean(times)
                    std_time = np.std(times)
                    
                    performance_results[solver_type] = {
                        'avg_time': avg_time,
                        'std_time': std_time,
                        'min_time': np.min(times),
                        'max_time': np.max(times)
                    }
                    
                    self.log_test_result("PERFORMANCE", f"{solver_type}_timing", True,
                                       f"平均时间: {avg_time:.4f}±{std_time:.4f}s",
                                       performance_results[solver_type])
                    
                    # 性能等级评估
                    if avg_time < 0.01:
                        grade = "优秀"
                    elif avg_time < 0.05:
                        grade = "良好"
                    elif avg_time < 0.1:
                        grade = "一般"
                    else:
                        grade = "较慢"
                    
                    self.log_test_result("PERFORMANCE", f"{solver_type}_grade", True,
                                       f"性能等级: {grade}")
                    
                except Exception as e:
                    self.log_test_result("PERFORMANCE", f"{solver_type}_timing", False,
                                       f"性能测试失败: {str(e)}")
            
            # 性能对比
            if len(performance_results) > 1:
                fastest = min(performance_results.keys(), 
                            key=lambda x: performance_results[x]['avg_time'])
                slowest = max(performance_results.keys(), 
                            key=lambda x: performance_results[x]['avg_time'])
                
                speedup = (performance_results[slowest]['avg_time'] / 
                          performance_results[fastest]['avg_time'])
                
                self.log_test_result("PERFORMANCE", "solver_comparison", True,
                                   f"最快: {fastest}, 最慢: {slowest}, 加速比: {speedup:.1f}x")
                
        except Exception as e:
            self.log_test_result("PERFORMANCE", "benchmark_test", False,
                               f"性能基准测试失败: {str(e)}")

    def run_comprehensive_validation(self):
        """运行全面验证"""
        print("🔍 开始全面气动声学验证...")
        print(f"项目路径: {project_root}")
        print()
        
        start_time = time.time()
        
        # 运行所有测试
        try:
            self.test_aerodynamic_solvers_deep()
            self.test_acoustic_modules_complete()
            self.test_aeroacoustic_coupling()
            self.test_complete_workflow_execution()
            self.test_performance_benchmarks()
        except Exception as e:
            print(f"验证过程中发生异常: {e}")
            traceback.print_exc()
        
        total_time = time.time() - start_time
        
        # 生成验证报告
        self.generate_comprehensive_report(total_time)

    def generate_comprehensive_report(self, total_time: float):
        """生成全面验证报告"""
        print("\n" + "=" * 100)
        print("🎯 全面气动声学验证报告")
        print("=" * 100)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"验证时间: {total_time:.2f}s")
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        # 按类别统计
        print(f"\n按类别统计:")
        for category, results in self.detailed_results.items():
            category_passed = sum(1 for r in results if r['passed'])
            category_total = len(results)
            print(f"  {category}: {category_passed}/{category_total} "
                  f"({category_passed/category_total*100:.1f}%)")
        
        # 详细失败列表
        failed_results = [r for r in self.test_results if not r['passed']]
        if failed_results:
            print(f"\n❌ 失败测试详情:")
            for i, result in enumerate(failed_results, 1):
                print(f"  {i}. [{result['category']}] {result['test_name']}")
                if result['details']:
                    print(f"     {result['details']}")
        
        # 性能摘要
        performance_tests = [r for r in self.test_results 
                           if r['category'] == 'PERFORMANCE' and r['metrics']]
        if performance_tests:
            print(f"\n⚡ 性能摘要:")
            for result in performance_tests:
                if 'avg_time' in result['metrics']:
                    avg_time = result['metrics']['avg_time']
                    print(f"  {result['test_name']}: {avg_time:.4f}s")
        
        # 总体评估
        print(f"\n🎯 总体评估:")
        if failed_tests == 0:
            print("🎉 优秀！所有测试通过，系统完全可用。")
            grade = "A+"
        elif failed_tests <= 2:
            print("✅ 良好！大部分功能正常，少数问题需要修复。")
            grade = "B+"
        elif failed_tests <= 5:
            print("⚠️  一般！核心功能可用，但存在一些问题。")
            grade = "C+"
        else:
            print("❌ 较差！存在多个严重问题，需要大量修复工作。")
            grade = "D"
        
        print(f"系统评级: {grade}")
        
        # 建议
        print(f"\n💡 建议:")
        if failed_tests == 0:
            print("  - 系统已准备好投入生产使用")
            print("  - 可以开始进行实际项目应用")
            print("  - 建议定期运行验证以确保系统稳定性")
        else:
            print("  - 优先修复失败的测试项目")
            print("  - 重点关注核心功能模块")
            print("  - 修复后重新运行验证")
        
        # 保存报告
        self.save_validation_report(total_time, grade)

    def save_validation_report(self, total_time: float, grade: str):
        """保存验证报告"""
        report_file = project_root / "COMPREHENSIVE_VALIDATION_REPORT.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 全面气动声学验证报告\n\n")
            f.write(f"**生成时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**验证时长**: {total_time:.2f}秒\n")
            f.write(f"**系统评级**: {grade}\n\n")
            
            # 测试统计
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result['passed'])
            
            f.write("## 测试统计\n\n")
            f.write(f"- 总测试数: {total_tests}\n")
            f.write(f"- 通过测试: {passed_tests}\n")
            f.write(f"- 失败测试: {total_tests - passed_tests}\n")
            f.write(f"- 通过率: {passed_tests/total_tests*100:.1f}%\n\n")
            
            # 详细结果
            f.write("## 详细测试结果\n\n")
            for category, results in self.detailed_results.items():
                f.write(f"### {category}\n\n")
                for result in results:
                    status = "✅" if result['passed'] else "❌"
                    f.write(f"- {status} **{result['test_name']}**")
                    if result['details']:
                        f.write(f": {result['details']}")
                    f.write("\n")
                f.write("\n")
        
        print(f"\n📄 详细报告已保存: {report_file}")


def main():
    """主函数"""
    validator = ComprehensiveValidator()
    validator.run_comprehensive_validation()


if __name__ == "__main__":
    main()