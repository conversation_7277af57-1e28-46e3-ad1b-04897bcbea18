#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Non-Uniform Inflow Models - Medium Fidelity
非均匀流入模型 - 中保真度

This module implements non-uniform inflow models for medium-fidelity BEMT analysis:
- Linear inflow variation
- Cosine inflow distribution
- Forward flight inflow asymmetry
- Ground effect modeling

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings

class NonUniformInflow:
    """
    非均匀流入模型
    
    模拟旋翼盘面上的非均匀诱导速度分布：
    - 径向变化
    - 方位角变化
    - 前飞不对称性
    - 地面效应
    """
    
    def __init__(self, 
                 rotor_geometry,
                 inflow_settings: Optional[Dict] = None,
                 **kwargs):
        """
        初始化非均匀流入模型
        
        Parameters:
        -----------
        rotor_geometry : object
            旋翼几何对象
        inflow_settings : dict, optional
            流入模型设置
        **kwargs : dict
            额外参数
        """
        
        self.geometry = rotor_geometry
        self.settings = inflow_settings or self._get_default_settings()
        
        # 几何参数
        self.radius = getattr(rotor_geometry, 'radius', 1.0)
        self.num_blades = getattr(rotor_geometry, 'num_blades', 4)
        
        # 模型参数
        self.inflow_model = self.settings.get('inflow_model', 'linear')
        self.enable_azimuthal_variation = self.settings.get('enable_azimuthal_variation', True)
        self.enable_ground_effect = self.settings.get('enable_ground_effect', False)
        
        print(f"非均匀流入模型初始化完成")
        print(f"  流入模型: {self.inflow_model}")
        print(f"  方位角变化: {self.enable_azimuthal_variation}")
        print(f"  地面效应: {self.enable_ground_effect}")
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            'inflow_model': 'linear',  # 'uniform', 'linear', 'cosine'
            'enable_azimuthal_variation': True,
            'enable_ground_effect': False,
            'ground_height': 10.0,  # 离地高度 [m]
            'inflow_ratio_tip': 0.8,  # 叶尖流入比
            'inflow_ratio_root': 1.2,  # 根部流入比
            'forward_flight_factor': 0.1  # 前飞不对称因子
        }
    
    def compute_induced_velocity(self, 
                               solution_vars: Dict[str, np.ndarray],
                               flight_condition,
                               r_stations: np.ndarray,
                               azimuth: float = 0.0) -> np.ndarray:
        """
        计算非均匀诱导速度
        
        Parameters:
        -----------
        solution_vars : dict
            求解变量
        flight_condition : object
            飞行条件
        r_stations : ndarray
            径向站位
        azimuth : float
            方位角 [rad]
            
        Returns:
        --------
        lambda_i_nonuniform : ndarray
            非均匀诱导速度比
        """
        
        # 基础诱导速度
        lambda_i_base = solution_vars['lambda_i'].copy()
        
        # 径向变化
        radial_factor = self._compute_radial_variation(r_stations)
        
        # 方位角变化
        if self.enable_azimuthal_variation:
            azimuthal_factor = self._compute_azimuthal_variation(
                r_stations, azimuth, flight_condition
            )
        else:
            azimuthal_factor = np.ones_like(r_stations)
        
        # 地面效应
        if self.enable_ground_effect:
            ground_factor = self._compute_ground_effect(r_stations, flight_condition)
        else:
            ground_factor = np.ones_like(r_stations)
        
        # 合成非均匀流入
        lambda_i_nonuniform = lambda_i_base * radial_factor * azimuthal_factor * ground_factor
        
        return lambda_i_nonuniform
    
    def _compute_radial_variation(self, r_stations: np.ndarray) -> np.ndarray:
        """计算径向变化"""
        
        # 无量纲径向位置
        r_R = r_stations / self.radius
        
        if self.inflow_model == 'uniform':
            # 均匀分布
            return np.ones_like(r_R)
        
        elif self.inflow_model == 'linear':
            # 线性分布
            tip_ratio = self.settings.get('inflow_ratio_tip', 0.8)
            root_ratio = self.settings.get('inflow_ratio_root', 1.2)
            
            return root_ratio + (tip_ratio - root_ratio) * r_R
        
        elif self.inflow_model == 'cosine':
            # 余弦分布
            return 1.0 + 0.2 * np.cos(np.pi * r_R)
        
        else:
            return np.ones_like(r_R)
    
    def _compute_azimuthal_variation(self, 
                                   r_stations: np.ndarray,
                                   azimuth: float,
                                   flight_condition) -> np.ndarray:
        """计算方位角变化"""
        
        # 前飞速度影响
        V_inf = flight_condition.V_inf_magnitude
        omega_R = flight_condition.omega * self.radius
        
        if omega_R > 0:
            advance_ratio = V_inf / omega_R
        else:
            advance_ratio = 0.0
        
        # 前飞不对称性
        if advance_ratio > 0.01:
            forward_factor = self.settings.get('forward_flight_factor', 0.1)
            asymmetry = 1.0 + forward_factor * advance_ratio * np.cos(azimuth)
        else:
            asymmetry = 1.0
        
        return np.full_like(r_stations, asymmetry)
    
    def _compute_ground_effect(self, 
                             r_stations: np.ndarray,
                             flight_condition) -> np.ndarray:
        """计算地面效应"""
        
        ground_height = self.settings.get('ground_height', 10.0)
        
        # 简化的地面效应模型
        if ground_height < 2 * self.radius:
            # 地面效应显著
            height_ratio = ground_height / self.radius
            ground_factor = 1.0 - 0.2 * np.exp(-height_ratio)
        else:
            # 地面效应可忽略
            ground_factor = 1.0
        
        return np.full_like(r_stations, ground_factor)
    
    def get_inflow_distribution(self, 
                              solution_vars: Dict[str, np.ndarray],
                              flight_condition,
                              r_stations: np.ndarray,
                              n_azimuth: int = 36) -> Dict[str, np.ndarray]:
        """
        获取完整的流入分布
        
        Parameters:
        -----------
        solution_vars : dict
            求解变量
        flight_condition : object
            飞行条件
        r_stations : ndarray
            径向站位
        n_azimuth : int
            方位角分辨率
            
        Returns:
        --------
        inflow_distribution : dict
            流入分布数据
        """
        
        azimuth_angles = np.linspace(0, 2*np.pi, n_azimuth, endpoint=False)
        
        # 计算每个方位角的流入分布
        inflow_matrix = np.zeros((len(r_stations), n_azimuth))
        
        for i, azimuth in enumerate(azimuth_angles):
            lambda_i = self.compute_induced_velocity(
                solution_vars, flight_condition, r_stations, azimuth
            )
            inflow_matrix[:, i] = lambda_i
        
        return {
            'r_stations': r_stations,
            'azimuth_angles': np.degrees(azimuth_angles),
            'lambda_i_matrix': inflow_matrix,
            'lambda_i_mean': np.mean(inflow_matrix, axis=1),
            'lambda_i_std': np.std(inflow_matrix, axis=1)
        }
    
    def update_settings(self, new_settings: Dict[str, Any]):
        """更新模型设置"""
        self.settings.update(new_settings)
        
        # 更新模型参数
        self.inflow_model = self.settings.get('inflow_model', 'linear')
        self.enable_azimuthal_variation = self.settings.get('enable_azimuthal_variation', True)
        self.enable_ground_effect = self.settings.get('enable_ground_effect', False)
        
        print(f"非均匀流入模型设置已更新")

class SimpleInflowModel:
    """简化的流入模型（用作回退）"""
    
    def __init__(self, rotor_geometry, **kwargs):
        self.geometry = rotor_geometry
        self.radius = getattr(rotor_geometry, 'radius', 1.0)
    
    def compute_induced_velocity(self, solution_vars, flight_condition, r_stations, azimuth=0.0):
        """简化的诱导速度计算"""
        return solution_vars['lambda_i'].copy()
    
    def get_inflow_distribution(self, solution_vars, flight_condition, r_stations, n_azimuth=36):
        """简化的流入分布"""
        lambda_i = solution_vars['lambda_i']
        azimuth_angles = np.linspace(0, 360, n_azimuth, endpoint=False)
        
        return {
            'r_stations': r_stations,
            'azimuth_angles': azimuth_angles,
            'lambda_i_matrix': np.tile(lambda_i[:, np.newaxis], (1, n_azimuth)),
            'lambda_i_mean': lambda_i,
            'lambda_i_std': np.zeros_like(lambda_i)
        }

# 工厂函数
def create_non_uniform_inflow(rotor_geometry, 
                            inflow_settings: Optional[Dict] = None,
                            **kwargs) -> NonUniformInflow:
    """创建非均匀流入模型的工厂函数"""
    return NonUniformInflow(rotor_geometry, inflow_settings, **kwargs)

def create_simple_inflow_model(rotor_geometry, **kwargs) -> SimpleInflowModel:
    """创建简化流入模型的工厂函数"""
    return SimpleInflowModel(rotor_geometry, **kwargs)

# 预定义设置
LINEAR_INFLOW_SETTINGS = {
    'inflow_model': 'linear',
    'inflow_ratio_tip': 0.8,
    'inflow_ratio_root': 1.2,
    'enable_azimuthal_variation': True,
    'forward_flight_factor': 0.1
}

COSINE_INFLOW_SETTINGS = {
    'inflow_model': 'cosine',
    'enable_azimuthal_variation': True,
    'forward_flight_factor': 0.15
}

GROUND_EFFECT_SETTINGS = {
    'inflow_model': 'linear',
    'enable_ground_effect': True,
    'ground_height': 5.0,
    'enable_azimuthal_variation': True
}
