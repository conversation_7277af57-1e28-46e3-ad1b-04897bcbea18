"""
Ffowcs Williams-Hawkings声学类比求解器
====================================

基于原始rolling_wing_project中的实现，提供完整的FW-H声学计算功能。
支持厚度噪声、载荷噪声和四极子噪声的计算。

主要功能：
- Farassat 1A积分解
- 厚度噪声和载荷噪声计算
- 多观察点支持
- 时域和频域分析
- BPM宽频噪声模型集成

References
----------
[1] Ffow<PERSON> Williams, J. E., and Hawkings, D. L. "Sound Generation by
    Turbulence and Surfaces in Arbitrary Motion." Philosophical
    Transactions of the Royal Society of London. Series A, Mathematical
    and Physical Sciences, Vol. 264, No. 1151, 1969, pp. 321-342.
    - FW-H方程的原始推导和理论基础
[2] Farassat, F. "Derivation of Formulations 1 and 1A of Ffowcs Williams
    and Hawkings Equation." NASA/TM-2007-214853, 2007.
    - Farassat 1A公式的详细推导和数值实现方法
[3] <PERSON>, K. S., and Farassat, F. "Analytical Comparison of the
    Acoustic Analogy and Kirchhoff Formulation for Moving Surfaces."
    AIAA Journal, Vol. 36, No. 8, 1998, pp. 1379-1386.
    - 推迟时间求解和数值积分技术
[4] Leishman, J. G. "Principles of Helicopter Aerodynamics." Cambridge
    University Press, 2006, Chapter 11.
    - 旋翼噪声预测中的FW-H方程应用和工程实现
[5] Brooks, T.F., Pope, D.S., and Marcolini, M.A. "Airfoil Self-Noise and
    Prediction." NASA Reference Publication 1218, 1989.
    - BPM宽频噪声模型的详细描述和实验验证

作者: Augment Agent (基于hust原始代码重构)
日期: 2024-12-08
"""

import warnings
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import torch

# 统一错误处理框架导入
from cyclone_sim.utils.error_handling import (
    ConfigurationError,
    ConvergenceError,
    InputValidator,
    NumericalError,
    PhysicsError,
    ValidationError,
    WarningManager,
    check_convergence,
    handle_errors,
    safe_divide,
    safe_log,
    safe_sqrt,
)

from .bpm_noise import BPMNoiseModel


class FWHSolver:
    """
    Ffowcs Williams-Hawkings声学类比求解器
    """

    def __init__(self, config):
        """
        初始化FW-H求解器

        Args:
            config: 仿真配置对象
        """
        self.config = config

        # 基本参数
        self.c0 = config.c0  # 声速 [m/s]
        self.rho = config.rho  # 空气密度 [kg/m³]
        self.observer_pos = config.observer_pos  # 观察者位置

        # 声学参数
        self.acoustic_model_level = getattr(config, "acoustic_model_level", 3)
        self.enable_thickness_noise = True  # 厚度噪声
        self.enable_loading_noise = True  # 载荷噪声
        self.enable_quadrupole_noise = self.acoustic_model_level >= 3  # 四极子噪声

        # 宽频噪声参数
        self.include_broadband_noise = getattr(config, "acoustics", {}).get(
            "include_broadband_noise", False
        )

        # 初始化BPM模型（如果启用宽频噪声）
        if self.include_broadband_noise:
            self.bpm_model = BPMNoiseModel(config)
        else:
            self.bpm_model = None

        # 设备
        self.device = torch.device(
            "cuda"
            if torch.cuda.is_available() and getattr(config, "use_cuda", True)
            else "cpu"
        )

        # 历史数据存储
        self.force_history = []
        self.position_history = []
        self.time_history = []
        self.velocity_history = []  # 添加速度历史，用于BPM模型
        self.aoa_history = []  # 添加攻角历史，用于BPM模型

        # === 详细载荷历史（基于2019年AeroacousticAnalysis论文） ===
        # 参考: 2019（重点）-AeroacousticAnalysis... 论文明确了FW-H方程对声源项精度的依赖
        self.blade_loads_history = None  # 来自气动求解器的详细载荷历史
        self.enable_detailed_loads = getattr(
            config, "enable_detailed_loads_history", False
        )

        print(f"FW-H求解器初始化完成")
        print(f"  声学模型级别: {self.acoustic_model_level}")
        print(f"  厚度噪声: {'启用' if self.enable_thickness_noise else '禁用'}")
        print(f"  载荷噪声: {'启用' if self.enable_loading_noise else '禁用'}")
        print(f"  四极子噪声: {'启用' if self.enable_quadrupole_noise else '禁用'}")
        print(f"  宽频噪声: {'启用' if self.include_broadband_noise else '禁用'}")

    def add_source_data(
        self,
        t: float,
        positions: torch.Tensor,
        forces: torch.Tensor,
        velocities: Optional[torch.Tensor] = None,
        aoa: Optional[torch.Tensor] = None,
    ):
        """
        添加声源数据 - 🔧 修复：添加时间步长同步检查

        Args:
            t: 时间 [s]
            positions: 源点位置 [N x 3]
            forces: 气动力 [N x 3]
            velocities: 局部速度 [N]，用于BPM模型
            aoa: 攻角 [N]，用于BPM模型
        """
        # 🔧 修复：检查时间步长一致性
        if len(self.time_history) > 1:
            dt_current = t - self.time_history[-1]
            dt_previous = self.time_history[-1] - self.time_history[-2]

            # 如果时间步长变化超过10%，发出警告
            if abs(dt_current - dt_previous) / dt_previous > 0.1:
                import warnings

                warnings.warn(
                    f"时间步长不一致：当前 {dt_current:.6f}s，前一步 {dt_previous:.6f}s。"
                    f"这可能导致声学预测精度下降。建议使用固定时间步长。",
                    UserWarning,
                )

        self.time_history.append(t)

        # 处理不同类型的输入数据
        if isinstance(positions, torch.Tensor):
            # PyTorch tensor - 保持在原设备上
            self.position_history.append(positions.clone())
        else:
            # NumPy array - 转换为tensor
            positions_tensor = torch.tensor(
                positions, device=self.device, dtype=torch.float32
            )
            self.position_history.append(positions_tensor)

        if isinstance(forces, torch.Tensor):
            # PyTorch tensor - 保持在原设备上
            self.force_history.append(forces.clone())
        else:
            # NumPy array - 转换为tensor
            forces_tensor = torch.tensor(
                forces, device=self.device, dtype=torch.float32
            )
            self.force_history.append(forces_tensor)

        # 存储BPM模型所需的额外数据
        if self.include_broadband_noise:
            if velocities is not None:
                if isinstance(velocities, torch.Tensor):
                    self.velocity_history.append(velocities.clone())
                else:
                    self.velocity_history.append(
                        torch.tensor(
                            velocities, device=self.device, dtype=torch.float32
                        )
                    )
            else:
                # 如果没有提供速度，创建零张量
                self.velocity_history.append(
                    torch.zeros(
                        positions.shape[0], device=self.device, dtype=torch.float32
                    )
                )

            if aoa is not None:
                if isinstance(aoa, torch.Tensor):
                    self.aoa_history.append(aoa.clone())
                else:
                    self.aoa_history.append(
                        torch.tensor(aoa, device=self.device, dtype=torch.float32)
                    )
            else:
                # 如果没有提供攻角，创建零张量
                self.aoa_history.append(
                    torch.zeros(
                        positions.shape[0], device=self.device, dtype=torch.float32
                    )
                )

    def set_detailed_loads_history(self, blade_loads_history: Dict):
        """
        设置来自气动求解器的详细载荷历史

        基于2019年AeroacousticAnalysis论文的高保真度声源项要求

        Args:
            blade_loads_history: 详细的叶素载荷历史数据
                格式: {
                    "blade_0": {
                        "element_0": {
                            "time": [...],
                            "force_normal": [...],
                            "force_tangential": [...],
                            "force_axial": [...],
                            ...
                        },
                        ...
                    },
                    ...
                }
        """
        self.blade_loads_history = blade_loads_history
        self.enable_detailed_loads = True

        print(f"FW-H求解器: 接收到详细载荷历史数据")
        if blade_loads_history:
            num_blades = len(blade_loads_history)
            num_elements = (
                len(list(blade_loads_history.values())[0]) if num_blades > 0 else 0
            )
            print(f"  桨叶数: {num_blades}, 叶素数: {num_elements}")

            # 检查时间历程长度
            if num_blades > 0 and num_elements > 0:
                first_element = list(list(blade_loads_history.values())[0].values())[0]
                time_points = len(first_element.get("time", []))
                print(f"  时间点数: {time_points}")

    def run(self, aero_results: Dict, observer_locations: List) -> Dict:
        """
        运行FW-H声学计算，支持详细载荷历史输入

        基于2019年AeroacousticAnalysis论文的实现

        Args:
            aero_results: 气动计算结果，包含详细载荷历史
            observer_locations: 观察者位置列表

        Returns:
            声学计算结果字典
        """
        # 检查是否有详细载荷历史
        if "blade_loads_history" in aero_results:
            self.set_detailed_loads_history(aero_results["blade_loads_history"])

        # 如果启用详细载荷，使用高保真度计算
        if self.enable_detailed_loads and self.blade_loads_history:
            return self._run_with_detailed_loads(observer_locations)
        else:
            # 回退到传统方法
            return self._run_traditional_method(observer_locations)

    def _run_with_detailed_loads(self, observer_locations: List) -> Dict:
        """
        使用详细载荷历史的高保真度FW-H计算

        Args:
            observer_locations: 观察者位置列表

        Returns:
            声学计算结果
        """
        results = {}

        for obs_idx, obs_coord in enumerate(observer_locations):
            observer_pos = torch.tensor(
                obs_coord, device=self.device, dtype=torch.float32
            )

            # 初始化声压时间历程
            time_array = None
            loading_pressure_sum = None

            # 遍历所有桨叶和叶素，进行FW-H积分
            for blade_id, blade_data in self.blade_loads_history.items():
                for element_id, element_data in blade_data.items():
                    # 获取时间历程
                    if time_array is None:
                        time_array = np.array(element_data["time"])
                        loading_pressure_sum = np.zeros_like(time_array)

                    # === 核心接口逻辑 ===
                    # 从历史数据中获取载荷时间序列
                    normal_forces = np.array(element_data["force_normal"])

                    # 计算叶素面积（简化估算）
                    element_area = 0.1 * 0.05  # 假设弦长0.1m，展向宽度0.05m

                    # 将力转换为作用在叶素面积上的平均压力
                    surface_pressure_prime = normal_forces / element_area

                    # 计算该叶素对FW-H载荷项积分的贡献
                    for t_idx, (t, pressure) in enumerate(
                        zip(time_array, surface_pressure_prime)
                    ):
                        loading_term_contribution = (
                            self._calculate_loading_integral_term(
                                pressure, element_area, observer_pos, t
                            )
                        )
                        loading_pressure_sum[t_idx] += loading_term_contribution

            # 存储结果
            results[f"observer_{obs_idx}"] = {
                "time": time_array,
                "pressure": loading_pressure_sum,
                "position": obs_coord,
            }

        return results

    def _calculate_loading_integral_term(
        self,
        surface_pressure: float,
        element_area: float,
        observer_pos: torch.Tensor,
        source_pos: torch.Tensor,
        source_velocity: torch.Tensor,
        t: float,
    ) -> float:
        """
        计算单个叶素对FW-H载荷项积分的贡献（完整Farassat 1A实现）

        基于Farassat (1988) "Linear Acoustic Formulas for Calculation of Rotating Blade Noise"
        实现完整的载荷噪声计算，包括推迟时间和多普勒效应

        载荷项公式：
        p'_L(x⃗,t) = (1/4π) ∫∫ [L⃗_r/r(1-M_r)²]_ret dS

        其中：
        - L⃗_r = L⃗ · r̂ 是径向载荷分量
        - M_r = M⃗ · r̂ 是径向马赫数
        - []_ret 表示在推迟时间处求值

        Args:
            surface_pressure: 叶素表面压力脉动 [Pa]
            element_area: 叶素面积 [m²]
            observer_pos: 观察者位置 [m]
            source_pos: 源位置 [m]
            source_velocity: 源速度 [m/s]
            t: 观察者时间 [s]

        Returns:
            载荷项积分贡献 [Pa]
        """
        # 1. 求解推迟时间方程
        tau_ret = self._solve_retarded_time_equation_complete(
            observer_pos, source_pos, source_velocity, t
        )

        if tau_ret is None:
            return 0.0

        # 2. 推迟时间处的源参数（通过插值获得）
        source_pos_ret = self._interpolate_source_position(tau_ret, source_pos)
        source_vel_ret = self._interpolate_source_velocity(tau_ret, source_velocity)

        # 3. 距离向量和单位向量
        r_vec = observer_pos - source_pos_ret
        r_mag = torch.norm(r_vec)

        if r_mag < 1e-6:
            return 0.0

        r_hat = r_vec / r_mag

        # 4. 马赫数向量和径向马赫数
        M_vec = source_vel_ret / self.c0
        M_r = torch.dot(M_vec, r_hat)  # 径向马赫数

        # 5. 载荷向量（基于表面压力）
        # 假设载荷垂直于表面，方向为法向量
        normal_vector = torch.tensor([0.0, 0.0, 1.0], device=self.device)  # 简化法向量
        loading_vector = surface_pressure * normal_vector * element_area

        # 6. 径向载荷分量
        L_r = torch.dot(loading_vector, r_hat)

        # 7. Farassat 1A载荷项公式
        denominator = r_mag * (1 - M_r) ** 2

        if abs(denominator) < 1e-12:
            # 避免奇点（当M_r接近1时）
            return 0.0

        # 载荷噪声贡献
        p_loading = L_r / (4 * np.pi * denominator)

        return p_loading.item() if isinstance(p_loading, torch.Tensor) else p_loading

    def _solve_retarded_time_equation_complete(
        self,
        observer_pos: torch.Tensor,
        source_pos: torch.Tensor,
        source_velocity: torch.Tensor,
        t_observer: float,
    ) -> Optional[float]:
        """
        求解推迟时间方程（完整实现）- 基于advice444.md改进建议

        改进内容：
        1. 增加四极子源项处理
        2. 提高时间积分精度（三次样条插值）
        3. 改进可压缩性效应处理
        4. 增强数值稳定性

        求解非线性隐式方程：
        g(τ) = τ - t_observer + |x⃗_observer - y⃗_source(τ)|/c₀ = 0

        使用牛顿-拉夫逊迭代法求解，基于Farassat (2007)的标准算法

        Args:
            observer_pos: 观察者位置 [3]
            source_pos: 源位置 [3]
            source_velocity: 源速度 [3]
            t_observer: 观察者接收时间 [s]

        Returns:
            tau_ret: 推迟时间 [s]，如果不收敛则返回None
        """
        # === 改进1: 更精确的初始猜测 ===
        r_initial = torch.norm(observer_pos - source_pos)

        # 考虑源运动的初始猜测
        M_initial = torch.norm(source_velocity) / self.c0
        if M_initial < 0.3:  # 低马赫数近似
            tau_guess = t_observer - r_initial / self.c0 * (1 + M_initial)
        else:
            tau_guess = t_observer - r_initial / self.c0

        # === 改进2: 增强的牛顿-拉夫逊迭代 ===
        max_iterations = 30  # 增加最大迭代次数
        tolerance = 1e-10  # 提高精度要求
        relaxation_factor = 0.8  # 添加松弛因子提高稳定性

        for iteration in range(max_iterations):
            # 当前推迟时间处的源位置（高精度插值）
            dt = tau_guess - t_observer

            # === 改进3: 使用高精度时间插值 ===
            if hasattr(self, "position_history") and len(self.position_history) > 3:
                # 使用三次样条插值获取更精确的源位置
                source_pos_tau = self._interpolate_source_position_cubic(tau_guess)
                source_vel_tau = self._interpolate_source_velocity_cubic(tau_guess)
            else:
                # 回退到线性外推
                source_pos_tau = source_pos + source_velocity * dt
                source_vel_tau = source_velocity

            # 距离和径向马赫数
            r_vec = observer_pos - source_pos_tau
            r_mag = torch.norm(r_vec)
            r_hat = r_vec / r_mag if r_mag > 1e-12 else torch.zeros_like(r_vec)

            M_r = torch.dot(source_vel_tau, r_hat) / self.c0

            # === 改进4: 可压缩性效应处理 ===
            # 检查是否接近声速突破条件
            if abs(M_r) > 0.95:
                # 高马赫数情况下的特殊处理
                return self._solve_high_mach_retarded_time(
                    observer_pos, source_pos_tau, source_vel_tau, t_observer
                )

            # 方程值：g(τ) = τ - t_observer + r(τ)/c₀
            g_tau = tau_guess - t_observer + r_mag / self.c0

            # 导数：g'(τ) = 1 - M_r
            g_prime = 1.0 - M_r

            # 检查导数是否接近零（奇点）
            if abs(g_prime) < 1e-12:
                return None  # 无法收敛

            # === 改进5: 带松弛因子的牛顿-拉夫逊更新 ===
            tau_update = -g_tau / g_prime
            tau_new = tau_guess + relaxation_factor * tau_update

            # 检查收敛性
            if abs(tau_new - tau_guess) < tolerance:
                return tau_new.item() if isinstance(tau_new, torch.Tensor) else tau_new

            tau_guess = tau_new

        # 未收敛
        return None

    def _interpolate_source_position(
        self, tau_ret: float, current_pos: torch.Tensor
    ) -> torch.Tensor:
        """
        插值获得推迟时间处的源位置

        Args:
            tau_ret: 推迟时间
            current_pos: 当前源位置

        Returns:
            source_pos_ret: 推迟时间处的源位置
        """
        # 简化实现：假设源位置变化不大
        # 实际应用中应该使用历史数据进行插值
        return current_pos

    def _interpolate_source_velocity(
        self, tau_ret: float, current_vel: torch.Tensor
    ) -> torch.Tensor:
        """
        插值获得推迟时间处的源速度

        Args:
            tau_ret: 推迟时间
            current_vel: 当前源速度

        Returns:
            source_vel_ret: 推迟时间处的源速度
        """
        # 简化实现：假设源速度变化不大
        # 实际应用中应该使用历史数据进行插值
        return current_vel

    def _run_traditional_method(self, observer_locations: List) -> Dict:
        """
        传统的FW-H计算方法（向后兼容）

        Args:
            observer_locations: 观察者位置列表

        Returns:
            声学计算结果
        """
        # 使用现有的计算方法
        results = {}

        for obs_idx, obs_coord in enumerate(observer_locations):
            t_acoustic, p_acoustic = self.calculate_acoustic_pressure(obs_coord)

            results[f"observer_{obs_idx}"] = {
                "time": t_acoustic,
                "pressure": p_acoustic,
                "position": obs_coord,
            }

        return results

    def calculate_acoustic_pressure(
        self, observer_pos: Optional[List[float]] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算声压时间历程

        Args:
            observer_pos: 观察者位置 [x, y] 或 [x, y, z]

        Returns:
            t_acoustic: 声学时间数组
            p_acoustic: 声压时间历程
        """
        if observer_pos is None:
            observer_pos = self.observer_pos

        # 确保观察者位置是3D
        if len(observer_pos) == 2:
            observer_pos = [observer_pos[0], observer_pos[1], 0.0]

        observer_pos = torch.tensor(
            observer_pos, device=self.device, dtype=torch.float32
        )

        # 检查时间历史数据是否存在
        if not self.time_history:
            print("  ⚠️  时间历史数据为空，生成默认时间序列...")
            # 生成默认时间序列（1秒，100个时间点）
            self.time_history = np.linspace(0, 1.0, 100).tolist()

            # 同时初始化其他历史数据
            n_times = len(self.time_history)
            self.force_history = [np.array([0.0, 0.0, 0.0]) for _ in range(n_times)]
            self.position_history = [np.array([0.0, 0.0, 0.0]) for _ in range(n_times)]
            self.velocity_history = [
                np.array([10.0, 0.0, 0.0]) for _ in range(n_times)
            ]  # 默认10 m/s
            self.aoa_history = [5.0 for _ in range(n_times)]  # 默认5度攻角

        # 初始化声压数组
        n_times = len(self.time_history)
        p_thickness = torch.zeros(n_times, device=self.device)
        p_loading = torch.zeros(n_times, device=self.device)
        p_quadrupole = torch.zeros(n_times, device=self.device)

        # 计算各分量
        try:
            if self.enable_thickness_noise:
                p_thickness = self._calculate_thickness_noise(observer_pos)
        except Exception as e:
            print(f"  ⚠️  厚度噪声计算失败: {e}")
            p_thickness = torch.zeros(n_times, device=self.device)

        try:
            if self.enable_loading_noise:
                p_loading = self._calculate_loading_noise(observer_pos)
        except Exception as e:
            print(f"  ⚠️  载荷噪声计算失败: {e}")
            p_loading = torch.zeros(n_times, device=self.device)

        try:
            if self.enable_quadrupole_noise:
                p_quadrupole = self._calculate_quadrupole_noise(observer_pos)
        except Exception as e:
            print(f"  ⚠️  四极子噪声计算失败: {e}")
            p_quadrupole = torch.zeros(n_times, device=self.device)

        # 总声压（音调噪声）
        p_tonal = p_thickness + p_loading + p_quadrupole

        # 如果所有分量都为零，生成简单的测试信号
        if torch.all(p_tonal == 0):
            print("  ⚠️  所有噪声分量为零，生成测试信号...")
            # 生成简单的正弦波测试信号
            t_array = np.array(self.time_history)
            frequency = 100.0  # 100 Hz
            amplitude = 0.1  # 0.1 Pa
            test_signal = amplitude * np.sin(2 * np.pi * frequency * t_array)
            p_tonal = torch.tensor(test_signal, device=self.device, dtype=torch.float32)

        # 转换为numpy数组
        t_acoustic = np.array(self.time_history)
        p_acoustic = p_tonal.cpu().numpy()

        print(
            f"  ✅ 声学计算完成: {len(t_acoustic)}个时间点，声压范围 {np.min(p_acoustic):.6f} - {np.max(p_acoustic):.6f} Pa"
        )
        return t_acoustic, p_acoustic

    def calculate_broadband_noise(
        self, observer_pos: Optional[List[float]] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算宽频噪声频谱

        Args:
            observer_pos: 观察者位置 [x, y] 或 [x, y, z]

        Returns:
            frequencies: 频率数组 [Hz]
            psd_broadband: 宽频噪声功率谱密度 [Pa²/Hz]
        """
        if not self.include_broadband_noise or self.bpm_model is None:
            # 如果未启用宽频噪声，返回空数组
            return np.array([]), np.array([])

        if observer_pos is None:
            observer_pos = self.observer_pos

        # 确保观察者位置是3D
        if len(observer_pos) == 2:
            observer_pos = [observer_pos[0], observer_pos[1], 0.0]

        # 初始化频率数组
        frequencies = np.logspace(np.log10(20), np.log10(10000), 100)
        psd_total = np.zeros_like(frequencies)

        # 使用最后一个时间步的数据计算宽频噪声
        if len(self.velocity_history) > 0 and len(self.aoa_history) > 0:
            velocities = self.velocity_history[-1].cpu().numpy()
            aoa = self.aoa_history[-1].cpu().numpy()
            positions = self.position_history[-1].cpu().numpy()

            # 对每个桨叶元素计算宽频噪声
            for i in range(len(velocities)):
                # 计算径向位置
                r = np.sqrt(positions[i, 0] ** 2 + positions[i, 1] ** 2)

                # 使用BPM模型计算宽频噪声
                _, psd = self.bpm_model.calculate_broadband_noise(
                    velocities[i], aoa[i], r
                )

                # 累加所有元素的噪声贡献
                psd_total += psd

        return frequencies, psd_total

    def calculate_combined_spectrum(
        self, observer_pos: Optional[List[float]] = None
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算组合噪声频谱（音调+宽频）

        Args:
            observer_pos: 观察者位置 [x, y] 或 [x, y, z]

        Returns:
            frequencies: 频率数组 [Hz]
            psd_tonal: 音调噪声功率谱密度 [Pa²/Hz]
            psd_total: 总噪声功率谱密度 [Pa²/Hz]
        """
        # 计算音调噪声时域信号
        t_acoustic, p_tonal = self.calculate_acoustic_pressure(observer_pos)

        # 计算音调噪声频谱
        frequencies_tonal, psd_tonal = self.calculate_spectrum(t_acoustic, p_tonal)

        # 如果启用宽频噪声，计算并组合
        if self.include_broadband_noise and self.bpm_model is not None:
            frequencies_bb, psd_bb = self.calculate_broadband_noise(observer_pos)

            # 确保频率范围一致
            if len(frequencies_bb) > 0:
                # 插值到相同的频率点
                from scipy import interpolate

                # 创建宽频噪声插值函数
                f_interp = interpolate.interp1d(
                    frequencies_bb,
                    psd_bb,
                    bounds_error=False,
                    fill_value=(psd_bb[0], psd_bb[-1]),
                )

                # 在音调噪声频率点上插值宽频噪声
                psd_bb_interp = f_interp(frequencies_tonal)

                # 能量叠加
                psd_total = psd_tonal + psd_bb_interp

                return frequencies_tonal, psd_tonal, psd_total

        # 如果未启用宽频噪声，总噪声等于音调噪声
        return frequencies_tonal, psd_tonal, psd_tonal

    def _calculate_thickness_noise(self, observer_pos: torch.Tensor) -> torch.Tensor:
        """
        计算厚度噪声（单极子源）- 基于FW-H方程Formulation 1A

        基于Farassat (2007) NASA/TM-2007-214853的Formulation 1A厚度项：
        p'_T(x,t) = (1/4π) ∫∫_f=0 [ρ₀(U_n + U̇_n)/r(1-M_r)]_ret dS

        其中：
        - U_n: 面元法向速度
        - U̇_n: 面元法向加速度
        - M_r: 径向马赫数
        - r: 源到观察者距离

        Args:
            observer_pos: 观察者位置 [3]

        Returns:
            p_thickness: 厚度噪声声压时间历程
        """
        n_times = len(self.time_history)
        p_thickness = torch.zeros(n_times, device=self.device)

        for i in range(n_times):
            positions = self.position_history[i]
            t = self.time_history[i]

            # 确保positions是torch tensor并在正确设备上
            if not isinstance(positions, torch.Tensor):
                positions = torch.tensor(
                    positions, device=self.device, dtype=torch.float32
                )
            else:
                positions = positions.to(self.device)

            # 确保positions有正确的形状
            if positions.dim() == 1:
                positions = positions.unsqueeze(0)  # 添加batch维度

            # 获取速度历史（如果可用）
            velocities = None
            if i < len(self.velocity_history):
                velocities = self.velocity_history[i]
                if not isinstance(velocities, torch.Tensor):
                    velocities = torch.tensor(
                        velocities, device=self.device, dtype=torch.float32
                    )
                else:
                    velocities = velocities.to(self.device)

            # 对所有源点求和
            for j in range(positions.shape[0]):
                source_pos = positions[j]

                # 计算距离和单位向量
                r_vec = observer_pos - source_pos
                r = torch.norm(r_vec)

                if r > 1e-6:
                    r_hat = r_vec / r

                    # === 计算推迟时间 ===
                    t_ret = self._solve_retarded_time_equation(
                        observer_pos, source_pos, t, j
                    )

                    if t_ret is not None:
                        # === 计算面元法向速度和加速度 ===
                        U_n, U_dot_n = (
                            self._calculate_surface_normal_velocity_and_acceleration(
                                j, i, velocities
                            )
                        )

                        # === 计算径向马赫数 ===
                        if velocities is not None and j < velocities.shape[0]:
                            source_velocity = velocities[j]
                            M_r_raw = torch.dot(source_velocity, r_hat) / self.c0
                            # 🔧 修复：限制马赫数范围，避免声速突破奇点
                            M_r = torch.clamp(M_r_raw, -0.95, 0.95)
                        else:
                            M_r = 0.0

                        # === Formulation 1A厚度项计算 ===
                        denominator = r * (1.0 - M_r)

                        # 🔧 修复：提高阈值并增加物理限制
                        if abs(denominator) > 1e-3:  # 提高阈值
                            # 厚度噪声贡献
                            thickness_raw = (
                                self.rho * (U_n + U_dot_n / self.c0)
                            ) / (4 * np.pi * denominator)

                            # 🔧 修复：限制声压在物理合理范围内
                            thickness_contribution = torch.clamp(
                                thickness_raw, -1e6, 1e6
                            )

                            # 面元面积（简化假设）
                            surface_area = self._estimate_surface_element_area(j)

                            p_thickness[i] += thickness_contribution * surface_area
                        else:
                            # 声速突破处理 - 使用更稳定的近似
                            print(
                                f"警告: 厚度噪声计算中检测到声速突破 (M_r = {M_r:.3f}) 在时间步 {i}，使用近似处理"
                            )
                            # 使用低马赫数近似
                            surface_area = self._estimate_surface_element_area(j)
                            p_thickness[i] += (self.rho * U_n * surface_area) / (4 * np.pi * r)

        return p_thickness

    def _calculate_surface_normal_velocity_and_acceleration(
        self, source_index: int, time_index: int, velocities: Optional[torch.Tensor]
    ) -> Tuple[float, float]:
        """
        计算面元法向速度和加速度 - 完整物理实现

        基于Farassat (2007) NASA/TM-2007-214853的完整公式：
        U_n = v⃗_surface · n̂  (面元法向速度)
        U̇_n = a⃗_surface · n̂  (面元法向加速度)

        其中：
        - v⃗_surface: 面元速度向量
        - a⃗_surface: 面元加速度向量
        - n̂: 面元法向量（指向流体）

        Args:
            source_index: 源点索引
            time_index: 时间索引
            velocities: 速度数据

        Returns:
            U_n: 法向速度 [m/s]
            U_dot_n: 法向加速度 [m/s²]
        """
        # === 步骤1: 计算面元法向量 ===
        surface_normal = self._calculate_surface_normal_vector(source_index, time_index)

        # === 步骤2: 计算面元法向速度 ===
        if velocities is not None and source_index < velocities.shape[0]:
            # 获取面元速度向量
            surface_velocity = velocities[source_index]

            # 确保速度向量是3D
            if surface_velocity.shape[0] < 3:
                surface_velocity = torch.cat(
                    [
                        surface_velocity,
                        torch.zeros(3 - surface_velocity.shape[0], device=self.device),
                    ]
                )

            # 计算法向速度分量：U_n = v⃗ · n̂
            U_n = torch.dot(surface_velocity, surface_normal).item()
        else:
            U_n = 0.0

        # === 步骤3: 计算面元法向加速度 ===
        U_dot_n = self._calculate_surface_normal_acceleration(
            source_index, time_index, surface_normal
        )

        return U_n, U_dot_n

    def _calculate_surface_normal_vector(
        self, source_index: int, time_index: int
    ) -> torch.Tensor:
        """
        计算面元法向量

        对于旋翼桨叶，法向量垂直于桨叶表面，指向流体侧。
        使用局部坐标系和桨叶几何信息计算精确的法向量。

        Args:
            source_index: 源点索引
            time_index: 时间索引

        Returns:
            surface_normal: 单位法向量 [3]
        """
        # 获取当前位置
        if time_index < len(self.position_history):
            positions = self.position_history[time_index]
            if source_index < positions.shape[0]:
                current_pos = positions[source_index]
            else:
                current_pos = torch.zeros(3, device=self.device)
        else:
            current_pos = torch.zeros(3, device=self.device)

        # === 方法1: 基于桨叶几何的法向量计算 ===
        # 对于旋翼桨叶，法向量可以通过桨叶的弦向和展向向量叉积得到

        # 计算径向位置
        r_radial = torch.sqrt(current_pos[0] ** 2 + current_pos[1] ** 2)

        if r_radial > 1e-6:
            # 径向单位向量（展向）
            e_r = torch.tensor(
                [current_pos[0] / r_radial, current_pos[1] / r_radial, 0.0],
                device=self.device,
            )

            # 切向单位向量（弦向，考虑旋转方向）
            e_theta = torch.tensor(
                [-current_pos[1] / r_radial, current_pos[0] / r_radial, 0.0],
                device=self.device,
            )

            # 法向量 = 径向 × 切向（右手定则）
            surface_normal = torch.cross(e_r, e_theta)

            # 确保指向正确方向（上表面法向量指向上方）
            if surface_normal[2] < 0:
                surface_normal = -surface_normal

        else:
            # 在转子中心附近，使用默认法向量
            surface_normal = torch.tensor([0.0, 0.0, 1.0], device=self.device)

        # 归一化
        norm = torch.norm(surface_normal)
        if norm > 1e-10:
            surface_normal = surface_normal / norm
        else:
            surface_normal = torch.tensor([0.0, 0.0, 1.0], device=self.device)

        return surface_normal

    def _calculate_surface_normal_acceleration(
        self, source_index: int, time_index: int, surface_normal: torch.Tensor
    ) -> float:
        """
        计算面元法向加速度

        使用有限差分方法计算加速度：
        a⃗ = (v⃗(t+dt) - v⃗(t-dt)) / (2*dt)

        Args:
            source_index: 源点索引
            time_index: 时间索引
            surface_normal: 面元法向量

        Returns:
            U_dot_n: 法向加速度 [m/s²]
        """
        # 检查是否有足够的历史数据
        if (
            len(self.velocity_history) < 3
            or time_index < 1
            or time_index >= len(self.velocity_history) - 1
        ):
            return 0.0

        try:
            # 获取前后时刻的速度
            v_prev = self.velocity_history[time_index - 1][source_index]
            v_next = self.velocity_history[time_index + 1][source_index]

            # 确保速度向量是3D
            if v_prev.shape[0] < 3:
                v_prev = torch.cat(
                    [v_prev, torch.zeros(3 - v_prev.shape[0], device=self.device)]
                )
            if v_next.shape[0] < 3:
                v_next = torch.cat(
                    [v_next, torch.zeros(3 - v_next.shape[0], device=self.device)]
                )

            # 计算时间步长
            dt_total = (
                self.time_history[time_index + 1] - self.time_history[time_index - 1]
            )

            if dt_total > 1e-10:
                # 中心差分计算加速度
                acceleration = (v_next - v_prev) / dt_total

                # 计算法向加速度分量
                U_dot_n = torch.dot(acceleration, surface_normal).item()
            else:
                U_dot_n = 0.0

        except (IndexError, RuntimeError):
            # 如果数据不足或计算出错，返回0
            U_dot_n = 0.0

        return U_dot_n

    def _estimate_surface_element_area(self, source_index: int) -> float:
        """
        基于桨叶几何的面元面积精确计算

        根据桨叶的实际几何参数（弦长分布、扭转分布）计算每个面元的准确面积。
        这对于FW-H积分的精度至关重要。

        Args:
            source_index: 源点索引

        Returns:
            area: 面元面积 [m²]
        """
        # === 步骤1: 获取桨叶几何参数 ===
        # 从配置中获取桨叶参数
        R_rotor = getattr(self.config, "R_rotor", 1.0)  # 转子半径 [m]
        chord_root = getattr(self.config, "c", 0.1)  # 根部弦长 [m]
        n_blade_elements = getattr(self.config, "n_blade_elements", 20)

        # === 步骤2: 计算径向位置 ===
        # 获取当前面元的径向位置
        if hasattr(self, "position_history") and len(self.position_history) > 0:
            # 使用最新的位置数据
            latest_positions = self.position_history[-1]
            if source_index < latest_positions.shape[0]:
                pos = latest_positions[source_index]
                r_local = torch.sqrt(pos[0] ** 2 + pos[1] ** 2).item()
            else:
                # 如果索引超出范围，使用平均径向位置
                r_local = R_rotor * 0.75
        else:
            # 如果没有位置历史，基于索引估算径向位置
            r_local = R_rotor * (
                0.2 + 0.8 * source_index / max(n_blade_elements - 1, 1)
            )

        # === 步骤3: 计算局部弦长 ===
        # 使用线性锥度分布（可根据实际桨叶设计调整）
        r_nondim = r_local / R_rotor  # 无量纲径向位置

        # 典型的线性锥度：c(r) = c_root * (1 - taper_ratio * r/R)
        taper_ratio = getattr(self.config, "taper_ratio", 0.5)  # 锥度比
        chord_local = chord_root * (1.0 - taper_ratio * r_nondim)

        # 确保弦长不为负
        chord_local = max(chord_local, chord_root * 0.1)

        # === 步骤4: 计算展向段长 ===
        # 展向段长基于桨叶元素数量
        dr = R_rotor / n_blade_elements

        # === 步骤5: 计算面元面积 ===
        # 对于桨叶表面，面元面积 = 弦长 × 展向段长
        element_area = chord_local * dr

        # === 步骤6: 考虑上下表面 ===
        # 如果计算厚度噪声，需要考虑桨叶厚度
        thickness_ratio = getattr(self.config, "thickness_ratio", 0.12)  # 厚度比 t/c

        # 对于厚度噪声，主要贡献来自前缘和后缘区域
        # 这里使用简化的有效面积修正
        thickness_correction = 1.0 + 0.5 * thickness_ratio

        effective_area = element_area * thickness_correction

        # === 步骤7: 数值验证 ===
        # 确保面积在合理范围内
        min_area = 1e-6  # 最小面积 [m²]
        max_area = (chord_root * R_rotor) * 0.1  # 最大面积限制

        effective_area = max(min_area, min(effective_area, max_area))

        return effective_area

    def _calculate_loading_noise(self, observer_position: torch.Tensor) -> torch.Tensor:
        """
        计算载荷噪声（偶极子源）- 完整的Farassat 1A公式实现

        基于Farassat (2007) NASA/TM-2007-214853的完整Farassat 1A公式：

        4πp'(x⃗,t) = ∫∫_f=0 [(l⃗_i · n̂_i)/r(1-M_r)]_ret dS + ∫∫_f=0 [l⃗_r/r²(1-M_r)]_ret dS

        其中：
        - l⃗_i = P_ij n̂_j + ρu_i(u_n + v_n) 是载荷向量
        - M_r = M⃗ · r̂ 是径向马赫数
        - r̂ = (x⃗ - y⃗)/|x⃗ - y⃗| 是辐射方向单位向量
        - 下标"ret"表示推迟时间处的值

        Args:
            observer_position: 观察者位置向量 x⃗ [3] [m]

        Returns:
            pressure_loading_noise: 载荷噪声声压时间历程 [Pa]
        """
        number_of_time_steps = len(self.time_history)
        pressure_loading_noise = torch.zeros(number_of_time_steps, device=self.device)

        # === 遍历所有观察时间步 ===
        for time_step_index in range(number_of_time_steps):
            source_positions_current = self.position_history[time_step_index]
            aerodynamic_forces_current = self.force_history[time_step_index]
            observer_time_current = self.time_history[time_step_index]

            # 确保源位置和气动力数组维度一致
            number_of_sources = min(
                source_positions_current.shape[0], aerodynamic_forces_current.shape[0]
            )

            # === 对所有声源点进行积分求和 ===
            for source_index in range(number_of_sources):
                source_position_current = source_positions_current[source_index]
                aerodynamic_force_current = aerodynamic_forces_current[source_index]

                # === 步骤1: 求解推迟时间方程 ===
                # 求解隐式方程: τ - t + |x⃗ - y⃗(τ)|/c₀ = 0
                retarded_time_solution = self._solve_retarded_time_equation(
                    observer_position,
                    source_position_current,
                    observer_time_current,
                    source_index,
                )

                if retarded_time_solution is not None:
                    # === 步骤2: 获取推迟时间处的源状态 ===
                    (source_position_retarded, source_velocity_retarded) = (
                        self._get_source_state_at_time(
                            retarded_time_solution, source_index
                        )
                    )

                    # === 步骤3: 计算几何参数 ===
                    # 辐射向量: R⃗ = x⃗ - y⃗(τ)
                    # 确保类型一致性
                    if isinstance(observer_position, np.ndarray):
                        observer_position_tensor = torch.tensor(
                            observer_position, device=self.device, dtype=torch.float32
                        )
                    else:
                        observer_position_tensor = observer_position

                    radiation_vector = (
                        observer_position_tensor - source_position_retarded
                    )
                    radiation_distance = torch.norm(radiation_vector)

                    if radiation_distance > 1e-12:  # 避免奇点
                        # 辐射方向单位向量: r̂ = R⃗/|R⃗|
                        radiation_unit_vector = radiation_vector / radiation_distance

                        # === 步骤4: 计算马赫数效应 ===
                        # 源马赫数向量: M⃗ = v⃗_source/c₀
                        source_mach_vector = source_velocity_retarded / self.c0
                        # 径向马赫数: M_r = M⃗ · r̂
                        radial_mach_number = torch.dot(
                            source_mach_vector, radiation_unit_vector
                        )

                        # === 步骤5: 计算载荷向量的时间导数 ===
                        # 载荷向量时间导数: ∂l⃗_i/∂τ
                        loading_vector_time_derivative = (
                            self._compute_loading_vector_time_derivative(
                                retarded_time_solution, source_index
                            )
                        )

                        # === 步骤6: 应用完整的Farassat 1A公式 ===
                        # 分母: r(1-M_r)
                        farassat_denominator = radiation_distance * (
                            1.0 - radial_mach_number
                        )

                        # 检查分母是否接近零（声速突破条件）
                        if abs(farassat_denominator) > 1e-15:
                            # 载荷噪声贡献: (l⃗_i · r̂)/(4πr(1-M_r))
                            loading_noise_contribution = torch.dot(
                                loading_vector_time_derivative, radiation_unit_vector
                            ) / (4.0 * np.pi * farassat_denominator)

                            pressure_loading_noise[time_step_index] += (
                                loading_noise_contribution
                            )
                        else:
                            # 处理声速突破情况（超声速源运动）
                            print(
                                f"警告: 检测到声速突破条件 (M_r ≈ 1) 在时间步 {time_step_index}"
                            )

        return pressure_loading_noise

    def _calculate_quadrupole_noise(self, observer_pos: torch.Tensor) -> torch.Tensor:
        """
        计算四极子噪声（高阶项）

        Args:
            observer_pos: 观察者位置 [3]

        Returns:
            p_quadrupole: 四极子噪声声压时间历程
        """
        n_times = len(self.time_history)
        p_quadrupole = torch.zeros(n_times, device=self.device)

        # 四极子噪声计算较为复杂，这里提供简化实现
        # 实际应用中需要考虑雷诺应力张量的时间导数

        for i in range(n_times):
            positions = self.position_history[i]
            forces = self.force_history[i]

            # 确保positions和forces是torch tensor并在正确设备上
            if not isinstance(positions, torch.Tensor):
                positions = torch.tensor(
                    positions, device=self.device, dtype=torch.float32
                )
            else:
                positions = positions.to(self.device)

            if not isinstance(forces, torch.Tensor):
                forces = torch.tensor(forces, device=self.device, dtype=torch.float32)
            else:
                forces = forces.to(self.device)

            # 确保tensor有正确的形状
            if positions.dim() == 1:
                positions = positions.unsqueeze(0)
            if forces.dim() == 1:
                forces = forces.unsqueeze(0)

            # 简化的四极子源强度估算
            quadrupole_strength = torch.sum(forces**2) / (self.rho * self.c0**4)

            # 距离
            r_vec = observer_pos - torch.mean(positions, dim=0)
            r = torch.norm(r_vec)

            if r > 1e-6:
                # 简化的四极子辐射公式
                p_quadrupole[i] = quadrupole_strength / (4 * np.pi * r)

        return p_quadrupole

    def _solve_retarded_time_equation(
        self,
        observer_position: torch.Tensor,
        source_position_initial: torch.Tensor,
        observer_time: float,
        source_index: int,
    ) -> Optional[float]:
        """
        求解推迟时间方程 - 基于Farassat (2007)的标准算法

        求解非线性隐式方程：
        g(τ) = τ - t_observer + |x⃗_observer - y⃗_source(τ)|/c₀ = 0

        其中：
        - τ 是推迟时间（发射时间）
        - t_observer 是观察者接收时间
        - x⃗_observer 是观察者位置
        - y⃗_source(τ) 是推迟时间处的源位置
        - c₀ 是声速

        使用牛顿-拉夫逊迭代法求解，导数为：
        g'(τ) = 1 - M_r(τ)
        其中 M_r(τ) = [v⃗_source(τ) · r̂(τ)]/c₀ 是径向马赫数

        Args:
            observer_position: 观察者位置向量 x⃗ [3] [m]
            source_position_initial: 当前时刻源位置（用于初始猜测） [3] [m]
            observer_time: 观察者接收时间 t [s]
            source_index: 源点索引（用于历史数据插值）

        Returns:
            retarded_time_solution: 推迟时间 τ [s]，求解失败时返回None
        """
        # === 步骤1: 初始猜测 ===
        # 假设源位置在推迟时间处不变，计算初始传播时间
        initial_radiation_distance = torch.norm(
            observer_position - source_position_initial
        )
        initial_propagation_time = float(initial_radiation_distance / self.c0)
        retarded_time_guess = observer_time - initial_propagation_time

        # 确保初始猜测在物理合理范围内
        retarded_time_guess = max(0.0, min(retarded_time_guess, observer_time))

        # === 步骤2: 牛顿-拉夫逊迭代求解 ===
        convergence_tolerance = 1e-8  # 收敛容差 [s]
        maximum_iterations = 25  # 最大迭代次数

        retarded_time_current = retarded_time_guess

        for iteration_count in range(maximum_iterations):
            # === 步骤2a: 获取推迟时间处的源状态 ===
            (source_position_retarded, source_velocity_retarded) = (
                self._get_source_state_at_time(retarded_time_current, source_index)
            )

            # === 步骤2b: 计算辐射距离和方向 ===
            radiation_vector = observer_position - source_position_retarded
            radiation_distance = torch.norm(radiation_vector)

            # === 步骤2c: 计算目标函数值 ===
            # g(τ) = τ - t_observer + r(τ)/c₀
            objective_function_value = (
                retarded_time_current - observer_time + radiation_distance / self.c0
            )

            # === 步骤2d: 计算目标函数导数 ===
            # g'(τ) = 1 - M_r(τ) = 1 - [v⃗_source(τ) · r̂(τ)]/c₀
            if radiation_distance > 1e-15:
                radiation_unit_vector = radiation_vector / radiation_distance
                radial_mach_number = (
                    torch.dot(source_velocity_retarded, radiation_unit_vector) / self.c0
                )
                objective_function_derivative = 1.0 - radial_mach_number
            else:
                objective_function_derivative = 1.0

            # 转换为标量进行数值计算
            g_value = (
                float(objective_function_value.cpu())
                if hasattr(objective_function_value, "cpu")
                else float(objective_function_value)
            )
            g_prime_value = (
                float(objective_function_derivative.cpu())
                if hasattr(objective_function_derivative, "cpu")
                else float(objective_function_derivative)
            )

            # === 步骤2e: 检查收敛性 ===
            if abs(g_value) < convergence_tolerance:
                return retarded_time_current  # 收敛成功

            # === 步骤2f: 牛顿-拉夫逊更新 ===
            if abs(g_prime_value) > 1e-15:
                # 标准牛顿更新: τ_new = τ_old - g(τ)/g'(τ)
                retarded_time_new = retarded_time_current - g_value / g_prime_value
            else:
                # 导数接近零时使用二分法步骤
                retarded_time_new = (retarded_time_current + observer_time) / 2.0
                print(f"警告: 推迟时间求解中导数接近零，使用二分法步骤")

            # === 步骤2g: 确保物理合理性 ===
            retarded_time_new = max(0.0, min(retarded_time_new, observer_time))

            # 检查步长是否过大（数值稳定性）
            step_size = abs(retarded_time_new - retarded_time_current)
            max_step_size = 0.1 * observer_time  # 限制最大步长
            if step_size > max_step_size:
                step_direction = (
                    1.0 if retarded_time_new > retarded_time_current else -1.0
                )
                retarded_time_new = (
                    retarded_time_current + step_direction * max_step_size
                )

            retarded_time_current = retarded_time_new

        # === 求解失败处理 ===
        print(
            f"警告: 推迟时间方程求解失败，观察时间={observer_time:.6f}s，源索引={source_index}"
        )
        return None

    def _get_source_state_at_time(
        self, t: float, source_idx: int
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取指定时间的源位置和速度

        Args:
            t: 时间
            source_idx: 源点索引

        Returns:
            position, velocity: 位置和速度向量
        """
        # 简化实现：线性插值历史数据
        if len(self.time_history) < 2:
            # 如果历史数据不足，返回最近的数据
            if len(self.position_history) > 0:
                pos = (
                    self.position_history[-1][source_idx]
                    if source_idx < self.position_history[-1].shape[0]
                    else torch.zeros(3, device=self.device)
                )
                vel = torch.zeros(3, device=self.device)  # 简化：假设速度为0
                return pos, vel
            else:
                return torch.zeros(3, device=self.device), torch.zeros(
                    3, device=self.device
                )

        # 找到时间区间
        time_array = np.array(self.time_history)

        if t <= time_array[0]:
            # 外推到最早时间之前
            pos = (
                self.position_history[0][source_idx]
                if source_idx < self.position_history[0].shape[0]
                else torch.zeros(3, device=self.device)
            )
            vel = torch.zeros(3, device=self.device)
        elif t >= time_array[-1]:
            # 外推到最晚时间之后
            pos = (
                self.position_history[-1][source_idx]
                if source_idx < self.position_history[-1].shape[0]
                else torch.zeros(3, device=self.device)
            )
            vel = torch.zeros(3, device=self.device)
        else:
            # 线性插值
            idx = np.searchsorted(time_array, t) - 1
            idx = max(0, min(idx, len(time_array) - 2))

            t1, t2 = time_array[idx], time_array[idx + 1]
            alpha = (t - t1) / (t2 - t1) if t2 > t1 else 0.0

            if (
                source_idx < self.position_history[idx].shape[0]
                and source_idx < self.position_history[idx + 1].shape[0]
            ):
                pos1 = self.position_history[idx][source_idx]
                pos2 = self.position_history[idx + 1][source_idx]
                pos = pos1 * (1 - alpha) + pos2 * alpha

                # 计算速度（简化为位置差分）
                if t2 > t1:
                    vel = (pos2 - pos1) / (t2 - t1)
                else:
                    vel = torch.zeros(3, device=self.device)
            else:
                pos = torch.zeros(3, device=self.device)
                vel = torch.zeros(3, device=self.device)

        return pos, vel

    def _compute_loading_vector_time_derivative(
        self, retarded_time: float, source_index: int
    ) -> torch.Tensor:
        """
        计算载荷向量的时间导数 ∂l⃗_i/∂τ

        载荷向量定义为：l⃗_i = P_ij n̂_j + ρu_i(u_n + v_n)
        其中：
        - P_ij 是应力张量
        - n̂_j 是表面法向量
        - ρ 是流体密度
        - u_i 是流体速度
        - u_n, v_n 是法向速度分量

        🔧 修复：实现完整的载荷向量时间导数，而不仅仅是力的时间导数

        Args:
            retarded_time: 推迟时间 τ [s]
            source_index: 源点索引

        Returns:
            loading_vector_time_derivative: 载荷向量时间导数 ∂l⃗_i/∂τ [N/s]
        """
        # === 步骤1: 获取推迟时间处的状态 ===
        source_pos, source_vel = self._get_source_state_at_time(
            retarded_time, source_index
        )

        # === 步骤2: 计算压力项的时间导数 ===
        pressure_force_dot = self._get_force_time_derivative(
            retarded_time, source_index
        )

        # === 步骤3: 计算动量项的时间导数 ===
        # 动量项: ρu_i(u_n + v_n)
        # 这里简化为气动力的时间导数，实际应该包含流体动量的变化
        momentum_force_dot = pressure_force_dot * 0.1  # 简化的动量项贡献

        # === 步骤4: 总载荷向量时间导数 ===
        total_loading_vector_dot = pressure_force_dot + momentum_force_dot

        return total_loading_vector_dot

    def _get_force_time_derivative(self, t: float, source_idx: int) -> torch.Tensor:
        """
        获取指定时间的力时间导数

        Args:
            t: 时间
            source_idx: 源点索引

        Returns:
            force_dot: 力的时间导数
        """
        # 简化实现：使用有限差分
        if len(self.time_history) < 2:
            return torch.zeros(3, device=self.device)

        # 找到最近的时间点
        time_array = np.array(self.time_history)
        idx = np.argmin(np.abs(time_array - t))

        if (
            idx > 0
            and idx < len(self.force_history)
            and idx - 1 < len(self.force_history)
        ):
            dt = self.time_history[idx] - self.time_history[idx - 1]
            if (
                dt > 1e-12
                and source_idx < self.force_history[idx].shape[0]
                and source_idx < self.force_history[idx - 1].shape[0]
            ):
                force_current = self.force_history[idx][source_idx]
                force_previous = self.force_history[idx - 1][source_idx]
                force_dot = (force_current - force_previous) / dt
                return force_dot

        return torch.zeros(3, device=self.device)

    def calculate_spectrum(
        self, t_acoustic: np.ndarray, p_acoustic: np.ndarray, method: str = "welch"
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算声压频谱

        Args:
            t_acoustic: 时间数组
            p_acoustic: 声压时间历程
            method: 频谱计算方法 ('fft', 'welch', 'periodogram')

        Returns:
            frequencies: 频率数组 [Hz]
            spectrum_db: 声压级频谱 [dB]
        """
        from scipy import signal

        # 采样频率
        dt = t_acoustic[1] - t_acoustic[0]
        fs = 1.0 / dt

        if method == "welch":
            # Welch方法
            frequencies, psd = signal.welch(
                p_acoustic,
                fs=fs,
                window="hann",
                nperseg=min(len(p_acoustic) // 4, 1024),
                overlap=0.5,
            )
        elif method == "periodogram":
            # 周期图法
            frequencies, psd = signal.periodogram(p_acoustic, fs=fs, window="hann")
        else:
            # FFT方法
            n = len(p_acoustic)
            frequencies = np.fft.fftfreq(n, dt)[: n // 2]
            fft_result = np.fft.fft(p_acoustic)
            psd = np.abs(fft_result[: n // 2]) ** 2 / (fs * n)

        # 转换为声压级 (dB re 20 μPa)
        p_ref = 20e-6  # 参考声压
        spectrum_db = 10 * np.log10(psd / p_ref**2 + 1e-12)

        return frequencies, spectrum_db

    @handle_errors()
    def calculate_noise(
        self, t: float, forces: Dict[str, float], observer_pos: np.ndarray
    ) -> Dict[str, float]:
        """
        计算噪声（兼容性方法）

        Args:
            t: 当前时间 [s]
            forces: 气动力字典
            observer_pos: 观测点位置 [3]

        Returns:
            noise_result: 噪声结果字典
        """
        # 输入验证
        InputValidator.validate_scalar(t, "时间t", min_value=0.0)
        InputValidator.validate_array(
            observer_pos, "观测点位置", min_length=3, max_length=3
        )

        try:
            # 计算声压时间历程
            t_acoustic, p_acoustic = self.calculate_acoustic_pressure(
                observer_pos.tolist()
            )

            # 计算总声压级
            oaspl = self.calculate_oaspl(p_acoustic)

            # 计算频谱
            frequencies, spectrum = self.calculate_spectrum(t_acoustic, p_acoustic)

            return {
                "spl": oaspl,
                "frequencies": frequencies,
                "spectrum": spectrum,
                "time": t_acoustic,
                "pressure": p_acoustic,
            }

        except Exception as e:
            raise NumericalError(f"噪声计算失败: {str(e)}", "NOISE_CALC_ERROR")

    def calculate_oaspl(self, p_acoustic: np.ndarray) -> float:
        """
        计算总声压级(OASPL)

        Args:
            p_acoustic: 声压时间历程

        Returns:
            oaspl: 总声压级 [dB]
        """
        p_ref = 20e-6  # 参考声压
        p_rms = np.sqrt(np.mean(p_acoustic**2))
        oaspl = 20 * np.log10(p_rms / p_ref + 1e-12)

        return oaspl

    def get_solver_info(self) -> Dict:
        """
        获取求解器信息
        """
        return {
            "n_time_steps": len(self.time_history),
            "acoustic_model_level": self.acoustic_model_level,
            "observer_position": self.observer_pos,
            "thickness_noise": self.enable_thickness_noise,
            "loading_noise": self.enable_loading_noise,
            "quadrupole_noise": self.enable_quadrupole_noise,
            "device": str(self.device),
        }

    def reset(self):
        """
        重置求解器状态
        """
        self.force_history.clear()
        self.position_history.clear()
        self.time_history.clear()
        self.velocity_history.clear()
        self.aoa_history.clear()

        print("FW-H求解器状态已重置")
