"""
物理修正模块
===========

包含各种物理修正模型，用于提高BEMT求解器的精度。

模块内容：
- corrections.py: 统一物理修正系统
- tip_loss.py: 叶尖损失修正
- hub_loss.py: 叶根损失修正
- viscous_effects.py: 粘性效应修正
- compressibility.py: 压缩性修正
- rotational_effects.py: 旋转效应修正

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from .corrections import UnifiedPhysicalCorrections
from .tip_loss import TipLossCorrection, PrandtlTipLoss, GoldsteinTipLoss
from .hub_loss import HubLossCorrection
from .viscous_effects import ViscousEffectsCorrection
from .compressibility import CompressibilityCorrection
from .rotational_effects import RotationalEffectsCorrection

__all__ = [
    'UnifiedPhysicalCorrections',
    'TipLossCorrection',
    'PrandtlTipLoss',
    'GoldsteinTipLoss',
    'HubLossCorrection',
    'ViscousEffectsCorrection',
    'CompressibilityCorrection',
    'RotationalEffectsCorrection'
]