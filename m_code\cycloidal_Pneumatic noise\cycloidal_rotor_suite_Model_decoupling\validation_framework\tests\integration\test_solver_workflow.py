#!/usr/bin/env python3
"""
集成验证 - 求解器工作流程测试
============================

验证完整的求解器工作流程，从初始化到结果输出。

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import sys
import os
import time
from pathlib import Path
import numpy as np

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "bemt_medium_fidelity_validation"))

from validation_framework.core.test_runner import register_test, ValidationPhase

def test_complete_solver_workflow():
    """测试完整的求解器工作流程"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        workflow_results = {}
        
        # 步骤1: 配置创建和验证
        try:
            config = {
                'R_rotor': 1.0,
                'B': 4,
                'c': 0.1,
                'omega_rotor': 120.0,
                'rho': 1.225,
                'bemt_n_elements': 10,
                'max_iterations': 50,
                'tolerance': 1e-4
            }
            
            config_manager = ConfigManager(config)
            workflow_results['config_creation'] = True
            
        except Exception as e:
            workflow_results['config_creation'] = f"配置创建失败: {e}"
            return {
                'passed': False,
                'error_message': "工作流程在配置创建阶段失败",
                'details': {'workflow_results': workflow_results}
            }
        
        # 步骤2: 求解器创建
        try:
            factory = SolverFactory()
            solver = factory.create_solver('bemt_medium', config_manager.to_dict())
            
            # 验证求解器对象
            assert solver is not None
            workflow_results['solver_creation'] = True
            
        except Exception as e:
            workflow_results['solver_creation'] = f"求解器创建失败: {e}"
            return {
                'passed': False,
                'error_message': "工作流程在求解器创建阶段失败",
                'details': {'workflow_results': workflow_results}
            }
        
        # 步骤3: 求解器初始化检查
        try:
            # 检查求解器是否正确初始化
            initialization_checks = {}
            
            # 检查配置是否正确设置
            if hasattr(solver, 'config') or hasattr(solver, '_config'):
                initialization_checks['config_set'] = True
            else:
                initialization_checks['config_set'] = "配置未设置"
            
            # 检查几何参数是否初始化
            if hasattr(solver, 'R_rotor') or hasattr(solver, '_R_rotor'):
                initialization_checks['geometry_init'] = True
            else:
                initialization_checks['geometry_init'] = "几何参数未初始化"
            
            # 检查叶片是否创建
            if hasattr(solver, 'blades') or hasattr(solver, '_blades'):
                initialization_checks['blades_created'] = True
            else:
                initialization_checks['blades_created'] = "叶片未创建"
            
            workflow_results['solver_initialization'] = initialization_checks
            
        except Exception as e:
            workflow_results['solver_initialization'] = f"初始化检查失败: {e}"
        
        # 步骤4: 求解执行
        try:
            start_time = time.time()
            
            # 尝试执行求解
            if hasattr(solver, 'solve'):
                result = solver.solve()
                execution_time = time.time() - start_time
                
                # 验证结果
                if result is not None:
                    workflow_results['solve_execution'] = {
                        'success': True,
                        'execution_time': execution_time,
                        'result_type': type(result).__name__
                    }
                else:
                    workflow_results['solve_execution'] = "求解返回None"
                    
            elif hasattr(solver, 'solve_step'):
                # 如果只有solve_step方法，尝试单步求解
                result = solver.solve_step(0.0, 0.01)
                execution_time = time.time() - start_time
                
                workflow_results['solve_execution'] = {
                    'success': True,
                    'execution_time': execution_time,
                    'result_type': type(result).__name__,
                    'method': 'solve_step'
                }
            else:
                workflow_results['solve_execution'] = "没有可用的求解方法"
                
        except Exception as e:
            workflow_results['solve_execution'] = f"求解执行失败: {e}"
        
        # 步骤5: 结果验证
        try:
            if 'solve_execution' in workflow_results and isinstance(workflow_results['solve_execution'], dict):
                if workflow_results['solve_execution'].get('success'):
                    # 检查执行时间是否合理
                    exec_time = workflow_results['solve_execution']['execution_time']
                    if exec_time < 60:  # 应该在60秒内完成
                        workflow_results['performance_check'] = True
                    else:
                        workflow_results['performance_check'] = f"执行时间过长: {exec_time:.2f}s"
                else:
                    workflow_results['performance_check'] = "求解未成功，无法检查性能"
            else:
                workflow_results['performance_check'] = "无法获取执行信息"
                
        except Exception as e:
            workflow_results['performance_check'] = f"结果验证失败: {e}"
        
        # 统计工作流程成功的步骤
        successful_steps = 0
        total_steps = 0
        
        for step, result in workflow_results.items():
            total_steps += 1
            if result is True or (isinstance(result, dict) and result.get('success')):
                successful_steps += 1
        
        success_rate = successful_steps / max(total_steps, 1)
        
        return {
            'passed': success_rate >= 0.6,  # 60%的步骤成功即可
            'details': {
                'workflow_results': workflow_results,
                'successful_steps': successful_steps,
                'total_steps': total_steps,
                'success_rate': success_rate
            },
            'error_message': None if success_rate >= 0.6 else f"工作流程成功率不足: {success_rate:.1%}"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"完整工作流程测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_multi_step_simulation():
    """测试多步仿真流程"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        # 创建求解器
        config = {
            'R_rotor': 1.0,
            'B': 4,
            'c': 0.1,
            'omega_rotor': 120.0,
            'rho': 1.225,
            'bemt_n_elements': 10
        }
        
        config_manager = ConfigManager(config)
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config_manager.to_dict())
        
        # 执行多步仿真
        simulation_results = {}
        
        if hasattr(solver, 'solve_step'):
            try:
                dt = 0.01
                n_steps = 5  # 测试5个时间步
                
                results = []
                for i in range(n_steps):
                    t = i * dt
                    result = solver.solve_step(t, dt)
                    results.append(result)
                
                simulation_results['multi_step_success'] = True
                simulation_results['completed_steps'] = len(results)
                simulation_results['total_steps'] = n_steps
                
                # 检查结果一致性
                if all(r is not None for r in results):
                    simulation_results['result_consistency'] = True
                else:
                    simulation_results['result_consistency'] = "部分步骤返回None"
                
            except Exception as e:
                simulation_results['multi_step_success'] = f"多步仿真失败: {e}"
                
        elif hasattr(solver, 'solve'):
            try:
                # 如果只有solve方法，尝试多次调用
                results = []
                for i in range(3):
                    result = solver.solve()
                    results.append(result)
                
                simulation_results['multi_solve_success'] = True
                simulation_results['completed_solves'] = len(results)
                
            except Exception as e:
                simulation_results['multi_solve_success'] = f"多次求解失败: {e}"
        else:
            simulation_results['no_solve_method'] = "没有可用的求解方法"
        
        # 评估结果
        successful_tests = sum(1 for result in simulation_results.values() if result is True)
        total_tests = len(simulation_results)
        
        return {
            'passed': successful_tests >= 1,  # 至少一个测试成功
            'details': {
                'simulation_results': simulation_results,
                'successful_tests': successful_tests,
                'total_tests': total_tests
            },
            'error_message': None if successful_tests >= 1 else "多步仿真测试全部失败"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"多步仿真测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_error_recovery():
    """测试错误恢复能力"""
    try:
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        error_recovery_results = {}
        
        # 测试1: 无效配置的处理
        try:
            invalid_config = {
                'R_rotor': -1.0,  # 无效值
                'B': 4,
                'c': 0.1,
                'omega_rotor': 120.0,
                'rho': 1.225
            }
            
            try:
                config_manager = ConfigManager(invalid_config)
                factory = SolverFactory()
                solver = factory.create_solver('bemt_medium', config_manager.to_dict())
                
                # 如果没有抛出异常，说明错误处理不够严格
                error_recovery_results['invalid_config_handling'] = "应该拒绝无效配置"
                
            except Exception:
                # 预期的异常
                error_recovery_results['invalid_config_handling'] = True
                
        except Exception as e:
            error_recovery_results['invalid_config_handling'] = f"无效配置测试失败: {e}"
        
        # 测试2: 极端参数的处理
        try:
            extreme_config = {
                'R_rotor': 1.0,
                'B': 4,
                'c': 0.1,
                'omega_rotor': 1000.0,  # 极高转速
                'rho': 1.225,
                'bemt_n_elements': 100,  # 极多叶素
                'max_iterations': 5,     # 极少迭代
                'tolerance': 1e-10       # 极严格容差
            }
            
            config_manager = ConfigManager(extreme_config)
            factory = SolverFactory()
            solver = factory.create_solver('bemt_medium', config_manager.to_dict())
            
            # 尝试求解
            if hasattr(solver, 'solve'):
                try:
                    result = solver.solve()
                    error_recovery_results['extreme_params'] = True
                except Exception as e:
                    error_recovery_results['extreme_params'] = f"极端参数求解失败: {e}"
            else:
                error_recovery_results['extreme_params'] = "无solve方法，跳过极端参数测试"
                
        except Exception as e:
            error_recovery_results['extreme_params'] = f"极端参数测试失败: {e}"
        
        # 评估结果
        successful_tests = sum(1 for result in error_recovery_results.values() if result is True)
        total_tests = len(error_recovery_results)
        
        return {
            'passed': successful_tests >= 1,  # 至少一个错误恢复测试成功
            'details': {
                'error_recovery_results': error_recovery_results,
                'successful_tests': successful_tests,
                'total_tests': total_tests
            },
            'error_message': None if successful_tests >= 1 else "错误恢复测试全部失败"
        }
        
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"错误恢复测试出错: {e}",
            'details': {'exception': str(e)}
        }

def test_resource_management():
    """测试资源管理"""
    try:
        import psutil
        import gc
        
        from bemt_medium_fidelity_validation.core.solver_factory import SolverFactory
        from bemt_medium_fidelity_validation.utils.config import ConfigManager
        
        resource_results = {}
        
        # 记录初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建多个求解器实例
        solvers = []
        config = {
            'R_rotor': 1.0,
            'B': 4,
            'c': 0.1,
            'omega_rotor': 120.0,
            'rho': 1.225,
            'bemt_n_elements': 20
        }
        
        try:
            config_manager = ConfigManager(config)
            factory = SolverFactory()
            
            # 创建5个求解器实例
            for i in range(5):
                solver = factory.create_solver('bemt_medium', config_manager.to_dict())
                solvers.append(solver)
            
            # 记录峰值内存使用
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            resource_results['memory_usage'] = {
                'initial_mb': initial_memory,
                'peak_mb': peak_memory,
                'increase_mb': memory_increase,
                'reasonable': memory_increase < 500  # 内存增长应小于500MB
            }
            
            # 清理资源
            del solvers
            gc.collect()
            
            # 检查内存是否释放
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_released = peak_memory - final_memory
            
            resource_results['memory_cleanup'] = {
                'final_mb': final_memory,
                'released_mb': memory_released,
                'cleanup_effective': memory_released > memory_increase * 0.5  # 至少释放50%
            }
            
        except Exception as e:
            resource_results['resource_test_error'] = f"资源管理测试失败: {e}"
        
        # 评估结果
        memory_reasonable = resource_results.get('memory_usage', {}).get('reasonable', False)
        cleanup_effective = resource_results.get('memory_cleanup', {}).get('cleanup_effective', False)
        
        return {
            'passed': memory_reasonable or cleanup_effective,  # 内存使用合理或清理有效
            'details': {
                'resource_results': resource_results,
                'memory_reasonable': memory_reasonable,
                'cleanup_effective': cleanup_effective
            },
            'error_message': None if (memory_reasonable or cleanup_effective) else "资源管理不当"
        }
        
    except ImportError:
        return {
            'passed': True,  # 如果没有psutil，跳过测试
            'details': {'skipped': 'psutil not available'},
            'error_message': None
        }
    except Exception as e:
        return {
            'passed': False,
            'error_message': f"资源管理测试出错: {e}",
            'details': {'exception': str(e)}
        }

# 注册测试
register_test(ValidationPhase.INTEGRATION, "完整求解器工作流程测试")(test_complete_solver_workflow)
register_test(ValidationPhase.INTEGRATION, "多步仿真流程测试")(test_multi_step_simulation)
register_test(ValidationPhase.INTEGRATION, "错误恢复能力测试")(test_error_recovery)
register_test(ValidationPhase.INTEGRATION, "资源管理测试")(test_resource_management)

if __name__ == "__main__":
    # 单独运行集成测试
    print("🧪 运行求解器工作流程集成测试...")
    
    tests = [
        ("完整求解器工作流程", test_complete_solver_workflow),
        ("多步仿真流程", test_multi_step_simulation),
        ("错误恢复能力", test_error_recovery),
        ("资源管理", test_resource_management)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试...")
        result = test_func()
        
        if result['passed']:
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败: {result['error_message']}")
            if 'details' in result:
                print(f"   详情: {result['details']}")
    
    print("\n🎯 求解器工作流程集成测试完成")