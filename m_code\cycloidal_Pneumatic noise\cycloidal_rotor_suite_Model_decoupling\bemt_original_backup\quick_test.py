#!/usr/bin/env python3
"""
快速测试脚本
===========

测试BEMT中保真度模块的基本功能
"""

import sys
import os
import numpy as np

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_config_manager():
    """测试配置管理器"""
    print("1. 测试配置管理器...")
    try:
        from utils.config import ConfigManager
        
        config = ConfigManager({
            'R_rotor': 0.4,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 120.0,
            'rho': 1.225
        })
        
        print(f"   ✅ 配置创建成功: R_rotor={config.get('R_rotor')}")
        return True
    except Exception as e:
        print(f"   ❌ 配置管理器失败: {e}")
        return False

def test_geometry():
    """测试几何模块"""
    print("2. 测试几何模块...")
    try:
        from geometry.rotor import RotorGeometry
        
        config = {
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'hub_radius': 0.05
        }
        
        geometry = RotorGeometry(config)
        positions = geometry.get_radial_positions(10)
        
        print(f"   ✅ 几何创建成功: {len(positions)} 个径向位置")
        return True
    except Exception as e:
        print(f"   ❌ 几何模块失败: {e}")
        return False

def test_corrections():
    """测试物理修正"""
    print("3. 测试物理修正...")
    try:
        from physics.corrections import UnifiedPhysicalCorrections
        
        config = {
            'B': 4,
            'enable_tip_loss': True,
            'enable_hub_loss': True
        }
        
        corrections = UnifiedPhysicalCorrections(config)
        
        test_data = {
            'Cl': 1.0,
            'Cd': 0.01,
            'alpha': 0.1,
            'r_R': 0.8
        }
        
        result = corrections.apply_all(test_data)
        
        print(f"   ✅ 物理修正成功: Cl {test_data['Cl']:.3f} -> {result['Cl']:.3f}")
        return True
    except Exception as e:
        print(f"   ❌ 物理修正失败: {e}")
        return False

def test_airfoil_database():
    """测试翼型数据库"""
    print("4. 测试翼型数据库...")
    try:
        from aerodynamics.airfoil_database import AirfoilDatabase
        
        database = AirfoilDatabase()
        
        # 测试系数查询
        Cl, Cd, Cm = database.get_coefficients('NACA0012', 8.0, 100000)
        
        print(f"   ✅ 翼型数据库成功: Cl={Cl:.3f}, Cd={Cd:.4f}")
        return True
    except Exception as e:
        print(f"   ❌ 翼型数据库失败: {e}")
        return False

def test_bemt_solver():
    """测试BEMT求解器"""
    print("5. 测试BEMT求解器...")
    try:
        from core.bemt_solver import BEMTSolver
        from utils.config import ConfigManager
        
        config = ConfigManager({
            'R_rotor': 0.4,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 120.0,
            'rho': 1.225,
            'collective_pitch': 8.0,
            'n_radial_stations': 10,
            'convergence_tolerance': 1e-4,
            'max_iterations': 20
        })
        
        solver = BEMTSolver(config)
        
        # 执行求解
        result = solver.solve()
        
        if result and 'thrust' in result:
            thrust = result['thrust']
            power = result['power']
            converged = result.get('converged', False)
            
            print(f"   ✅ BEMT求解成功:")
            print(f"     推力: {thrust:.2f} N")
            print(f"     功率: {power:.2f} W")
            print(f"     收敛: {'是' if converged else '否'}")
            return True
        else:
            print(f"   ❌ BEMT求解失败: 结果格式错误")
            return False
            
    except Exception as e:
        print(f"   ❌ BEMT求解器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_calculator():
    """测试性能计算器"""
    print("6. 测试性能计算器...")
    try:
        from core.performance_calculator import PerformanceCalculator
        from utils.config import ConfigManager
        
        config = ConfigManager({
            'R_rotor': 0.4,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 120.0,
            'rho': 1.225
        })
        
        calc = PerformanceCalculator(config)
        
        # 测试性能计算
        test_result = {
            'forces': np.array([[10, 0, 100], [10, 0, 100], [10, 0, 100], [10, 0, 100]]),
            'moments': np.array([[0, 0, 4], [0, 0, 4], [0, 0, 4], [0, 0, 4]]),
            'circulation': np.array([0.1, 0.1, 0.1, 0.1])
        }
        
        # 创建简单几何对象
        class MockGeometry:
            pass
        
        geometry = MockGeometry()
        performance = calc.calculate_performance(test_result, geometry, 0.0)
        
        print(f"   ✅ 性能计算成功:")
        print(f"     推力系数: {performance.get('CT', 0):.4f}")
        print(f"     功率系数: {performance.get('CP', 0):.4f}")
        print(f"     效率: {performance.get('efficiency', 0):.3f}")
        return True
        
    except Exception as e:
        print(f"   ❌ 性能计算器失败: {e}")
        return False

def test_convergence_controller():
    """测试收敛控制器"""
    print("7. 测试收敛控制器...")
    try:
        from core.convergence import ConvergenceController
        
        controller = ConvergenceController(
            tolerance=1e-4,
            max_iterations=50,
            relaxation_factor=0.5
        )
        
        # 模拟收敛过程
        old_values = np.array([0.1, 0.2, 0.3])
        new_values = np.array([0.101, 0.201, 0.301])
        
        # 计算残差
        residual = np.linalg.norm(new_values - old_values)
        
        converged = controller.check_convergence(residual)
        
        print(f"   ✅ 收敛控制成功:")
        print(f"     收敛状态: {'是' if converged else '否'}")
        print(f"     残差: {residual:.6f}")
        print(f"     容差: {controller.tolerance:.6f}")
        return True
        
    except Exception as e:
        print(f"   ❌ 收敛控制器失败: {e}")
        return False

def test_integration_workflow():
    """测试完整工作流程"""
    print("8. 测试完整工作流程...")
    try:
        from core.solver_factory import SolverFactory
        from utils.config import ConfigManager
        
        # 创建完整配置
        config = ConfigManager({
            'R_rotor': 0.5,
            'B': 4,
            'c': 0.08,
            'omega_rotor': 100.0,
            'rho': 1.225,
            'collective_pitch': 10.0,
            'n_radial_stations': 15,
            'convergence_tolerance': 1e-4,
            'max_iterations': 30,
            'solver_type': 'bemt_medium_fidelity'
        })
        
        # 使用工厂创建求解器
        factory = SolverFactory()
        solver = factory.create_solver('bemt_medium', config.to_dict())
        
        # 执行完整求解
        result = solver.solve()
        
        if result:
            print(f"   ✅ 完整工作流程成功:")
            print(f"     推力: {result.get('thrust', 0):.2f} N")
            print(f"     功率: {result.get('power', 0):.2f} W")
            print(f"     迭代次数: {result.get('iterations', 0)}")
            return True
        else:
            print(f"   ❌ 完整工作流程失败: 无结果返回")
            return False
            
    except Exception as e:
        print(f"   ❌ 完整工作流程失败: {e}")
        return False

def test_performance_benchmark():
    """性能基准测试"""
    print("9. 性能基准测试...")
    try:
        import time
        from core.bemt_solver import BEMTSolver
        from utils.config import ConfigManager
        
        # 不同复杂度的测试配置
        configs = [
            {'n_radial_stations': 10, 'name': '简单'},
            {'n_radial_stations': 20, 'name': '中等'},
            {'n_radial_stations': 50, 'name': '复杂'}
        ]
        
        print(f"   📊 性能基准测试结果:")
        
        for config_data in configs:
            config = ConfigManager({
                'R_rotor': 0.4,
                'B': 4,
                'c': 0.08,
                'omega_rotor': 120.0,
                'rho': 1.225,
                'collective_pitch': 8.0,
                'n_radial_stations': config_data['n_radial_stations'],
                'convergence_tolerance': 1e-4,
                'max_iterations': 20
            })
            
            solver = BEMTSolver(config)
            
            # 计时求解
            start_time = time.time()
            result = solver.solve()
            end_time = time.time()
            
            solve_time = end_time - start_time
            
            print(f"     {config_data['name']}配置 ({config_data['n_radial_stations']}站): {solve_time:.3f}s")
        
        print(f"   ✅ 性能基准测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 性能基准测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 BEMT中保真度模块快速测试")
    print("=" * 50)
    
    tests = [
        test_config_manager,
        test_geometry,
        test_corrections,
        test_airfoil_database,
        test_bemt_solver,
        test_performance_calculator,
        test_convergence_controller,
        test_integration_workflow,
        test_performance_benchmark
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append(False)
        print()
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    # 详细结果分析
    test_names = [
        "配置管理器", "几何模块", "物理修正", "翼型数据库", 
        "BEMT求解器", "性能计算器", "收敛控制器", "完整工作流程", "性能基准"
    ]
    
    print("\n📋 详细测试报告:")
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    # 生成建议
    print(f"\n💡 建议:")
    if passed == total:
        print("   🎉 所有测试通过！系统完全可用")
        print("   - 可以开始进行实际的旋翼仿真计算")
        print("   - 建议进行更复杂的验证测试")
    elif passed >= total * 0.8:
        print("   ✅ 大部分测试通过，系统基本可用")
        print("   - 可以进行基本的仿真计算")
        print("   - 建议修复失败的测试项")
    elif passed >= total * 0.6:
        print("   ⚠️  部分测试通过，系统部分可用")
        print("   - 建议先修复关键模块")
        print("   - 谨慎使用仿真功能")
    else:
        print("   ❌ 多个测试失败，系统需要修复")
        print("   - 请检查依赖项和配置")
        print("   - 建议逐个修复失败的模块")
    
    if passed == total:
        return 0
    elif passed >= total * 0.6:
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())