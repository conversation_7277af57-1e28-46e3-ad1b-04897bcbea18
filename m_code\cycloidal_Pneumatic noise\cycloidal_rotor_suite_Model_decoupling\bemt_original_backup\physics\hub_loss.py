"""
叶根损失修正
===========

实现叶根损失修正模型。

核心功能：
- 叶根涡流效应修正
- 桂毂损失因子计算

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any
from .corrections import PhysicalCorrectionBase


class HubLossCorrection(PhysicalCorrectionBase):
    """叶根损失修正"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.correction_type = 'hub_loss'
        self.hub_radius_ratio = config.get('hub_radius_ratio', 0.2)
    
    def apply(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """应用叶根损失修正"""
        r = input_data['r']
        R = input_data['R']
        B = input_data['B']
        phi = input_data.get('phi', 0.1)
        
        # 计算叶根损失因子
        hub_loss_factor = self._calculate_hub_loss_factor(r, R, B, phi)
        
        # 应用修正
        result = input_data.copy()
        result['Cl'] = input_data['Cl'] * hub_loss_factor
        result['hub_loss_factor'] = hub_loss_factor
        
        return result
    
    def _calculate_hub_loss_factor(self, r: float, R: float, B: int, phi: float) -> float:
        """计算叶根损失因子"""
        r_R = r / R
        hub_ratio = self.hub_radius_ratio
        
        if r_R <= hub_ratio:
            return 0.0
        
        # 叶根损失修正
        f_arg = B * (r_R - hub_ratio) / (2 * hub_ratio * abs(np.sin(phi)))
        f_arg = max(f_arg, 0.01)
        
        F = (2 / np.pi) * np.arccos(np.exp(-f_arg))
        
        return max(F, 0.1)
    
    def get_correction_info(self) -> Dict[str, Any]:
        """获取修正信息"""
        return {
            'type': self.correction_type,
            'description': '叶根损失修正',
            'parameters': {
                'hub_radius_ratio': self.hub_radius_ratio
            }
        }