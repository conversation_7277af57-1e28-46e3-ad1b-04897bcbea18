"""
误差度量模块
===========

提供各种误差度量方法用于验证分析。
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Union

def calculate_relative_error(computed: Union[float, np.ndarray], 
                           experimental: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """计算相对误差"""
    return np.abs(computed - experimental) / np.abs(experimental) * 100

def calculate_rmse(computed: np.ndarray, experimental: np.ndarray) -> float:
    """计算均方根误差"""
    return np.sqrt(np.mean((computed - experimental)**2))

def calculate_mae(computed: np.ndarray, experimental: np.ndarray) -> float:
    """计算平均绝对误差"""
    return np.mean(np.abs(computed - experimental))

def calculate_correlation_coefficient(computed: np.ndarray, 
                                    experimental: np.ndarray) -> float:
    """计算相关系数"""
    return np.corrcoef(computed, experimental)[0, 1]

class ValidationMetrics:
    """验证度量类"""
    
    def __init__(self):
        self.metrics = {}
    
    def add_comparison(self, name: str, computed: np.ndarray, 
                      experimental: np.ndarray):
        """添加对比数据"""
        self.metrics[name] = {
            'computed': computed,
            'experimental': experimental,
            'relative_error': calculate_relative_error(computed, experimental),
            'rmse': calculate_rmse(computed, experimental),
            'mae': calculate_mae(computed, experimental),
            'correlation': calculate_correlation_coefficient(computed, experimental),
        }
    
    def get_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        summary = {}
        for name, metrics in self.metrics.items():
            summary[name] = {
                'mean_relative_error': np.mean(metrics['relative_error']),
                'max_relative_error': np.max(metrics['relative_error']),
                'rmse': metrics['rmse'],
                'mae': metrics['mae'],
                'correlation': metrics['correlation'],
            }
        return summary