"""
翼型数据库管理器
===============

完整复刻原始模块的翼型数据库功能，支持真实翼型数据的加载和插值。

核心功能：
- 翼型几何数据管理
- 气动力系数插值
- 多雷诺数支持
- XFOIL数据集成
- 高效插值算法

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import os
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
from scipy import interpolate
from scipy.interpolate import griddata, RectBivariateSpline
import json


class AirfoilData:
    """单个翼型数据类"""
    
    def __init__(self, name: str):
        """
        初始化翼型数据
        
        Args:
            name: 翼型名称
        """
        self.name = name
        self.geometry = None  # 翼型几何坐标
        self.polar_data = {}  # 极线数据 {Re: {alpha: [Cl, Cd, Cm]}}
        self.reynolds_numbers = []  # 可用雷诺数列表
        self.alpha_range = (-180, 180)  # 攻角范围
        self.interpolators = {}  # 插值器缓存
        
    def add_polar_data(self, reynolds: float, alpha_deg: np.ndarray, 
                      Cl: np.ndarray, Cd: np.ndarray, Cm: np.ndarray):
        """
        添加极线数据
        
        Args:
            reynolds: 雷诺数
            alpha_deg: 攻角数组 [度]
            Cl, Cd, Cm: 气动力系数数组
        """
        self.polar_data[reynolds] = {
            'alpha': alpha_deg,
            'Cl': Cl,
            'Cd': Cd,
            'Cm': Cm
        }
        
        if reynolds not in self.reynolds_numbers:
            self.reynolds_numbers.append(reynolds)
            self.reynolds_numbers.sort()
        
        # 更新攻角范围
        self.alpha_range = (min(self.alpha_range[0], np.min(alpha_deg)),
                           max(self.alpha_range[1], np.max(alpha_deg)))
        
        # 清除插值器缓存
        self.interpolators.clear()
    
    def get_coefficients(self, alpha_deg: float, reynolds: float) -> Tuple[float, float, float]:
        """
        获取指定攻角和雷诺数的气动力系数
        
        Args:
            alpha_deg: 攻角 [度]
            reynolds: 雷诺数
            
        Returns:
            Cl, Cd, Cm
        """
        if not self.polar_data:
            raise ValueError(f"翼型 {self.name} 没有极线数据")
        
        # 限制攻角范围
        alpha_deg = np.clip(alpha_deg, self.alpha_range[0], self.alpha_range[1])
        
        # 如果只有一个雷诺数，直接插值
        if len(self.reynolds_numbers) == 1:
            Re = self.reynolds_numbers[0]
            return self._interpolate_single_reynolds(alpha_deg, Re)
        
        # 多雷诺数插值
        return self._interpolate_multi_reynolds(alpha_deg, reynolds)
    
    def _interpolate_single_reynolds(self, alpha_deg: float, reynolds: float) -> Tuple[float, float, float]:
        """单雷诺数插值"""
        data = self.polar_data[reynolds]
        
        # 创建插值器（如果不存在）
        if reynolds not in self.interpolators:
            self.interpolators[reynolds] = {
                'Cl': interpolate.interp1d(data['alpha'], data['Cl'], 
                                         kind='cubic', bounds_error=False, fill_value='extrapolate'),
                'Cd': interpolate.interp1d(data['alpha'], data['Cd'], 
                                         kind='cubic', bounds_error=False, fill_value='extrapolate'),
                'Cm': interpolate.interp1d(data['alpha'], data['Cm'], 
                                         kind='cubic', bounds_error=False, fill_value='extrapolate')
            }
        
        interp = self.interpolators[reynolds]
        Cl = float(interp['Cl'](alpha_deg))
        Cd = float(interp['Cd'](alpha_deg))
        Cm = float(interp['Cm'](alpha_deg))
        
        return Cl, Cd, Cm
    
    def _interpolate_multi_reynolds(self, alpha_deg: float, reynolds: float) -> Tuple[float, float, float]:
        """多雷诺数插值"""
        # 找到最近的两个雷诺数
        Re_array = np.array(self.reynolds_numbers)
        
        if reynolds <= Re_array[0]:
            # 低于最小雷诺数，使用最小雷诺数数据
            return self._interpolate_single_reynolds(alpha_deg, Re_array[0])
        elif reynolds >= Re_array[-1]:
            # 高于最大雷诺数，使用最大雷诺数数据
            return self._interpolate_single_reynolds(alpha_deg, Re_array[-1])
        else:
            # 在范围内，进行双线性插值
            idx = np.searchsorted(Re_array, reynolds)
            Re_low = Re_array[idx-1]
            Re_high = Re_array[idx]
            
            # 获取两个雷诺数的系数
            Cl_low, Cd_low, Cm_low = self._interpolate_single_reynolds(alpha_deg, Re_low)
            Cl_high, Cd_high, Cm_high = self._interpolate_single_reynolds(alpha_deg, Re_high)
            
            # 雷诺数插值权重
            weight = (reynolds - Re_low) / (Re_high - Re_low)
            
            # 线性插值
            Cl = Cl_low + weight * (Cl_high - Cl_low)
            Cd = Cd_low + weight * (Cd_high - Cd_low)
            Cm = Cm_low + weight * (Cm_high - Cm_low)
            
            return Cl, Cd, Cm
    
    def set_geometry(self, x_coords: np.ndarray, y_coords: np.ndarray):
        """设置翼型几何"""
        self.geometry = {
            'x': x_coords,
            'y': y_coords
        }
    
    def get_info(self) -> Dict[str, Any]:
        """获取翼型信息"""
        return {
            'name': self.name,
            'reynolds_numbers': self.reynolds_numbers,
            'alpha_range': self.alpha_range,
            'has_geometry': self.geometry is not None,
            'data_points': sum(len(data['alpha']) for data in self.polar_data.values())
        }


class AirfoilDatabase:
    """
    翼型数据库管理器
    
    管理多个翼型的数据，提供统一的查询接口。
    """
    
    def __init__(self, data_dir: Optional[str] = None):
        """
        初始化翼型数据库
        
        Args:
            data_dir: 数据目录路径
        """
        if data_dir is None:
            # 默认数据目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.data_dir = os.path.join(current_dir, "../data/airfoils")
        else:
            self.data_dir = data_dir
        
        # 翼型数据存储
        self.airfoils: Dict[str, AirfoilData] = {}
        self.default_airfoil = "NACA0012"
        
        # 支持的翼型列表
        self.supported_airfoils = [
            "NACA0006", "NACA0008", "NACA0009", "NACA0010", 
            "NACA0012", "NACA0015", "NACA0018", "NACA0021",
            "NACA2412", "NACA4412", "NACA6412"
        ]
        
        # 加载翼型数据
        self._load_all_airfoils()
        
        print(f"翼型数据库初始化完成")
        print(f"  数据目录: {self.data_dir}")
        print(f"  可用翼型: {len(self.airfoils)}")
        print(f"  默认翼型: {self.default_airfoil}")
    
    def _load_all_airfoils(self):
        """加载所有翼型数据"""
        for airfoil_name in self.supported_airfoils:
            try:
                self._load_airfoil_data(airfoil_name)
            except Exception as e:
                warnings.warn(f"加载翼型 {airfoil_name} 失败: {e}")
                
                # 为默认翼型创建理论数据
                if airfoil_name == self.default_airfoil:
                    self._create_theoretical_airfoil(airfoil_name)
    
    def _load_airfoil_data(self, airfoil_name: str):
        """
        加载单个翼型数据
        
        Args:
            airfoil_name: 翼型名称
        """
        airfoil_dir = os.path.join(self.data_dir, airfoil_name)
        
        if not os.path.exists(airfoil_dir):
            # 尝试创建理论数据
            self._create_theoretical_airfoil(airfoil_name)
            return
        
        airfoil = AirfoilData(airfoil_name)
        
        # 加载几何数据
        geometry_file = os.path.join(airfoil_dir, f"{airfoil_name}.dat")
        if os.path.exists(geometry_file):
            self._load_geometry_data(airfoil, geometry_file)
        
        # 加载极线数据
        polar_files = [f for f in os.listdir(airfoil_dir) if f.endswith('.pol')]
        for polar_file in polar_files:
            polar_path = os.path.join(airfoil_dir, polar_file)
            self._load_polar_data(airfoil, polar_path)
        
        if airfoil.polar_data:
            self.airfoils[airfoil_name] = airfoil
            print(f"加载翼型: {airfoil_name} ({len(airfoil.reynolds_numbers)} 个雷诺数)")
        else:
            warnings.warn(f"翼型 {airfoil_name} 没有有效的极线数据")
    
    def _load_geometry_data(self, airfoil: AirfoilData, geometry_file: str):
        """加载翼型几何数据"""
        try:
            data = np.loadtxt(geometry_file, skiprows=1)
            airfoil.set_geometry(data[:, 0], data[:, 1])
        except Exception as e:
            warnings.warn(f"加载几何文件失败 {geometry_file}: {e}")
    
    def _load_polar_data(self, airfoil: AirfoilData, polar_file: str):
        """加载极线数据"""
        try:
            # 解析文件名获取雷诺数
            filename = os.path.basename(polar_file)
            reynolds = self._extract_reynolds_from_filename(filename)
            
            # 读取极线数据
            data = np.loadtxt(polar_file, skiprows=10)  # 跳过XFOIL头部
            
            if data.shape[1] >= 3:
                alpha = data[:, 0]
                Cl = data[:, 1]
                Cd = data[:, 2]
                Cm = data[:, 4] if data.shape[1] > 4 else np.zeros_like(Cl)
                
                airfoil.add_polar_data(reynolds, alpha, Cl, Cd, Cm)
            
        except Exception as e:
            warnings.warn(f"加载极线文件失败 {polar_file}: {e}")
    
    def _extract_reynolds_from_filename(self, filename: str) -> float:
        """从文件名提取雷诺数"""
        # 常见的雷诺数格式：Re50k.pol, Re100000.pol, etc.
        import re
        
        # 匹配 Re 后面的数字
        match = re.search(r'Re(\d+)([kKmM]?)', filename)
        if match:
            number = float(match.group(1))
            unit = match.group(2).lower() if match.group(2) else ''
            
            if unit == 'k':
                return number * 1000
            elif unit == 'm':
                return number * 1000000
            else:
                return number
        
        # 默认雷诺数
        return 100000.0
    
    def _create_theoretical_airfoil(self, airfoil_name: str):
        """创建理论翼型数据"""
        airfoil = AirfoilData(airfoil_name)
        
        # 生成理论极线数据
        reynolds_list = [50000, 100000, 200000, 500000, 1000000]
        alpha_range = np.linspace(-20, 20, 81)
        
        for Re in reynolds_list:
            Cl, Cd, Cm = self._calculate_theoretical_coefficients(alpha_range, Re, airfoil_name)
            airfoil.add_polar_data(Re, alpha_range, Cl, Cd, Cm)
        
        # 生成理论几何（NACA翼型）
        if airfoil_name.startswith('NACA') and len(airfoil_name) == 8:
            x_coords, y_coords = self._generate_naca_geometry(airfoil_name)
            airfoil.set_geometry(x_coords, y_coords)
        
        self.airfoils[airfoil_name] = airfoil
        print(f"创建理论翼型: {airfoil_name}")
    
    def _calculate_theoretical_coefficients(self, alpha_deg: np.ndarray, 
                                          reynolds: float, airfoil_name: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """计算理论气动力系数"""
        alpha_rad = np.radians(alpha_deg)
        
        # 基于薄翼理论的升力系数
        Cl = 2 * np.pi * alpha_rad
        
        # 失速修正
        alpha_stall = 15.0  # 失速攻角
        stall_mask = np.abs(alpha_deg) > alpha_stall
        
        if np.any(stall_mask):
            # 失速后的升力
            Cl[stall_mask] = np.sign(alpha_deg[stall_mask]) * (
                1.4 - 0.4 * np.cos(2 * alpha_rad[stall_mask])
            )
        
        # 阻力系数
        Cd_0 = 0.008  # 零升阻力
        k = 0.05  # 诱导阻力因子
        
        # 雷诺数修正
        Re_ref = 100000
        Re_factor = (reynolds / Re_ref) ** (-0.2)
        Cd_0 *= Re_factor
        
        Cd = Cd_0 + k * alpha_rad**2
        
        # 失速后阻力增加
        if np.any(stall_mask):
            Cd[stall_mask] += 0.5 * (1 - np.cos(2 * alpha_rad[stall_mask]))
        
        # 力矩系数
        Cm = -0.25 * Cl  # 简化的力矩系数
        
        return Cl, Cd, Cm
    
    def _generate_naca_geometry(self, airfoil_name: str) -> Tuple[np.ndarray, np.ndarray]:
        """生成NACA翼型几何"""
        # 提取NACA参数
        if len(airfoil_name) == 8 and airfoil_name.startswith('NACA'):
            digits = airfoil_name[4:]
            thickness = int(digits[2:]) / 100.0  # 厚度比
        else:
            thickness = 0.12  # 默认12%厚度
        
        # 生成坐标点
        x = np.linspace(0, 1, 101)
        
        # NACA对称翼型厚度分布
        yt = 5 * thickness * (
            0.2969 * np.sqrt(x) - 
            0.1260 * x - 
            0.3516 * x**2 + 
            0.2843 * x**3 - 
            0.1015 * x**4
        )
        
        # 上下表面
        x_coords = np.concatenate([x, x[::-1]])
        y_coords = np.concatenate([yt, -yt[::-1]])
        
        return x_coords, y_coords
    
    def get_coefficients(self, airfoil_name: str, alpha_deg: float, 
                        reynolds_number: float) -> Tuple[float, float, float]:
        """
        获取气动力系数
        
        Args:
            airfoil_name: 翼型名称
            alpha_deg: 攻角 [度]
            reynolds_number: 雷诺数
            
        Returns:
            Cl, Cd, Cm
        """
        # 检查翼型是否存在
        if airfoil_name not in self.airfoils:
            if airfoil_name in self.supported_airfoils:
                # 尝试加载
                self._load_airfoil_data(airfoil_name)
            
            if airfoil_name not in self.airfoils:
                # 使用默认翼型
                warnings.warn(f"翼型 {airfoil_name} 不存在，使用默认翼型 {self.default_airfoil}")
                airfoil_name = self.default_airfoil
        
        airfoil = self.airfoils[airfoil_name]
        return airfoil.get_coefficients(alpha_deg, reynolds_number)
    
    def get_airfoil_list(self) -> List[str]:
        """获取可用翼型列表"""
        return list(self.airfoils.keys())
    
    def get_airfoil_info(self, airfoil_name: str) -> Dict[str, Any]:
        """获取翼型信息"""
        if airfoil_name not in self.airfoils:
            raise ValueError(f"翼型 {airfoil_name} 不存在")
        
        return self.airfoils[airfoil_name].get_info()
    
    def add_custom_airfoil(self, airfoil_name: str, alpha_deg: np.ndarray,
                          Cl: np.ndarray, Cd: np.ndarray, Cm: np.ndarray,
                          reynolds: float = 100000):
        """
        添加自定义翼型数据
        
        Args:
            airfoil_name: 翼型名称
            alpha_deg: 攻角数组 [度]
            Cl, Cd, Cm: 气动力系数数组
            reynolds: 雷诺数
        """
        if airfoil_name in self.airfoils:
            airfoil = self.airfoils[airfoil_name]
        else:
            airfoil = AirfoilData(airfoil_name)
            self.airfoils[airfoil_name] = airfoil
        
        airfoil.add_polar_data(reynolds, alpha_deg, Cl, Cd, Cm)
        print(f"添加自定义翼型数据: {airfoil_name} (Re={reynolds:.0f})")
    
    def export_airfoil_data(self, airfoil_name: str, output_dir: str):
        """导出翼型数据"""
        if airfoil_name not in self.airfoils:
            raise ValueError(f"翼型 {airfoil_name} 不存在")
        
        airfoil = self.airfoils[airfoil_name]
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 导出极线数据
        for reynolds in airfoil.reynolds_numbers:
            data = airfoil.polar_data[reynolds]
            filename = os.path.join(output_dir, f"{airfoil_name}_Re{reynolds:.0f}.dat")
            
            # 组织数据
            export_data = np.column_stack([
                data['alpha'], data['Cl'], data['Cd'], data['Cm']
            ])
            
            # 写入文件
            header = f"# {airfoil_name} polar data, Re={reynolds:.0f}\n"
            header += "# Alpha(deg)  Cl  Cd  Cm"
            
            np.savetxt(filename, export_data, header=header, fmt='%10.6f')
        
        # 导出几何数据
        if airfoil.geometry is not None:
            geom_file = os.path.join(output_dir, f"{airfoil_name}_geometry.dat")
            geom_data = np.column_stack([airfoil.geometry['x'], airfoil.geometry['y']])
            
            header = f"# {airfoil_name} geometry coordinates\n# x  y"
            np.savetxt(geom_file, geom_data, header=header, fmt='%10.6f')
        
        print(f"翼型数据已导出到: {output_dir}")
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        total_data_points = 0
        reynolds_range = [float('inf'), 0]
        alpha_range = [float('inf'), -float('inf')]
        
        for airfoil in self.airfoils.values():
            info = airfoil.get_info()
            total_data_points += info['data_points']
            
            if airfoil.reynolds_numbers:
                reynolds_range[0] = min(reynolds_range[0], min(airfoil.reynolds_numbers))
                reynolds_range[1] = max(reynolds_range[1], max(airfoil.reynolds_numbers))
            
            alpha_range[0] = min(alpha_range[0], airfoil.alpha_range[0])
            alpha_range[1] = max(alpha_range[1], airfoil.alpha_range[1])
        
        return {
            'total_airfoils': len(self.airfoils),
            'total_data_points': total_data_points,
            'reynolds_range': reynolds_range if reynolds_range[0] != float('inf') else [0, 0],
            'alpha_range': alpha_range if alpha_range[0] != float('inf') else [0, 0],
            'supported_airfoils': len(self.supported_airfoils),
            'default_airfoil': self.default_airfoil
        }