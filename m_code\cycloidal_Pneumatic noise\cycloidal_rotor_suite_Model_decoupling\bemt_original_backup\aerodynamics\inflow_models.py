"""
入流模型
=======

实现不同的入流模型，用于计算桨盘面的诱导速度分布。

核心功能：
- 均匀入流模型
- 非均匀入流模型
- 动态入流模型
- 前飞入流修正

作者: Kiro AI Assistant
日期: 2025-01-28
"""

import numpy as np
from typing import Dict, Any, Tuple, Optional
from abc import ABC, abstractmethod
import warnings


class InflowModel(ABC):
    """入流模型基类"""
    
    @abstractmethod
    def calculate_inflow(self, thrust: float, radius_positions: np.ndarray,
                        azimuth_positions: np.ndarray, **kwargs) -> np.ndarray:
        """
        计算入流速度
        
        Args:
            thrust: 推力 [N]
            radius_positions: 径向位置数组 [m]
            azimuth_positions: 方位角位置数组 [rad]
            **kwargs: 其他参数
            
        Returns:
            入流速度数组 [m/s]
        """
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass


class UniformInflow(InflowModel):
    """
    均匀入流模型
    
    基于动量理论的简单均匀入流模型。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化均匀入流模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.rho = config.get('rho', 1.225)  # 空气密度
        self.R_rotor = config.get('R_rotor', 0.3)  # 转子半径
        self.disk_area = np.pi * self.R_rotor**2
        
        # 修正因子
        self.tip_loss_factor = config.get('tip_loss_factor', 0.97)
        self.wake_skew_factor = config.get('wake_skew_factor', 1.0)
        
        print("均匀入流模型初始化完成")
    
    def calculate_inflow(self, thrust: float, radius_positions: np.ndarray,
                        azimuth_positions: np.ndarray, **kwargs) -> np.ndarray:
        """
        计算均匀入流速度
        
        基于理想动量理论：v_i = sqrt(T / (2 * ρ * A))
        """
        # 基本动量理论
        if thrust > 0:
            v_ideal = np.sqrt(thrust / (2 * self.rho * self.disk_area))
        else:
            v_ideal = 0.0
        
        # 应用修正因子
        v_uniform = v_ideal * self.tip_loss_factor * self.wake_skew_factor
        
        # 返回均匀分布
        n_points = len(radius_positions)
        inflow_velocities = np.full(n_points, v_uniform)
        
        return inflow_velocities
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': 'uniform_inflow',
            'description': '基于动量理论的均匀入流模型',
            'parameters': {
                'tip_loss_factor': self.tip_loss_factor,
                'wake_skew_factor': self.wake_skew_factor
            }
        }


class NonUniformInflow(InflowModel):
    """
    非均匀入流模型
    
    考虑径向和方位角变化的非均匀入流分布。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化非均匀入流模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.rho = config.get('rho', 1.225)
        self.R_rotor = config.get('R_rotor', 0.3)
        self.disk_area = np.pi * self.R_rotor**2
        
        # 非均匀性参数
        self.radial_variation = config.get('radial_inflow_variation', 0.2)
        self.azimuthal_variation = config.get('azimuthal_inflow_variation', 0.1)
        
        # 前飞参数
        self.forward_velocity = config.get('forward_velocity', 0.0)
        self.advance_ratio = self.forward_velocity / (config.get('omega_rotor', 100.0) * self.R_rotor)
        
        print("非均匀入流模型初始化完成")
        print(f"  径向变化: {self.radial_variation:.1%}")
        print(f"  方位角变化: {self.azimuthal_variation:.1%}")
        print(f"  前进比: {self.advance_ratio:.3f}")
    
    def calculate_inflow(self, thrust: float, radius_positions: np.ndarray,
                        azimuth_positions: np.ndarray, **kwargs) -> np.ndarray:
        """
        计算非均匀入流速度
        
        包含径向和方位角变化
        """
        # 基础均匀入流
        if thrust > 0:
            v_uniform = np.sqrt(thrust / (2 * self.rho * self.disk_area))
        else:
            v_uniform = 0.0
        
        # 径向变化
        r_R = radius_positions / self.R_rotor
        radial_factor = 1.0 + self.radial_variation * (1.0 - r_R)
        
        # 方位角变化（前飞效应）
        azimuthal_factor = 1.0 + self.azimuthal_variation * np.cos(azimuth_positions)
        
        # 前飞修正
        forward_factor = self._calculate_forward_flight_correction(
            r_R, azimuth_positions
        )
        
        # 组合所有效应
        inflow_velocities = v_uniform * radial_factor * azimuthal_factor * forward_factor
        
        return inflow_velocities
    
    def _calculate_forward_flight_correction(self, r_R: np.ndarray, 
                                           azimuth: np.ndarray) -> np.ndarray:
        """计算前飞修正因子"""
        if self.advance_ratio < 1e-6:
            return np.ones_like(r_R)
        
        # 简化的前飞修正
        # 基于Glauert的前飞理论
        mu = self.advance_ratio
        
        # 方位角相关的修正
        cos_psi = np.cos(azimuth)
        sin_psi = np.sin(azimuth)
        
        # 前飞诱导速度修正
        correction = 1.0 + mu * cos_psi * (1.0 + 0.5 * mu * cos_psi)
        
        return correction
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': 'non_uniform_inflow',
            'description': '考虑径向和方位角变化的非均匀入流模型',
            'parameters': {
                'radial_variation': self.radial_variation,
                'azimuthal_variation': self.azimuthal_variation,
                'advance_ratio': self.advance_ratio
            }
        }


class DynamicInflow(InflowModel):
    """
    动态入流模型
    
    考虑入流的时间滞后效应。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化动态入流模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.rho = config.get('rho', 1.225)
        self.R_rotor = config.get('R_rotor', 0.3)
        self.disk_area = np.pi * self.R_rotor**2
        self.omega_rotor = config.get('omega_rotor', 100.0)
        
        # 动态参数
        self.time_constant = config.get('inflow_time_constant', 0.1)  # 时间常数 [s]
        self.damping_ratio = config.get('inflow_damping_ratio', 0.7)  # 阻尼比
        
        # 状态变量
        self.inflow_state = 0.0  # 当前入流状态
        self.inflow_rate = 0.0   # 入流变化率
        
        # 历史数据
        self.thrust_history = []
        self.inflow_history = []
        
        print("动态入流模型初始化完成")
        print(f"  时间常数: {self.time_constant:.3f}s")
        print(f"  阻尼比: {self.damping_ratio:.2f}")
    
    def calculate_inflow(self, thrust: float, radius_positions: np.ndarray,
                        azimuth_positions: np.ndarray, **kwargs) -> np.ndarray:
        """
        计算动态入流速度
        
        使用二阶动态系统模拟入流响应
        """
        dt = kwargs.get('dt', 0.001)
        
        # 目标入流速度（稳态值）
        if thrust > 0:
            v_target = np.sqrt(thrust / (2 * self.rho * self.disk_area))
        else:
            v_target = 0.0
        
        # 动态响应（二阶系统）
        omega_n = 1.0 / self.time_constant  # 自然频率
        zeta = self.damping_ratio
        
        # 状态空间形式：[inflow, inflow_rate]
        # dx/dt = [0, 1; -omega_n^2, -2*zeta*omega_n] * x + [0; omega_n^2] * u
        
        # 计算加速度
        inflow_acceleration = (omega_n**2 * (v_target - self.inflow_state) - 
                             2 * zeta * omega_n * self.inflow_rate)
        
        # 积分更新状态
        self.inflow_rate += inflow_acceleration * dt
        self.inflow_state += self.inflow_rate * dt
        
        # 更新历史
        self.thrust_history.append(thrust)
        self.inflow_history.append(self.inflow_state)
        
        # 限制历史长度
        max_history = 100
        if len(self.thrust_history) > max_history:
            self.thrust_history.pop(0)
            self.inflow_history.pop(0)
        
        # 返回均匀分布的动态入流
        n_points = len(radius_positions)
        inflow_velocities = np.full(n_points, max(0.0, self.inflow_state))
        
        return inflow_velocities
    
    def reset_state(self):
        """重置动态状态"""
        self.inflow_state = 0.0
        self.inflow_rate = 0.0
        self.thrust_history.clear()
        self.inflow_history.clear()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': 'dynamic_inflow',
            'description': '考虑时间滞后效应的动态入流模型',
            'parameters': {
                'time_constant': self.time_constant,
                'damping_ratio': self.damping_ratio,
                'current_state': self.inflow_state,
                'current_rate': self.inflow_rate
            }
        }


class PetersHeInflow(InflowModel):
    """
    Peters-He动态入流模型
    
    基于Peters和He的动态入流理论。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Peters-He模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.rho = config.get('rho', 1.225)
        self.R_rotor = config.get('R_rotor', 0.3)
        self.disk_area = np.pi * self.R_rotor**2
        self.omega_rotor = config.get('omega_rotor', 100.0)
        
        # Peters-He模型参数
        self.n_modes = config.get('peters_he_modes', 3)  # 模态数量
        
        # 状态变量（入流模态）
        self.lambda_states = np.zeros(self.n_modes)
        
        # 模型系数
        self._initialize_coefficients()
        
        print(f"Peters-He动态入流模型初始化完成 ({self.n_modes} 个模态)")
    
    def _initialize_coefficients(self):
        """初始化Peters-He模型系数"""
        # 简化的系数（实际应用中需要更精确的值）
        self.L_matrix = np.eye(self.n_modes) * 0.5  # 质量矩阵
        self.M_matrix = np.diag([1.0, 2.0, 3.0][:self.n_modes])  # 刚度矩阵
        
        # 形状函数系数
        self.shape_coefficients = np.ones(self.n_modes)
    
    def calculate_inflow(self, thrust: float, radius_positions: np.ndarray,
                        azimuth_positions: np.ndarray, **kwargs) -> np.ndarray:
        """
        计算Peters-He动态入流
        
        使用模态叠加方法
        """
        dt = kwargs.get('dt', 0.001)
        
        # 计算载荷分布（简化）
        if thrust > 0:
            load_distribution = thrust / self.disk_area * np.ones_like(radius_positions)
        else:
            load_distribution = np.zeros_like(radius_positions)
        
        # 更新模态状态
        self._update_modal_states(load_distribution, dt)
        
        # 重构入流分布
        inflow_velocities = self._reconstruct_inflow(radius_positions, azimuth_positions)
        
        return inflow_velocities
    
    def _update_modal_states(self, load_distribution: np.ndarray, dt: float):
        """更新模态状态"""
        # 简化的模态更新（实际需要更复杂的计算）
        for i in range(self.n_modes):
            # 模态力
            modal_force = np.mean(load_distribution) * self.shape_coefficients[i]
            
            # 简单的一阶动态响应
            time_constant = 1.0 / self.M_matrix[i, i]
            self.lambda_states[i] += dt * (modal_force - self.lambda_states[i]) / time_constant
    
    def _reconstruct_inflow(self, radius_positions: np.ndarray, 
                          azimuth_positions: np.ndarray) -> np.ndarray:
        """重构入流分布"""
        r_R = radius_positions / self.R_rotor
        inflow_velocities = np.zeros_like(radius_positions)
        
        # 模态叠加
        for i in range(self.n_modes):
            # 径向形状函数（简化）
            if i == 0:
                shape_function = np.ones_like(r_R)  # 均匀模态
            elif i == 1:
                shape_function = 2 * r_R - 1  # 线性模态
            else:
                shape_function = np.cos(i * np.pi * r_R)  # 高阶模态
            
            inflow_velocities += self.lambda_states[i] * shape_function
        
        return np.maximum(inflow_velocities, 0.0)  # 确保非负
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': 'peters_he_inflow',
            'description': 'Peters-He动态入流模型',
            'parameters': {
                'n_modes': self.n_modes,
                'modal_states': self.lambda_states.tolist()
            }
        }


def create_inflow_model(model_type: str, config: Dict[str, Any]) -> InflowModel:
    """
    创建入流模型
    
    Args:
        model_type: 模型类型
        config: 配置参数
        
    Returns:
        入流模型实例
    """
    models = {
        'uniform': UniformInflow,
        'non_uniform': NonUniformInflow,
        'dynamic': DynamicInflow,
        'peters_he': PetersHeInflow
    }
    
    if model_type not in models:
        available_types = list(models.keys())
        raise ValueError(f"不支持的入流模型: {model_type}. 可用类型: {available_types}")
    
    return models[model_type](config)