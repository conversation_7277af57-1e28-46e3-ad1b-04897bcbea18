#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行清理操作脚本
===============

自动执行原始目录的清理操作。

作者: Augment Agent
日期: 2025-07-28
"""

import os
import shutil
from pathlib import Path
import json
from datetime import datetime


def execute_automatic_cleanup():
    """执行自动清理"""
    
    print("🧹 执行自动清理操作")
    print("=" * 60)
    
    # 路径设置
    base_path = Path(__file__).parent.parent.parent
    original_path = base_path / "bemt_medium_fidelity_validation"
    backup_path = base_path / "bemt_original_backup"
    
    print(f"原始目录: {original_path}")
    print(f"备份目录: {backup_path}")
    
    # 创建备份
    if not backup_path.exists():
        print("💾 创建备份...")
        shutil.copytree(original_path, backup_path)
        print(f"   备份已创建: {backup_path}")
    else:
        print("   备份已存在，跳过")
    
    cleanup_stats = {
        'files_deleted': 0,
        'directories_removed': 0,
        'files_preserved': 0,
        'errors': []
    }
    
    # 1. 删除缓存文件
    print("\n🗑️  删除缓存和临时文件...")
    cache_patterns = ['**/__pycache__', '**/*.pyc', '**/*.pyo']
    
    for pattern in cache_patterns:
        for item in original_path.glob(pattern):
            try:
                if item.is_file():
                    item.unlink()
                    cleanup_stats['files_deleted'] += 1
                elif item.is_dir():
                    shutil.rmtree(item)
                    cleanup_stats['directories_removed'] += 1
                print(f"   删除: {item.relative_to(original_path)}")
            except Exception as e:
                cleanup_stats['errors'].append(f"删除失败 {item}: {e}")
    
    # 2. 删除备份文件
    print("\n🗑️  删除备份文件...")
    backup_files = [
        'aerodynamics/blade_element_backup.py',
        'physics/corrections_backup.py'
    ]
    
    for backup_file in backup_files:
        file_path = original_path / backup_file
        if file_path.exists():
            try:
                file_path.unlink()
                cleanup_stats['files_deleted'] += 1
                print(f"   删除备份: {backup_file}")
            except Exception as e:
                cleanup_stats['errors'].append(f"删除备份失败 {backup_file}: {e}")
    
    # 3. 整理文档
    print("\n📚 整理文档...")
    doc_archive_path = original_path / "docs_archive"
    doc_archive_path.mkdir(exist_ok=True)
    
    docs_to_archive = [
        'FINAL_ORGANIZATION_COMPLETION_REPORT.md',
        'PROJECT_COMPLETION_SUMMARY.md', 
        'FINAL_VALIDATION_REPORT.md',
        'advice1.md'
    ]
    
    for doc_file in docs_to_archive:
        source_path = original_path / doc_file
        if source_path.exists():
            try:
                target_path = doc_archive_path / doc_file
                shutil.move(str(source_path), str(target_path))
                cleanup_stats['files_preserved'] += 1
                print(f"   归档文档: {doc_file}")
            except Exception as e:
                cleanup_stats['errors'].append(f"归档失败 {doc_file}: {e}")
    
    # 4. 删除空目录
    print("\n📁 删除空目录...")
    for root, dirs, files in os.walk(original_path, topdown=False):
        root_path = Path(root)
        if root_path != original_path:
            try:
                if not any(root_path.iterdir()):
                    root_path.rmdir()
                    cleanup_stats['directories_removed'] += 1
                    print(f"   删除空目录: {root_path.relative_to(original_path)}")
            except OSError:
                pass
    
    # 生成清理报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'cleanup_stats': cleanup_stats,
        'backup_location': str(backup_path)
    }
    
    # 保存报告
    report_path = original_path / "cleanup_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 清理完成:")
    print(f"   删除文件: {cleanup_stats['files_deleted']}")
    print(f"   删除目录: {cleanup_stats['directories_removed']}")
    print(f"   保留文档: {cleanup_stats['files_preserved']}")
    print(f"   错误数量: {len(cleanup_stats['errors'])}")
    
    if cleanup_stats['errors']:
        print(f"\n❌ 错误:")
        for error in cleanup_stats['errors']:
            print(f"   {error}")
    
    print(f"\n✅ 清理报告已保存: {report_path}")
    
    return report


if __name__ == "__main__":
    report = execute_automatic_cleanup()
