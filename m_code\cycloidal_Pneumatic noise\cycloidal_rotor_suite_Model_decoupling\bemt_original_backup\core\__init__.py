"""
BEMT核心求解器模块
================

包含BEMT求解器的核心算法实现，完整复刻原始模块功能。

模块内容：
- bemt_solver.py: 主要的BEMT求解器
- solver_factory.py: 求解器工厂类
- convergence.py: 收敛控制算法
- time_integration.py: 时间积分方法
- performance_calculator.py: 性能计算器

作者: Kiro AI Assistant
日期: 2025-01-28
"""

from .bemt_solver import BEMTSolver
from .solver_factory import SolverFactory
from .convergence import ConvergenceController
from .time_integration import TimeIntegrator
from .performance_calculator import PerformanceCalculator

__all__ = [
    'BEMTSolver',
    'SolverFactory',
    'ConvergenceController', 
    'TimeIntegrator',
    'PerformanceCalculator'
]