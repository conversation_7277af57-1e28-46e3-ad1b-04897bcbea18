#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UH-60黑鹰直升机BEMT验证测试
==========================

使用真实的UH-60参数进行完整的BEMT求解验证。

作者: Augment Agent
日期: 2025-07-28
"""

import sys
import os
import numpy as np
import time
from pathlib import Path

# 添加重构模块路径
bemt_refactored_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(bemt_refactored_path.parent))

import bemt_refactored as bemt


class UH60ValidationTest:
    """UH-60验证测试类"""
    
    def __init__(self):
        """初始化UH-60参数"""
        
        # UH-60黑鹰直升机真实参数
        self.uh60_params = {
            'radius': 8.18,           # 旋翼半径 [m]
            'hub_radius': 0.61,       # 桂毂半径 [m]
            'num_blades': 4,          # 桨叶数量
            'chord_root': 0.533,      # 根部弦长 [m]
            'chord_tip': 0.356,       # 叶尖弦长 [m]
            'twist_root': -8.0,       # 根部扭转角 [度]
            'twist_tip': -18.0,       # 叶尖扭转角 [度]
            'airfoil': 'sc1095',      # 翼型
            'solidity': 0.082,        # 实度比
            'disk_loading': 287.0,    # 盘载荷 [N/m²]
            'max_gross_weight': 83500 # 最大起飞重量 [N]
        }
        
        # 测试飞行条件
        self.test_conditions = {
            'hover': {
                'rpm': 258,
                'forward_speed': 0.0,
                'vertical_speed': 0.0,
                'density': 1.225,
                'description': '海平面悬停'
            },
            'forward_flight': {
                'rpm': 258,
                'forward_speed': 77.2,  # 150 knots
                'vertical_speed': 0.0,
                'density': 1.225,
                'description': '前飞150节'
            },
            'climb': {
                'rpm': 258,
                'forward_speed': 41.2,  # 80 knots
                'vertical_speed': 5.0,  # 1000 fpm
                'density': 1.225,
                'description': '爬升80节'
            },
            'high_altitude': {
                'rpm': 258,
                'forward_speed': 51.4,  # 100 knots
                'vertical_speed': 0.0,
                'density': 0.909,      # 3000m高度
                'description': '高空100节'
            }
        }
        
        # 期望的性能范围（基于实际数据）
        self.expected_performance = {
            'hover': {
                'thrust_range': (60000, 85000),    # N
                'power_range': (1200, 1800),       # kW
                'figure_of_merit_range': (0.6, 0.8)
            },
            'forward_flight': {
                'thrust_range': (50000, 75000),    # N
                'power_range': (1000, 1500),       # kW
            },
            'climb': {
                'thrust_range': (65000, 90000),    # N
                'power_range': (1400, 2000),       # kW
            },
            'high_altitude': {
                'thrust_range': (45000, 70000),    # N
                'power_range': (1100, 1600),       # kW
            }
        }
    
    def create_uh60_solver(self, enable_advanced_features: bool = True) -> bemt.SimpleBEMT:
        """创建UH-60求解器"""
        
        solver = bemt.SimpleBEMT(
            radius=self.uh60_params['radius'],
            hub_radius=self.uh60_params['hub_radius'],
            num_blades=self.uh60_params['num_blades'],
            num_stations=30,
            airfoil=self.uh60_params['airfoil'],
            collective_pitch=8.0,  # 初始总距
            enable_gpu=False,
            enable_adaptive_mesh=enable_advanced_features,
            enable_dynamic_stall=enable_advanced_features
        )
        
        return solver
    
    def run_single_condition_test(self, condition_name: str, condition: dict) -> dict:
        """运行单个飞行条件测试"""
        
        print(f"\n🧪 测试条件: {condition['description']}")
        print("-" * 50)
        
        # 创建求解器
        solver = self.create_uh60_solver()
        
        # 求解
        start_time = time.time()
        result = solver.solve(
            rpm=condition['rpm'],
            forward_speed=condition['forward_speed'],
            vertical_speed=condition['vertical_speed'],
            density=condition['density'],
            verbose=False
        )
        solve_time = time.time() - start_time
        
        # 提取关键结果
        thrust = result['thrust']
        power = result['power']
        ct = result['ct']
        cp = result['cp']
        figure_of_merit = result.get('figure_of_merit', 0)
        
        print(f"   求解时间: {solve_time:.3f}s")
        print(f"   收敛状态: {'✅ 收敛' if result['converged'] else '❌ 未收敛'} ({result['iterations']}次迭代)")
        print(f"   推力: {thrust:.0f} N")
        print(f"   功率: {power/1000:.1f} kW")
        print(f"   推力系数: {ct:.4f}")
        print(f"   功率系数: {cp:.4f}")
        
        if condition_name == 'hover' and figure_of_merit > 0:
            print(f"   品质因数: {figure_of_merit:.3f}")
        
        # 验证结果合理性
        validation_result = self._validate_performance(condition_name, thrust, power, figure_of_merit)
        
        return {
            'condition': condition_name,
            'converged': result['converged'],
            'solve_time': solve_time,
            'thrust': thrust,
            'power': power,
            'ct': ct,
            'cp': cp,
            'figure_of_merit': figure_of_merit,
            'validation': validation_result,
            'full_result': result
        }
    
    def _validate_performance(self, condition_name: str, thrust: float, power: float, fm: float) -> dict:
        """验证性能结果"""
        
        if condition_name not in self.expected_performance:
            return {'valid': True, 'warnings': ['无参考数据']}
        
        expected = self.expected_performance[condition_name]
        warnings = []
        
        # 推力验证
        thrust_min, thrust_max = expected['thrust_range']
        if not (thrust_min <= thrust <= thrust_max):
            warnings.append(f"推力超出范围: {thrust:.0f}N (期望: {thrust_min}-{thrust_max}N)")
        
        # 功率验证
        power_kw = power / 1000
        power_min, power_max = expected['power_range']
        if not (power_min <= power_kw <= power_max):
            warnings.append(f"功率超出范围: {power_kw:.1f}kW (期望: {power_min}-{power_max}kW)")
        
        # 品质因数验证（仅悬停）
        if condition_name == 'hover' and 'figure_of_merit_range' in expected:
            fm_min, fm_max = expected['figure_of_merit_range']
            if fm > 0 and not (fm_min <= fm <= fm_max):
                warnings.append(f"品质因数超出范围: {fm:.3f} (期望: {fm_min}-{fm_max})")
        
        return {
            'valid': len(warnings) == 0,
            'warnings': warnings
        }
    
    def run_comprehensive_validation(self) -> dict:
        """运行综合验证测试"""
        
        print("🚁 UH-60黑鹰直升机BEMT验证测试")
        print("=" * 80)
        print(f"旋翼参数:")
        print(f"   半径: {self.uh60_params['radius']:.2f}m")
        print(f"   桨叶数: {self.uh60_params['num_blades']}")
        print(f"   实度比: {self.uh60_params['solidity']:.3f}")
        print(f"   翼型: {self.uh60_params['airfoil']}")
        
        # 运行所有测试条件
        test_results = {}
        
        for condition_name, condition in self.test_conditions.items():
            try:
                result = self.run_single_condition_test(condition_name, condition)
                test_results[condition_name] = result
                
                # 显示验证结果
                if result['validation']['valid']:
                    print(f"   ✅ 性能验证通过")
                else:
                    print(f"   ⚠️  性能验证警告:")
                    for warning in result['validation']['warnings']:
                        print(f"      - {warning}")
                        
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                test_results[condition_name] = {'error': str(e)}
        
        # 生成总结报告
        return self._generate_validation_report(test_results)
    
    def _generate_validation_report(self, test_results: dict) -> dict:
        """生成验证报告"""
        
        print(f"\n📊 验证总结报告")
        print("=" * 80)
        
        successful_tests = 0
        total_tests = len(test_results)
        valid_performance = 0
        
        for condition_name, result in test_results.items():
            if 'error' not in result:
                successful_tests += 1
                if result['validation']['valid']:
                    valid_performance += 1
                
                print(f"{condition_name:15s}: "
                      f"{'✅' if result['converged'] else '❌'} "
                      f"T={result['thrust']:6.0f}N "
                      f"P={result['power']/1000:6.1f}kW "
                      f"{'✅' if result['validation']['valid'] else '⚠️'}")
            else:
                print(f"{condition_name:15s}: ❌ 错误")
        
        success_rate = successful_tests / total_tests
        performance_rate = valid_performance / total_tests
        
        print(f"\n🎯 总体评估:")
        print(f"   求解成功率: {success_rate:.1%} ({successful_tests}/{total_tests})")
        print(f"   性能验证率: {performance_rate:.1%} ({valid_performance}/{total_tests})")
        
        overall_success = success_rate >= 0.75 and performance_rate >= 0.5
        print(f"   总体评估: {'🎉 验证成功' if overall_success else '⚠️  需要改进'}")
        
        return {
            'test_results': test_results,
            'success_rate': success_rate,
            'performance_rate': performance_rate,
            'overall_success': overall_success
        }


def main():
    """主函数"""
    
    # 创建验证测试
    validator = UH60ValidationTest()
    
    # 运行综合验证
    report = validator.run_comprehensive_validation()
    
    return report


if __name__ == "__main__":
    validation_report = main()
