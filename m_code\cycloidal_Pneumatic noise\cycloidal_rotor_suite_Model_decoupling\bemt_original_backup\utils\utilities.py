#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BEMT Utilities - Consolidated
BEMT工具函数 - 整合版

Consolidated utility functions for the simplified BEMT module.
This file combines functionality from multiple utility modules.

整合了多个工具模块功能的简化BEMT工具函数。

Author: BEMT Development Team
Date: 2025-01-24
Version: 2.0 (Consolidated)
"""

import numpy as np
import time
import warnings
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

# =============================================================================
# 物理常数 (Physical Constants) - 按照advice1.md建议
# =============================================================================

# 基本物理常数
GRAVITY = 9.80665  # 重力加速度 [m/s²]
AIR_DENSITY_SL = 1.225  # 海平面空气密度 [kg/m³]
AIR_TEMPERATURE_SL = 288.15  # 海平面温度 [K]
AIR_PRESSURE_SL = 101325.0  # 海平面压力 [Pa]
SOUND_SPEED_SL = 343.0  # 海平面声速 [m/s]
GAS_CONSTANT = 287.0  # 空气气体常数 [J/(kg·K)]
GAMMA = 1.4  # 比热比

# 数学常数
PI = np.pi
DEG2RAD = np.pi / 180.0
RAD2DEG = 180.0 / np.pi

# 数值常数
SMALL_NUMBER = 1e-12
DEFAULT_TOLERANCE = 1e-6

# =============================================================================
# 数学工具函数 (Math Utilities)
# =============================================================================

def safe_divide(numerator: Union[float, np.ndarray], 
               denominator: Union[float, np.ndarray],
               default_value: float = 0.0) -> Union[float, np.ndarray]:
    """安全除法，避免除零错误"""
    denominator = np.asarray(denominator)
    numerator = np.asarray(numerator)
    
    result = np.full_like(denominator, default_value, dtype=float)
    nonzero_mask = np.abs(denominator) > 1e-12
    result[nonzero_mask] = numerator[nonzero_mask] / denominator[nonzero_mask]
    
    return result

def safe_sqrt(value: Union[float, np.ndarray], 
              min_value: float = 0.0) -> Union[float, np.ndarray]:
    """安全开方，确保输入非负"""
    value = np.asarray(value)
    safe_value = np.maximum(value, min_value)
    return np.sqrt(safe_value)

def clip_angle(angle: Union[float, np.ndarray], 
               min_deg: float = -180.0, 
               max_deg: float = 180.0) -> Union[float, np.ndarray]:
    """限制角度范围"""
    return np.clip(angle, min_deg, max_deg)

def deg2rad(degrees: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """角度转弧度"""
    return degrees * np.pi / 180.0

def rad2deg(radians: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """弧度转角度"""
    return radians * 180.0 / np.pi

def linear_interpolation(x: np.ndarray, y: np.ndarray,
                        x_new: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """线性插值"""
    return np.interp(x_new, x, y)

def trapezoidal_integration(y: np.ndarray, x: Optional[np.ndarray] = None) -> float:
    """梯形积分"""
    if x is None:
        return np.trapz(y)
    else:
        return np.trapz(y, x)

def vector_magnitude(vector: np.ndarray) -> Union[float, np.ndarray]:
    """计算向量模长"""
    return np.linalg.norm(vector, axis=-1)

def normalize_vector(vector: np.ndarray) -> np.ndarray:
    """向量归一化"""
    magnitude = vector_magnitude(vector)
    magnitude = np.maximum(magnitude, 1e-12)  # 避免除零

    if vector.ndim == 1:
        return vector / magnitude
    else:
        return vector / magnitude[..., np.newaxis]

# =============================================================================
# 收敛监控 (Convergence Monitoring)
# =============================================================================

class SimpleConvergenceMonitor:
    """简化的收敛监控器"""
    
    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 100):
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        self.reset()
    
    def reset(self):
        """重置监控器"""
        self.iteration = 0
        self.residual_history = []
        self.converged = False
    
    def update(self, residual: float) -> bool:
        """更新收敛状态"""
        self.iteration += 1
        self.residual_history.append(residual)
        
        if residual < self.tolerance:
            self.converged = True
            return True
        
        if self.iteration >= self.max_iterations:
            self.converged = False
            return True  # 强制停止
        
        return False
    
    def get_convergence_rate(self) -> float:
        """计算收敛率"""
        if len(self.residual_history) < 3:
            return 0.0
        
        recent = self.residual_history[-3:]
        if recent[0] > 0 and recent[-1] > 0:
            return np.log(recent[0] / recent[-1]) / 2
        return 0.0

# =============================================================================
# 错误处理 (Error Handling)
# =============================================================================

class BEMTError(Exception):
    """BEMT基础异常类"""
    pass

class BEMTConvergenceError(BEMTError):
    """BEMT收敛错误"""
    def __init__(self, message: str, iterations: int = 0, residual: float = 0.0):
        super().__init__(message)
        self.iterations = iterations
        self.residual = residual

class BEMTInputError(BEMTError):
    """BEMT输入参数错误"""
    pass

def validate_positive(value: Union[int, float], name: str, 
                     min_value: float = 0.0) -> float:
    """验证正数输入"""
    try:
        value = float(value)
    except (TypeError, ValueError):
        raise BEMTInputError(f"参数 {name} 必须是数值")
    
    if not np.isfinite(value):
        raise BEMTInputError(f"参数 {name} 必须是有限数值")
    
    if value <= min_value:
        raise BEMTInputError(f"参数 {name} 必须 > {min_value}")
    
    return value

def validate_array(array: Union[List, np.ndarray], name: str, 
                  min_length: int = 1) -> np.ndarray:
    """验证数组输入"""
    try:
        array = np.asarray(array, dtype=float)
    except (TypeError, ValueError):
        raise BEMTInputError(f"参数 {name} 必须是数值数组")
    
    if not np.all(np.isfinite(array)):
        raise BEMTInputError(f"参数 {name} 包含非有限值")
    
    if len(array) < min_length:
        raise BEMTInputError(f"参数 {name} 长度必须 >= {min_length}")
    
    return array

# =============================================================================
# 性能分析 (Performance Profiling)
# =============================================================================

class SimpleProfiler:
    """简化的性能分析器"""
    
    def __init__(self):
        self.timers = {}
        self.enabled = True
    
    def start_timer(self, name: str):
        """开始计时"""
        if self.enabled:
            self.timers[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if not self.enabled or name not in self.timers:
            return 0.0
        
        elapsed = time.time() - self.timers[name]
        del self.timers[name]
        return elapsed
    
    def enable(self):
        """启用性能分析"""
        self.enabled = True
    
    def disable(self):
        """禁用性能分析"""
        self.enabled = False

# 全局性能分析器实例
_profiler = SimpleProfiler()

def start_timer(name: str):
    """开始计时"""
    _profiler.start_timer(name)

def end_timer(name: str) -> float:
    """结束计时"""
    return _profiler.end_timer(name)

# =============================================================================
# 数据处理 (Data Processing)
# =============================================================================

def format_number(value: float, precision: int = 3) -> str:
    """格式化数值显示"""
    if np.isnan(value):
        return "NaN"
    elif np.isinf(value):
        return "∞" if value > 0 else "-∞"
    elif abs(value) > 10**precision or (value != 0 and abs(value) < 10**(-precision)):
        return f"{value:.{precision}e}"
    else:
        return f"{value:.{precision}f}"

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 1e-3:
        return f"{seconds*1e6:.1f} μs"
    elif seconds < 1:
        return f"{seconds*1e3:.1f} ms"
    elif seconds < 60:
        return f"{seconds:.2f} s"
    else:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"

def create_summary_dict(data: Dict[str, Any], title: str = "摘要") -> str:
    """创建摘要字符串"""
    lines = [title, "=" * len(title)]
    
    for key, value in data.items():
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                value_str = format_number(value)
            else:
                value_str = str(value)
        else:
            value_str = str(value)
        
        lines.append(f"{key}: {value_str}")
    
    return "\n".join(lines)

# =============================================================================
# 物理常数 (Physical Constants)
# =============================================================================

# 数学常数
PI = np.pi
DEG2RAD = np.pi / 180.0
RAD2DEG = 180.0 / np.pi

# 物理常数
GRAVITY = 9.80665       # 重力加速度 [m/s²]
AIR_DENSITY_SL = 1.225  # 海平面空气密度 [kg/m³]
AIR_TEMPERATURE_SL = 288.15  # 海平面温度 [K]
AIR_TEMP_SL = 288.15    # 海平面温度 [K] (兼容性)
AIR_PRESSURE_SL = 101325.0   # 海平面压力 [Pa]
SOUND_SPEED_SL = 343.0  # 海平面音速 [m/s]
GAS_CONSTANT = 287.0    # 空气气体常数 [J/(kg·K)]
GAMMA = 1.4             # 比热比

# 数值常数
SMALL_NUMBER = 1e-12    # 小数值
DEFAULT_TOLERANCE = 1e-6  # 默认收敛容差

# =============================================================================
# 单位转换 (Unit Conversions)
# =============================================================================

def rpm_to_rad_per_sec(rpm: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """RPM转换为rad/s"""
    return rpm * PI / 30.0

def rad_per_sec_to_rpm(rad_per_sec: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """rad/s转换为RPM"""
    return rad_per_sec * 30.0 / PI

def knots_to_mps(knots: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """节转换为m/s"""
    return knots * 0.514444

def mps_to_knots(mps: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """m/s转换为节"""
    return mps / 0.514444

# =============================================================================
# 物理计算函数 (Physical Calculations) - 按照advice1.md建议
# =============================================================================

def calculate_air_density(altitude, temperature=None):
    """计算指定高度的空气密度"""
    if temperature is None:
        # 标准大气温度
        temperature = AIR_TEMPERATURE_SL - 0.0065 * altitude

    pressure = AIR_PRESSURE_SL * (temperature / AIR_TEMPERATURE_SL) ** (GRAVITY / (0.0065 * GAS_CONSTANT))
    density = pressure / (GAS_CONSTANT * temperature)
    return density

def calculate_reynolds_number(velocity, chord, density, viscosity):
    """计算雷诺数"""
    return density * velocity * chord / viscosity

def calculate_mach_number(velocity, temperature=AIR_TEMPERATURE_SL):
    """计算马赫数"""
    sound_speed = np.sqrt(GAMMA * GAS_CONSTANT * temperature)
    return velocity / sound_speed

# =============================================================================
# 性能统计和监控 (Performance Monitoring) - 按照advice1.md建议
# =============================================================================

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.reset()

    def reset(self):
        """重置统计"""
        self.start_time = None
        self.end_time = None
        self.function_calls = {}
        self.memory_usage = []

    def start_timing(self):
        """开始计时"""
        self.start_time = time.time()

    def end_timing(self):
        """结束计时"""
        self.end_time = time.time()
        return self.get_elapsed_time()

    def get_elapsed_time(self):
        """获取经过时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0

    def record_function_call(self, func_name):
        """记录函数调用"""
        self.function_calls[func_name] = self.function_calls.get(func_name, 0) + 1

    def get_statistics(self):
        """获取统计信息"""
        return {
            'elapsed_time': self.get_elapsed_time(),
            'function_calls': self.function_calls.copy(),
            'total_calls': sum(self.function_calls.values())
        }

# =============================================================================
# 配置管理 (Configuration Management) - 按照advice1.md建议
# =============================================================================

class SimpleConfig:
    """简化配置管理器"""

    def __init__(self, **kwargs):
        self._config = kwargs

    def get(self, key, default=None):
        """获取配置值"""
        return self._config.get(key, default)

    def set(self, key, value):
        """设置配置值"""
        self._config[key] = value

    def update(self, **kwargs):
        """更新配置"""
        self._config.update(kwargs)

    def to_dict(self):
        """转换为字典"""
        return self._config.copy()

def create_default_config():
    """创建默认配置"""
    return SimpleConfig(
        # 求解器参数
        max_iterations=100,
        tolerance=1e-6,
        relaxation_factor=0.5,

        # 物理模型开关
        enable_dynamic_stall=True,
        enable_3d_effects=True,
        enable_tip_loss=True,
        enable_hub_loss=True,

        # 几何参数
        num_stations=15,
        hub_radius_ratio=0.1,

        # 翼型参数
        airfoil_name='NACA0012',

        # 输出控制
        verbose=True,
        save_convergence_history=True
    )

# =============================================================================
# 结果处理和导出 (Result Processing) - 按照advice1.md建议
# =============================================================================

def extract_key_results(results):
    """提取关键结果"""
    key_results = {}

    # 基本性能参数
    performance_keys = ['thrust', 'torque', 'power', 'CT', 'CQ', 'CP', 'FM']
    for key in performance_keys:
        if key in results:
            key_results[key] = results[key]

    # 收敛信息
    convergence_keys = ['converged', 'iterations', 'residual', 'solve_time']
    for key in convergence_keys:
        if key in results:
            key_results[key] = results[key]

    return key_results

def format_results_summary(results):
    """格式化结果摘要"""
    summary = []
    summary.append("=" * 50)
    summary.append("BEMT中保真度分析结果摘要")
    summary.append("=" * 50)

    # 基本载荷
    if 'thrust' in results:
        summary.append(f"推力: {format_number(results['thrust'])} N")
    if 'torque' in results:
        summary.append(f"扭矩: {format_number(results['torque'])} N·m")
    if 'power' in results:
        summary.append(f"功率: {format_number(results['power'])} W")

    summary.append("-" * 30)

    # 无量纲系数
    if 'CT' in results:
        summary.append(f"推力系数 CT: {format_number(results['CT'], 4)}")
    if 'CQ' in results:
        summary.append(f"扭矩系数 CQ: {format_number(results['CQ'], 4)}")
    if 'CP' in results:
        summary.append(f"功率系数 CP: {format_number(results['CP'], 4)}")

    summary.append("-" * 30)

    # 效率参数
    if 'FM' in results and results['FM'] > 0:
        summary.append(f"品质因数 FM: {format_number(results['FM'], 3)}")

    summary.append("-" * 30)

    # 收敛信息
    if 'converged' in results:
        status = "✅ 收敛" if results['converged'] else "❌ 未收敛"
        summary.append(f"收敛状态: {status}")
    if 'iterations' in results:
        summary.append(f"迭代次数: {results['iterations']}")
    if 'solve_time' in results:
        summary.append(f"求解时间: {format_time(results['solve_time'])}")

    summary.append("=" * 50)

    return "\n".join(summary)

# =============================================================================
# 导出列表 (Exports) - 按照advice1.md建议扩展
# =============================================================================

__all__ = [
    # 异常类
    'BEMTError', 'BEMTConvergenceError', 'BEMTInputError', 'BEMTPhysicsError',

    # 数值计算
    'safe_divide', 'safe_sqrt', 'safe_log', 'safe_arctan2', 'clip_array',

    # 单位转换
    'rpm_to_rad_per_sec', 'rad_per_sec_to_rpm', 'knots_to_mps', 'mps_to_knots',
    'deg_to_rad', 'rad_to_deg', 'hp_to_watts', 'watts_to_hp',

    # 格式化
    'format_number', 'format_time', 'format_percentage', 'format_scientific',

    # 输入验证
    'validate_positive', 'validate_range', 'validate_integer', 'validate_array',

    # 插值和数值方法
    'interpolate_1d', 'smooth_array', 'gradient_1d',

    # 物理计算
    'calculate_air_density', 'calculate_reynolds_number', 'calculate_mach_number',

    # 工具类
    'PerformanceMonitor', 'SimpleConfig', 'SimpleConvergenceMonitor',

    # 配置管理
    'create_default_config',

    # 结果处理
    'extract_key_results', 'format_results_summary', 'save_results_to_text',

    # 调试工具
    'check_array_health', 'diagnose_convergence_issues',

    # 测试函数
    'run_basic_tests',

    # 物理常数
    'GRAVITY', 'AIR_DENSITY_SL', 'AIR_TEMPERATURE_SL', 'AIR_PRESSURE_SL',
    'SOUND_SPEED_SL', 'GAS_CONSTANT', 'GAMMA', 'PI', 'DEG2RAD', 'RAD2DEG'
]
