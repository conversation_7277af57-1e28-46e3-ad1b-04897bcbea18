#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Common Functions - Medium Fidelity BEMT
公共函数 - 中保真度BEMT

This module provides common utility functions used throughout the BEMT module:
- Data processing utilities
- File I/O helpers
- Validation functions
- Formatting utilities

Author: BEMT Medium-Fidelity Development Team
Date: 2025-01-24
Version: 1.0
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import numpy as np
import warnings

def ensure_directory(directory_path: Union[str, Path]) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    Parameters:
    -----------
    directory_path : str or Path
        目录路径
        
    Returns:
    --------
    path : Path
        目录路径对象
    """
    
    path = Path(directory_path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def save_json(data: Dict[str, Any], file_path: Union[str, Path], 
              indent: int = 2, ensure_ascii: bool = False) -> bool:
    """
    保存数据到JSON文件
    
    Parameters:
    -----------
    data : dict
        要保存的数据
    file_path : str or Path
        文件路径
    indent : int
        缩进空格数
    ensure_ascii : bool
        是否确保ASCII编码
        
    Returns:
    --------
    success : bool
        是否保存成功
    """
    
    try:
        file_path = Path(file_path)
        ensure_directory(file_path.parent)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=ensure_ascii, 
                     default=_json_serializer)
        
        return True
        
    except Exception as e:
        warnings.warn(f"保存JSON文件失败: {str(e)}")
        return False

def load_json(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
    """
    从JSON文件加载数据
    
    Parameters:
    -----------
    file_path : str or Path
        文件路径
        
    Returns:
    --------
    data : dict or None
        加载的数据，失败时返回None
    """
    
    try:
        file_path = Path(file_path)
        
        if not file_path.exists():
            warnings.warn(f"JSON文件不存在: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return data
        
    except Exception as e:
        warnings.warn(f"加载JSON文件失败: {str(e)}")
        return None

def _json_serializer(obj):
    """JSON序列化器，处理numpy数组等特殊类型"""
    
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif hasattr(obj, '__dict__'):
        return obj.__dict__
    else:
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def format_number(value: float, precision: int = 3, 
                 scientific: bool = False) -> str:
    """
    格式化数值显示
    
    Parameters:
    -----------
    value : float
        数值
    precision : int
        精度
    scientific : bool
        是否使用科学计数法
        
    Returns:
    --------
    formatted : str
        格式化后的字符串
    """
    
    if np.isnan(value):
        return "NaN"
    elif np.isinf(value):
        return "∞" if value > 0 else "-∞"
    elif scientific or abs(value) > 10**precision or (value != 0 and abs(value) < 10**(-precision)):
        return f"{value:.{precision}e}"
    else:
        return f"{value:.{precision}f}"

def format_time(seconds: float) -> str:
    """
    格式化时间显示
    
    Parameters:
    -----------
    seconds : float
        时间（秒）
        
    Returns:
    --------
    formatted : str
        格式化后的时间字符串
    """
    
    if seconds < 1e-3:
        return f"{seconds*1e6:.1f} μs"
    elif seconds < 1:
        return f"{seconds*1e3:.1f} ms"
    elif seconds < 60:
        return f"{seconds:.2f} s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.0f}s"

def format_memory(bytes_value: int) -> str:
    """
    格式化内存大小显示
    
    Parameters:
    -----------
    bytes_value : int
        字节数
        
    Returns:
    --------
    formatted : str
        格式化后的内存大小字符串
    """
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(bytes_value)
    
    for unit in units:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    
    return f"{size:.1f} PB"

def create_summary_table(data: Dict[str, Any], 
                        title: str = "摘要表格") -> str:
    """
    创建摘要表格
    
    Parameters:
    -----------
    data : dict
        数据字典
    title : str
        表格标题
        
    Returns:
    --------
    table : str
        格式化的表格字符串
    """
    
    lines = [title, "=" * len(title)]
    
    max_key_length = max(len(str(key)) for key in data.keys()) if data else 0
    
    for key, value in data.items():
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                value_str = format_number(value)
            else:
                value_str = str(value)
        else:
            value_str = str(value)
        
        lines.append(f"{str(key):<{max_key_length}} : {value_str}")
    
    return "\n".join(lines)

def validate_file_path(file_path: Union[str, Path], 
                      must_exist: bool = False,
                      create_parent: bool = False) -> Path:
    """
    验证文件路径
    
    Parameters:
    -----------
    file_path : str or Path
        文件路径
    must_exist : bool
        文件是否必须存在
    create_parent : bool
        是否创建父目录
        
    Returns:
    --------
    path : Path
        验证后的路径对象
        
    Raises:
    -------
    ValueError
        如果路径无效
    """
    
    path = Path(file_path)
    
    if must_exist and not path.exists():
        raise ValueError(f"文件不存在: {path}")
    
    if create_parent:
        ensure_directory(path.parent)
    
    return path

def merge_dictionaries(*dicts: Dict[str, Any], 
                      deep_merge: bool = False) -> Dict[str, Any]:
    """
    合并多个字典
    
    Parameters:
    -----------
    *dicts : dict
        要合并的字典
    deep_merge : bool
        是否深度合并
        
    Returns:
    --------
    merged : dict
        合并后的字典
    """
    
    if not dicts:
        return {}
    
    result = {}
    
    for d in dicts:
        if not isinstance(d, dict):
            continue
        
        if deep_merge:
            result = _deep_merge_dict(result, d)
        else:
            result.update(d)
    
    return result

def _deep_merge_dict(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """深度合并字典"""
    
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge_dict(result[key], value)
        else:
            result[key] = value
    
    return result

def filter_dictionary(data: Dict[str, Any], 
                     keys: Optional[List[str]] = None,
                     exclude_keys: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    过滤字典
    
    Parameters:
    -----------
    data : dict
        原始字典
    keys : list, optional
        要包含的键列表
    exclude_keys : list, optional
        要排除的键列表
        
    Returns:
    --------
    filtered : dict
        过滤后的字典
    """
    
    if keys is not None:
        # 只包含指定的键
        return {k: v for k, v in data.items() if k in keys}
    elif exclude_keys is not None:
        # 排除指定的键
        return {k: v for k, v in data.items() if k not in exclude_keys}
    else:
        return data.copy()

def get_file_size(file_path: Union[str, Path]) -> int:
    """
    获取文件大小
    
    Parameters:
    -----------
    file_path : str or Path
        文件路径
        
    Returns:
    --------
    size : int
        文件大小（字节）
    """
    
    try:
        return Path(file_path).stat().st_size
    except:
        return 0

def list_files(directory: Union[str, Path], 
              pattern: str = "*",
              recursive: bool = False) -> List[Path]:
    """
    列出目录中的文件
    
    Parameters:
    -----------
    directory : str or Path
        目录路径
    pattern : str
        文件模式
    recursive : bool
        是否递归搜索
        
    Returns:
    --------
    files : list
        文件路径列表
    """
    
    directory = Path(directory)
    
    if not directory.exists():
        return []
    
    if recursive:
        return list(directory.rglob(pattern))
    else:
        return list(directory.glob(pattern))

# 导出所有函数
__all__ = [
    'ensure_directory',
    'save_json',
    'load_json',
    'format_number',
    'format_time',
    'format_memory',
    'create_summary_table',
    'validate_file_path',
    'merge_dictionaries',
    'filter_dictionary',
    'get_file_size',
    'list_files'
]
